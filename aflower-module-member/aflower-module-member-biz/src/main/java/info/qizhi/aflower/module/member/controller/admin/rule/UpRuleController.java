package info.qizhi.aflower.module.member.controller.admin.rule;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule.*;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.service.rule.UpRuleService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员升级规则")
@RestController
@RequestMapping("/member/up-rule")
@Validated
public class UpRuleController {

    @Resource
    private UpRuleService upRuleService;

    @Resource
    private MemberTypeService memberTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建会员升级规则")
    @PreAuthorize("@ss.hasPermission('member:up-rule:create')")
    public CommonResult<Long> createUpRule(@Valid @RequestBody UpRuleSaveReqVO createReqVO) {
        return success(upRuleService.createUpRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员升级规则")
    @PreAuthorize("@ss.hasPermission('member:up-rule:create')")
    public CommonResult<Boolean> updateUpRule(@Valid @RequestBody UpRuleSaveReqVO updateReqVO) {
        upRuleService.updateUpRule(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新会员升级规则状态")
    @PreAuthorize("@ss.hasPermission('member:up-rule:create')")
    public CommonResult<Boolean> updateUpRuleStatus(@Valid @RequestBody UpRuleUpdateStatusReqVO updateReqVO) {
        upRuleService.updateUpRuleStatus(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员升级规则")
    @PreAuthorize("@ss.hasPermission('member:up-rule:query')")
    public CommonResult<UpRuleRespVO> getUpRule(@Valid UpRuleReqVO reqVO) {
        UpRuleRespVO upRule = upRuleService.getUpRuleRespVO(reqVO);
        return success(upRule);
    }

    @GetMapping("/list")
    @Operation(summary = "获得简单会员升级规则列表")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('member:up-rule:query:list')")
    public CommonResult<List<UpRuleRespVO>> getUpRuleList(@RequestParam("gcode") String gcode) {
        List<UpRuleRespVO> listResult = upRuleService.getUpRuleList(gcode);
        buildMemberType(listResult, gcode);
        return success(listResult);
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得会员升级规则列表", description = "只包含房型代码和房型名称,用于前端列表")
    //@PreAuthorize("@ss.hasPermission('member:up-rule:update')")
    public CommonResult<List<UpRuleRespVO>> getUpRuleSimpleList(@Valid UpRuleSimpleReqVO reqVO) {
        List<UpRuleRespVO> listResult = upRuleService.getUpRuleSimpleList(reqVO);
        buildMemberType(listResult, reqVO.getGcode());
        return success(listResult);
    }

    private void buildMemberType(List<UpRuleRespVO> list, String gcode) {
        if(CollUtil.isEmpty(list)) {
            return;
        }
        List<MemberTypeDO> memberTypeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(gcode));
        Map<String, MemberTypeDO> memberTypeMap = CollectionUtils.convertMap(memberTypeList, MemberTypeDO::getMtCode);

        list.forEach(item -> {
            MemberTypeDO memberType = memberTypeMap.get(item.getSourceMtCode());
            if(memberType != null) {
                item.setSourceMtName(memberType.getMtName());
            }
            memberType = memberTypeMap.get(item.getTargetMtCode());
            if(memberType != null) {
                item.setTargetMtName(memberType.getMtName());
            }
        });
    }

}