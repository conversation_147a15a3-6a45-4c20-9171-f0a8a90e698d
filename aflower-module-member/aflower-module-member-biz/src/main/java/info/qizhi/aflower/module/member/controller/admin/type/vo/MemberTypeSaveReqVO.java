package info.qizhi.aflower.module.member.controller.admin.type.vo;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.validation.PositiveNumber;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.time.LocalTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员类型新增/修改 Request VO")
@Data
public class MemberTypeSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7820")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "{mtName.notempty}")
    @Size(max = 30, message = "{mtName.size}")
    private String mtName;

    @Schema(description = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{level.notempty}")
    private Integer level;

    @Schema(description = "是否永久有效;0：有时间期限，1：永久有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isForEver.notnull}")
    private String isForEver;

    @Schema(description = "有效期（月）;如：12  自升级开始有效期12个月, 当永久有效时，该字段为空", requiredMode = Schema.RequiredMode.REQUIRED)
    @Range(min = 1, max = 120, message = "有效期（月）;如：12则自升级开始有效期12个月")
    private Integer indateMonth;

    @Schema(description = "是否免费;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isFree.notempty}")
    private String isFree;

    @Schema(description = "购卡金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.ka.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @PositiveNumber
    private Long fee;

    @Schema(description = "允许修改卡费金额;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{canUpdateFee.notempty}")
    private String canUpdateFee;

    @Schema(description = "开启注册必须短信验证码;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{needSms.notempty}")
    private String needSms;

    @Schema(description = "是否支持储值;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{supportStore.notempty}")
    private String supportStore;

    @Schema(description = "消费验证方式;0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{verifyMode.notempty}")
    private String verifyMode;

    @Schema(description = "是否延迟noshow时间;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{delayNoshow.notempty}")
    private String delayNoshow;

    @Schema(description = "延迟时间;当delay_noshow=1时，该字段有值")
    private LocalTime noshowTime;

    @Schema(description = "是否有效;0：否 1：是")
    private String isEnable;

    @Schema(description = "图标类型")
    private String imageType;

    @Schema(description = "是否初始化;0：否 1：是")
    private String isInit;

    @Size(max = 255, message = "{remark.size}")
    private String remark;


}