package info.qizhi.aflower.module.member.service.type;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.dal.mysql.member.MemberMapper;
import info.qizhi.aflower.module.member.dal.mysql.type.MemberTypeMapper;
import info.qizhi.aflower.module.member.service.benefit.MemberBenefitService;
import info.qizhi.aflower.module.member.service.benefit.MemberTypeBenefitService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员类型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberTypeServiceImpl implements MemberTypeService {

    @Resource
    private MemberTypeMapper typeMapper;

    @Resource
    private MemberMapper memberMapper;

    @Resource
    @Lazy
    private MemberTypeBenefitService memberTypeBenefitService;

    @Resource
    @Lazy
    private MemberBenefitService memberBenefitService;


    @Override
    public String createType(MemberTypeSaveReqVO createReqVO) {
        // 有延迟时间必须填写时间
        if (NumberEnum.ONE.getNumber().equals(createReqVO.getDelayNoshow()) && createReqVO.getNoshowTime() == null) {
            throw exception(MEMBER_TYPE_NOSHOW_TIME_NOT_EXISTS);
        }
        if(NumberEnum.ONE.getNumber().equals(createReqVO.getIsFree())){
            createReqVO.setFee(0L);
        }
        // 会员类型默认状态
        createReqVO.setIsEnable(NumberEnum.ONE.getNumber());
        // 会员类型代码
        createReqVO.setMtCode(IdUtil.getSnowflakeNextIdStr());
        // 是否永久有效
        if (NumberEnum.ONE.getNumber().equals(createReqVO.getIsForEver())) {
            createReqVO.setIndateMonth(NumberEnum.MAX.getNumberInt());
        }
        // 插入
        MemberTypeDO type = BeanUtils.toBean(createReqVO, MemberTypeDO.class);
        typeMapper.insert(type);
        // 返回
        return type.getMtCode();
    }

    @Override
    public void updateType(MemberTypeSaveReqVO updateReqVO) {
        // 校验存在
        MemberTypeDO memberTypeDO = typeMapper.selectOne(MemberTypeDO::getGcode, updateReqVO.getGcode(), MemberTypeDO::getMtCode, updateReqVO.getMtCode());
        if (memberTypeDO == null) {
            throw exception(TYPE_NOT_EXISTS);
        }
        if(NumberEnum.ONE.getNumber().equals(updateReqVO.getIsFree())){
            updateReqVO.setFee(0L);
        }
        if (NumberEnum.ZERO.getNumber().equals(memberTypeDO.getIsEnable())) {
            // 当禁用会员类型时，判断会员类型下是否有会员
            if (NumberEnum.ZERO.getNumber().equals(updateReqVO.getIsEnable())) {
                if (validateMemberExists(memberTypeDO.getMtCode(), memberTypeDO.getGcode()) > 0) {
                    throw exception(TYPE_MEMBER_NOT_EMPTY);
                }
            }
        }
        // 更新
        MemberTypeDO updateObj = BeanUtils.toBean(updateReqVO, MemberTypeDO.class);
        updateObj.setId(memberTypeDO.getId());
        typeMapper.updateById(updateObj);
    }

    @Override
    public void updateStatus(Long id, String isEnable) {
        MemberTypeDO memberTypeDO = validateTypeExists(id);
        if (NumberEnum.ZERO.getNumber().equals(isEnable)) {
            // 校验会员类型下是否有会员
            if (validateMemberExists(memberTypeDO.getMtCode(), memberTypeDO.getGcode()) > 0) {
                throw exception(TYPE_MEMBER_NOT_EMPTY);
            }
        }
        typeMapper.updateById(new MemberTypeDO().setId(id).setIsEnable(isEnable));
    }


    private Long validateMemberExists(String mtCode, String gcode) {
        return memberMapper.selectCountByMtCode(gcode, mtCode);
    }

    private MemberTypeDO validateTypeExists(Long id) {
        MemberTypeDO memberTypeDO = typeMapper.selectById(id);
        if (memberTypeDO == null) {
            throw exception(TYPE_NOT_EXISTS);
        }
        return memberTypeDO;
    }

    @Override
    public MemberTypeDO getType(String mtCode, String gcode) {
        return typeMapper.selectOneByMtCode(mtCode, gcode);
    }

    @Override
    public MemberTypeDO getTypeByName(String gcode, String mtName) {
        return typeMapper.selectOneByMtName(gcode, mtName);
    }

    /**
     * 获取会员类型列表
     * @param reqVO 查询
     * @return
     */
    @Override
    public List<MemberTypeDO> getTypeList(MemberTypeReqVO reqVO) {
        return typeMapper.selectList(reqVO);
    }

}