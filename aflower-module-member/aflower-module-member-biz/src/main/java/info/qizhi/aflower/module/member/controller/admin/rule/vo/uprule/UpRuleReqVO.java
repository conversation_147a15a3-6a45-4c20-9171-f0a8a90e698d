package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员升级规则分页 Request VO")
@Data
@ToString(callSuper = true)
public class UpRuleReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "规则代码")
    private String ruleCode;

}