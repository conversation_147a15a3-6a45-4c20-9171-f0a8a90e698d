package info.qizhi.aflower.module.member.service.storecard;

import info.qizhi.aflower.framework.common.enums.StoreCardHandleEnum;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.storecard.StoreCardDO;
import jakarta.validation.Valid;

import java.util.List;


/**
 * 会员储值卡 Service 接口
 *
 * <AUTHOR>
 */
public interface StoreCardService {

    /**
     * 创建会员储值卡
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createStoreCard(@Valid StoreCardSaveReqVO createReqVO);

    /**
     * 根据编号更新会员储值卡
     *
     * @param storeCard 会员卡
     */
    void updateStoreCardById(StoreCardDO storeCard);

    /**
     * 根据编号更新会员储值卡
     *
     * @param storeCards 会员卡列表
     */
    void updateStoreCardBatch(List<StoreCardDO> storeCards);

    /**
     * 批量创建会员卡
     *
     * @param storeCardList 会员卡列表
     */
    void createStoreCards(List<StoreCardDO> storeCardList);

    /**
     * 更新会员储值卡
     *
     * @param updateReqVO 更新信息
     */
    void updateStoreCard(@Valid StoreCardSaveReqVO updateReqVO);

    /**
     * 删除会员储值卡
     *
     * @param gcode 集团代码
     * @param mcode 会员代码
     */
    void deleteStoreCard(String gcode, String mcode);


    /**
     * 获得会员储值卡
     *
     * @param storeCardNo 储值卡号
     * @return 会员储值卡
     */
    StoreCardDO getStoreCardByNo(String storeCardNo);

    /**
     * 根据储值卡号获取会员储值卡
     *
     * @param reqVO 入参
     * @return
     */
    List<StoreCardDO> getByStoreCardNo(StoreCardReqVO reqVO);

    /**
     * 获得会员储值卡列表
     *
     * @param
     * @return 会员储值卡列表
     */
    List<StoreCardDO> getStoreCardList(String gcode, String hcode, List<String> mcodes);


    /**
     * 获得会员储值卡分页
     *
     * @param pageReqVO 分页查询
     * @return 会员储值卡分页
     */
    PageResult<StoreCardDO> getStoreCardPage(StoreCardPageReqVO pageReqVO);

    /**
     * 储值卡余额 积分 等计算
     *
     * @param storeCard    储值卡
     * @param handleEnum   操作类型
     * @param consumeFee   消费金额
     * @param consumeFee   消费金额
     * @param giveFee      赠送金额
     * @param consumePoint 消费积分
     * @param givePoint    赠送积分
     */
    void calculateStoreCard(StoreCardHandleEnum handleEnum, StoreCardDO storeCard,
                            Long consumeFee, Long rechargeFee,  Long giveFee, Long consumePoint, Long givePoint);

}