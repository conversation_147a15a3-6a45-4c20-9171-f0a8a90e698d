package info.qizhi.aflower.module.member.service.benefit;

import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitReqVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitRespVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

public interface MemberBenefitService {
    /**
     * 添加会员权益
     * @param createReqVO
     */
    void create(@Valid MemberBenefitSaveReqVO createReqVO);

    /**
     * 获得会员类型及权益列表
     * @param reqVO
     * @return
     */
    List<MemberBenefitRespVO> getTypeBenefitList(@Valid MemberBenefitReqVO reqVO);

    /**
     *
     * @param createReqVOs
     */
    void createBtach(@Valid List<MemberBenefitSaveReqVO> createReqVOs);
}
