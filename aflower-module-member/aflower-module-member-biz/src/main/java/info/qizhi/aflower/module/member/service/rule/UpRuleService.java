package info.qizhi.aflower.module.member.service.rule;

import info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule.*;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.rule.UpRuleDO;

import java.util.List;

/**
 * 会员升级规则 Service 接口
 *
 * <AUTHOR>
 */
public interface UpRuleService {

    /**
     * 创建会员升级规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUpRule(@Valid UpRuleSaveReqVO createReqVO);

    /**
     * 更新会员升级规则
     *
     * @param updateReqVO 更新信息
     */
    void updateUpRule(@Valid UpRuleSaveReqVO updateReqVO);

    void updateUpRuleStatus(@Valid UpRuleUpdateStatusReqVO updateReqVO);

    /**
     * 获得会员升级规则
     *
     * @param reqVO 编号
     * @return 会员升级规则
     */
    UpRuleDO getUpRule(UpRuleReqVO reqVO);

    /**
     * 获取会员升级规则
     *
     * @param reqVO 入参
     * @return 会员升级规则
     */
    UpRuleRespVO getUpRuleRespVO(UpRuleReqVO reqVO);

    /**
     * 获得会员升级规则列表
     *
     * @param gcode 集团代码
     * @return 会员升级规则列表
     */
    List<UpRuleRespVO> getUpRuleList(String gcode);

    /**
     * 获得简单会员升级规则列表
     * @param reqVO
     * @return
     */
    List<UpRuleRespVO> getUpRuleSimpleList(@Valid UpRuleSimpleReqVO reqVO);
}