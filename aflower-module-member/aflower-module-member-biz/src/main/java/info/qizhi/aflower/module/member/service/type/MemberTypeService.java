package info.qizhi.aflower.module.member.service.type;

import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会员类型 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberTypeService {

    /**
     * 创建会员类型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    String createType(@Valid MemberTypeSaveReqVO createReqVO);

    /**
     * 更新会员类型
     *
     * @param updateReqVO 更新信息
     */
    void updateType(@Valid MemberTypeSaveReqVO updateReqVO);

    /**
     * 更新会员类型状态
     * @param id
     * @param isEnable
     * @return
     */
    void updateStatus(Long id, String isEnable);


    /**
     * 获得会员类型
     *
     * @param
     * @return 会员类型
     */
    MemberTypeDO getType(String mtCode, String gcode);

    /**
     * 获得会员类型根据名称
     *
     * @param
     * @return 会员类型
     */
    MemberTypeDO getTypeByName(String gcode, String mtName);

    /**
     * 获得会员类型列表
     *
     * @param pageReqVO 分页查询
     * @return 会员类型分页
     */
    List<MemberTypeDO> getTypeList(MemberTypeReqVO pageReqVO);
}