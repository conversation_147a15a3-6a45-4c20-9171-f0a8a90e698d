package info.qizhi.aflower.module.member.service.storecard;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.StoreCardHandleEnum;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.storecard.StoreCardDO;
import info.qizhi.aflower.module.member.dal.mysql.storecard.StoreCardMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员储值卡 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Lazy
public class StoreCardServiceImpl implements StoreCardService {

    @Resource
    private StoreCardMapper storeCardMapper;

    @Override
    public Long createStoreCard(StoreCardSaveReqVO createReqVO) {
        // 插入
        createReqVO.setState(NumberEnum.ONE.getNumber());
        StoreCardDO storeCard = BeanUtils.toBean(createReqVO, StoreCardDO.class);
        storeCardMapper.insert(storeCard);
        return storeCard.getId();
    }

    @Override
    public void updateStoreCardById(StoreCardDO storeCard) {
        if (storeCard == null) {
            return;
        }
        storeCardMapper.updateById(storeCard);
    }

    @Override
    public void updateStoreCardBatch(List<StoreCardDO> storeCards) {
        storeCardMapper.updateBatch(storeCards);
    }

    @Override
    public void createStoreCards(List<StoreCardDO> storeCardList) {
        if (CollUtil.isEmpty(storeCardList)) {
            return;
        }
        storeCardMapper.insertBatch(storeCardList);
    }

    @Override
    public void updateStoreCard(StoreCardSaveReqVO updateReqVO) {
        // 校验存在
        StoreCardDO storeCardDO = getStoreCardByNo(updateReqVO.getStoreCardNo());
        if (storeCardDO == null) {
            throw exception(STORE_CARD_NOT_EXISTS);
        }
        // 更新
        StoreCardDO updateObj = BeanUtils.toBean(updateReqVO, StoreCardDO.class);
        updateObj.setId(storeCardDO.getId());
        storeCardMapper.updateById(updateObj);
    }

    @Override
    public void deleteStoreCard(String gcode, String mcode) {
        // 校验存在
        StoreCardDO storeCardDO = storeCardMapper.selectOne(StoreCardDO::getGcode, gcode, StoreCardDO::getMcode, mcode);
        if (storeCardDO == null) {
            throw exception(STORE_CARD_NOT_EXISTS);
        }
        storeCardMapper.deleteById(storeCardDO.getId());
    }

    @Override
    public StoreCardDO getStoreCardByNo(String storeCardNo) {
        return storeCardMapper.selectOne(StoreCardDO::getStoreCardNo, storeCardNo);
    }

    @Override
    public List<StoreCardDO> getByStoreCardNo(StoreCardReqVO reqVO) {
        return storeCardMapper.selectByStoreCardNo(reqVO);
    }

    /**
     * 获得会员储值卡列表
     *
     * @param gcode
     * @param hcode
     * @param mcodes
     * @return 会员储值卡列表
     */
    @Override
    public List<StoreCardDO> getStoreCardList(String gcode, String hcode, List<String> mcodes) {
        return storeCardMapper.selectList(gcode, hcode, mcodes);
    }


    @Override
    public PageResult<StoreCardDO> getStoreCardPage(StoreCardPageReqVO pageReqVO) {
        return storeCardMapper.selectPage(pageReqVO);
    }


    @Override
    public void calculateStoreCard(StoreCardHandleEnum handleEnum, StoreCardDO storeCard,
                                   Long consumeFee, Long rechargeFee, Long giveFee, Long consumePoint, Long givePoint) {
        switch (handleEnum) {
            case CONSUME:
                // 消费
                consume(storeCard, consumeFee, givePoint);
                break;
            case RECHARGE:
                // 充值
                recharge(storeCard, consumeFee, giveFee, givePoint);
                break;
            case RED:
                // 冲调
                red(storeCard, consumeFee, rechargeFee, giveFee, givePoint);
                break;
            default:
                throw exception(STORE_CARD_HANDLE_NOT_EXISTS);
        }
    }

    /**
     * 储值卡消费
     *
     * @param storeCard  储值卡
     * @param consumeFee 消费金额
     * @param givePoint  赠送积分
     */
    private void consume(StoreCardDO storeCard, Long consumeFee, Long givePoint) {
        // 判断余额是否充足
        if (storeCard.getBalance() < consumeFee) {
            throw exception(STORE_CARD_BALANCE_NOT_ENOUGH);
        }
        storeCard.setBalance(storeCard.getBalance() - consumeFee)
                 .setTotalFee(storeCard.getTotalFee() + consumeFee)
                 .setTotalPoint(storeCard.getTotalPoint() + givePoint)
                 .setPoint(storeCard.getPoint() + givePoint);
    }

    /**
     * 储值卡充值
     *
     * @param storeCard   储值卡
     * @param rechargeFee 充值金额
     * @param giveFee     赠送金额
     * @param givePoint   赠送积分
     */
    private void recharge(StoreCardDO storeCard, Long rechargeFee, Long giveFee, Long givePoint) {
        storeCard.setBalance(storeCard.getBalance() + rechargeFee + giveFee)
                 .setTotalGive(storeCard.getTotalGive() + giveFee)
                 .setTotalRecharge(storeCard.getTotalRecharge() + rechargeFee)
                 .setTotalPoint(storeCard.getTotalPoint() + givePoint)
                 .setPoint(storeCard.getPoint() + givePoint);
    }

    /**
     * 储值卡冲调
     * 需要将充值金额、赠送金额、赠送积分都冲掉
     *
     * @param storeCard  储值卡
     * @param consumeFee 冲调金额
     * @param giveFee    赠送金额
     * @param givePoint  赠送积分
     */
    private void red(StoreCardDO storeCard, Long consumeFee, Long rechargeFee, Long giveFee, Long givePoint) {
        storeCard.setBalance(storeCard.getBalance() + consumeFee + giveFee - rechargeFee)
                 .setTotalFee(storeCard.getTotalFee()-consumeFee)
                 .setTotalPoint(storeCard.getTotalPoint() - givePoint)
                 .setPoint(storeCard.getPoint() - givePoint)
                 .setTotalGive(storeCard.getTotalGive() + giveFee)
                 .setTotalRecharge(storeCard.getTotalFee() + consumeFee);
    }

}