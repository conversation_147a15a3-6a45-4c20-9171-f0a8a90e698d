package info.qizhi.aflower.module.member.service.user;

import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberInfoAndTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberPwdReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.UserStoreCardRespVO;
import jakarta.validation.Valid;

/**
 * 用户会员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface UserService {

    /**
     * 获得储值卡
     * @param reqVO
     * @return
     */
    UserStoreCardRespVO getMemberAndStoreCard(@Valid MemberReqVO reqVO);

    /**
     * 获得用户会员信息列表
     * @param gcode
     * @return
     */
    AppMemberInfoAndTypeRespVO getMemberTypeList(@Valid String gcode);

    /**
     * 获得用户会员等级信息详情
     * @param gcode
     * @return
     */
    AppMemberTypeRespVO getMemberType(@Valid String gcode);

    /**
     * 修改会员密码
     * @param reqVO
     */
    void updatePwd(@Valid AppMemberPwdReqVO reqVO);
}