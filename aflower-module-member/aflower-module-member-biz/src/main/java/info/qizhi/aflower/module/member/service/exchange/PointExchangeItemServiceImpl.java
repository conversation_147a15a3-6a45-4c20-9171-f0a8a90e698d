package info.qizhi.aflower.module.member.service.exchange;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeItemPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeItemSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.exchange.PointExchangeItemDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.exchange.PointExchangeItemMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 积分兑换项目 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointExchangeItemServiceImpl implements PointExchangeItemService {

    @Resource
    private PointExchangeItemMapper pointExchangeItemMapper;

    @Override
    public Long createPointExchangeItem(PointExchangeItemSaveReqVO createReqVO) {
        // 插入
        PointExchangeItemDO pointExchangeItem = BeanUtils.toBean(createReqVO, PointExchangeItemDO.class);
        pointExchangeItem.setItemCode(IdUtil.getSnowflakeNextIdStr());
        pointExchangeItemMapper.insert(pointExchangeItem);
        // 返回
        return pointExchangeItem.getId();
    }

    @Override
    public void updatePointExchangeItem(PointExchangeItemSaveReqVO updateReqVO) {
        // 校验存在
        validatePointExchangeItemExists(updateReqVO.getId());
        // 更新
        PointExchangeItemDO updateObj = BeanUtils.toBean(updateReqVO, PointExchangeItemDO.class);
        pointExchangeItemMapper.updateById(updateObj);
    }

    @Override
    public void deletePointExchangeItem(Long id) {
        // 校验存在
        validatePointExchangeItemExists(id);
        // 删除
        pointExchangeItemMapper.deleteById(id);
    }

    private void validatePointExchangeItemExists(Long id) {
        if (pointExchangeItemMapper.selectById(id) == null) {
            throw exception(POINT_EXCHANGE_ITEM_NOT_EXISTS);
        }
    }

    @Override
    public PointExchangeItemDO getPointExchangeItem(Long id) {
        return pointExchangeItemMapper.selectById(id);
    }

    @Override
    public PageResult<PointExchangeItemDO> getPointExchangeItemPage(PointExchangeItemPageReqVO pageReqVO) {
        return pointExchangeItemMapper.selectPage(pageReqVO);
    }

}