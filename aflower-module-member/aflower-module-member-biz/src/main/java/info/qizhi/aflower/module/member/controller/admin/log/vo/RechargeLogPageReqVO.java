package info.qizhi.aflower.module.member.controller.admin.log.vo;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员储值日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RechargeLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0")
    private String hcode;

    @Schema(description = "门店名称", example = "王五")
    private String hname;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "会员级别名称", example = "王五")
    private String mtName;

    @Schema(description = "姓名", example = "芋艿")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "充值渠道")
    private String rechargeChannel;

    @Schema(description = "储值卡号")
    private String storeCardNo;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate beginBizDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endBizDate;

}