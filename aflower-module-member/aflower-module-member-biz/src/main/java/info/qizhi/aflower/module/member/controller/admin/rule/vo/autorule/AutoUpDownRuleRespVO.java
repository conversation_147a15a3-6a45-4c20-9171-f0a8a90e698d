package info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import info.qizhi.aflower.module.member.dal.dataobject.rule.AutoUpDownRuleDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员类型自动升降级规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AutoUpDownRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27657")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "规则代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则代码")
    private String ruleCode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("源会员类型代码")
    private String sourceMtCode;
    @Schema(description = "源会员类型名称")
    private String sourceMtName;
    @Schema(description = "源会员类型是否永久有效")
    private String isForEver;
    @Schema(description = "源会员类型有效期(月")
    private Integer indateMonth;

    @Schema(description = "目标会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标会员类型代码")
    private String targetMtCode;
    @Schema(description = "目标会员类型名称")
    private String targetMtName;


    @Schema(description = "自动升级条件")
    @ExcelProperty("自动升级条件")
    @TableField(typeHandler = AutoUpDownRuleDO.AutomaticUpgradeConditionTypeHandler.class)
    private AutoUpDownRuleDO.AutomaticUpgradeCondition autoUpCondition;

    @Schema(description = "是否自动保级;0:否 1:是，当自动保级时，自动保级条件字段无效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否自动保级;0:否 1:是，当自动保级时，自动保级条件字段无效")
    private String isAutoRetain;

    @Schema(description = "自动保级条件")
    @ExcelProperty("自动保级条件")
    @TableField(typeHandler = AutoUpDownRuleDO.AutomaticClassificationConditionTypeHandler.class)
    private AutoUpDownRuleDO.AutomaticClassificationCondition autoRetainCondition;

    @Schema(description = "是否自动降到下一级;0:否 1:是")
    @ExcelProperty("是否自动降到下一级;0:否 1:是")
    private String isAutoDown;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "自动降级后的会员类型代码")
    @ExcelProperty("自动降级后的会员类型代码")
    private String autoDownMtCode;

    @Schema(description = "自动降级后的会员类型名称")
    private String autoDownMtName;

}