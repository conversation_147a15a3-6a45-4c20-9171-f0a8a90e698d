package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

@Schema(description = "管理后台 - 会员级别变动日志新增/修改 Request VO")
@Data
public class LevelLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15230")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;0表示没有发生在门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{name.notempty}")
    @Length(max = 12, message = "姓名长度不能超过12个字符")
    private String name;

    @Schema(description = "源级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{sourceMtcode.notempty}")
    private String sourceMtcode;

    @Schema(description = "源级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "{sourceMtname.notempty}")
    private String sourceMtname;

    @Schema(description = "目标级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{targetMtcode.notempty}")
    private String targetMtcode;

    @Schema(description = "目标级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "{targetMtname.notempty}")
    private String targetMtname;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "变更类型;0:升级 1:降级 2:保级", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{changeType.notempty}")
    private String changeType;

    @Schema(description = "支付类型;0: 现付 1：挂房账", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{payType.notempty}")
    private String payType;

    @Schema(description = "支付金额")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "消耗积分")
    private Integer point;

    @Schema(description = "储值卡号")
    private String storeCardNo;

    @Schema(description = "付款方式")
    private String payMode;

    @Schema(description = "描述", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "创建人", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String creator;

    @Schema(description = "现付账订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String cashBillOrderNo;


}