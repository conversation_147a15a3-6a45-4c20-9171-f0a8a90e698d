package info.qizhi.aflower.module.member.service.storecard;

import info.qizhi.aflower.module.member.controller.admin.storecard.vo.MemberCardDeductPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.MemberCardDeductSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.storecard.MemberCardDeductDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.storecard.MemberCardDeductMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员卡销售提成 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberCardDeductServiceImpl implements MemberCardDeductService {

    @Resource
    private MemberCardDeductMapper cardDeductMapper;

    @Override
    public Long createCardDeduct(MemberCardDeductSaveReqVO createReqVO) {
        // 插入
        MemberCardDeductDO cardDeduct = BeanUtils.toBean(createReqVO, MemberCardDeductDO.class);
        cardDeductMapper.insert(cardDeduct);
        // 返回
        return cardDeduct.getId();
    }

    @Override
    public void updateCardDeduct(MemberCardDeductSaveReqVO updateReqVO) {
        // 校验存在
        validateCardDeductExists(updateReqVO.getId());
        // 更新
        MemberCardDeductDO updateObj = BeanUtils.toBean(updateReqVO, MemberCardDeductDO.class);
        cardDeductMapper.updateById(updateObj);
    }

    @Override
    public void deleteCardDeduct(Long id) {
        // 校验存在
        validateCardDeductExists(id);
        // 删除
        cardDeductMapper.deleteById(id);
    }

    private void validateCardDeductExists(Long id) {
        if (cardDeductMapper.selectById(id) == null) {
            throw exception(CARD_DEDUCT_NOT_EXISTS);
        }
    }

    @Override
    public MemberCardDeductDO getCardDeduct(Long id) {
        return cardDeductMapper.selectById(id);
    }

    @Override
    public PageResult<MemberCardDeductDO> getCardDeductPage(MemberCardDeductPageReqVO pageReqVO) {
        return cardDeductMapper.selectPage(pageReqVO);
    }

}