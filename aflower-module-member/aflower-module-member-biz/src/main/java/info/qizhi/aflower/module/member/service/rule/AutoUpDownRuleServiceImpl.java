package info.qizhi.aflower.module.member.service.rule;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleReqVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.rule.AutoUpDownRuleDO;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.rule.AutoUpDownRuleMapper;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员类型自动升降级规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AutoUpDownRuleServiceImpl implements AutoUpDownRuleService {

    @Resource
    private AutoUpDownRuleMapper autoUpDownRuleMapper;

    @Override
    public Long createAutoUpDownRule(AutoUpDownRuleSaveReqVO createReqVO) {
        // 插入
        AutoUpDownRuleDO autoUpDownRule = BeanUtils.toBean(createReqVO, AutoUpDownRuleDO.class);
        autoUpDownRule.setRuleCode(IdUtil.getSnowflakeNextIdStr());
        if (NumberEnum.ONE.getNumber().equals(createReqVO.getIsAutoDown())){
            if (StrUtil.isBlank(createReqVO.getAutoDownMtCode())){
                throw exception(AUTO_DOWN_MTCODE_NOT_NULL);
            }
        }
        autoUpDownRuleMapper.insert(autoUpDownRule);
        // 返回
        return autoUpDownRule.getId();
    }

    @Override
    public void updateAutoUpDownRule(AutoUpDownRuleSaveReqVO updateReqVO) {
        // 校验存在
        AutoUpDownRuleDO autoUpDownRuleDO = autoUpDownRuleMapper.selectOne(AutoUpDownRuleDO::getGcode, updateReqVO.getGcode(), AutoUpDownRuleDO::getRuleCode, updateReqVO.getRuleCode());
        if (autoUpDownRuleDO == null) {
            throw exception(AUTO_UP_DOWN_RULE_NOT_EXISTS);
        }
        // 更新
        AutoUpDownRuleDO updateObj = BeanUtils.toBean(updateReqVO, AutoUpDownRuleDO.class);
        updateObj.setId(autoUpDownRuleDO.getId());
        autoUpDownRuleMapper.updateById(updateObj);
    }


    @Override
    public AutoUpDownRuleDO getAutoUpDownRule(String ruleCode,String gcode) {
        return autoUpDownRuleMapper.selectOneByRuleCode(ruleCode,gcode);
    }

    @Override
    public List<AutoUpDownRuleDO> getAutoUpDownRuleList(AutoUpDownRuleReqVO reqVO) {
        return autoUpDownRuleMapper.selectList(reqVO);
    }

}