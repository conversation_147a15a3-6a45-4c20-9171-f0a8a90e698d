package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.ConsumeAccountEnum;
import info.qizhi.aflower.framework.common.enums.DictDataEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberSaveReqVO;
import info.qizhi.aflower.module.pms.api.account.dto.ConsumeAccountSaveReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface MemberAccountConvert {

    MemberAccountConvert INSTANCE = Mappers.getMapper(MemberAccountConvert.class);

    default ConsumeAccountSaveReqDTO memberAccountConvert(MemberSaveReqVO reqVO, String handle) {
        ConsumeAccountSaveReqDTO.Consume consume = new ConsumeAccountSaveReqDTO.Consume();
        consume.setFee(reqVO.getBuyFee());
        consume.setSubCode(ConsumeAccountEnum.MEMBER_CARD.getCode());

        if (handle.equals(NumberEnum.ONE.getNumber())) {
            consume.setAccDetail(DictDataEnum.DICT_DATA_REG_MEMBER.getLabel() + " 挂房账：" + "手机号：" + reqVO.getPhone() + " 姓名：" + reqVO.getName());
        }

        if (handle.equals(NumberEnum.TWO.getNumber())) {
            consume.setAccDetail(DictDataEnum.DICT_DATA_MEMBER_UPDATE.getLabel());
        }
        ConsumeAccountSaveReqDTO consumeAccountSaveReqDTO = new ConsumeAccountSaveReqDTO();
        consumeAccountSaveReqDTO.setGcode(reqVO.getGcode());
        consumeAccountSaveReqDTO.setHcode(reqVO.getHcode());
        consumeAccountSaveReqDTO.setTogetherCode(reqVO.getTogetherCode());
        consumeAccountSaveReqDTO.setConsume(consume);
        return consumeAccountSaveReqDTO;
    }

}
