package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/*
    <AUTHOR>
    @description 
    @create 2024 12 2024/12/23 16:36
*/
@Data
@ExcelIgnoreUnannotated
public class MemberAndStoreCardNoDesRespVO {

	@Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30240")
	@ExcelProperty("id")
	private Long id;

	@Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("集团代码")
	private String gcode;

	@Schema(description = "门店代码;注册是集团，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("门店代码;注册是集团，该字段为0")
	private String hcode;

	@Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
	@ExcelProperty("姓名")
	private String name;

	@Schema(description = "手机号码")
	@ExcelProperty("手机号码")
	private String phone;

	@Schema(description = "邮件地址")
	@ExcelProperty("邮件地址")
	private String email;

	@Schema(description = "证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("证件号码")
	private String idNo;

	@Schema(description = "住址")
	@ExcelProperty("住址")
	private String address;

}
