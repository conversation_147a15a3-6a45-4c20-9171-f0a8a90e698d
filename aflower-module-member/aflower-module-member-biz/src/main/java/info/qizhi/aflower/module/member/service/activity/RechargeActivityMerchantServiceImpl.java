package info.qizhi.aflower.module.member.service.activity;

import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityMerchantPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityMerchantSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityMerchantDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.activity.RechargeActivityMerchantMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 充值活动参与门店 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RechargeActivityMerchantServiceImpl implements RechargeActivityMerchantService {

    @Resource
    private RechargeActivityMerchantMapper rechargeActivityMerchantMapper;


    @Override
    public Long createRechargeActivityMerchant(RechargeActivityMerchantSaveReqVO createReqVO) {
        // 插入
        RechargeActivityMerchantDO rechargeActivityMerchant = BeanUtils.toBean(createReqVO, RechargeActivityMerchantDO.class);
        rechargeActivityMerchantMapper.insert(rechargeActivityMerchant);
        // 返回
        return rechargeActivityMerchant.getId();
    }

    @Override
    public void updateRechargeActivityMerchant(RechargeActivityMerchantSaveReqVO updateReqVO) {
        // 校验存在
        validateRechargeActivityMerchantExists(updateReqVO.getId());
        // 更新
        RechargeActivityMerchantDO updateObj = BeanUtils.toBean(updateReqVO, RechargeActivityMerchantDO.class);
        rechargeActivityMerchantMapper.updateById(updateObj);
    }

    @Override
    public void deleteRechargeActivityMerchant(Long id) {
        // 校验存在
        validateRechargeActivityMerchantExists(id);
        // 删除
        rechargeActivityMerchantMapper.deleteById(id);
    }

    private void validateRechargeActivityMerchantExists(Long id) {
        if (rechargeActivityMerchantMapper.selectById(id) == null) {
            throw exception(RECHARGE_ACTIVITY_MERCHANT_NOT_EXISTS);
        }
    }

    @Override
    public RechargeActivityMerchantDO getRechargeActivityMerchant(Long id) {
        return rechargeActivityMerchantMapper.selectById(id);
    }

    @Override
    public PageResult<RechargeActivityMerchantDO> getRechargeActivityMerchantPage(RechargeActivityMerchantPageReqVO pageReqVO) {
        return rechargeActivityMerchantMapper.selectPage(pageReqVO);
    }

}