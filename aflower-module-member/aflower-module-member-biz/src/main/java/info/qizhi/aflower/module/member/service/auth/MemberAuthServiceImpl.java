package info.qizhi.aflower.module.member.service.auth;


import cn.hutool.core.lang.Assert;
import com.alibaba.nacos.common.utils.UuidUtils;
import info.qizhi.aflower.framework.common.enums.UserTypeEnum;
import info.qizhi.aflower.framework.common.util.monitor.TracerUtils;
import info.qizhi.aflower.framework.common.util.servlet.ServletUtils;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberRespVO;
import info.qizhi.aflower.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import info.qizhi.aflower.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import info.qizhi.aflower.module.member.convert.auth.AuthConvert;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.service.member.MemberService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import info.qizhi.aflower.module.system.api.logger.LoginLogApi;
import info.qizhi.aflower.module.system.api.logger.dto.LoginLogCreateReqDTO;
import info.qizhi.aflower.module.system.api.oauth2.OAuth2TokenApi;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import info.qizhi.aflower.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import info.qizhi.aflower.module.system.api.sms.SmsCodeApi;
import info.qizhi.aflower.module.system.api.social.SocialClientApi;
import info.qizhi.aflower.module.system.api.social.SocialUserApi;
import info.qizhi.aflower.module.system.api.social.dto.SocialUserBindReqDTO;
import info.qizhi.aflower.module.system.api.social.dto.SocialWxPhoneNumberInfoRespDTO;
import info.qizhi.aflower.module.system.enums.logger.LoginLogTypeEnum;
import info.qizhi.aflower.module.system.enums.logger.LoginResultEnum;
import info.qizhi.aflower.module.system.enums.oauth2.OAuth2ClientConstants;
import info.qizhi.aflower.module.system.enums.social.SocialTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

import static info.qizhi.aflower.framework.common.util.servlet.ServletUtils.getClientIP;


/**
 * 会员的认证 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MemberAuthServiceImpl implements MemberAuthService {

    @Resource
    private SmsCodeApi smsCodeApi;
    @Resource
    private LoginLogApi loginLogApi;
    @Resource
    private SocialUserApi socialUserApi;
    @Resource
    private SocialClientApi socialClientApi;
    @Resource
    private OAuth2TokenApi oauth2TokenApi;
    @Resource
    private MemberService memberService;
    @Resource
    private MemberTypeService memberTypesService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AppAuthLoginRespVO weixinMiniAppLogin(AppAuthWeixinMiniAppLoginReqVO reqVO) {
        reqVO.setState(UuidUtils.generateUuid());
        // 获得对应的手机号信息
        SocialWxPhoneNumberInfoRespDTO phoneNumberInfo = socialClientApi.getWxMaPhoneNumberInfo(
                UserTypeEnum.C.getValue(), reqVO.getPhoneCode()).getCheckedData();
        Assert.notNull(phoneNumberInfo, "获得手机信息失败，结果为空");
        log.info("{}",phoneNumberInfo);

        // 获得获得注册用户
        MemberRespVO member = memberService.createUserIfAbsent(reqVO.getGcode(), phoneNumberInfo.getPhoneNumber(), phoneNumberInfo.getPhoneNumber());
        Assert.notNull(member.getId(), "获取用户失败，结果为空");

        MemberTypeDO type = memberTypesService.getType(member.getMtCode(), member.getGcode());

        // 绑定社交用户
        String openid = socialUserApi.bindSocialUser(new SocialUserBindReqDTO(member.getId().toString(), getUserType().getValue(),
                SocialTypeEnum.WECHAT_MINI_APP.getType(), reqVO.getCode(), reqVO.getState())).getCheckedData();

        if(!openid.equals(member.getUnionId())){
            memberService.updateAppMember(openid, member.getId());
        }

        // 创建 Token 令牌，记录登录日志
        return createTokenAfterLoginSuccess(member, type.getMtName(), LoginLogTypeEnum.LOGIN_SOCIAL, openid);
    }

    private AppAuthLoginRespVO createTokenAfterLoginSuccess(MemberRespVO member, String mtName,
                                                            LoginLogTypeEnum logType, String openid) {
        // 插入登陆日志
        createLoginLog(member.getId(), member.getPhone(), logType, LoginResultEnum.SUCCESS);
        // 创建 Token 令牌
        OAuth2AccessTokenRespDTO accessTokenRespDTO = oauth2TokenApi.createAccessToken(new OAuth2AccessTokenCreateReqDTO()
                .setUserId(member.getId()).setUserType(getUserType().getValue())
                        .setGcode(member.getGcode()).setPhone(member.getPhone())
                .setClientId(OAuth2ClientConstants.CLIENT_ID_DEFAULT)).getCheckedData();
        // 构建返回结果
        return AuthConvert.INSTANCE.convert(accessTokenRespDTO, openid, member, mtName);
    }

    private void createLoginLog(Long userId, String mobile, LoginLogTypeEnum logType, LoginResultEnum loginResult) {
        // 插入登录日志
        LoginLogCreateReqDTO reqDTO = new LoginLogCreateReqDTO();
        reqDTO.setLogType(logType.getType());
        reqDTO.setTraceId(TracerUtils.getTraceId());
        reqDTO.setUserId(userId);
        reqDTO.setUserType(getUserType().getValue());
        reqDTO.setUsername(mobile);
        reqDTO.setUserAgent(ServletUtils.getUserAgent());
        reqDTO.setUserIp(getClientIP());
        reqDTO.setResult(loginResult.getResult());
        loginLogApi.createLoginLog(reqDTO);
        // 更新最后登录时间
        if (userId != null && Objects.equals(LoginResultEnum.SUCCESS.getResult(), loginResult.getResult())) {
           // userService.updateUserLogin(userId, getClientIP());
        }
    }

    private UserTypeEnum getUserType() {
        return UserTypeEnum.C;
    }

}
