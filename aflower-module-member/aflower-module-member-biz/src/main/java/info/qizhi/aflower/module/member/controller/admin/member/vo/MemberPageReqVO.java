package info.qizhi.aflower.module.member.controller.admin.member.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MemberPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "姓名", example = "芋艿")
    private String name;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "证件类型", example = "1")
    private String idType;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码")
    private String mtCode;

    @Schema(description = "生日")
    private LocalDate birthday;

    @Schema(description = "是否拉黑;0：否 1：拉黑")
    @InStringEnum(value = BooleanEnum.class,message =  "{isBlack.inenum}")
    private String isBlack;

    @Schema(description = "是否允许发送短信;0:不允许 1：允许")
    @InStringEnum(value = BooleanEnum.class,message =  "{isSendMsg.inenum}")
    private String isSendMsg;

    @Schema(description = "有效期;有效期")
    private LocalDate period;

    @Schema(description = "状态;1:正常  0:停用")
    private String state;


}