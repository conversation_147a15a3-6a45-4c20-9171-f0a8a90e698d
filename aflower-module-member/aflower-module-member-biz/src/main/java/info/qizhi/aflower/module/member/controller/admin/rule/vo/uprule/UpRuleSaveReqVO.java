package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员升级规则新增/修改 Request VO")
@Data
public class UpRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7601")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "规则代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ruleCode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{sourceMtCode.notempty}")
    private String sourceMtCode;

    @Schema(description = "目标会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{targetMtCode.notempty}")
    private String targetMtCode;

    @Schema(description = "升级方式(积分兑换、现金支付、挂房账)")
    @NotEmpty(message = "升级方式不能为空")
    private String upMode;

    @Schema(description = "相应额度", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{amount.notnull}")
    //@JsonDeserialize(using = YuanToFenDeserializer.class)
    private String amount;

    @Schema(description = "升级渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= BooleanEnum.class, message = "{isEnable.inenum}")
    private String isEnable;

    @Schema(description = "备注", example = "你猜")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}