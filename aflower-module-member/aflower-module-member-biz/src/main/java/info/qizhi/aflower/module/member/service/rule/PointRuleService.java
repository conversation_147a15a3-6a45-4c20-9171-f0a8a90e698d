package info.qizhi.aflower.module.member.service.rule;

import info.qizhi.aflower.module.member.api.rule.dto.PointRuleSaveReqDTO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule.PointRuleSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.rule.PointRuleDO;

/**
 * 积分规则配置 Service 接口
 *
 * <AUTHOR>
 */
public interface PointRuleService {

    /**
     * 创建积分规则配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPointRule(PointRuleSaveReqDTO createReqVO);

    /**
     * 更新积分规则配置
     *
     * @param updateReqVO 更新信息
     */
    void updatePointRule(@Valid PointRuleSaveReqVO updateReqVO);

    /**
     * 获得积分规则配置
     *
     * @param gcode 编号
     * @return 积分规则配置
     */
    PointRuleDO getPointRule(String gcode);

}