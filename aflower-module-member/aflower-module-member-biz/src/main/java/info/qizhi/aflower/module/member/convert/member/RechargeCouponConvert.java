package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponGenerateBO;
import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 充值优惠卷转换
 *
 * <AUTHOR>
 */
@Mapper
public interface RechargeCouponConvert {

    RechargeCouponConvert INSTANCE = Mappers.getMapper(RechargeCouponConvert.class);

    default CouponGenerateBO rechargeCouponConvert(RechargeActivityDO.TicketsParameter reqVO) {
        return new CouponGenerateBO()
                .setTemplateCode(reqVO.getTemplateCode())
                .setNum(reqVO.getNum());
    }
}
