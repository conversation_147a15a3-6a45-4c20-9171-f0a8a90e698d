package info.qizhi.aflower.module.member.controller.admin.rule;

import info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule.PointRuleRespVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule.PointRuleSaveReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.module.member.dal.dataobject.rule.PointRuleDO;
import info.qizhi.aflower.module.member.service.rule.PointRuleService;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 积分规则配置")
@RestController
@RequestMapping("/member/point-rule")
@Validated
public class PointRuleController {

    @Resource
    private PointRuleService pointRuleService;

    @PutMapping("/update")
    @Operation(summary = "更新积分规则配置")
    @PreAuthorize("@ss.hasPermission('member:point-rule:update')")
    public CommonResult<Boolean> updatePointRule(@Valid @RequestBody PointRuleSaveReqVO updateReqVO) {
        pointRuleService.updatePointRule(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得积分规则配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('member:point-rule:query')")
    public CommonResult<PointRuleRespVO> getPointRule(@RequestParam("gcode") String gcode) {
        PointRuleDO pointRule = pointRuleService.getPointRule(gcode);
        return success(BeanUtils.toBean(pointRule, PointRuleRespVO.class));
    }

}