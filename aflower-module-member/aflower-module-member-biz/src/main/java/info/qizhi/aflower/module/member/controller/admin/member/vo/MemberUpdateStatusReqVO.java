package info.qizhi.aflower.module.member.controller.admin.member.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息新增/修改 Request VO")
@Data
public class MemberUpdateStatusReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "状态;1:正常  0:停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class,message =  "{state.updata.inenum}")
    @NotEmpty(message = "{state.updata.inenum}")
    private String state;


}
