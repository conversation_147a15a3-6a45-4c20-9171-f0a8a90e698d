package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.CouponActivityTypeEnum;
import info.qizhi.aflower.module.marketing.api.couponactivity.dto.CouponActivityReqDTO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberUpgradeReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 会员赠券活动转换
 * <AUTHOR>
 */
@Mapper
public interface MemberUpgreadCouponConvert {

    MemberUpgreadCouponConvert INSTANCE = Mappers.getMapper(MemberUpgreadCouponConvert.class);

    default CouponActivityReqDTO memberUpgradeCouponConvert(MemberUpgradeReqVO reqVO) {
        CouponActivityReqDTO couponActivityReqDTO = new CouponActivityReqDTO();
        couponActivityReqDTO.setActivityCode(CouponActivityTypeEnum.UPGRADE.getCode());
        couponActivityReqDTO.setGcode(reqVO.getGcode());
        couponActivityReqDTO.setHcode(reqVO.getHcode());
        couponActivityReqDTO.setSourceMtCode(reqVO.getSourceMtcode());
        couponActivityReqDTO.setTargetMtCode(reqVO.getTargetMtcode());
        return couponActivityReqDTO;
    }
}
