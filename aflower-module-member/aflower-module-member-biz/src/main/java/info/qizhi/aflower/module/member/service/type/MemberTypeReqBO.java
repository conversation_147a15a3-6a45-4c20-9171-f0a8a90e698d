package info.qizhi.aflower.module.member.service.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "小游 - 会员查询信息 BO")
@Data
public class MemberTypeReqBO {

    @Schema(description = "搜索关键字", example = "C1234567890")
    private String keyword;

    @Schema(description = "0：V3会员编号，1：手机号，2：会员卡，3：三方ID", example = "0：V3会员编号，1：手机号，2：会员卡，3：三方ID")
    private Integer itype;

}
