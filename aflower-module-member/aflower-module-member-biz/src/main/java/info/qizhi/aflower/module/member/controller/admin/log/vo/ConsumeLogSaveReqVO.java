package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员消费日志新增/修改 Request VO")
@Data
public class ConsumeLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5445")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String hname;

    @Schema(description = "每次消费代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String consumeCode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mtCode.notempty}")
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String mtName;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{name.notempty}")
    @Length(max = 12, message = "姓名长度不能超过12个字符")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mobile.notempty}")
    @Mobile
    private String phone;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNo.notblank}")
    private String orderNo;

    @Schema(description = "订单类型;cash:现付账订单, room:房间订单")
    private String orderType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{feemoeny.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "场景", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{scene.notempty}")
    private String scene;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeCardNo;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{payMethod.notempty}")
    private String payMethod;

    @Schema(description = "营业日")
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "消费类型;0:房费消费 1:非房费消费")
    @NotEmpty(message = "{consumeType.notempty}")
    private String consumeType;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "会员密码")
    private String pwd;

    @Schema(description = "是否为冲调操作; 0:是，1:否")
    private String offset;

    @Schema(description = "操作员")
    @NotEmpty(message = "{operator.notempty}")
    private String operator;

}