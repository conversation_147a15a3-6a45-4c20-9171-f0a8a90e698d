package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.CouponActivityTypeEnum;
import info.qizhi.aflower.module.marketing.api.couponactivity.dto.CouponActivityReqDTO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 会员赠券活动转换
 */
@Mapper
public interface MemberCouponConvert {

    MemberCouponConvert INSTANCE = Mappers.getMapper(MemberCouponConvert.class);

    default CouponActivityReqDTO memberCouponConvert(MemberSaveReqVO reqVO) {
        CouponActivityReqDTO couponActivityReqDTO = new CouponActivityReqDTO();
        couponActivityReqDTO.setActivityCode(CouponActivityTypeEnum.REGISTER.getCode());
        couponActivityReqDTO.setGcode(reqVO.getGcode());
        couponActivityReqDTO.setHcode(reqVO.getHcode());
        couponActivityReqDTO.setName(reqVO.getName());
        couponActivityReqDTO.setPhone(reqVO.getPhone());
        couponActivityReqDTO.setSourceMtCode(reqVO.getMtCode());
        couponActivityReqDTO.setTargetMtCode(reqVO.getMtCode());
        return couponActivityReqDTO;
    }
}
