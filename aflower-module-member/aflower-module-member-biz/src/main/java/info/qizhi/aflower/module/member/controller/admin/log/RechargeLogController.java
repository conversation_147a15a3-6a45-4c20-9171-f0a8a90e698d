package info.qizhi.aflower.module.member.controller.admin.log;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.RechargeLogDO;
import info.qizhi.aflower.module.member.service.log.RechargeLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员储值日志")
@RestController
@RequestMapping("/member/recharge-log")
@Validated
public class RechargeLogController {

    @Resource
    private RechargeLogService rechargeLogService;

    @PostMapping("/create")
    @Operation(summary = "创建会员储值日志")
    @PreAuthorize("@ss.hasPermission('member:recharge-log:create')")
    public CommonResult<Long> createRechargeLog(@Valid @RequestBody RechargeLogSaveReqVO createReqVO) {
        return success(rechargeLogService.createRechargeLog(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员储值日志(冲调)")
    //@PreAuthorize("@ss.hasPermission('member:recharge-log:query:get')")
    public CommonResult<RechargeLogRespVO> getRechargeLog(@Valid @RequestBody RechargeLogReqVO reqVO) {
        RechargeLogDO rechargeLog = rechargeLogService.getRechargeLogByCashBillOrderNo(reqVO);
        return success(BeanUtils.toBean(rechargeLog, RechargeLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员储值日志分页")
    @PreAuthorize("@ss.hasPermission('member:recharge-log:query:page')")
    public CommonResult<PageResult<RechargeLogRespVO>> getRechargeLogPage(@Valid RechargeLogPageReqVO pageReqVO) {
        PageResult<RechargeLogDO> pageResult = rechargeLogService.getRechargeLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RechargeLogRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员储值日志 Excel")
    @PreAuthorize("@ss.hasPermission('member:recharge-log:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportRechargeLogExcel(@Valid RechargeLogPageReqVO pageReqVO,
                                       HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RechargeLogDO> list = rechargeLogService.getRechargeLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员储值日志.xls", "数据", RechargeLogRespVO.class,
                BeanUtils.toBean(list, RechargeLogRespVO.class));
    }

}