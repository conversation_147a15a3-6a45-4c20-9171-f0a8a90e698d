package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.ConsumeAccountEnum;
import info.qizhi.aflower.module.member.controller.admin.member.vo.RechargeSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberSaveReqVO;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderSaveReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 现付账订单转换
 *
 * <AUTHOR>
 */
@Mapper
public interface CashBillOrderConvert {

    CashBillOrderConvert INSTANCE = Mappers.getMapper(CashBillOrderConvert.class);

    default CashBillOrderSaveReqDTO memberCashBillOrderConvert(MemberSaveReqVO reqVO) {
        return new CashBillOrderSaveReqDTO().setGcode(reqVO.getGcode())
                                            .setHcode(reqVO.getHcode())
                                            .setIsRoomAccount(BooleanEnum.FALSE.getValue())
                                            .setSeller(reqVO.getSeller())
                                            .setPath(reqVO.getDevelopWay())
                                            .setSeller(reqVO.getSeller())
                                            .setRemark(reqVO.getRemark())
                                            .setAccType(AccountTypeEnum.CASH.getCode());
    }

    default CashBillOrderSaveReqDTO rechargeCashBillOrderConvert(RechargeSaveReqVO reqVO) {
        CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO = new CashBillOrderSaveReqDTO().setGcode(reqVO.getGcode())
                                                                                       .setHcode(reqVO.getHhcode())
                                                                                       .setIsRoomAccount(BooleanEnum.FALSE.getValue())
                                                                                       .setPath(reqVO.getRechargeChannel())
                                                                                       .setBuyContent(ConsumeAccountEnum.MEMBER_RECHARGE.getLabel())
                                                                                       .setSeller(reqVO.getSeller())
                                                                                       .setAccType(AccountTypeEnum.CASH.getCode())
                                                                                       .setRemark(reqVO.getRemark());
        CashBillOrderSaveReqDTO.Consume consume = new CashBillOrderSaveReqDTO.Consume().setFee(reqVO.getFee())
                                                                                       .setSubCode(ConsumeAccountEnum.MEMBER_RECHARGE.getCode())
                                                                                       .setAccDetail(ConsumeAccountEnum.MEMBER_RECHARGE.getLabel());
        CashBillOrderSaveReqDTO.Pay pay = new CashBillOrderSaveReqDTO.Pay().setFee(reqVO.getFee())
                                                                            .setMcode(reqVO.getMcode())
                                                                           .setPayCode(reqVO.getPayCode())
                                                                           .setSubCode(reqVO.getPayMethod())
                                                                           .setBankType(reqVO.getBankType()).setBankCardNo(reqVO.getBankNo());
        cashBillOrderSaveReqDTO.setConsume(consume).setPay(pay);
        return cashBillOrderSaveReqDTO;
    }

}
