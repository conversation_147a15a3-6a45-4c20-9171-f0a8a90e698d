package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员储值日志新增/修改 Request VO")
@Data
public class RechargeLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13176")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "发生门店代码;不是在门店充值，该字段为空0不能为空")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String hname;

    @Schema(description = "每次充值的代码;系统生成唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeCode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String mtName;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "{name.notempty}")
    @Length(max = 12, message = "姓名长度不能超过12个字符")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mobile.notempty}")
    @Mobile
    private String phone;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.putin.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "赠送积分", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long givePoint;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{payMethod.notempty}")
    private String payMethod;

    @Schema(description = "授权码;微信 支付宝 付款时的授权码")
    private String payCode;

    @Schema(description = "充值渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeChannel;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeCardNo;

    @Schema(description = "充值赠送金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "充值赠送金额不能为空")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long giveMoney;

    @Schema(description = "充值活动代码;0表示没有参与活动", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "充值活动代码;0表示没有参与活动不能为空")
    private String activityCode;

    @Schema(description = "营业日")
    private LocalDate bizDate;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "现付账订单号")
    private String cashBillOrderNo;

    @Schema(description = "是否为冲调操作; 0:是，1:否")
    private String offset;

    @Schema(description = "备注")
    private String remark;

}