package info.qizhi.aflower.module.member.controller.admin.type.vo;


import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 会员类型新增/修改 Request VO")
@Data
public class MemberTypeUpdateStatusReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7820")
    @NotNull(message = "{id.notnull}")
    private Long id;

    @Schema(description = "是否有效;0：否 1：是")
    @NotEmpty(message = "状态不能为空")
    @InStringEnum(value = BooleanEnum.class, message = "是否有效;0：否 1：是")
    private String isEnable;



}