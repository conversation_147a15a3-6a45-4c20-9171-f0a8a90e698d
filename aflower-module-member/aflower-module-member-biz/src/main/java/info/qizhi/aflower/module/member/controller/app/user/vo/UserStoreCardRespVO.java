package info.qizhi.aflower.module.member.controller.app.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.desensitize.core.slider.annotation.MobileDesensitize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserStoreCardRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30240")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码;注册是集团，该字段为0")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店名称")
    private String hName;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("姓名")
    private String name;


    @Schema(description = "手机号码")
    @ExcelProperty("手机号码")
    @MobileDesensitize
    private String phone;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员类型(会员级别);0:散客 ，其他为会员类型代码")
    private String mtCode;

    @Schema(description = "会员类型(会员级别)名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员类型(会员级别)名称")
    private String mtName;

    @Schema(description = "消费次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "25649")
    @ExcelProperty("消费次数")
    private Integer feeCount;

    @Schema(description = "是否允许发送短信;0:不允许 1：允许", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否允许发送短信;0:不允许 1：允许")
    private String isSendMsg;

    @Schema(description = "有效期;有效期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("有效期;有效期")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime period;

    @Schema(description = "状态;1:正常  0:停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;1:正常  0:停用")
    private String state;

    @Schema(description = "优惠券数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long couponNum;

    @Schema(description = "消费验证方式;0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String verifyMode;

    @Schema(description = "储值卡信息")
    private List<StoreCard> storeCards;

    @Data
    public static class StoreCard {
        @Schema(description = "门店代码;注册是集团，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
        private String hcode;

        @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
        private String storeCardNo;

        @Schema(description = "外部储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
        private String outCardNo;

        @Schema(description = "状态;valid：有效 invalid：无效 freeze：冻结", requiredMode = Schema.RequiredMode.REQUIRED)
        private String state;

        @Schema(description = "是否集团储值卡;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        private String isG;

        @Schema(description = "该卡是否能充值;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        private String isRecharge;

        @Schema(description = "余额", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long balance;

        @Schema(description = "累积积分", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long totalPoint;

        @Schema(description = "可用积分", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long point;

        @Schema(description = "累积已用积分", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long usedPoint;

        @Schema(description = "总消费", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "总赠送", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long totalGive;

        @Schema(description = "总充值", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalRecharge;
    }
}