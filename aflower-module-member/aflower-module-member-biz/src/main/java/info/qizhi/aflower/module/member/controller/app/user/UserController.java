package info.qizhi.aflower.module.member.controller.app.user;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberInfoAndTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberPwdReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.UserStoreCardRespVO;
import info.qizhi.aflower.module.member.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "订房小程序")
@RestController
@RequestMapping("/member/app")
@Validated
@Slf4j
public class UserController {

    @Resource
    private UserService userService;

    @GetMapping("/get/card")
    @Operation(summary = "获得会员卡信息")
    public CommonResult<UserStoreCardRespVO> getMember(@Valid MemberReqVO reqVO) {
        UserStoreCardRespVO member = userService.getMemberAndStoreCard(reqVO);
        return success(member);
    }

    @GetMapping("/list")
    @Operation(summary = "获得用户会员信息及会员等级列表")
    public CommonResult<AppMemberInfoAndTypeRespVO> getMemberTypeList(@Valid @RequestParam("gcode") String gcode) {
        AppMemberInfoAndTypeRespVO member = userService.getMemberTypeList(gcode);
        return success(member);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户会员等级及权益信息")
    public CommonResult<AppMemberTypeRespVO> getMemberType(@RequestParam("gcode") String gcode) {
        AppMemberTypeRespVO member = userService.getMemberType(gcode);
        return success(member);
    }

    @PutMapping("/update-password")
    @Operation(summary = "修改用户会员密码")
    public CommonResult<Boolean> updatePwd(@Valid @RequestBody AppMemberPwdReqVO reqVO) {
        userService.updatePwd(reqVO);
        return success(true);
    }

}
