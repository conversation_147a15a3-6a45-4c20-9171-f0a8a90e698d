package info.qizhi.aflower.module.member.controller.admin.member.vo;

import info.qizhi.aflower.framework.common.validation.IdCard;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Request VO")
@Data
public class MemberReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0")
    private String hcode;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "姓名", example = "芋艿")
    @Length(max = 10, message = "{name.length}")
    private String name;

    @Schema(description = "手机号码")
    //@Mobile
    private String phone;

    @Schema(description = "关键词查询，支持手机号，会员卡号查询")
    private String keyWords;

    @Schema(description = "证件类型", example = "1")
    private String idType;

    @Schema(description = "证件号码")
    @IdCard
    private String idNo;

}