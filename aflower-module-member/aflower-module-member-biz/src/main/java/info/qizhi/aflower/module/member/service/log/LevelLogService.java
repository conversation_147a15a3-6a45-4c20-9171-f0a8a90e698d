package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.log.LevelLogDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;


/**
 * 会员级别变动日志 Service 接口
 *
 * <AUTHOR>
 */
public interface LevelLogService {

    /**
     * 创建会员级别变动日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createLevelLog(@Valid LevelLogSaveReqVO createReqVO);


    /**
     * 获得会员级别变动日志
     *
     * @param
     * @return 会员级别变动日志
     */
    LevelLogDO getLevelLog(LevelLogReqVO reqVO);

    /**
     * 获得会员级别变动日志分页
     *
     * @param pageReqVO 分页查询
     * @return 会员级别变动日志分页
     */
    PageResult<LevelLogRespVO> getLevelLogPage(LevelLogPageReqVO pageReqVO);

}