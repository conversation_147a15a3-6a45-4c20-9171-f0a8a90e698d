package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 会员升级规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UpRuleSimpleRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7601")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "规则代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则代码")
    private String ruleCode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("源会员类型代码")
    private String sourceMtCode;
    private String sourceMtName;

    @Schema(description = "目标会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标会员类型代码")
    private String targetMtCode;
    private String targetMtName;

    @Schema(description = "升级方式(积分兑换、现金支付、挂房账)")
    @ExcelProperty("升级方式")
    private String upMode;

    @Schema(description = "相应额度", requiredMode = Schema.RequiredMode.REQUIRED)
    private String amount;

    @Schema(description = "升级渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

}
