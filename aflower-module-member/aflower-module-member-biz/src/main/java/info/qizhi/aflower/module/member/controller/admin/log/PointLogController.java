package info.qizhi.aflower.module.member.controller.admin.log;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.PointLogDO;
import info.qizhi.aflower.module.member.service.log.PointLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员积分日志")
@RestController
@RequestMapping("/member/point-log")
@Validated
public class PointLogController {

    @Resource
    private PointLogService pointLogService;

    @PostMapping("/create")
    @Operation(summary = "创建会员积分日志")
    @PreAuthorize("@ss.hasPermission('member:point-log:create')")
    public CommonResult<Long> createPointLog(@Valid @RequestBody PointLogSaveReqVO createReqVO) {
        return success(pointLogService.createPointLog(createReqVO));
    }


    @GetMapping("/get")
    @Operation(summary = "获得会员积分日志")
    @PreAuthorize("@ss.hasPermission('member:point-log:query:get')")
    public CommonResult<PointLogRespVO> getPointLog(@RequestParam("mcode") String mcode,@RequestParam("hcode")String hcode,@RequestParam("gcode")String gcode) {
        PointLogDO pointLog = pointLogService.getPointLog(mcode,hcode,gcode);
        return success(BeanUtils.toBean(pointLog, PointLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员积分日志分页")
    @PreAuthorize("@ss.hasPermission('member:point-log:query:page')")
    public CommonResult<PageResult<PointLogRespVO>> getPointLogPage(@Valid PointLogPageReqVO pageReqVO) {
        PageResult<PointLogDO> pageResult = pointLogService.getPointLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, PointLogRespVO.class));
    }


}