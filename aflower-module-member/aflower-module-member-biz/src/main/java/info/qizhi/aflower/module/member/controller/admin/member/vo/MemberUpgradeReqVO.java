package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.MemberPathwayEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息升级 Request VO")
@Data
public class MemberUpgradeReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "会员卡所属门店")
    private String BelongHcode;

    @Schema(description = "门店名称(会员升级操作需要传门店名称)")
    private String hname;

    @Schema(description = "会员代码")
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "源级别代码")
    @NotEmpty(message = "{sourceMtcode.notempty}")
    private String sourceMtcode;

    @Schema(description = "目标级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{targetMtcode.notempty}")
    private String targetMtcode;

    @Schema(description = "单号(入账账号)房间订单号")
    private String no;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "消费金额")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long buyFee;

    @Schema(description = "支付方式; 0:现付,1:挂房账")
    @NotEmpty(message = "{payType.inenum}")
    @InStringEnum(value = BooleanEnum.class, message = "支付方式只能是0，1")
    private String payType;

    @Schema(description = "付款方式")
    private String payMethod;

    @Schema(description = "银行卡号")
    private String bankNo;

    @Schema(description = "银行类型代码")
    private String bankType;

    @Schema(description = "付款金额")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long payFee;

    @Schema(description = "AR账套代码")
    private String arSetCode;

    @Schema(description = "付款码、AR账套代码")
    private String payCode;

    @Schema(description = "支付积分")
    private Integer payPoint;

    @Schema(description = "储值卡号")
    private String storeCardNo;


    @Schema(description = "发展途径;会员发展途径，如：微信:mini_app 门店:lobby")
    @InStringEnum(value = MemberPathwayEnum.class)
    @NotBlank(message = "{developWay.notblank}")
    private String developWay;

    @Schema(description = "备注", example = "你说的对")
    private String remark;
}
