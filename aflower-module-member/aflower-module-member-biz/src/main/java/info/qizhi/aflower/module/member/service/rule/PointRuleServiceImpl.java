package info.qizhi.aflower.module.member.service.rule;

import info.qizhi.aflower.module.member.api.rule.dto.PointRuleSaveReqDTO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule.PointRuleSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.rule.PointRuleDO;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.rule.PointRuleMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 积分规则配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointRuleServiceImpl implements PointRuleService {

    @Resource
    private PointRuleMapper pointRuleMapper;

    @Override
    public void createPointRule(PointRuleSaveReqDTO createReqVO) {
        PointRuleDO pointRuleDO = getPointRule(createReqVO.getGcode());
        if (pointRuleDO != null) {
            throw exception(POINT_RULE_EXISTS);
        }
        pointRuleMapper.insert(BeanUtils.toBean(createReqVO, PointRuleDO.class));
    }

    @Override
    public void updatePointRule(PointRuleSaveReqVO updateReqVO) {
        // 校验存在
        PointRuleDO pointRuleDO = getPointRule(updateReqVO.getGcode());
        if (pointRuleDO == null){
            throw exception(POINT_RULE_NOT_EXISTS);
        }
        // 更新
        PointRuleDO updateObj = BeanUtils.toBean(updateReqVO, PointRuleDO.class);
        updateObj.setId(pointRuleDO.getId());
        pointRuleMapper.updateById(updateObj);
    }

    @Override
    public PointRuleDO getPointRule(String gcode) {
        return pointRuleMapper.selectOne(PointRuleDO::getGcode,gcode);
    }



}