package info.qizhi.aflower.module.member.service.rule;

import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleReqVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.rule.AutoUpDownRuleDO;

import java.util.List;

/**
 * 会员类型自动升降级规则 Service 接口
 *
 * <AUTHOR>
 */
public interface AutoUpDownRuleService {

    /**
     * 创建会员类型自动升降级规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createAutoUpDownRule(@Valid AutoUpDownRuleSaveReqVO createReqVO);

    /**
     * 更新会员类型自动升降级规则
     *
     * @param updateReqVO 更新信息
     */
    void updateAutoUpDownRule(@Valid AutoUpDownRuleSaveReqVO updateReqVO);

    /**
     * 获得会员类型自动升降级规则
     *
     * @param
     * @return 会员类型自动升降级规则
     */
    AutoUpDownRuleDO getAutoUpDownRule(String sourceMtCode,String gcode);

    /**
     * 获得会员类型自动升降级规则列表
     *
     * @return 会员类型自动升降级规则列表
     */
    List<AutoUpDownRuleDO> getAutoUpDownRuleList(@Valid AutoUpDownRuleReqVO reqVO);

}