package info.qizhi.aflower.module.member.service.exchange;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeGiftTypePageReqVO;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeGiftTypeSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.exchange.PointExchangeGiftTypeDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.exchange.PointExchangeGiftTypeMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 积分兑换-礼品分类 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointExchangeGiftTypeServiceImpl implements PointExchangeGiftTypeService {

    @Resource
    private PointExchangeGiftTypeMapper pointExchangeGiftTypeMapper;

    @Override
    public Long createPointExchangeGiftType(PointExchangeGiftTypeSaveReqVO createReqVO) {
        // 插入
        PointExchangeGiftTypeDO pointExchangeGiftType = BeanUtils.toBean(createReqVO, PointExchangeGiftTypeDO.class);
        pointExchangeGiftType.setGtCode(IdUtil.getSnowflakeNextIdStr());
        pointExchangeGiftTypeMapper.insert(pointExchangeGiftType);
        // 返回
        return pointExchangeGiftType.getId();
    }

    @Override
    public void updatePointExchangeGiftType(PointExchangeGiftTypeSaveReqVO updateReqVO) {
        // 校验存在
        validatePointExchangeGiftTypeExists(updateReqVO.getId());
        // 更新
        PointExchangeGiftTypeDO updateObj = BeanUtils.toBean(updateReqVO, PointExchangeGiftTypeDO.class);
        pointExchangeGiftTypeMapper.updateById(updateObj);
    }

    @Override
    public void deletePointExchangeGiftType(Long id) {
        // 校验存在
        validatePointExchangeGiftTypeExists(id);
        // 删除
        pointExchangeGiftTypeMapper.deleteById(id);
    }

    private void validatePointExchangeGiftTypeExists(Long id) {
        if (pointExchangeGiftTypeMapper.selectById(id) == null) {
            throw exception(POINT_EXCHANGE_GIFT_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public PointExchangeGiftTypeDO getPointExchangeGiftType(Long id) {
        return pointExchangeGiftTypeMapper.selectById(id);
    }

    @Override
    public PageResult<PointExchangeGiftTypeDO> getPointExchangeGiftTypePage(PointExchangeGiftTypePageReqVO pageReqVO) {
        return pointExchangeGiftTypeMapper.selectPage(pageReqVO);
    }

}