package info.qizhi.aflower.module.member.controller.admin.type.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalTime;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员类型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MemberTypeRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7820")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员类型代码")
    private String mtCode;

    @Schema(description = "会员类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("会员类型名称")
    private String mtName;

    @Schema(description = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("级别")
    private Integer level;

    @Schema(description = "是否永久有效;0：有时间期限，1：永久有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否永久有效;0：有时间期限，1：永久有效")
    private String isForEver;

    @Schema(description = "有效期（月）;如：12  自升级开始有效期12个月, 当永久有效时，该字段为空", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("有效期（月）;如：12  自升级开始有效期12个月, 当永久有效时，该字段为空")
    private Integer indateMonth;

    @Schema(description = "是否免费;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否免费;0：否 1：是")
    private String isFree;

    @Schema(description = "购卡金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("购卡金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "允许修改卡费金额;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("允许修改卡费金额;0：否 1：是")
    private String canUpdateFee;

    @Schema(description = "开启注册必须短信验证码;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开启注册必须短信验证码;0：否 1：是")
    private String needSms;

    @Schema(description = "是否支持储值;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否支持储值;0：否 1：是")
    private String supportStore;

    @Schema(description = "消费验证方式;0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消费验证方式;0:无需验证 1:消费需要验证会员密码 2:消费需要短信验证码")
    private String verifyMode;

    @Schema(description = "是否延迟noshow时间;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否延迟noshow时间;0：否 1：是")
    private String delayNoshow;

    @Schema(description = "延迟时间;当delay_noshow=1时，该字段有值")
    @ExcelProperty("延迟时间;当delay_noshow=1时，该字段有值")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime noshowTime;

    @Schema(description = "是否有效;0：否 1：是")
    @ExcelProperty("是否有效;0：否 1：是")
    private String isEnable;

    @Schema(description = "是否初始化;0：否 1：是")
    private String isInit;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}