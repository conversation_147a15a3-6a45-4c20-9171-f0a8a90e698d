package info.qizhi.aflower.module.member.controller.admin.member.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息列表 Request DTO")
@Data
public class MemberListReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mtCode.notempty}")
    private String mtCode;

    @Schema(description = "是否拉黑;0：否 1：拉黑")
    @NotEmpty(message = "{isBlack.inenum}")
    @InStringEnum(value = BooleanEnum.class,message =  "{isBlack.inenum}")
    private String isBlack;

    @Schema(description = "状态;1:正常  0:停用")
    @InStringEnum(value = BooleanEnum.class,message =  "状态;1:正常  0:停用")
    @NotEmpty(message = "{state.inenum}")
    private String state;

    @Schema(description = "是否允许发短信;0：不允许 1：允许")
    @InStringEnum(value = BooleanEnum.class,message =  "{isBlack.inenum}")
    private String isSendMsg;

    @Schema(description = "性别；0：女，1：男，2：保密", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sex;

    @Schema(description = "会员代码列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> mcodes;

}
