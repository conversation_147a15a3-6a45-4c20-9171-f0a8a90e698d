package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.MemberPathwayEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.common.validation.Mobile;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

/*
    <AUTHOR>
    @description 
    @create 2024 11 2024/11/19 下午5:51
*/
@Schema(description = "管理后台 - 会员信息新增/修改 Request VO")
@Data
public class MemberStarSaveReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30240")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @Length(max = 10, message = "姓名长度不能超过10个字符")
    private String name;

    @Schema(description = "性别;1:男 2:女, 3:保密")
    private String sex;

    @Schema(description = "手机号码")
    @Mobile
    private String phone;

    @Schema(description = "邮件地址")
    @Email
    private String email;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{idType.notempty}")
    private String idType;

    @Schema(description = "证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idNo;

    @Schema(description = "住址")
    private String address;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mtCode.notempty}")
    private String mtCode;

    @Schema(description = "生日")
    @JsonDeserialize(using = CustomLocalDateDeserializer.class)
    private LocalDate birthday;

    @Schema(description = "对应微信union_id", example = "3808")
    private String unionId;

    @Schema(description = "是否拉黑;0：否 1：拉黑", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class,message = "{isBlack.notempty}")
    private String isBlack;

    @Schema(description = "拉黑原因", example = "不好")
    @Size(max = 255, message = "{blackReason.size}")
    private String blackReason;

    @Schema(description = "是否允许发送短信;0:不允许 1：允许", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class,message =  "{isSendMsg.inenum}")
    private String isSendMsg;

    @Schema(description = "姓名拼音", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pinyin;

    @Schema(description = "有效期;有效期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate period;

    @Schema(description = "发展途径;会员发展途径，如：微信:mini_app 门店:lobby")
    @InStringEnum(value = MemberPathwayEnum.class)
    @NotBlank(message = "{developWay.notblank}")
    private String developWay;

    @Schema(description = "状态;1:正常  0:停用", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class,message =  "{state.inenum}")
    private String state;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "消费金额")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long buyFee;

    @Schema(description = "支付方式; 0:现付,1:挂房账")
    @InStringEnum(value = BooleanEnum.class,message =  "{payType.inenum}")
    private String payType;

    @Schema(description = "付款科目代码")
    private String payMethod;

    @Schema(description = "银行代码")
    private String bankType;

    @Schema(description = "银行卡号")
    private String bankNo;

    @Schema(description = "付款金额")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long payFee;

    @Schema(description = "AR账套代码")
    private String arSetCode;

    @Schema(description = "微信，支付宝 支付序列号")
    private String payCode;

    @Schema(description = "外部卡号")
    private String outCardNo;

    @Schema(description = "支付积分")
    private Integer payPoint;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "备注", example = "随便")
    @Size(max = 255, message = "{remark.size}")
    private String remark;
}
