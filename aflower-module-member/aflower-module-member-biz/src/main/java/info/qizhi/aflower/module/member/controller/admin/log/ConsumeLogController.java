package info.qizhi.aflower.module.member.controller.admin.log;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.ConsumeLogDO;
import info.qizhi.aflower.module.member.service.log.ConsumeLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员消费日志")
@RestController
@RequestMapping("/member/consume-log")
@Validated
public class ConsumeLogController {

    @Resource
    private ConsumeLogService consumeLogService;

    @PostMapping("/create")
    @Operation(summary = "创建会员消费日志")
    @PreAuthorize("@ss.hasPermission('member:consume-log:create')")
    public CommonResult<Long> createConsumeLog(@Valid @RequestBody ConsumeLogSaveReqVO createReqVO) {
        return success(consumeLogService.createConsumeLog(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员消费日志")
    //@PreAuthorize("@ss.hasPermission('member:query:get')")
    public CommonResult<ConsumeLogRespVO> getConsumeLog(@RequestParam("mcode") String mcode,@RequestParam("hcode")String hcode,@RequestParam("gcode")String gcode) {
        ConsumeLogDO consumeLog = consumeLogService.getConsumeLog(mcode,hcode,gcode);
        return success(BeanUtils.toBean(consumeLog, ConsumeLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员消费日志分页")
    //@PreAuthorize("@ss.hasPermission('member:query:get')")
    public CommonResult<PageResult<ConsumeLogRespVO>> getConsumeLogPage(@Valid ConsumeLogPageReqVO pageReqVO) {
        PageResult<ConsumeLogRespVO> pageResult = consumeLogService.getConsumeLogPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员消费日志 Excel")
    @PreAuthorize("@ss.hasPermission('member:consume-log:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportConsumeLogExcel(@Valid ConsumeLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ConsumeLogRespVO> list = consumeLogService.getConsumeLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员消费日志.xls", "数据", ConsumeLogRespVO.class,
                        list);
    }

}