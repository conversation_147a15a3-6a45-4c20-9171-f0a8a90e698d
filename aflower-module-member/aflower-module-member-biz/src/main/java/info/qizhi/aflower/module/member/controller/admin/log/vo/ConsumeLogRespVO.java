package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员消费日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ConsumeLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5445")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "发生门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生门店代码")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("门店名称")
    private String hname;

    @Schema(description = "每次消费代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("每次消费代码")
    private String consumeCode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码")
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("会员级别名称")
    private String mtName;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("储值卡号")
    private String storeCardNo;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号")
    private String phone;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单类型")
    private String orderType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "场景", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("场景")
    private String scene;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式")
    private String payMethod;

    @Schema(description = "支付方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式名称")
    private String payMethodName;

    @Schema(description = "营业日")
    @ExcelProperty("营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "消费类型")
    @ExcelProperty("消费类型")
    private String consumeType;

    @Schema(description = "操作员")
    @ExcelProperty("操作员")
    private String operator;

}