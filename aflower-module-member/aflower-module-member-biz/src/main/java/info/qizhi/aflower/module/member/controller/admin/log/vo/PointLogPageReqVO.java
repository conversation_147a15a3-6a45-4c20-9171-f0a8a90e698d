package info.qizhi.aflower.module.member.controller.admin.log.vo;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员积分日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PointLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0")
    private String hcode;

    @Schema(description = "门店名称", example = "张三")
    private String hname;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "姓名", example = "赵六")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "积分")
    private Integer point;

    @Schema(description = "会员级别名称", example = "赵六")
    private String mtCode;

    @Schema(description = "会员级别名称", example = "赵六")
    private String mtName;

    @Schema(description = "有效期;日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] indate;

    @Schema(description = "状态;0：无效 1：有效")
    private String state;

    @Schema(description = "积分类型;0：获得积分 1：消费积分", example = "2")
    private String pointType;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] bizDate;

    @Schema(description = "场景")
    private String scene;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单类型;0 入住订单，1 现付账", example = "2")
    private String orderType;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate beginBizDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endBizDate;
}