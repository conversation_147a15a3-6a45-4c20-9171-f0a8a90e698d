package info.qizhi.aflower.module.member.service.rule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.UpGradeMethodEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule.*;
import info.qizhi.aflower.module.member.dal.dataobject.rule.UpRuleDO;
import info.qizhi.aflower.module.member.dal.mysql.rule.UpRuleMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 会员升级规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UpRuleServiceImpl implements UpRuleService {

    @Resource
    private UpRuleMapper upRuleMapper;

    @Override
    public Long createUpRule(UpRuleSaveReqVO createReqVO) {
        if (createReqVO.getSourceMtCode().equals(createReqVO.getTargetMtCode())){
            throw exception(UP_RULE_SOURCE_TARGET_ERROR);
        }
        UpRuleDO upRuleDO =validateUpRuleExists(createReqVO);
        if(upRuleDO != null) {
            throw exception(UP_RULE_EXISTS);
        }
        UpRuleDO upRule = BeanUtils.toBean(createReqVO, UpRuleDO.class);
        // 判断升级方式是否为积分兑换
        if (UpGradeMethodEnum.POINT.getCode().equals(createReqVO.getUpMode())) {
            upRule.setAmount(Long.valueOf(createReqVO.getAmount()));
        } else if (UpGradeMethodEnum.CASH.getCode().equals(createReqVO.getUpMode()) || UpGradeMethodEnum.ROOMFEE.getCode().equals(createReqVO.getUpMode())) {
            // 将现金转换为分
            upRule.setAmount(new BigDecimal(createReqVO.getAmount()).multiply(new BigDecimal("100")).longValue());
        } else {
            throw exception(UP_RULE_NOT_EXISTS);
        }
        // 插入
        upRule.setRuleCode(IdUtil.getSnowflakeNextIdStr());
        upRuleMapper.insert(upRule);
        // 返回
        return upRule.getId();
    }

    @Override
    public void updateUpRule(UpRuleSaveReqVO updateReqVO) {
        // 校验存在
        UpRuleDO upRuleDO = getUpRule(new UpRuleReqVO().setGcode(updateReqVO.getGcode()).setRuleCode(updateReqVO.getRuleCode()));
        if (upRuleDO == null) {
            throw exception(UP_RULE_NOT_EXISTS);
        }
        UpRuleDO upRule = validateUpRuleExists(updateReqVO);
        if(upRule != null && !upRuleDO.getRuleCode().equals(updateReqVO.getRuleCode())){
            throw exception(UP_RULE_EXISTS);
        }
        if (UpGradeMethodEnum.POINT.getCode().equals(updateReqVO.getUpMode())) {
            updateReqVO.setAmount(updateReqVO.getAmount());
        } else if (UpGradeMethodEnum.CASH.getCode().equals(updateReqVO.getUpMode()) || UpGradeMethodEnum.ROOMFEE.getCode().equals(updateReqVO.getUpMode())) {
            // 将现金转换为分
            updateReqVO.setAmount(new BigDecimal(updateReqVO.getAmount()).multiply(new BigDecimal("100")).toString());
        } else {
            throw exception(UP_RULE_NOT_EXISTS);
        }
        // 更新
        UpRuleDO updateObj = BeanUtils.toBean(updateReqVO, UpRuleDO.class);
        updateObj.setId(upRuleDO.getId());
        upRuleMapper.updateById(updateObj);
    }

    @Override
    public void updateUpRuleStatus(UpRuleUpdateStatusReqVO updateReqVO) {
        if(upRuleMapper.selectById(updateReqVO.getId())==null) {
            throw exception(UP_RULE_NOT_EXISTS);
        }
        upRuleMapper.updateById(BeanUtils.toBean(updateReqVO, UpRuleDO.class));
    }

    private UpRuleDO validateUpRuleExists(UpRuleSaveReqVO reqVO) {
        UpRuleDO upRule = upRuleMapper.selectByReqVO(reqVO);
        return upRule;
    }

    @Override
    public UpRuleDO getUpRule(UpRuleReqVO reqVO) {
        return upRuleMapper.selectOne(UpRuleDO::getRuleCode, reqVO.getRuleCode(), UpRuleDO::getGcode, reqVO.getGcode());
    }

    @Override
    public UpRuleRespVO getUpRuleRespVO(UpRuleReqVO reqVO) {
        UpRuleDO upRuleDO = getUpRule(reqVO);
        if (upRuleDO == null) {
            throw exception(UP_RULE_NOT_EXISTS);
        }
        return buildUpRuleRespVO(upRuleDO);
    }

    @Override
    public List<UpRuleRespVO> getUpRuleList(String gcode) {
        List<UpRuleDO> list = upRuleMapper.selectList(UpRuleDO::getGcode, gcode);
        if(CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        List<UpRuleRespVO> respVOS = CollUtil.newArrayList();
        respVOS = list.parallelStream().map(this::buildUpRuleRespVO).collect(Collectors.toList());
        return respVOS;
    }

    @Override
    public List<UpRuleRespVO> getUpRuleSimpleList(UpRuleSimpleReqVO reqVO) {
        List<UpRuleDO> list = upRuleMapper.selectList(reqVO);
        if(CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        List<UpRuleRespVO> respVOS = CollUtil.newArrayList();
        respVOS = list.parallelStream().map(this::buildUpRuleRespVO).collect(Collectors.toList());
        return respVOS;
    }

    private UpRuleRespVO buildUpRuleRespVO(UpRuleDO upRuleDO) {
        UpRuleRespVO upRuleRespVO = BeanUtils.toBean(upRuleDO, UpRuleRespVO.class);
        if (UpGradeMethodEnum.POINT.getCode().equals(upRuleRespVO.getUpMode())) {
            upRuleRespVO.setAmount(String.valueOf(upRuleRespVO.getAmount()));
        } else if (UpGradeMethodEnum.CASH.getCode().equals(upRuleRespVO.getUpMode()) || UpGradeMethodEnum.ROOMFEE.getCode().equals(upRuleRespVO.getUpMode())) {
            // 将amount的分转换为元
            upRuleRespVO.setAmount(new BigDecimal(upRuleRespVO.getAmount()).divide(new BigDecimal("100").setScale(2, RoundingMode.HALF_UP)).toPlainString());
        }
        return upRuleRespVO;
    }

}