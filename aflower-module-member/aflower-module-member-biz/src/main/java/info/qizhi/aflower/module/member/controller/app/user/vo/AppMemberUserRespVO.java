package info.qizhi.aflower.module.member.controller.app.user.vo;

import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Schema(description = "用户信息 Response VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppMemberUserRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30240")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;


    @Data
    public static class Member {
        @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String mtCode;

        @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String mtName;

        @Schema(description = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer level;

        @Schema(description = "权益信息", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<MemberTypeBenefitRespVO.MemberBenefit> benefitList;

        @Data
        public static class MemberBenefit {
            @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED)
            private String name;

            @Schema(description = "权益内容", requiredMode = Schema.RequiredMode.REQUIRED)
            private String value;

            @Schema(description = "图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
            private String url;

        }
    }


}
