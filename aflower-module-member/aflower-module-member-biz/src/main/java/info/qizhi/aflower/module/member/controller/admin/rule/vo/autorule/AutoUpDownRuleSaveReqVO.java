package info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule;

import info.qizhi.aflower.module.member.dal.dataobject.rule.AutoUpDownRuleDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 会员类型自动升降级规则新增/修改 Request VO")
@Data
public class AutoUpDownRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27657")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "规则代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String ruleCode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{sourceMtCode.notempty}")
    private String sourceMtCode;

    @Schema(description = "目标会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{targetMtCode.notempty}")
    private String targetMtCode;

    @Schema(description = "自动升级条件")
    private AutoUpDownRuleDO.AutomaticUpgradeCondition autoUpCondition;

    @Schema(description = "是否自动保级;0:否 1:是，当自动保级时，自动保级条件字段无效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isAutoRetain.notempty}")
    private String isAutoRetain;

    @Schema(description = "自动保级条件")
    private AutoUpDownRuleDO.AutomaticClassificationCondition autoRetainCondition;

    @Schema(description = "是否自动降到下一级;0:否 1:是")
    private String isAutoDown;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "自动降级后的会员类型代码")
    private String autoDownMtCode;

}