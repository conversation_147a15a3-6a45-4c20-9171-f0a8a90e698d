package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员储值日志新增/修改 Request VO")
@Data
public class RechargeSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hhcode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.putin.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{payMethod.notempty}")
    private String payMethod;

    @Schema(description = "付款码")
    private String payCode;

    @Schema(description = "充值渠道,门店:lobby,小程序:mini_app", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{rechargeChannel.notempty}")
    private String rechargeChannel;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeCardNo;

    @Schema(description = "赠送金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{giveFee.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long giveMoney;

    @Schema(description = "充值活动代码;0表示没有参与活动", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{activityCode.notempty}")
    private String activityCode;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "AR账账户(应收帐账套代码)")
    private String arSetCode;

    @Schema(description = "银行卡号")
    private String bankNo;

    @Schema(description = "银行类型代码")
    private String bankType;

    @Schema(description = "短信是否发送")
    private String isSendSms;

    @Schema(description = "备注")
    private String remark;

}