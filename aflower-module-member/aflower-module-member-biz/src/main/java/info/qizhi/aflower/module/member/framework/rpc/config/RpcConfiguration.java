package info.qizhi.aflower.module.member.framework.rpc.config;

import info.qizhi.aflower.module.hiii.api.xiaoyou.XiaoYouApi;
import info.qizhi.aflower.module.marketing.api.coupon.CouponApi;
import info.qizhi.aflower.module.marketing.api.couponactivity.CouponActivityApi;
import info.qizhi.aflower.module.marketing.api.sms.send.SmsSendClientApi;
import info.qizhi.aflower.module.pay.api.vip.VipInfoApi;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.accset.AccSetApi;
import info.qizhi.aflower.module.pms.api.arset.ArSetApi;
import info.qizhi.aflower.module.pms.api.cashbillorder.CashBillOrderApi;
import info.qizhi.aflower.module.pms.api.channel.ChannelApi;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.GroupParamConfigApi;
import info.qizhi.aflower.module.pms.api.customer.CustomerApi;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.serviceintegration.ServiceIntegrationApi;
import info.qizhi.aflower.module.pms.api.shiftTime.ShiftTimeApi;
import info.qizhi.aflower.module.system.api.logger.LoginLogApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.sms.SmsCodeApi;
import info.qizhi.aflower.module.system.api.social.SocialClientApi;
import info.qizhi.aflower.module.system.api.social.SocialUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {SmsCodeApi.class, LoginLogApi.class, SocialUserApi.class, SocialClientApi.class,
        MerchantApi.class, CustomerApi.class, GroupParamConfigApi.class ,
        AccountApi.class, CashBillOrderApi.class, ArSetApi.class, ChannelApi.class,
        CouponActivityApi.class, CouponApi.class, ShiftTimeApi.class, AccSetApi.class,OrderApi.class, GeneralConfigApi.class, XiaoYouApi.class,
        ServiceIntegrationApi.class, VipInfoApi.class, SmsSendClientApi.class})
public class RpcConfiguration {
}
