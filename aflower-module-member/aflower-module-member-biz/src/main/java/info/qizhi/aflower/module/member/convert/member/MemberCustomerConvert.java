package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberSaveReqVO;
import info.qizhi.aflower.module.pms.api.customer.dto.CustomerDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 客历转换
 * <AUTHOR>
 */
@Mapper
public interface MemberCustomerConvert {

    MemberCustomerConvert INSTANCE = Mappers.getMapper(MemberCustomerConvert.class);

    default CustomerDTO memberCustomerConvert(MemberSaveReqVO createReqVO) {
        CustomerDTO customerDTO = new CustomerDTO();
        customerDTO.setGcode(createReqVO.getGcode());
        customerDTO.setHcode(createReqVO.getHcode());
        customerDTO.setName(createReqVO.getName());
        customerDTO.setSex(createReqVO.getSex());
        customerDTO.setIdType(createReqVO.getIdType());
        customerDTO.setIdNo(createReqVO.getIdNo());
        customerDTO.setBirthday(createReqVO.getBirthday());
        customerDTO.setPhone(createReqVO.getPhone());
        customerDTO.setNation(createReqVO.getNation());
        customerDTO.setAddress(createReqVO.getAddress());
        customerDTO.setIsBlack(createReqVO.getIsBlack());
        customerDTO.setBlackReason(createReqVO.getBlackReason());
        customerDTO.setIsSms(createReqVO.getIsSendMsg());
        customerDTO.setRemark(createReqVO.getRemark());
        customerDTO.setIsMember(NumberEnum.ONE.getNumber());
        return customerDTO;
    }

}
