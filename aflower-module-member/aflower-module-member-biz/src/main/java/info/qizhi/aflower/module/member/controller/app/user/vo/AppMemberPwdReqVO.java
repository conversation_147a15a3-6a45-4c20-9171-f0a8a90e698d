package info.qizhi.aflower.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员密码信息 Request VO")
@Data
public class AppMemberPwdReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "会员代码")
    @NotEmpty(message = "会员代码不能为空")
    private String mcode;

    @Schema(description = "密码")
    @NotEmpty(message = "密码不能为空")
    private String pwd;

}