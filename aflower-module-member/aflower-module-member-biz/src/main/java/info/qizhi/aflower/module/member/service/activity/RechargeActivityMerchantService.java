package info.qizhi.aflower.module.member.service.activity;

import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityMerchantPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityMerchantSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityMerchantDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;


/**
 * 充值活动参与门店 Service 接口
 *
 * <AUTHOR>
 */
public interface RechargeActivityMerchantService {

    /**
     * 创建充值活动参与门店
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRechargeActivityMerchant(@Valid RechargeActivityMerchantSaveReqVO createReqVO);

    /**
     * 更新充值活动参与门店
     *
     * @param updateReqVO 更新信息
     */
    void updateRechargeActivityMerchant(@Valid RechargeActivityMerchantSaveReqVO updateReqVO);

    /**
     * 删除充值活动参与门店
     *
     * @param id 编号
     */
    void deleteRechargeActivityMerchant(Long id);

    /**
     * 获得充值活动参与门店
     *
     * @param id 编号
     * @return 充值活动参与门店
     */
    RechargeActivityMerchantDO getRechargeActivityMerchant(Long id);

    /**
     * 获得充值活动参与门店分页
     *
     * @param pageReqVO 分页查询
     * @return 充值活动参与门店分页
     */
    PageResult<RechargeActivityMerchantDO> getRechargeActivityMerchantPage(RechargeActivityMerchantPageReqVO pageReqVO);

}