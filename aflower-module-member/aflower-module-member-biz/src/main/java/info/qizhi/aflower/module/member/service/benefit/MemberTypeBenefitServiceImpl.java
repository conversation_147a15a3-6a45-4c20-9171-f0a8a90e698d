package info.qizhi.aflower.module.member.service.benefit;

import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.*;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.MemberTypeBenefitDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.dal.mysql.benefit.MemberTypeBenefitMapper;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.TYPE_NOT_EXISTS;

@Service
@Validated
public class MemberTypeBenefitServiceImpl implements MemberTypeBenefitService{
    @Resource
    private MemberTypeBenefitMapper memberTypeBenefitMapper;
    @Resource
    private MemberTypeService memberTypeService;
    @Resource
    private MemberBenefitService memberBenefitService;
    @Resource
    private MemberTypeService typeService;

    @Override
    public void create(MemberTypeBenefitSaveReqVO createReqVO) {
        MemberTypeDO type = memberTypeService.getType(createReqVO.getMtCode(), createReqVO.getGcode());
        if( type == null){
            throw exception(TYPE_NOT_EXISTS);
        }
        MemberTypeBenefitDO memberTypeBenefitDO = BeanUtils.toBean(createReqVO, MemberTypeBenefitDO.class);
        memberTypeBenefitMapper.insert(memberTypeBenefitDO);
    }

    @Override
    public List<MemberTypeBenefitRespVO> getMemberTypeBenefitList(MemberTypeBenefitReqVO reqVO) {
        List<MemberTypeDO> typeList = typeService.getTypeList(new MemberTypeReqVO().setGcode(reqVO.getGcode())
                .setIsEnable(reqVO.getIsEnable()));

        // 获得权益列表
        List<MemberBenefitRespVO> benefitRespVOS = memberBenefitService.getTypeBenefitList(new MemberBenefitReqVO().setGcode(reqVO.getGcode()));
        Map<String, MemberBenefitRespVO> memberBenefitRespVOMap = CollectionUtils.convertMap(benefitRespVOS, MemberBenefitRespVO::getBenefitCode);
        // 获得会员类型权益
        List<MemberTypeBenefitDO> memberTypeBenefitList = getTypeBenefitList(new MemberTypeBenefitReqVO()
                .setGcode(reqVO.getGcode()));
        Map<String, List<MemberTypeBenefitDO>> typeBenefitListMap = CollectionUtils.convertMultiMap(memberTypeBenefitList, MemberTypeBenefitDO::getMtCode);

        List<MemberTypeBenefitRespVO> list = new ArrayList<>();
        typeList.forEach(type -> {
            MemberTypeBenefitRespVO respVO = BeanUtils.toBean(type, MemberTypeBenefitRespVO.class);

            List<MemberTypeBenefitDO> memberBenefitDOList = typeBenefitListMap.getOrDefault(type.getMtCode(), new ArrayList<>());
            List<MemberTypeBenefitRespVO.MemberBenefit> benefitList = new ArrayList<>();
            memberBenefitDOList.forEach(memberBenefitDO -> {
                MemberTypeBenefitRespVO.MemberBenefit bean = BeanUtils.toBean(memberBenefitDO, MemberTypeBenefitRespVO.MemberBenefit.class);
                bean.setName(memberBenefitRespVOMap.getOrDefault(bean.getBenefitCode(), new MemberBenefitRespVO()).getName());
                bean.setIsSys(memberBenefitRespVOMap.getOrDefault(bean.getBenefitCode(), new MemberBenefitRespVO()).getIsSys());
                benefitList.add(bean);
            });
            respVO.setBenefitList(benefitList);

            list.add(respVO);
        });
        return list;
    }

    @Override
    public List<MemberTypeBenefitDO> getTypeBenefitList(MemberTypeBenefitReqVO reqVO) {
        return  memberTypeBenefitMapper.selectList(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTypeBenefit(MemberTypeBenefitUpdateReqVO reqVO) {
        MemberTypeDO type = memberTypeService.getType(reqVO.getMtCode(), reqVO.getGcode());
        if (type == null) {
            throw exception(TYPE_NOT_EXISTS);
        }
        memberTypeBenefitMapper.delete(MemberTypeBenefitDO::getMtCode, reqVO.getMtCode());
        List<MemberTypeBenefitDO> list = new ArrayList<>();
        reqVO.getBenefitList().forEach(benefit -> {
            MemberTypeBenefitDO memberTypeBenefitDO = new MemberTypeBenefitDO();
            memberTypeBenefitDO.setMtCode(reqVO.getMtCode());
            memberTypeBenefitDO.setGcode(reqVO.getGcode());
            memberTypeBenefitDO.setValue(benefit.getValue());
            memberTypeBenefitDO.setBenefitCode(benefit.getBenefitCode());

            list.add(memberTypeBenefitDO);
        });
        memberTypeBenefitMapper.insertBatch(list);
    }
}
