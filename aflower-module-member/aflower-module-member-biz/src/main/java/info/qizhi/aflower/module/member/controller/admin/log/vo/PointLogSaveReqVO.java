package info.qizhi.aflower.module.member.controller.admin.log.vo;

import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;

@Schema(description = "管理后台 - 会员积分日志新增/修改 Request VO")
@Data
public class PointLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6431")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "积分发生门店代码;不是在门店发生，该字段为空0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.point.notempty}")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String hname;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "{name.notempty}")
    @Length(max = 10, message = "姓名长度不能超过10个字符")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mobile.notempty}")
    @Mobile
    private String phone;

    @Schema(description = "积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{point.notnull}")
    private Long point;

    @Schema(description = "会员级别代码", example = "赵六")
    @NotNull(message = "{mtCode.notnull}")
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String mtName;

    @Schema(description = "有效期;日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{indate.notnull}")
    private LocalDate indate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{state.notempty}")
    private String state;

    @Schema(description = "积分类型;0：获得积分 1：消费积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{pointType.notempty}")
    private String pointType;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "场景;1. 手动增减积分 2 积分换房 3 消费获取 4 升级减去积分 5 积分兑换 6 积分清零 7 积分抵现  9 商城兑换增减积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{scene.point.notempty}")
    private String scene;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNo.notblank}")
    private String orderNo;

    @Schema(description = "订单类型;0 入住订单，1 现付账", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{orderType.notempty}")
    private String orderType;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}