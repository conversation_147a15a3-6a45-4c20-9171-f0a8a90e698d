package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogListReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.RechargeLogDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会员储值日志 Service 接口
 *
 * <AUTHOR>
 */
public interface RechargeLogService {

    /**
     * 创建会员储值日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRechargeLog(@Valid RechargeLogSaveReqVO createReqVO);


    /**
     * 获得会员储值日志（冲调）
     *
     * @param
     * @return 会员储值日志
     */
    RechargeLogDO getRechargeLogByCashBillOrderNo(RechargeLogReqVO reqVO);


    /**
     * 获得会员储值日志分页
     *
     * @param pageReqVO 分页查询
     * @return 会员储值日志分页
     */
    PageResult<RechargeLogDO> getRechargeLogPage(RechargeLogPageReqVO pageReqVO);

    /**
     * 获得会员储值日志列表
     *
     * @param reqVO 列表查询
     * @return 会员储值日志列表
     */
    List<RechargeLogDO> getRechargeLogList(RechargeLogListReqVO reqVO);

}