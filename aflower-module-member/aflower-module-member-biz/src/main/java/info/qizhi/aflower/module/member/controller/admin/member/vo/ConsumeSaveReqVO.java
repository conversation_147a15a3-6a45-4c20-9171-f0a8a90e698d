package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员消费 Request VO")
@Data
public class ConsumeSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mcode.notempty}")
    private String mcode;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mobile.notempty}")
    @Mobile
    private String phone;

    @Schema(description = "付款方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{{payMethod.notempty}}")
    private String payMethod;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNo.notblank}")
    private String orderNo;

    @Schema(description = "订单类型;cash:现付账订单, room:房间订单")
    private String orderType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{feemoeny.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "场景", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{scene.notempty}")
    private String scene;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{storeCardNo.notempty}")
    private String storeCardNo;

    @Schema(description = "消费类型;0:房费消费 1:非房费消费")
    @NotEmpty(message = "{consumeType.notempty}")
    private String consumeType;

    @Schema(description = "会员密码")
    private String pwd;

}