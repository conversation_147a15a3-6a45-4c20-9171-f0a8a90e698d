package info.qizhi.aflower.module.member.controller.admin.type;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.type.vo.*;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 会员类型")
@RestController
@RequestMapping("/member/type")
@Validated
public class MemberTypeController {

    @Resource
    private MemberTypeService typeService;

    @PostMapping("/create")
    @Operation(summary = "创建会员类型")
    @PreAuthorize("@ss.hasPermission('member:type:create')")
    public CommonResult<String> createType(@Valid @RequestBody MemberTypeSaveReqVO createReqVO) {
        return success(typeService.createType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员类型")
    @PreAuthorize("@ss.hasPermission('member:type:create')")
    public CommonResult<Boolean> updateType(@Valid @RequestBody MemberTypeSaveReqVO updateReqVO) {
        typeService.updateType(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新会员类型状态")
    @PreAuthorize("@ss.hasPermission('member:type:create')")
    public CommonResult<Boolean> updateTypeStatus(@Valid @RequestBody MemberTypeUpdateStatusReqVO updateReqVO) {
        typeService.updateStatus(updateReqVO.getId(), updateReqVO.getIsEnable());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员类型")
    @PreAuthorize("@ss.hasPermission('member:type:query')")
    public CommonResult<MemberTypeRespVO> getType(@RequestParam("mtCode") String mtCode,@RequestParam("gcode") String gcode) {
        MemberTypeDO type = typeService.getType(mtCode,gcode);
        return success(BeanUtils.toBean(type, MemberTypeRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得会员类型列表")
    //@PreAuthorize("@ss.hasAnyPermissions('member:type:query:list , member:query:page')")
    public CommonResult<List<MemberTypeRespVO>> getTypeList(@Valid MemberTypeReqVO typeReqVO) {
        List<MemberTypeDO> listResult = typeService.getTypeList(typeReqVO);
        return success(BeanUtils.toBean(listResult, MemberTypeRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得会员类型简单列表", description = "用于前端下拉列表")
    //@PreAuthorize("@ss.hasPermission('member:type:query')")
    public CommonResult<List<MemberTypeSimpleRespVO>> getTypeSimpleList(@Valid MemberTypeReqVO typeReqVO) {
        List<MemberTypeDO> listResult = typeService.getTypeList(typeReqVO);
        return success(BeanUtils.toBean(listResult, MemberTypeSimpleRespVO.class));
    }


}