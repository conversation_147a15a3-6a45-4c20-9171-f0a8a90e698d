package info.qizhi.aflower.module.member.controller.admin.member.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Response VO")
@Data
public class MemberRespVO {

    @Schema(description = "id", example = "30240")
    private Long id;

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码;注册是集团，该字段为0")
    private String hcode;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "姓名", example = "芋艿")
    private String name;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "邮件地址")
    private String email;

    @Schema(description = "证件类型", example = "1")
    private String idType;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "住址")
    private String address;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码")
    private String mtCode;

    @Schema(description = "生日")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime birthday;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "对应微信union_id", example = "3808")
    private String unionId;

    @Schema(description = "累积间夜数")
    private BigDecimal nightCount;

    @Schema(description = "消费次数", example = "25649")
    private Integer feeCount;

    @Schema(description = "是否拉黑;0：否 1：拉黑")
    private String isBlack;

    @Schema(description = "拉黑原因", example = "不好")
    private String blackReason;

    @Schema(description = "是否允许发送短信;0:不允许 1：允许")
    private String isSendMsg;

    @Schema(description = "姓名拼音")
    private String pinyin;

    @Schema(description = "有效期;有效期")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime period;

    @Schema(description = "发展途径;会员发展途径，如：微信 门店")
    private String developWay;

    @Schema(description = "状态;1:正常  0:停用")
    private String state;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}