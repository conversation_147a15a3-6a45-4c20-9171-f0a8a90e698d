package info.qizhi.aflower.module.member.service.log;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberAndStoreCardRespVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.LevelLogDO;
import info.qizhi.aflower.module.member.dal.mysql.log.LevelLogMapper;
import info.qizhi.aflower.module.member.service.member.MemberService;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 会员级别变动日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class LevelLogServiceImpl implements LevelLogService {

    @Resource
    private LevelLogMapper levelLogMapper;
    @Resource
    private MerchantApi merchantApi;

    @Resource
    @Lazy
    private MemberService memberService;

    @Override
    public Long createLevelLog(LevelLogSaveReqVO createReqVO) {
        // 插入
        LevelLogDO levelLog = BeanUtils.toBean(createReqVO, LevelLogDO.class);
        levelLogMapper.insert(levelLog);
        // 返回
        return levelLog.getId();
    }
    @Override
    public LevelLogDO getLevelLog(LevelLogReqVO reqVO) {
        return levelLogMapper.selectOneByCashBillOrderNo(reqVO);
    }

    @Override
    public PageResult<LevelLogRespVO> getLevelLogPage(LevelLogPageReqVO pageReqVO) {
        PageResult<LevelLogDO> levelLogDOPageResult = levelLogMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(levelLogDOPageResult.getList())){
            return new PageResult<>(CollUtil.newArrayList(), levelLogDOPageResult.getTotal());
        }
        MemberAndStoreCardRespVO member = memberService.getMemberAndStoreCard(new MemberReqVO().setMcode(pageReqVO.getMcode()).setGcode(pageReqVO.getGcode()).setHcode(pageReqVO.getHcode()));
        List<MerchantRespDTO> data = merchantApi.getMerchantList(pageReqVO.getGcode()).getData();
        Map<String, String> hNameMap = data.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
        PageResult<LevelLogRespVO> bean = BeanUtils.toBean(levelLogDOPageResult, LevelLogRespVO.class);
        List<LevelLogRespVO> list = bean.getList();
        list.forEach(item -> {
            item.setHname(hNameMap.get(item.getHcode()));
            item.setMcard(member.getPhone());
        });
        return new PageResult<>(list, bean.getTotal());
    }

}