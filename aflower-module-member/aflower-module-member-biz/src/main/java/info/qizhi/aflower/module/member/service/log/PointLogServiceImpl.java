package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.PointLogDO;
import info.qizhi.aflower.module.member.dal.mysql.log.PointLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 会员积分日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class PointLogServiceImpl implements PointLogService {

    @Resource
    private PointLogMapper pointLogMapper;
    @Override
    public Long createPointLog(PointLogSaveReqVO createReqVO) {
        // 插入积分记录
        PointLogDO pointLog = BeanUtils.toBean(createReqVO, PointLogDO.class);
        pointLogMapper.insert(pointLog);
        // 返回积分记录ID
        return pointLog.getId();
    }

    @Override
    public void createPointLogBatch(List<PointLogSaveReqVO> createReqVO) {
        // 插入积分记录
        List<PointLogDO> pointLogs = BeanUtils.toBean(createReqVO, PointLogDO.class);
        pointLogMapper.insertBatch(pointLogs);

    }

    @Override
    public PointLogDO getPointLog(String mcode, String hcode, String gcode) {
        return pointLogMapper.selectOne(PointLogDO::getMcode, mcode,PointLogDO::getHcode, hcode,PointLogDO::getGcode, gcode);
    }

    @Override
    public PageResult<PointLogDO> getPointLogPage(PointLogPageReqVO pageReqVO) {
        return pointLogMapper.selectPage(pageReqVO);
    }

}