package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.PointLogDO;
import jakarta.validation.Valid;

import java.util.List;


/**
 * 会员积分日志 Service 接口
 *
 * <AUTHOR>
 */
public interface PointLogService {

    /**
     * 创建会员积分日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointLog(@Valid PointLogSaveReqVO createReqVO);

    /**
     * 创建会员积分日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createPointLogBatch(@Valid List<PointLogSaveReqVO> createReqVO);

    /**
     * 获得会员积分日志
     *
     * @param
     * @return 会员积分日志
     */
    PointLogDO getPointLog(String mcode, String hcode, String gcode);

    /**
     * 获得会员积分日志分页
     *
     * @param pageReqVO 分页查询
     * @return 会员积分日志分页
     */
    PageResult<PointLogDO> getPointLogPage(PointLogPageReqVO pageReqVO);

}