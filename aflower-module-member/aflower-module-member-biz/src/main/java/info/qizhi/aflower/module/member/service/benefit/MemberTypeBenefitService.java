package info.qizhi.aflower.module.member.service.benefit;

import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitReqVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitRespVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitUpdateReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.MemberTypeBenefitDO;
import jakarta.validation.Valid;

import java.util.List;

public interface MemberTypeBenefitService {
    /**
     * 添加会员权益
     * @param createReqVO
     */
    void create(@Valid MemberTypeBenefitSaveReqVO createReqVO);

    /**
     * 获得会员类型权益列表
     * @param reqVO
     * @return
     */
    List<MemberTypeBenefitRespVO> getMemberTypeBenefitList(@Valid MemberTypeBenefitReqVO reqVO);

    /**
     * 获得会员类型权益列表
     * @param reqVO
     * @return
     */
    List<MemberTypeBenefitDO> getTypeBenefitList(@Valid MemberTypeBenefitReqVO reqVO);

    /**
     * 修改会员类型权益
     * @param reqVO
     */
    void updateTypeBenefit(@Valid MemberTypeBenefitUpdateReqVO reqVO);
}
