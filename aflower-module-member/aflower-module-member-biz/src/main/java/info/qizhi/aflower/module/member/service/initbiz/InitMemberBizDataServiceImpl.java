package info.qizhi.aflower.module.member.service.initbiz;

import info.qizhi.aflower.module.member.dal.mysql.initbiz.InitMemberBizDataMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * @Author: TY
 * @CreateTime: 2024-08-23
 * @Description: 初始化会员数据
 * @Version: 1.0
 */
@Service
@Validated
public class InitMemberBizDataServiceImpl implements InitMemberBizDataService{

    @Resource
    private InitMemberBizDataMapper initMemberBizDataMapper;

    @Override
    public void initMemberBizData(String gcode, String hcode) {
        initMemberBizDataMapper.deleteMember(gcode, hcode);
        initMemberBizDataMapper.deleteMemberConsumeLog(gcode, hcode);
        initMemberBizDataMapper.deleteMemberLevelLog(gcode, hcode);
        initMemberBizDataMapper.deleteMemberPointLog(gcode, hcode);
        initMemberBizDataMapper.deleteMemberRechargeLog(gcode, hcode);
        initMemberBizDataMapper.deleteMemberStoreCard(gcode, hcode);
    }
}
