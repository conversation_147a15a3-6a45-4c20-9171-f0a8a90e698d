package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 会员升级规则状态修改 Request VO")
@Data
public class UpRuleUpdateStatusReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7601")
    @NotNull(message = "{id.notnull}")
    private Long id;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= BooleanEnum.class, message = "{isEnable.inenum}")
    @NotEmpty(message = "状态不能为空")
    private String isEnable;

}