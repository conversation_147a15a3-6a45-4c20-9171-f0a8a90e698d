package info.qizhi.aflower.module.member.controller.admin.log.vo;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员级别变动日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LevelLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0")
    private String hcode;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "源级别代码")
    private String sourceMtcode;

    @Schema(description = "源级别名称", example = "赵六")
    private String sourceMtname;

    @Schema(description = "目标级别代码")
    private String targetMtcode;

    @Schema(description = "目标级别名称", example = "芋艿")
    private String targetMtname;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] bizDate;

    @Schema(description = "变更类型;0:升级 1:降级 2:保级", example = "2")
    private String changeType;

    @Schema(description = "支付金额")
    private Long fee;

    @Schema(description = "付款方式")
    private String payMode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate beginBizDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endBizDate;

}