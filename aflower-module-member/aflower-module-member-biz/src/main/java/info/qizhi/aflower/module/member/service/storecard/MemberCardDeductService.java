package info.qizhi.aflower.module.member.service.storecard;

import info.qizhi.aflower.module.member.controller.admin.storecard.vo.MemberCardDeductPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.MemberCardDeductSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.storecard.MemberCardDeductDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;


/**
 * 会员卡销售提成 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberCardDeductService {

    /**
     * 创建会员卡销售提成
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCardDeduct(@Valid MemberCardDeductSaveReqVO createReqVO);

    /**
     * 更新会员卡销售提成
     *
     * @param updateReqVO 更新信息
     */
    void updateCardDeduct(@Valid MemberCardDeductSaveReqVO updateReqVO);

    /**
     * 删除会员卡销售提成
     *
     * @param id 编号
     */
    void deleteCardDeduct(Long id);

    /**
     * 获得会员卡销售提成
     *
     * @param id 编号
     * @return 会员卡销售提成
     */
    MemberCardDeductDO getCardDeduct(Long id);

    /**
     * 获得会员卡销售提成分页
     *
     * @param pageReqVO 分页查询
     * @return 会员卡销售提成分页
     */
    PageResult<MemberCardDeductDO> getCardDeductPage(MemberCardDeductPageReqVO pageReqVO);

}