package info.qizhi.aflower.module.member.service.member;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.lock.annotation.Lock4j;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.desensitize.core.slider.handler.MobileDesensitization;
import info.qizhi.aflower.framework.security.core.util.SecurityFrameworkUtils;
import info.qizhi.aflower.module.marketing.api.coupon.CouponApi;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponGenerateBO;
import info.qizhi.aflower.module.marketing.api.coupon.dto.CouponGenerateReqDTO;
import info.qizhi.aflower.module.marketing.api.couponactivity.CouponActivityApi;
import info.qizhi.aflower.module.marketing.api.couponactivity.dto.CouponActivityReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.send.SmsSendClientApi;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.SendRequestDTO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.*;
import info.qizhi.aflower.module.member.controller.admin.member.vo.*;
import info.qizhi.aflower.module.member.controller.admin.storecard.vo.StoreCardReqVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeSaveReqVO;
import info.qizhi.aflower.module.member.convert.member.*;
import info.qizhi.aflower.module.member.dal.dataobject.log.RechargeLogDO;
import info.qizhi.aflower.module.member.dal.dataobject.member.MemberDO;
import info.qizhi.aflower.module.member.dal.dataobject.rule.PointRuleDO;
import info.qizhi.aflower.module.member.dal.dataobject.storecard.StoreCardDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.dal.mysql.member.MemberMapper;
import info.qizhi.aflower.module.member.service.activity.RechargeActivityService;
import info.qizhi.aflower.module.member.service.log.ConsumeLogService;
import info.qizhi.aflower.module.member.service.log.LevelLogServiceImpl;
import info.qizhi.aflower.module.member.service.log.PointLogService;
import info.qizhi.aflower.module.member.service.log.RechargeLogService;
import info.qizhi.aflower.module.member.service.rule.PointRuleService;
import info.qizhi.aflower.module.member.service.storecard.StoreCardService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import info.qizhi.aflower.module.pay.api.vip.VipInfoApi;
import info.qizhi.aflower.module.pay.api.vip.dto.VipQueryReqDTO;
import info.qizhi.aflower.module.pay.api.vip.dto.VipQueryRespDTO;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.ConsumeAccountSaveReqDTO;
import info.qizhi.aflower.module.pms.api.accset.AccSetApi;
import info.qizhi.aflower.module.pms.api.accset.dto.AccSetRespDTO;
import info.qizhi.aflower.module.pms.api.arset.ArSetApi;
import info.qizhi.aflower.module.pms.api.arset.dto.CalculateArSetDTO;
import info.qizhi.aflower.module.pms.api.cashbillorder.CashBillOrderApi;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderSaveReqDTO;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderSaveRespDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.GroupParamConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.config.dto.group.GroupParamConfigMemberRespDTO;
import info.qizhi.aflower.module.pms.api.customer.CustomerApi;
import info.qizhi.aflower.module.pms.api.customer.dto.CustomerDTO;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.dto.OrderPersonRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.ServiceIntegrationApi;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import info.qizhi.aflower.module.pms.api.shiftTime.ShiftTimeApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.enums.PayAccountEnum.*;
import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.TOGETHERCODE_NOT_NULL;
import static info.qizhi.aflower.module.system.enums.ErrorCodeConstants.MERCHANT_NOT_EXISTS;

/**
 * 会员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MemberServiceImpl implements MemberService {

    private static final long TIMEOUT_MILLIS = 10000;

    @Resource
    private MemberMapper memberMapper;
    @Resource
    private LevelLogServiceImpl levelLogService;
    @Resource
    private MemberTypeService memberTypeService;
    @Resource
    private StoreCardService storeCardService;
    @Resource
    private CustomerApi customerApi;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private GroupParamConfigApi groupParamConfigApi;
    @Resource
    private AccountApi accountApi;
    @Resource
    private ShiftTimeApi shiftTimeApi;
    @Resource
    private CashBillOrderApi cashBillOrderApi;
    @Resource
    private PointLogService pointLogService;
    @Resource
    private ConsumeLogService consumeLogService;
    @Resource
    private RechargeActivityService rechargeActivityService;
    @Resource
    private RechargeLogService rechargeLogService;
    @Resource
    private PointRuleService pointRuleService;
    @Resource
    private CouponActivityApi couponActivityApi;
    @Resource
    private CouponApi couponApi;
    @Resource
    private AccSetApi accSetApi;
    @Resource
    private OrderApi orderApi;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private ArSetApi arSetApi;
    @Resource
    private ServiceIntegrationApi serviceIntegrationApi;
    @Resource
    private VipInfoApi vipInfoApi;
    @Resource
    private SmsSendClientApi smsSendClientApi;


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = "#createReqVO.gcode", expire = TIMEOUT_MILLIS, acquireTimeout = 5000)
    public Long register(MemberSaveReqVO createReqVO) {
        // 校验
        verifyParameters(createReqVO);
        // 初始化会员
        MemberDO member = MemberConvert.INSTANCE.convert(createReqVO);
        createReqVO.setMcode(member.getMcode());
        MemberTypeDO memberType = memberTypeService.getType(createReqVO.getMtCode(), createReqVO.getGcode());
        if (memberType == null) {
            throw exception(MEMBER_TYPE_NOT_EXISTS);
        }
        // 创建现付账订单并支付
        String cashBillOrderNo = handlePayment(createReqVO, NumberEnum.ONE.getNumber());
        // 插入会员信息
        memberMapper.insert(member);
        // 判断是否插入客史信息
        createCustomerHistory(createReqVO);
        // 创建储值卡
        createStoreCard(createReqVO, member.getMcode());
        // 注册赠券
        CouponActivityReqDTO couponActivityReqDTO = MemberCouponConvert.INSTANCE.memberCouponConvert(createReqVO);
        //couponActivityReqDTO.setOrderNo(cashBillOrderNo);
        if (NumberEnum.ZERO.getNumber().equals(createReqVO.getPayType())) {
            couponActivityReqDTO.setOrderType(CouponOrderTypeEnum.CASH.getCode());
        } else if (NumberEnum.ONE.getNumber().equals(createReqVO.getPayType())) {
            couponActivityReqDTO.setOrderType(CouponOrderTypeEnum.ROOM.getCode());
        }
        couponActivityApi.freeTicket(couponActivityReqDTO);
        // 发送短信
        registerSend(createReqVO, member);
        // 返回会员ID
        return member.getId();
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public MemberRespVO createUserIfAbsent(String gcode, String phone, String unionId) {
        // 校验手机号是否重复
        MemberDO memberByPhone = memberMapper.selectMember(new MemberReqVO().setGcode(gcode)
                .setPhone(phone.trim()));
        if(memberByPhone != null){
            return BeanUtils.toBean(memberByPhone, MemberRespVO.class);
        }
        MemberSaveReqVO createReqVO = buildMemberSaveReqVO(gcode, phone, unionId);
        // 校验
        verifyParameters(createReqVO);
        // 初始化会员
        MemberDO member = MemberConvert.INSTANCE.convert(createReqVO);
        createReqVO.setMcode(member.getMcode());
        MemberTypeDO memberType = memberTypeService.getType(createReqVO.getMtCode(), createReqVO.getGcode());
        if (memberType == null) {
            throw exception(MEMBER_TYPE_NOT_EXISTS);
        }
        // 插入会员信息
        memberMapper.insert(member);
        // 判断是否插入客史信息
        createCustomerHistory(createReqVO);
        // 创建储值卡
        createStoreCard(createReqVO, member.getMcode());
        // 发送短信
        registerSend(createReqVO, member);
        // 返回会员ID
        return BeanUtils.toBean(member, MemberRespVO.class);
    }

    private MemberSaveReqVO buildMemberSaveReqVO(String gcode, String phone, String unionId) {
        MemberSaveReqVO reqVO = new MemberSaveReqVO();
        MemberTypeDO memberTypeDO = memberTypeService.getTypeByName(gcode, MemberTypeEnum.ORDINARY_MEMBER.getLabel());
        String mtCode = "";
        if(memberTypeDO == null){
            mtCode = memberTypeService.createType(new MemberTypeSaveReqVO().setGcode(gcode)
                    .setMtName(MemberTypeEnum.ORDINARY_MEMBER.getLabel())
                    .setLevel(NumberEnum.ONE.getNumberInt())
                    .setIsForEver(NumberEnum.ONE.getNumber())
                    .setIsFree(NumberEnum.ONE.getNumber())
                    .setFee(0L)
                    .setCanUpdateFee(NumberEnum.ZERO.getNumber())
                    .setNeedSms(NumberEnum.ZERO.getNumber())
                    .setSupportStore(NumberEnum.ZERO.getNumber())
                    .setVerifyMode(NumberEnum.ZERO.getNumber())
                    .setDelayNoshow(NumberEnum.ZERO.getNumber())
                    .setIsEnable(NumberEnum.ONE.getNumber())
                    .setIsInit(NumberEnum.ONE.getNumber())
                    .setImageType(NumberEnum.ONE.getNumber())
                    .setRemark("注册会员是系统初始化生成，请不要修改")

            );
        }else {
            mtCode = memberTypeDO.getMtCode();
        }
        reqVO.setGcode(gcode).setHcode(NumberEnum.ZERO.getNumber()).setPhone(phone).setIdType(IdTypeEnum.OTHER.getCode())
                .setIdNo(phone).setSex(SexEnum.UNKNOWN.getSex()).setName(phone)
                .setMtCode(mtCode).setDevelopWay(MemberPathwayEnum.MINI_APP.getCode()).setUnionId(unionId)
                .setPayMethod(WX.getCode()).setPayType(NumberEnum.ZERO.getNumber()).setName(phone);
        return reqVO;
    }

    /**
     * 会员注册短信发送
     * @param reqVO
     * @param member
     */
    @Async
    public void registerSend(MemberSaveReqVO reqVO, MemberDO member) {
        // 会员不想发送
        if(!NumberEnum.ONE.getNumber().equals(reqVO.getIsSendSms())) {
            return;
        }
        Integer sign = smsSendClientApi.verifySend(14, reqVO.getHcode(), reqVO.getIsSendSms(), null).getData();
        if(NumberEnum.ZERO.getNumberInt().equals(sign)) return;
        // 属性处理
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = member.getCreateTime().format(formatter);
        String notFiled = "暂无";
        String oldFrontPhone = merchantApi.getMerchant(reqVO.getHcode()).getData().getFrontPhone();
        String frontPhone = (StrUtil.isEmpty(oldFrontPhone) ? notFiled : oldFrontPhone );
        List<HashMap<String, String>> send = new ArrayList<>();
        send.add(new HashMap<String, String>() {{
         put(SmsConfigParamEnum.MNAME.getCode(), reqVO.getName());
         put(SmsConfigParamEnum.CARD_NO.getCode(), member.getId().toString());
         put(SmsConfigParamEnum.OPERATE_TIME.getCode(), formattedDateTime);
         put(SmsConfigParamEnum.MEMBER_PWD.getCode(), member.getPwd());
         put(SmsConfigParamEnum.FRONT_PHONE.getCode(), frontPhone);
         put(SmsConfigParamEnum.RECEIVER.getCode(), reqVO.getPhone());
        }});
        smsSendClientApi.smsSendForMap(new SendRequestDTO().setSend(send).setSign(sign).setType(14));
    }

    private void verifyParameters(MemberSaveReqVO createReqVO) {
        // 校验证件号是否重复
        MemberDO memberDO = memberMapper.selectMember(new MemberReqVO().setGcode(createReqVO.getGcode())
                                                                       .setIdType(createReqVO.getIdType())
                                                                       .setIdNo(createReqVO.getIdNo().trim()));
        if (memberDO != null && !memberDO.getId().equals(createReqVO.getId())) {
            throw exception(MEMBER_ID_NO_EXIST);
        }
        //证件类型身份证号验证
        if (IdTypeEnum.IDCERT.getCode().equals(createReqVO.getIdType())) {
            if (!IdcardUtil.isValidCard(createReqVO.getIdNo())) {
                throw exception(IDCARD_INVALID);
            }
        }
        // 校验手机号是否重复
        MemberDO memberByPhone = memberMapper.selectMember(new MemberReqVO().setGcode(createReqVO.getGcode())
                                                                            .setPhone(createReqVO.getPhone().trim()));
        if (memberByPhone != null && !memberByPhone.getId().equals(createReqVO.getId())) {
            throw exception(MEMBER_PHONE_EXIST);
        }
    }
    private void verifyParameters(MemberStarSaveReqVO createReqVO) {
        // 校验证件号是否重复
        if(ObjectUtil.isNotEmpty(createReqVO.getIdNo())){
            MemberDO memberDO = memberMapper.selectMember(new MemberReqVO().setGcode(createReqVO.getGcode())
                    .setHcode(createReqVO.getHcode())
                    .setIdType(createReqVO.getIdType())
                    .setIdNo(createReqVO.getIdNo().trim()));
            if (memberDO != null && !memberDO.getId().equals(createReqVO.getId())) {
                throw exception(MEMBER_ID_NO_EXIST);
            }
            //证件类型身份证号验证
            if (IdTypeEnum.IDCERT.getCode().equals(createReqVO.getIdType())) {
                if (!IdcardUtil.isValidCard(createReqVO.getIdNo())) {
                    throw exception(IDCARD_INVALID);
                }
            }
        }
        if(ObjectUtil.isNotEmpty(createReqVO.getPhone())){
            // 校验手机号是否重复
            MemberDO memberByPhone = memberMapper.selectMember(new MemberReqVO().setGcode(createReqVO.getGcode())
                    .setHcode(createReqVO.getHcode())
                    .setPhone(createReqVO.getPhone().trim()));
            if (memberByPhone != null && !memberByPhone.getId().equals(createReqVO.getId())) {
                throw exception(MEMBER_PHONE_EXIST);
            }
        }
    }

    /**
     * 处理支付并返回现付账订单号
     *
     * @param reqVO  入参
     * @param handle 操作类型：1：注册，2：升级
     * @return 现付账订单号
     */
    private String handlePayment(MemberSaveReqVO reqVO, String handle) {
        // 现付
        if (Objects.equals(reqVO.getPayType(), BooleanEnum.FALSE.getValue())) {
            return handleCashPayment(reqVO, handle);
        } else { // 挂房账
            return handleRoomPayment(reqVO, handle);
        }
    }

    /**
     * 处理现付，创建现付账订单
     *
     * @param createReqVO 入参
     * @param handle      操作类型: 1：注册，2：升级
     * @return 现付账订单号
     */
    private String handleCashPayment(MemberSaveReqVO createReqVO, String handle) {
        if (StrUtil.isBlank(createReqVO.getPayMethod())) {
            throw exception(PAY_METHOD_NOT_NULL);
        }
        // 不允许使用会员卡支付
        if (STORE_CARD.getCode().equals(createReqVO.getPayMethod())) {
            throw exception(PAY_METHOD_NOT_ALLOWED);
        }
        // 获取现付账套
        String accCode = accSetApi.getMerchantAccCodeBySubCode(ConsumeAccountEnum.MEMBER_CARD.getCode(), createReqVO.getGcode(), createReqVO.getHcode()).getData();
        if (StringUtils.isBlank(accCode)) {
            throw exception(ACCOUNT_SET_NOT_EXIST);
        }
        CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO = CashBillOrderConvert.INSTANCE.memberCashBillOrderConvert(createReqVO)
                                                                                       .setAccCode(accCode);
        CashBillOrderSaveReqDTO.Consume consume = new CashBillOrderSaveReqDTO.Consume().setFee(createReqVO.getBuyFee())
                                                                                       .setSubCode(ConsumeAccountEnum.MEMBER_CARD.getCode());
        CashBillOrderSaveReqDTO.Pay pay = new CashBillOrderSaveReqDTO.Pay().setPayCode(createReqVO.getPayCode())
                                                                           .setFee(createReqVO.getPayFee())
                                                                           .setMcode(createReqVO.getMcode())
                                                                           .setSubCode(createReqVO.getPayMethod())
                                                                           .setBankType(createReqVO.getBankType())
                                                                           .setBankCardNo(createReqVO.getBankNo());
        cashBillOrderSaveReqDTO.setConsume(consume).setPay(pay);
        MobileDesensitization mobileDesensitization = new MobileDesensitization();
        String phone = mobileDesensitization.newDes(createReqVO.getPhone());
        if (Objects.equals(handle, NumberEnum.ONE.getNumber())) {
            cashBillOrderSaveReqDTO.setBuyContent(DictDataEnum.DICT_DATA_REG_MEMBER.getLabel() + "(姓名：" + createReqVO.getName() +  " 手机号：" + phone +")");
        }
        if (Objects.equals(handle, NumberEnum.TWO.getNumber())) {
            cashBillOrderSaveReqDTO.setBuyContent(DictDataEnum.DICT_DATA_MEMBER_UPDATE.getLabel() + "(姓名：" + createReqVO.getName() +  " 手机号：" + phone +")");
        }
        // 处理 AR 账支付方式
        if (Objects.equals(PayAccountEnum.CREDIT_S_ACCOUNT.getCode(), createReqVO.getPayMethod())) {
            return handleArPayment(createReqVO, cashBillOrderSaveReqDTO);
        } else { // 处理非 AR 账支付方式
            return handleNonArPayment(createReqVO, cashBillOrderSaveReqDTO);
        }
    }

    /**
     * 处理 AR 账支付方式
     *
     * @param createReqVO             入参
     * @param cashBillOrderSaveReqDTO 封装的现付账DTO
     * @return 返回现付账订单号
     */
    private String handleArPayment(MemberSaveReqVO createReqVO, CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO) {
        // 校验 AR 账号是否为空
        if (StrUtil.isBlank(createReqVO.getArSetCode())) {
            throw exception(MEMBER_AR_ACC_NO_NULL);
        }
        // 挂账金额更改
        arSetApi.calculateArSet(new CalculateArSetDTO().setGcode(createReqVO.getGcode())
                                                       .setArSetCode(createReqVO.getArSetCode())
//                                                       .setHandleType(ArSetHandleTypeEnum.CREDIT)
                                                       .setFee(createReqVO.getPayFee()));
        // 创建现付账订单和账务记录
        return createCashBillOrderAndAccRecords(cashBillOrderSaveReqDTO);

    }

    /**
     * 处理非 AR 账支付方式
     *
     * @param createReqVO             入参
     * @param cashBillOrderSaveReqDTO 现付账订单DTO
     * @return 现付账订单号
     */
    private String handleNonArPayment(MemberSaveReqVO createReqVO, CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO) {
        // 创建现付账订单和账务记录
        return createCashBillOrderAndAccRecords(cashBillOrderSaveReqDTO);
    }

    /**
     * 创建现付账订单
     *
     * @param cashBillOrderSaveReqDTO 现付账订单DTO
     * @return 现付账订单号
     */
    private String createCashBillOrderAndAccRecords(CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO) {

            // 调用远程服务创建现付账订单
            CommonResult<CashBillOrderSaveRespDTO> cashBillOrder = cashBillOrderApi.createCashBillOrder(cashBillOrderSaveReqDTO);
            if(StrUtil.isNotBlank(cashBillOrder.getMsg())){
                throw new ServiceException(cashBillOrder.getCode(),cashBillOrder.getMsg());
            }
            return cashBillOrder.getData().getCashBillOrderNo();
    }

    /**
     * 处理挂房账支付方式
     *
     * @param createReqVO 入参
     * @param handle      操作类型：1：注册，2：升级
     * @return 房间订单号
     */
    private String handleRoomPayment(MemberSaveReqVO createReqVO, String handle) {
        // 挂房账
        if (StrUtil.isBlank(createReqVO.getTogetherCode())) {
            throw exception(TOGETHERCODE_NOT_NULL);
        }
        // 获取订单信息
        OrderRespDTO order = getOrder(createReqVO.getTogetherCode());
        ConsumeAccountSaveReqDTO consumeAccountSaveReqDTO = MemberAccountConvert.INSTANCE.memberAccountConvert(createReqVO, handle);
        consumeAccountSaveReqDTO.setNo(order.getOrderNo())
                                .setAccType(AccountTypeEnum.GENERAL.getCode());
        CommonResult<Boolean> consumeAccount = accountApi.createConsumeAccount(consumeAccountSaveReqDTO);
        if (!consumeAccount.getData()) {
            throw exception(MEMBER_ACCOUNT_HANG_FAILED);
        }
        return order.getOrderNo();
    }

    /**
     * 根据宾客代码获取订单
     *
     * @param togetherCode 宾客代码
     * @return 订单
     */
    private OrderRespDTO getOrder(String togetherCode) {
        // 获取宾客信息
        OrderPersonRespDTO orderTogether = orderApi.getGuestInfo(togetherCode).getData();
        if (orderTogether == null) {
            throw exception(MEMBER_TOGETHER_ORDER_NOT_EXISTS);
        }
        if (!List.of(OrderStateEnum.CHECK_IN.getCode(), OrderStateEnum.CREDIT.getCode()).contains(orderTogether.getState())) {
            throw exception(MEMBER_ORDER_NOT_CHECK_IN);
        }
        // 获取订单信息
        OrderRespDTO order = orderApi.getOrderByOrderNo(orderTogether.getOrderNo()).getData();
        if (order == null) {
            throw exception(MEMBER_ORDER_NOT_EXISTS);
        }
        return order;
    }

    private void createCustomerHistory(MemberSaveReqVO createReqVO) {
        // 判断客史是否存在，存在则不插入 更新是否为会员 类型为是
        CustomerDTO customer = customerApi.getCustomer(createReqVO.getIdNo().trim(), createReqVO.getGcode(), createReqVO.getHcode()).getData();
        if (customer == null) {
            CustomerDTO customerDTO = MemberCustomerConvert.INSTANCE.memberCustomerConvert(createReqVO);
            if(StrUtil.isEmpty(customerDTO.getIsSms())){
                customerDTO.setIsSms(NumberEnum.ZERO.getNumber());
            }
            customerApi.createCustomer(customerDTO);
        } else {
            customerApi.updateCustomer(new CustomerDTO().setId(customer.getId()).setIsMember(BooleanEnum.TRUE.getValue()));
        }
    }

    /**
     * 发展途径为门店：直营门店注册会员创建集团卡，加盟门店注册会员创建门店卡和集团卡<br>
     * 发展途径为小程序：创建集团卡<br>
     *
     * @param createReqVO 入参
     */
    private void createStoreCard(MemberSaveReqVO createReqVO, String mcode) {
        List<StoreCardDO> storeCardList = CollUtil.newArrayList();
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(createReqVO.getHcode()).getData();
        // 创建集团卡
        StoreCardDO gStoreCardDO = StoreCardConvert.INSTANCE.storeCardConvert(createReqVO, mcode).setIsG(BooleanEnum.TRUE.getValue());
        storeCardList.add(gStoreCardDO);
        // 如果发展渠道为门店，需要再创建门店卡
        if (Objects.equals(createReqVO.getDevelopWay(), MemberPathwayEnum.LOBBY.getCode())) {
            StoreCardDO mStoreCardDO = StoreCardConvert.INSTANCE.storeCardConvert(createReqVO, mcode).setIsG(BooleanEnum.FALSE.getValue()).setHcode(createReqVO.getHcode());
            storeCardList.add(mStoreCardDO);
        }
        storeCardService.createStoreCards(storeCardList);
    }


    /**
     * 会员升级
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#updateReqVO.gcode", "#updateReqVO.mcode"}, expire = TIMEOUT_MILLIS, acquireTimeout = 5000)
    public void upgrade(MemberUpgradeReqVO updateReqVO) {
        // TODO 会员升级时需要选择升级方式
        // 获得营业日
        LocalDate bizDate = null;
        if (StrUtil.isNotBlank(updateReqVO.getHcode())) {
            bizDate = generalConfigApi.getBizDay(updateReqVO.getHcode()).getData();
        }
        //判断升级对象是否存在
        MemberDO member = memberMapper.selectOneByMcode(updateReqVO.getGcode(), updateReqVO.getMcode());
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }

        // 使用list查询出所有类型信息
        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(updateReqVO.getGcode()));
        MemberTypeDO sourceMemberType = CollectionUtils.findFirst(typeList, type -> type.getMtCode().equals(updateReqVO.getSourceMtcode()));
        MemberTypeDO targetMemberType = CollectionUtils.findFirst(typeList, type -> type.getMtCode().equals(updateReqVO.getTargetMtcode()));
        if (sourceMemberType == null || targetMemberType == null) {
            throw exception(MEMBER_TYPE_NOT_EXISTS);
        }
        // 判断升级后类型是否与当前类型不一样
        if (Objects.equals(member.getMtCode(), updateReqVO.getTargetMtcode())) {
            throw exception(MEMBER_TYPE_NOT_CHANGE, targetMemberType.getMtName());
        }
        //支付关联创建现付账
        MemberSaveReqVO memberSaveReqVO = BeanUtils.toBean(updateReqVO, MemberSaveReqVO.class);
        String cashBillOrderNo = handlePayment(memberSaveReqVO, NumberEnum.TWO.getNumber());
        if (cashBillOrderNo == null) {
            throw exception(MEMBER_PAYMENT_FAILED);
        }
        //升级修改会员信息
        upgradeMemberType(updateReqVO, member);
        // 生成消费记录
        createConsumeLog(updateReqVO, targetMemberType.getMtName(), cashBillOrderNo, member, bizDate);
        //生成级别变动日志
        createLevelLog(updateReqVO, sourceMemberType.getMtName(), targetMemberType.getMtName(), cashBillOrderNo, member, bizDate);
        //升级赠券
        CouponActivityReqDTO couponActivityReqDTO = MemberUpgreadCouponConvert.INSTANCE.memberUpgradeCouponConvert(updateReqVO)
                                                                                       .setName(member.getName())
                                                                                       .setPhone(member.getPhone());
        //couponActivityReqDTO.setOrderNo(cashBillOrderNo);
        // 现付
        if (NumberEnum.ZERO.getNumber().equals(updateReqVO.getPayType())) {
            couponActivityReqDTO.setOrderType(CouponOrderTypeEnum.CASH.getCode());
        }
        // 挂房账
        if (NumberEnum.ONE.getNumber().equals(updateReqVO.getPayType())) {
            couponActivityReqDTO.setOrderType(CouponOrderTypeEnum.ROOM.getCode());
        }
        couponActivityApi.freeTicket(couponActivityReqDTO);
    }


    /**
     * 创建消费日志
     *
     * @param updateReqVO 入参
     * @param newName     新会员类型名称
     * @param orderNo     订单号（现付账订单号或房间订单号）
     * @param member      会员
     * @param bizDate     营业日期
     */
    private void createConsumeLog(MemberUpgradeReqVO updateReqVO, String newName, String orderNo, MemberDO member, LocalDate bizDate) {
        ConsumeLogSaveReqVO consumeLogSaveReqVO = ConsumeLogConvert.INSTANCE.consumeLogMemberUpgradeConvert(updateReqVO)
                                                                            .setMtCode(updateReqVO.getTargetMtcode()).setMtName(newName)
                                                                            .setName(member.getName())
                                                                            .setPhone(member.getPhone())
                                                                            .setOrderNo(orderNo)
                                                                            .setStoreCardNo(updateReqVO.getStoreCardNo())
                                                                            .setOperator(SecurityFrameworkUtils.getLoginUserName())
                                                                            .setBizDate(bizDate == null ? LocalDate.now() : bizDate);
        if (NumberEnum.ONE.getNumber().equals(updateReqVO.getPayType())) {
            consumeLogSaveReqVO.setOrderType(CouponOrderTypeEnum.ROOM.getCode());
        }
        if (NumberEnum.ZERO.getNumber().equals(updateReqVO.getPayType())) {
            consumeLogSaveReqVO.setOrderType(CouponOrderTypeEnum.CASH.getCode());
        }
        MerchantRespDTO merchant = merchantApi.getMerchant(updateReqVO.getHcode()).getData();
        consumeLogSaveReqVO.setHname(merchant.getHname());
        consumeLogService.createConsumeLog(consumeLogSaveReqVO);
    }

    private void createLevelLog(MemberUpgradeReqVO updateReqVO, String oldName, String newName, String orderNo, MemberDO member, LocalDate bizDate) {
        // 根据更新请求转换生成等级日志保存请求对象
        LevelLogSaveReqVO levelLogSaveReqVO = LevelLogConvert.INSTANCE.leveLogConvert(updateReqVO)
                                                                      .setName(member.getName())
                                                                      .setBizDate(bizDate == null ? LocalDate.now() : bizDate);
        // 设置等级日志中的旧类型名称和新类型名称
        levelLogSaveReqVO.setSourceMtname(oldName);
        levelLogSaveReqVO.setTargetMtname(newName);
        if (NumberEnum.ZERO.getNumber().equals(updateReqVO.getPayType())) {
            levelLogSaveReqVO.setCashBillOrderNo(orderNo);
        }
        // 根据酒店类型获取会员卡号
        CommonResult<MerchantRespDTO> merchantRespDTOCommonResult = merchantApi.getMerchant(updateReqVO.getHcode());
        MerchantRespDTO merchant = merchantRespDTOCommonResult.getData();
        List<StoreCardDO> storeCardDOS = storeCardService.getStoreCardList(updateReqVO.getGcode(), updateReqVO.getBelongHcode(), List.of(updateReqVO.getMcode()));
        String storeCardNo;
        if (Objects.equals(merchant.getManageType(), ManageTypeEnum.DIRECT.getCode())) {
            storeCardNo = CollectionUtils.findFirst(storeCardDOS, storeCardDO -> storeCardDO.getIsG().equals(BooleanEnum.TRUE.getValue())).getStoreCardNo();
        } else {
            storeCardNo = CollectionUtils.findFirst(storeCardDOS, storeCardDO -> storeCardDO.getIsG().equals(BooleanEnum.FALSE.getValue())).getStoreCardNo();
        }
        if (StrUtil.isBlank(storeCardNo)) {
            throw exception(STORE_CARD_NOT_EXISTS);
        }
        levelLogSaveReqVO.setStoreCardNo(storeCardNo);
        // 创建等级日志
        levelLogService.createLevelLog(levelLogSaveReqVO);
    }

    private void upgradeMemberType(MemberUpgradeReqVO updateReqVO, MemberDO memberDO) {
        memberMapper.updateById(MemberDO.builder().id(memberDO.getId()).mtCode(updateReqVO.getTargetMtcode()).build());
    }


    @Override
    public void updateMember(MemberStarSaveReqVO updateReqVO) {
        // 校验
        verifyParameters(updateReqVO);
        updateMemberInfo(updateReqVO);
    }

    @Override
    public void updateMemberPwd(String mcode, String pwd) {
        MemberDO memberDO = memberMapper.selectOne(MemberDO::getMcode, mcode);
        if(memberDO == null){
            throw exception(MEMBER_NOT_EXISTS);
        }
        memberMapper.updateById(MemberDO.builder().id(memberDO.getId()).pwd(pwd).build());
    }

    @Override
    public void updateAppMember(String unionId, Long id) {
        MemberRespVO memberById = getMemberById(id);
        if(memberById == null){
            throw exception(MEMBER_NOT_EXISTS);
        }
        memberMapper.updateById(MemberDO.builder().id(id).unionId(unionId).build());
    }

    @Override
    public void updateMember2(MemberUpdateReqVO updateReqVO) {
        MemberSaveReqVO bean = BeanUtils.toBean(updateReqVO, MemberSaveReqVO.class);
        // 校验存在
        MemberDO member = memberMapper.selectOneByMcode(updateReqVO.getGcode(), updateReqVO.getMcode());
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        // 更新
        MemberDO updateObj = BeanUtils.toBean(updateReqVO, MemberDO.class);
        // 更新会员信息
        updateObj.setId(member.getId());
        memberMapper.updateById(updateObj);
    }

    @Override
    public MemberRespVO getMember(MemberReqVO reqVO) {
        return BeanUtils.toBean(memberMapper.selectMember(reqVO), MemberRespVO.class);
    }

    @Override
    public MemberRespVO getMemberById(Long id) {
        return BeanUtils.toBean(memberMapper.selectOne(MemberDO::getId, id), MemberRespVO.class);
    }

    @Override
    public MemberAndStoreCardRespVO getMemberAndStoreCard(MemberReqVO reqVO) {
        if(StrUtil.isNotBlank(reqVO.getPhone())){
            ServiceIntegrationRespDTO integration = serviceIntegrationApi.getServiceIntegration(
                    reqVO.getGcode(), reqVO.getHcode(), ServiceTypeEnum.PAYMENT.getCode()).getData();
            if (ObjectUtil.isNotEmpty(integration) && isStoreCardScenario(integration)) {
                return getVipInfo(reqVO, integration.getSolutionProvider());
            }
        }
        // 设置 hcode 为 null，保证会员信息在集团下可查
        String originalHcode = reqVO.getHcode();
        reqVO.setHcode(null);

        // 校验会员信息
        MemberDO memberDO = validateMember(reqVO);


        // 获取会员类型列表并构建映射
        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(reqVO.getGcode()));
        Map<String, MemberTypeDO> memberTypeMap = CollectionUtils.convertMap(typeList, MemberTypeDO::getMtCode);

        // 构建响应对象
        MemberAndStoreCardRespVO respVO = BeanUtils.toBean(memberDO, MemberAndStoreCardRespVO.class);
        populateMemberTypeDetails(respVO, memberTypeMap);

        // 获取储值卡信息
        List<StoreCardDO> storeCardList = storeCardService.getByStoreCardNo(new StoreCardReqVO()
                .setGcode(reqVO.getGcode()).setMcode(memberDO.getMcode()));
        List<MemberAndStoreCardRespVO.StoreCard> storeCards = BeanUtils.toBean(storeCardList, MemberAndStoreCardRespVO.StoreCard.class);
        List<MemberAndStoreCardRespVO.StoreCard> validCards = validateMerchantCard(storeCards, originalHcode);
        validateIsRecharge(validCards);
        respVO.setStoreCards(validCards);

        // 获取优惠券数量
        try {
            Long couponNum = couponApi.getMemberCouponListCount(reqVO.getGcode(), respVO.getPhone()).getData();
            respVO.setCouponNum(couponNum != null ? couponNum : 0L);
        } catch (Exception e) {
            // 外部服务调用异常处理
            respVO.setCouponNum(0L); // 默认值
        }

        return respVO;
    }

    /**
     * 判断是否是储值卡场景
     *
     * 本方法用于判断给定的服务集成响应对象是否包含储值卡场景通过检查场景及其参数来确定
     * 首先，它检查集成对象的场景是否非空接着，它进一步检查场景的参数是否非空且不为空
     * 最后，它通过流处理遍历参数，检查是否有参数的代码与储值卡代码相等如果找到匹配项，
     * 则认为是储值卡场景
     *
     * @param integration 服务集成响应对象，包含场景及其相关参数
     * @return 如果找到储值卡场景，则返回true；否则返回false
     */
    private boolean isStoreCardScenario(ServiceIntegrationRespDTO integration) {
        // 检查集成对象的场景是否非空
        return ObjectUtil.isNotEmpty(integration.getScenario()) &&
                // 检查场景的参数是否非空且不为空
                ObjectUtil.isNotEmpty(integration.getScenario().getParameters()) &&
                // 通过流处理遍历参数，查找是否有储值卡代码
                integration.getScenario().getParameters().stream()
                        .anyMatch(scenario -> STORE_CARD.getCode().equals(scenario.getCode()));
    }

    /**
     * 填充会员类型相关的详细信息到响应对象中。
     *
     * @param respVO 响应对象，包含会员和门店卡的相关信息。该对象会被更新以包含会员类型的名称和验证模式。
     * @param memberTypeMap 会员类型映射表，键为会员类型代码 (mtCode)，值为对应的会员类型数据对象 (MemberTypeDO)。
     *                      用于根据会员类型代码查找具体的会员类型信息。
     */
    private void populateMemberTypeDetails(MemberAndStoreCardRespVO respVO, Map<String, MemberTypeDO> memberTypeMap) {
        // 根据响应对象中的会员类型代码 (mtCode) 从会员类型映射表中获取对应的会员类型数据对象
        MemberTypeDO memberType = memberTypeMap.get(respVO.getMtCode());

        // 如果找到对应的会员类型数据对象，则将会员类型名称和验证模式设置到响应对象中
        if (memberType != null) {
            respVO.setMtName(memberType.getMtName());
            respVO.setVerifyMode(memberType.getVerifyMode());
        }
    }

    /**
     * 验证商户卡片信息的函数。
     *
     * 该函数接收一个商户卡片列表和一个商户代码（hcode），并根据特定条件过滤卡片列表。
     * 如果输入的卡片列表为空，则返回一个空列表。否则，返回经过过滤后的卡片列表。
     *
     * @param storeCards 商户卡片列表，包含每个商户卡片的信息。
     *                   如果为 null 或空列表，则直接返回空列表。
     * @param hcode      商户代码，用于匹配卡片中的 Hcode 字段。
     *                   如果为 null，则不进行 Hcode 的匹配校验。
     * @return 返回经过过滤后的商户卡片列表。
     *         过滤条件为：卡片的 IsG 字段不为零，或者 Hcode 字段与传入的 hcode 匹配。
     */
    private List<MemberAndStoreCardRespVO.StoreCard> validateMerchantCard(List<MemberAndStoreCardRespVO.StoreCard> storeCards, String hcode) {
        // 如果输入的商户卡片列表为空，直接返回空列表
        if (CollUtil.isEmpty(storeCards)) {
            return Collections.emptyList();
        }
        // 过滤商户卡片列表，保留满足以下条件的卡片：
        // 1. 卡片的 IsG 字段不为零；
        // 2. 或者，Hcode 字段与传入的 hcode 匹配（如果 hcode 为 null，则跳过此条件）。
        return storeCards.stream()
                .filter(storeCard -> !NumberEnum.ZERO.getNumber().equals(storeCard.getIsG()) || (hcode == null || hcode.equals(storeCard.getHcode())))
                .collect(Collectors.toList());
    }

    @Override
    public MemberAndStoreCardNoDesRespVO getMemberNoDes(String gcode, String mcode) {
        MemberReqVO memberSelectVO = new MemberReqVO().setGcode(gcode).setMcode(mcode);
        MemberDO memberDO = memberMapper.selectMember(memberSelectVO);
        return BeanUtil.toBean(memberDO, MemberAndStoreCardNoDesRespVO.class);
    }

    private MemberAndStoreCardRespVO getVipInfo(MemberReqVO reqVO, String platform) {
        CommonResult<VipQueryRespDTO> query = vipInfoApi.query(new VipQueryReqDTO().setHcode(reqVO.getHcode())
                .setKeyword(reqVO.getPhone())
                .setType(Integer.parseInt(NumberEnum.NINE.getNumber()))
                .setPlatform(platform));

        // 支付成功
        if (query.getCode() == 0) {
            return buildMemberAndStoreCardRespVO(query.getData());
        } else {
            throw exception(MEMBER_FAILURE, query.getMsg());
        }
    }

    private MemberAndStoreCardRespVO buildMemberAndStoreCardRespVO(VipQueryRespDTO data) {
        MemberAndStoreCardRespVO respVO=new MemberAndStoreCardRespVO();
        MemberAndStoreCardRespVO.StoreCard storeCard=new MemberAndStoreCardRespVO.StoreCard();
        respVO.setName(data.getVipName());
        respVO.setPhone(data.getMobile());
        respVO.setMtCode(data.getVipId());
        respVO.setMtName("普卡");
        storeCard.setBalance(data.getBalance());
        storeCard.setTotalGive(data.getGiveBalance());
        storeCard.setStoreCardNo(data.getVipId());
        respVO.setStoreCards(List.of(storeCard));
        return respVO;
    }


    private void updateMemberInfo(MemberStarSaveReqVO updateReqVO) {
        // 校验存在
        MemberDO member = memberMapper.selectOneByMcode(updateReqVO.getGcode(), updateReqVO.getMcode());
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        // 更新
        MemberDO updateObj = BeanUtils.toBean(updateReqVO, MemberDO.class);
        // 更新会员信息不允许修改会员类型
        updateObj.setId(member.getId()).setMtCode(null);
        memberMapper.updateById(updateObj);
    }

    /**
     * 校验储值卡是否为充值卡
     *
     * @param storeCardList 储值卡列表
     */
    private void validateIsRecharge(List<MemberAndStoreCardRespVO.StoreCard> storeCardList) {
        MerchantRespDTO merchant = merchantApi.getMerchant(storeCardList.getFirst().getHcode()).getData();
        storeCardList.forEach(storeCard -> {
            if(merchant == null){
                storeCard.setIsRecharge(BooleanEnum.TRUE.getValue());
            }else {
                if (Objects.equals(merchant.getManageType(), ManageTypeEnum.DIRECT.getCode())) {
                    if (Objects.equals(storeCard.getIsG(), BooleanEnum.TRUE.getValue())) {
                        storeCard.setIsRecharge(BooleanEnum.TRUE.getValue());
                    } else {
                        storeCard.setIsRecharge(BooleanEnum.FALSE.getValue());
                    }
                } else {
                    if (Objects.equals(storeCard.getIsG(), BooleanEnum.FALSE.getValue())) {
                        storeCard.setIsRecharge(BooleanEnum.TRUE.getValue());
                    } else {
                        storeCard.setIsRecharge(BooleanEnum.FALSE.getValue());
                    }
                }
            }
        });
    }

    @Override
    public MemberAndStoreCardSimpleRespVO getMemberAndStoreCard(MemberCardReqVO reqVO) {
        MemberAndStoreCardRespVO respVO = getMemberAndStoreCard(new MemberReqVO().setGcode(reqVO.getGcode())
                                                                                 .setHcode(reqVO.getHcode())
                                                                                 .setPhone(reqVO.getPhone())
                                                                                 .setIdNo(reqVO.getIdNo()));
        List<MemberAndStoreCardRespVO.StoreCard> storeCards = respVO.getStoreCards();
        if (CollUtil.isEmpty(storeCards)) {
            throw exception(MEMBER_STORE_CARD_NOT_EXISTS);
        }
        CommonResult<MerchantRespDTO> merchant = merchantApi.getMerchant(reqVO.getHcode());
        if (!merchant.isSuccess()) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
        List<MemberAndStoreCardRespVO.StoreCard> myCards = CollUtil.newArrayList();
        // 直营门店返回集团卡和当前门店卡
        if (Objects.equals(merchant.getData().getManageType(), ManageTypeEnum.DIRECT.getCode())) {
            myCards = CollectionUtils.filterList(storeCards, storeCard -> (Objects.equals(storeCard.getIsG(), BooleanEnum.TRUE.getValue())
                    && Objects.equals(storeCard.getState(), StoreCardStatusEnum.VALID.getCode())));
            if (CollUtil.isEmpty(myCards)) {
                throw exception(MEMBER_STORE_CARD_NOT_EXISTS);
            }
        } else { // 返回集团卡和当前门店的卡
            myCards = CollectionUtils.filterList(storeCards, storeCard -> (Objects.equals(storeCard.getIsG(), BooleanEnum.TRUE.getValue())
                    && Objects.equals(storeCard.getState(), StoreCardStatusEnum.VALID.getCode())) || (Objects.equals(storeCard.getHcode(), reqVO.getHcode())));
        }
        MemberAndStoreCardSimpleRespVO res = BeanUtils.toBean(respVO, MemberAndStoreCardSimpleRespVO.class);
        res.setStoreCards(BeanUtils.toBean(myCards, MemberAndStoreCardSimpleRespVO.StoreCard.class));
        return res;
    }

    private MemberDO validateMember(MemberReqVO reqVO) {
//        if (StringUtils.isBlank(reqVO.getPhone()) && StringUtils.isBlank(reqVO.getIdNo())) {
//            throw exception(MEMBER_PHONE_OR_IDNO_REQUIRED);
//        }
        MemberDO memberDO = memberMapper.selectMember(reqVO);
        if (memberDO == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        if (Objects.equals(memberDO.getState(), BooleanEnum.FALSE.getValue())) {
            throw exception(MEMBER_NOT_VALID);
        }
        return memberDO;
    }

    /**
     * 获得会员信息分页
     *
     * @param pageReqVO 分页查询
     * @return 会员信息分页
     */
    @Override
    public PageResult<MemberAndStoreCardRespVO> getMemberPage(MemberPageReqVO pageReqVO) {
        // 默认展示本门店信息(前端需携带默认门店代码 集团代码)
        // 判断集团配置-系统参数配置-会员列表信息管控规则 判断会员信息是否全部展示
        CommonResult<GroupParamConfigMemberRespDTO> groupParamConfigMember = groupParamConfigApi.getGroupParamConfigMember(pageReqVO.getGcode());
        GroupParamConfigMemberRespDTO groupParamConfigMemberResp = groupParamConfigMember.getData();
        String memberInfoSearch = groupParamConfigMemberResp.getValue().getMemberInfoSearch();
        // 判断是否展示全部会员信息
        if (NumberEnum.ONE.getNumber().equals(memberInfoSearch)) {
            // 不直接展示的话需要携带 姓名或身份证或手机号
            if (StringUtils.isNotBlank(pageReqVO.getName()) || StringUtils.isNotBlank(pageReqVO.getIdNo()) || StringUtils.isNotBlank(pageReqVO.getPhone())) {
                return queryAndProcessMemberData(pageReqVO);
            } else {
                return new PageResult<>(Collections.emptyList(), 0L);
            }
        } else {
            return queryAndProcessMemberData(pageReqVO);
        }
    }

    @Override
    public List<MemberAndStoreCardRespVO> getMemberList(MemberListReqVO reqVO) {
        List<MemberDO> memberDOS = memberMapper.selectList(reqVO);
        if (CollUtil.isEmpty(memberDOS)) {
            return CollUtil.newArrayList();
        }
        // 获取所有门店
        List<MerchantRespDTO> merchants = merchantApi.getMerchantList(reqVO.getGcode()).getData();
        Map<String, String> merchantsNameMap = CollectionUtils.convertMap(merchants, MerchantRespDTO::getHcode, MerchantRespDTO::getHname);
        // 获取所有会员级别
        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(reqVO.getGcode()));
        Map<String, MemberTypeDO> memberTypeDOMap = CollectionUtils.convertMap(typeList, MemberTypeDO::getMtCode);

        return CollectionUtils.convertList(memberDOS, memberDO -> {
            MemberAndStoreCardRespVO bean = BeanUtils.toBean(memberDO, MemberAndStoreCardRespVO.class);
            bean.setMtName(memberTypeDOMap.getOrDefault(bean.getMtCode(), new MemberTypeDO()).getMtName());
//            bean.setLevel(memberTypeDOMap.getOrDefault(bean.getMtCode(), new MemberTypeDO()).getLevel());
            bean.setHName(merchantsNameMap.getOrDefault(bean.getHcode(), ""));
            return bean;
        });
    }

    /**
     * 查询并处理会员数据
     *
     * @param pageReqVO 分页查询请求
     * @return 会员信息分页结果
     */
    private PageResult<MemberAndStoreCardRespVO> queryAndProcessMemberData(MemberPageReqVO pageReqVO) {
        PageResult<MemberDO> memberDOPageResult = memberMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(memberDOPageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), memberDOPageResult.getTotal());
        }
        List<MerchantRespDTO> merchants = merchantApi.getMerchantList(pageReqVO.getGcode()).getData();
        Map<String, String> merchantsNameMap = CollectionUtils.convertMap(merchants, MerchantRespDTO::getHcode, MerchantRespDTO::getHname);
        // 获取所有会员级别
        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(pageReqVO.getGcode()));
        // 构建会员级别代码到会员级别名称的映射
        Map<String, MemberTypeDO> memberTypeMap = CollectionUtils.convertMap(typeList, MemberTypeDO::getMtCode);
        List<MemberDO> memberDOList = memberDOPageResult.getList();
        List<MemberAndStoreCardRespVO> memberAndStoreCardRespVOList = memberDOList.stream().map(memberDO -> {
            MemberAndStoreCardRespVO bean = BeanUtils.toBean(memberDO, MemberAndStoreCardRespVO.class);
            bean.setMtName(memberTypeMap.get(bean.getMtCode()).getMtName());
//            bean.setLevel(memberTypeMap.get(bean.getMtCode()).getLevel());
            bean.setHName(merchantsNameMap.getOrDefault(bean.getHcode(), ""));
            return bean;
        }).collect(Collectors.toList());
        return new PageResult<>(memberAndStoreCardRespVOList, memberDOPageResult.getTotal());
    }


    /**
     * 充值
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#rechargeReqVO.gcode", "#rechargeReqVO.mcode"}, expire = TIMEOUT_MILLIS, acquireTimeout = 5000)
    public void recharge(RechargeSaveReqVO rechargeReqVO) {
        validUnit(rechargeReqVO);
        // 获取会员信息
        MemberDO member = getMember(rechargeReqVO.getGcode(), rechargeReqVO.getMcode());
        // 获取会员级别名称
        MemberTypeDO memberType = getMemberType(rechargeReqVO.getGcode(), member.getMtCode());
        // 校验判断
        verificationJudge(memberType);
        // 获取发生门店信息
        MerchantRespDTO merchant = getRechargeMerchant(rechargeReqVO.getHhcode());
        LocalDate bizDate = null;
        if (StrUtil.isNotBlank(rechargeReqVO.getHhcode())) {
            bizDate = generalConfigApi.getBizDay(rechargeReqVO.getHhcode()).getData();
        }
        // 获取活动信息
        RechargeActivityRespVO activity = getRechargeActivity(rechargeReqVO, member);
        // 更新储值卡余额和积分
        StoreCardDO storeCard = updateStoreCard(rechargeReqVO, activity);
        // 创建现付账订单
        String cashBillOrderNo = createRechargeCashBillOrder(rechargeReqVO, member);
        // 处理优惠券
        handleCoupon(rechargeReqVO, activity, member, cashBillOrderNo);
        // 处理积分记录
        handlePointLog(rechargeReqVO, activity, member, memberType, merchant, cashBillOrderNo, bizDate);
        // 生成储值日志
        handleRechargeLog(rechargeReqVO, storeCard, merchant, member, memberType, activity, bizDate, cashBillOrderNo);
        // 短信发送给
        smsSedRecharge(rechargeReqVO, storeCard);
    }

    /**
     * 发送充值短信
     * @param reqVO
     */
    private void smsSedRecharge(RechargeSaveReqVO reqVO, StoreCardDO storeCard){
        // 验证
        if(!NumberEnum.ONE.getNumber().equals(reqVO.getIsSendSms())) {
            return;
        }
        Integer sign = smsSendClientApi.verifySend(8, reqVO.getHcode(), reqVO.getIsSendSms(), reqVO.getMcode()).getData();
        if(NumberEnum.ZERO.getNumberInt().equals(sign)) {
            return;
        }
        // 属性处理
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String operateTime = storeCard.getUpdateTime().format(formatter);
        String notFiled = "暂无";
        String oldFrontPhone = merchantApi.getMerchant(reqVO.getHcode()).getData().getFrontPhone();
        String frontPhone = (StrUtil.isEmpty(oldFrontPhone) ? notFiled : oldFrontPhone );
        List<HashMap<String, String>> send = new ArrayList<>();
        MemberDO memberDO = memberMapper.selectOneByMcode(reqVO.getGcode(), reqVO.getMcode());
        String phone = memberDO.getPhone();
        // 拼装
        send.add(new HashMap<String, String>() {{
            put(SmsConfigParamEnum.MNAME.getCode(), memberDO.getName());
            put(SmsConfigParamEnum.OPERATE_TIME.getCode(), operateTime);
            put(SmsConfigParamEnum.RECHARGE_FEE.getCode(), String.valueOf(reqVO.getFee()/100.0));
            put(SmsConfigParamEnum.GIFT_AMOUNT.getCode(), String.valueOf(reqVO.getGiveMoney()/100.0));
            put(SmsConfigParamEnum.BALANCE.getCode(), String.valueOf(storeCard.getBalance()/100.0));
            put(SmsConfigParamEnum.RECEIVER.getCode(), phone);
        }});
        smsSendClientApi.smsSendForMap(new SendRequestDTO().setSend(send).setSign(sign).setType(16));

    }
    private void validUnit(RechargeSaveReqVO rechargeReqVO) {
        //获得付款方式配置信息
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO().setGcode(rechargeReqVO.getGcode()).setHcode(NumberEnum.ZERO.getNumber())
                .setCode(rechargeReqVO.getPayMethod()).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode())).getData();
        //获得门店信息
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(rechargeReqVO.getHcode()).getData();
        if(ObjectUtil.isNotEmpty(generalConfig) && StrUtil.isNotEmpty(generalConfig.getCurrencyUnit())
                && ObjectUtil.isNotEmpty(merchantRespDTO)){
            if(!generalConfig.getCurrencyUnit().equals(merchantRespDTO.getCurrencyUnit())){
                throw exception(PAY_TYPE_NOT_MATCH, CurrencyUnitEnum.getNameByCode(merchantRespDTO.getCurrencyUnit()));
            }
        }
    }

    private MemberDO getMember(String gcode, String mcode) {
        MemberDO member = memberMapper.selectOneByMcode(gcode, mcode);
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        return member;
    }

    /**
     * 处理储值日志
     *
     * @param reqVO           入参
     * @param merchant        门店
     * @param member          会员
     * @param memberType      会员类型
     * @param activity        活动
     * @param bizDate         营业日
     * @param cashBillOrderNo 现付账订单号
     */
    private void handleRechargeLog(RechargeSaveReqVO reqVO, StoreCardDO storeCard, MerchantRespDTO merchant, MemberDO member,
                                   MemberTypeDO memberType, RechargeActivityRespVO activity, LocalDate bizDate, String cashBillOrderNo) {
        RechargeLogSaveReqVO rechargeLogSaveReqVO = new RechargeLogSaveReqVO().setGcode(reqVO.getGcode())
                                                                              .setHcode(reqVO.getHhcode())
                                                                              .setRechargeCode(IdUtil.getSnowflakeNextIdStr())
                                                                              .setMcode(member.getMcode())
                                                                              .setMtCode(memberType.getMtCode())
                                                                              .setMtName(memberType.getMtName())
                                                                              .setName(member.getName())
                                                                              .setPhone(member.getPhone())
                                                                              .setFee(reqVO.getFee())
                                                                              .setPayMethod(reqVO.getPayMethod())
                                                                              .setPayCode(reqVO.getPayCode())
                                                                              .setRechargeChannel(reqVO.getRechargeChannel())
                                                                              .setStoreCardNo(storeCard.getStoreCardNo())
                                                                              .setBizDate(bizDate == null ? LocalDate.now() : bizDate)
                                                                              .setCashBillOrderNo(cashBillOrderNo)
                                                                              .setGiveMoney(reqVO.getGiveMoney())
                                                                              .setActivityCode(reqVO.getActivityCode())
                                                                              .setOperator(SecurityFrameworkUtils.getLoginUserName())
                                                                              .setRemark(reqVO.getRemark())
                                                                              .setSeller(reqVO.getSeller());
        if (merchant != null) {
            rechargeLogSaveReqVO.setHname(merchant.getHname());
        }
        if (activity != null && activity.getGiveFee() != null) {
            rechargeLogSaveReqVO.setGiveMoney(activity.getGiveFee())
                                .setGivePoint(activity.getGivePoint())
                                .setActivityCode(activity.getActivityCode());
        }
        rechargeLogService.createRechargeLog(rechargeLogSaveReqVO);
    }

    private MerchantRespDTO getRechargeMerchant(String hcode) {
        if (StrUtil.isNotBlank(hcode)) {
            return merchantApi.getMerchant(hcode).getData();
        }
        return null;
    }

    private void verificationJudge(MemberTypeDO memberType) {
        // 判断会员级别是否支持储值
        if (NumberEnum.ZERO.getNumber().equals(memberType.getSupportStore())) {
            throw exception(MEMBER_TYPE_NOT_SUPPORT_STORE);
        }
    }

    /**
     * 获取门店信息
     *
     * @param hcode 门店代码
     * @return 门店
     */
    private MerchantRespDTO getMerchantInfo(String hcode) {
        MerchantRespDTO merchantRespDTO = merchantApi.getMerchant(hcode).getData();
        if (merchantRespDTO == null) {
            throw exception(MERCHANT_NOT_EXISTS);
        }
        return merchantRespDTO;
    }

    // 获取会员级别名称
    private MemberTypeDO getMemberType(String gcode, String mtcode) {
        MemberTypeDO type = memberTypeService.getType(mtcode, gcode);
        if (type == null) {
            throw exception(MEMBER_TYPE_NOT_EXISTS);
        }
        return type;
    }

    /**
     * 更新会员储值卡金额和积分
     *
     * @param createReqVO 入参
     * @param activity    活动
     */
    private StoreCardDO updateStoreCard(RechargeSaveReqVO createReqVO, RechargeActivityRespVO activity) {
        // 更新储值卡信息
        StoreCardDO storeCard = validateStoreCard(createReqVO);
        if (activity == null) {
            storeCardService.calculateStoreCard(StoreCardHandleEnum.RECHARGE, storeCard, createReqVO.getFee(),0L, 0L, 0L, 0L);
        } else {
            // 根据活动设置会员卡的相关金额和积分
            storeCardService.calculateStoreCard(StoreCardHandleEnum.RECHARGE, storeCard, createReqVO.getFee(),0L, activity.getGiveFee(), 0L, activity.getGivePoint());
        }
        storeCardService.updateStoreCardById(storeCard);
        return storeCard;
    }

    /**
     * 处理优惠券
     *
     * @param createReqVO 入参
     * @param activity    活动
     * @param member      会员
     */
    private void handleCoupon(RechargeSaveReqVO createReqVO, RechargeActivityRespVO activity, MemberDO member, String cashBillOrderNo) {
        if (activity == null || CollUtil.isEmpty(activity.getTickets())) {
            return;
        }
        List<CouponGenerateBO> couponGenerates = activity.getTickets().parallelStream().map(ticketsParameter -> {
            CouponGenerateBO couponGenerateBO = RechargeCouponConvert.INSTANCE.rechargeCouponConvert(ticketsParameter);
            couponGenerateBO.setBelong(member.getPhone());
            couponGenerateBO.setName(member.getName());
            couponGenerateBO.setGcode(createReqVO.getGcode());
            couponGenerateBO.setHcode(createReqVO.getHcode());
            couponGenerateBO.setActivityCode(activity.getActivityCode());
            return couponGenerateBO;
        }).collect(Collectors.toList());
        couponApi.createCoupon(new CouponGenerateReqDTO().setCouponGenerate(couponGenerates)
                                                         .setGcode(createReqVO.getGcode())
                                                         .setHcode(createReqVO.getHcode())
                                                         //.setOrderNo(cashBillOrderNo)
                                                         .setOrderType(CouponOrderTypeEnum.CASH.getCode()));
    }

    /**
     * 创建充值现付账订单
     *
     * @param reqVO 入参
     * @return 现付账订单号
     */
    private String createRechargeCashBillOrder(RechargeSaveReqVO reqVO, MemberDO member) {
        // 查询现付账套
        AccSetRespDTO accSet = accSetApi.getAccSetBySubcode(ConsumeAccountEnum.MEMBER_RECHARGE.getCode(), reqVO.getGcode()).getData();
        if (accSet == null) {
            throw exception(ACCOUNT_SET_NOT_EXIST);
        }
        CashBillOrderSaveReqDTO cashBillOrderSaveReqDTO = CashBillOrderConvert.INSTANCE.rechargeCashBillOrderConvert(reqVO).setAccCode(accSet.getAccCode());
        // 设置buyContent属性值，值的格式是 "会员充值" + "充值金额" + "活动名称" + "手机号"
        MobileDesensitization desensitization = new MobileDesensitization();
        cashBillOrderSaveReqDTO.setBuyContent(String.format("会员充值(姓名：%s 手机号：%s 充值金额：%.2f)", member.getName(),desensitization.newDes(member.getPhone()), reqVO.getFee() / 100.0));
        CommonResult<CashBillOrderSaveRespDTO> cashBillOrderSaveRespDTO = cashBillOrderApi.createCashBillOrder(cashBillOrderSaveReqDTO);
        return cashBillOrderSaveRespDTO.getData().getCashBillOrderNo();
    }

    private RechargeActivityRespVO getRechargeActivity(RechargeSaveReqVO reqVO, MemberDO member) {
        if (StrUtil.isBlank(reqVO.getActivityCode()) || Objects.equals(reqVO.getActivityCode(), NumberEnum.ZERO.getNumber())) {
            return null;
        }
        RechargeActivityRespVO activity = rechargeActivityService.getRechargeActivity(reqVO.getGcode(), reqVO.getActivityCode());
        // 验证状态
        if (Objects.equals(activity.getState(), BooleanEnum.FALSE.getValue())) {
            throw exception(RECHARGE_ACTIVITY_NOT_ENABLE);
        }
        // 验证当前会员级别是否可以参与
        if (CollUtil.isEmpty(activity.getMts()) || !activity.getMts().contains(member.getMtCode())) {
            throw exception(MEMBER_TYPE_NOT_SUPPORT_RECHARGE_ACTIVITY);
        }
        // 判断当前活动是否支持传入的渠道
        if (CollUtil.isEmpty(activity.getChannels()) || !activity.getChannels().contains(reqVO.getRechargeChannel())) {
            throw exception(RECHARGE_ACTIVITY_NOT_SUPPORT_CHANNEL);
        }
        // 判断活动是否支持当前门店
        if (StrUtil.isNotBlank(reqVO.getHcode()) && !activity.getRechargeActivityMerchants().contains(reqVO.getHcode())) {
            throw exception(RECHARGE_ACTIVITY_NOT_SUPPORT_MERCHANT);
        }
        // 判断当前时间是否在活动时间内
        if (!LocalDateTimeUtil.isIn(LocalDateTime.now(), activity.getStartDate().atStartOfDay(), activity.getEndDate().atTime(LocalTime.MAX))) {
            throw exception(RECHARGE_ACTIVITY_NOT_IN_TIME);
        }
        // 判断当前会员卡充值次数
        if (activity.getTimes() > 0) {
            List<RechargeLogDO> rechargeLogList = rechargeLogService.getRechargeLogList(new RechargeLogListReqVO().setGcode(reqVO.getGcode()).setMcode(reqVO.getMcode()));
            if (rechargeLogList.size() >= activity.getTimes()) {
                throw exception(RECHARGE_ACTIVITY_TIMES_LIMIT, activity.getTimes());
            }
        }
        return activity;
    }

    /**
     * 验证储值卡
     *
     * @param createReqVO 入参
     * @return 储值卡
     */
    private StoreCardDO validateStoreCard(RechargeSaveReqVO createReqVO) {
        MerchantRespDTO merchant = merchantApi.getMerchant(createReqVO.getHcode()).getData();
        List<StoreCardDO> storeCardList = storeCardService.getStoreCardList(createReqVO.getGcode(), createReqVO.getHcode(), List.of(createReqVO.getMcode()));
        StoreCardDO storeCard = null;
        if (merchant ==null || Objects.equals(merchant.getManageType(), ManageTypeEnum.DIRECT.getCode())) {
            storeCard = storeCardList.stream().filter(s -> Objects.equals(s.getIsG(), BooleanEnum.TRUE.getValue())).findFirst().orElse(null);
        } else {
            storeCard = storeCardList.stream().filter(s -> Objects.equals(s.getIsG(), BooleanEnum.FALSE.getValue())).findFirst().orElse(null);
        }
        if (storeCard == null) {
            throw exception(STORE_CARD_NOT_EXISTS);
        }
        if (!Objects.equals(storeCard.getState(), StoreCardStatusEnum.VALID.getCode())) {
            throw exception(STORE_CARD_NOT_VALID);
        }
        return storeCard;
    }

    // 充值赠送积分
    private void handlePointLog(RechargeSaveReqVO rechargeReqVO, RechargeActivityRespVO activity, MemberDO member,
                                MemberTypeDO memberType, MerchantRespDTO merchant, String cashBillOrderNo, LocalDate bizDate) {
        if (activity == null) {
            return;
        }
        // 积分
        if (activity.getGivePoint() != null) {
            PointLogSaveReqVO pointLogSaveReqVO = PointLogConvert.INSTANCE
                    .pointLogRechargeLogSaveConvert(rechargeReqVO, member, memberType, cashBillOrderNo);
            if (merchant != null) {
                pointLogSaveReqVO.setHname(merchant.getHname());
                pointLogSaveReqVO.setHcode(rechargeReqVO.getHhcode());
            }
            pointLogSaveReqVO.setBizDate(bizDate == null ? LocalDate.now() : bizDate);
            pointLogSaveReqVO.setPoint(activity.getGivePoint());
            createPoint(pointLogSaveReqVO);
        }
    }

    /**
     * 会员卡消费
     *
     * @param consumeReqVO 入参
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = "{#consumeReqVO.gcode, #consumeReqVO.mcode}", expire = TIMEOUT_MILLIS, acquireTimeout = 5000)
    public void consume(ConsumeSaveReqVO consumeReqVO) {
        StoreCardDO storeCard = storeCardService.getStoreCardByNo(consumeReqVO.getStoreCardNo());
        if (!Objects.equals(STORE_CARD.getCode(), consumeReqVO.getPayMethod()) && !Objects.equals(STORE_CARD_REFUND.getCode(), consumeReqVO.getPayMethod())) {
            throw exception(MEMBER_PAY_METHOD_NOT_SUPPORT);
        }
        MemberDO member = validateMember(consumeReqVO.getGcode(), consumeReqVO.getMcode());
        // 获得营业日
        LocalDate bizDate = generalConfigApi.getBizDay(consumeReqVO.getHcode()).getData();
        MemberTypeDO memberType = getMemberType(consumeReqVO.getGcode(), member.getMtCode());

        // 判断储值卡余额是否足够
        if (storeCard.getBalance() < consumeReqVO.getFee()) {
            throw exception(STORE_CARD_NOT_ENOUGH);
        }
        // 密码验证
        if (NumberEnum.ONE.getNumber().equals(memberType.getVerifyMode())) {
            if (StrUtil.isBlank(consumeReqVO.getPwd())) {
                throw exception(MEMBER_PWD_NOT_EXISTS);
            }
            if (!consumeReqVO.getPwd().trim().equals(member.getPwd().trim())) {
                throw exception(MEMBER_PWD_ERROR);
            }
        }
        if (NumberEnum.TWO.getNumber().equals(memberType.getVerifyMode())) {
            // TODO 短信验证
        }
        // 获取门店信息
        MerchantRespDTO merchant = getMerchantInfo(consumeReqVO.getHcode());
        // 更新会员消费次数
        updateMemberFeeCount(member);
        // 生成消费日志
        consumeLog(consumeReqVO, member, merchant, memberType, bizDate);
        // 进行消费积分
        long consumePoint = consumePoint(consumeReqVO, merchant, member, memberType, bizDate);
        // 更新会员储值卡余额
        updateStoreCardBalance(consumeReqVO.getFee(), consumePoint, storeCard);
    }

    private MemberDO validateMember(String gcode, String mcode) {
        MemberDO member = getMember(gcode, mcode);
        // 验证状态
        if (Objects.equals(member.getState(), BooleanEnum.FALSE.getValue())) {
            throw exception(MEMBER_NOT_VALID);
        }
        return member;
    }

    private void consumeLog(ConsumeSaveReqVO reqVO, MemberDO member, MerchantRespDTO merchant, MemberTypeDO memberType, LocalDate bizDate) {
        ConsumeLogSaveReqVO consumeLogSaveReqVO = ConsumeLogConvert.INSTANCE.consumeLogConvert(reqVO, member, merchant, memberType);
        consumeLogSaveReqVO.setBizDate(bizDate == null ? LocalDate.now() : bizDate).setOperator(SecurityFrameworkUtils.getLoginUserName());
        consumeLogService.createConsumeLog(consumeLogSaveReqVO);
    }

    // 进行消费积分
    private long consumePoint(ConsumeSaveReqVO consumeReqVO, MerchantRespDTO merchant, MemberDO member, MemberTypeDO memberType, LocalDate bizDate) {
        // 获取积分权益
        PointRuleDO pointRule = pointRuleService.getPointRule(consumeReqVO.getGcode());
        if (pointRule == null) {
            throw exception(POINT_RULE_NOT_EXISTS);
        }
        long givePoint = 0;
        if (pointRule.getConsumePoint() > 0) {
            // 计算消费积分
            givePoint = calculateConsumePoints(consumeReqVO, pointRule);
            // 创建积分记录
            if (givePoint > 0) {
                PointLogSaveReqVO pointLogSaveReqVO = PointLogConvert.INSTANCE.pointLogConsumeLogSaveConvert(consumeReqVO, merchant, member, memberType, bizDate, NumberEnum.ZERO.getNumber());
                pointLogSaveReqVO.setPoint(givePoint);
                if (NumberEnum.ONE.getNumber().equals(pointRule.getIsForEver())) {
                    pointLogSaveReqVO.setIndate(LocalDate.now().plusYears(100L));
                }
                if (NumberEnum.ZERO.getNumber().equals(pointRule.getIsForEver())) {
                    pointLogSaveReqVO.setIndate(LocalDate.now().plusMonths(pointRule.getIndateMonth()));
                }
                pointLogService.createPointLog(pointLogSaveReqVO);
            }
        }
        return givePoint;
    }

    private long calculateConsumePoints(ConsumeSaveReqVO consumeReqVO, PointRuleDO pointRule) {
        long consumeFee = Math.divideExact(consumeReqVO.getFee(), 100);
        long givePoint = 0;

        if (NumberEnum.ONE.getNumber().equals(consumeReqVO.getConsumeType()) && pointRule.getConsumePoint() != 0) {
            givePoint = Math.toIntExact(Math.multiplyExact(consumeFee, pointRule.getConsumePoint()));
        } else if (NumberEnum.ZERO.getNumber().equals(consumeReqVO.getConsumeType()) && pointRule.getRoomRatePoint() != 0) {
            givePoint = Math.toIntExact(Math.multiplyExact(consumeFee, pointRule.getRoomRatePoint()));
        }
        return givePoint;
    }


    /**
     * 更新会员消费次数
     *
     * @param member 会员
     */
    private void updateMemberFeeCount(MemberDO member) {
        Long feeCount = member.getFeeCount() + NumberEnum.ONE.getNumberInt();
        memberMapper.updateById(MemberDO.builder().id(member.getId()).feeCount(feeCount).build());
    }

    /**
     * 更新会员储值卡余额
     *
     * @param fee       消费金额
     * @param givePoint 送的积分
     * @param storeCard 储值卡
     */
    private void updateStoreCardBalance(Long fee, Long givePoint, StoreCardDO storeCard) {
        storeCardService.calculateStoreCard(StoreCardHandleEnum.CONSUME, storeCard, fee, 0L,0L, 0L, givePoint);
        storeCardService.updateStoreCardById(storeCard);
    }

    /**
     * 积分
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPoint(PointLogSaveReqVO pointReqVO) {
        // 获得营业日
        LocalDate bizDate = generalConfigApi.getBizDay(pointReqVO.getHcode()).getData();
//        if (!pointReqVO.getBizDate().isEqual(bizDate)){
//            throw exception(BIZ_DATE_NOT_EQUAL);
//        }
        // 获取门店信息
        MerchantRespDTO merchantRespDTO = getMerchantInfo(pointReqVO.getHcode());
        // 设置门店名称
        pointReqVO.setHname(merchantRespDTO.getHname());
        // 获取会员信息
        MemberDO member = memberMapper.selectOneByMcode(pointReqVO.getGcode(), pointReqVO.getMcode());
        // 获取会员级别名称
        MemberTypeDO type = getMemberType(member.getGcode(), member.getMtCode());
        pointReqVO.setMtName(type.getMtName());
        // 判断是否是生日或会员日，若是生日或会员日则设置生日积分或会员日积分
        setBirthdayPoint(pointReqVO, member);
        // 更新会员积分信息
        updateMemberPoints(pointReqVO, member, merchantRespDTO);
        // 生成积分日志
        pointLogService.createPointLog(pointReqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPointBatch(List<PointLogSaveReqVO> pointReqVOs) {
        // 获得营业日
        //LocalDate bizDate = generalConfigApi.getBizDay(pointReqVO.getHcode()).getData();
//        if (!pointReqVO.getBizDate().isEqual(bizDate)){
//            throw exception(BIZ_DATE_NOT_EQUAL);
//        }
        List<StoreCardDO> storeCardDOS = validateStoreCards(pointReqVOs);
        // 更新会员积分信息
        updateBatchMemberPoint(pointReqVOs, storeCardDOS);
        // 生成积分日志
        pointLogService.createPointLogBatch(pointReqVOs);
    }

    /**
     * 验证储值卡
     *
     * @param pointReqVOs 入参
     * @return 储值卡
     */
    private List<StoreCardDO> validateStoreCards(List<PointLogSaveReqVO> pointReqVOs) {
        List<String> mCodes = CollectionUtils.convertList(pointReqVOs, PointLogSaveReqVO::getMcode);
        MerchantRespDTO merchant = merchantApi.getMerchant(pointReqVOs.getFirst().getHcode()).getData();
        List<StoreCardDO> storeCardList = storeCardService.getStoreCardList(pointReqVOs.getFirst().getGcode(),null, mCodes);
        List<StoreCardDO> storeCards = new ArrayList<>();
        if (Objects.equals(merchant.getManageType(), ManageTypeEnum.DIRECT.getCode())) {
            storeCards = storeCardList.stream().filter(s -> Objects.equals(s.getIsG(), BooleanEnum.TRUE.getValue())).toList();
        } else {
            storeCards = storeCardList.stream().filter(s -> Objects.equals(s.getIsG(), BooleanEnum.FALSE.getValue())).toList();
        }
        if (ObjectUtil.isEmpty(storeCards)) {
            throw exception(STORE_CARD_NOT_EXISTS);
        }
        return storeCards;
    }

    @Override
    public void updateMemberStatus(MemberUpdateStatusReqVO updateReqVO) {
        MemberDO memberDO = memberMapper.selectOneByMcode(updateReqVO.getGcode(), updateReqVO.getMcode());
        if (memberDO == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        memberMapper.updateById(MemberDO.builder().id(memberDO.getId()).state(updateReqVO.getState()).build());
    }

    /**
     * 删除会员信息
     */
    @Override
    public void deleteMember(String gcode, String mcode) {
        MemberDO memberDO = memberMapper.selectOneByMcode(gcode, mcode);
        if (memberDO == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        memberMapper.deleteById(memberDO.getId());
    }

    @Override
    public void verifyMemberPassword(String gcode, String mcode, String password) {
        MemberDO memberDO = memberMapper.selectOneByMcode(gcode, mcode);
        if(!memberDO.getPwd().equals(password)){
            throw exception(MEMBER_PASSWORD_NO_EQUAL);
        }
    }

    /**
     * 判断是否是生日，若是生日则设置生日积分
     *
     * @param pointReqVO 积分请求VO
     * @param member     会员信息
     */
    private void setBirthdayPoint(PointLogSaveReqVO pointReqVO, MemberDO member) {
        LocalDate birthday = member.getBirthday();
        LocalDate now = LocalDate.now();
        if (birthday != null) {
            if (now.getMonthValue() == birthday.getMonthValue() && now.getDayOfMonth() == birthday.getDayOfMonth()) {
                // 获取积分权益
                PointRuleDO pointRule = pointRuleService.getPointRule(pointReqVO.getGcode());
                if (pointRule == null) {
                    throw exception(POINT_RULE_NOT_EXISTS);
                }
                Long point = pointReqVO.getPoint();
                BigDecimal birthdayPoint = pointRule.getBirthdayPointTimes();
                BigDecimal newPointValue = birthdayPoint.multiply(BigDecimal.valueOf(point));
                pointReqVO.setPoint(newPointValue.longValue());
            }
        }
        // TODO 会员日积分
    }


    /**
     * 更新会员积分信息
     */
    private void updateMemberPoints(PointLogSaveReqVO createReqVO, MemberDO member, MerchantRespDTO merchantRespDTO) {
        Long point = createReqVO.getPoint();
        // 判断管理类型
        if (ManageTypeEnum.DIRECT.getCode().equals(merchantRespDTO.getManageType())) {
            updateMemberPoints(createReqVO, member, point, true);
        } else {
            updateMemberPoints(createReqVO, member, point, false);
        }
    }

    private void updateMemberPoints(PointLogSaveReqVO createReqVO, MemberDO member, Long point, boolean isGroup) {

        boolean isGetPoint = NumberEnum.ZERO.getNumber().equals(createReqVO.getPointType());
        Long totalPoint = isGetPoint ? point : -point;
        Long currentPoint = isGetPoint ? point : -point;
//
//        if (!isGetPoint && currentPoint > getCurrentPoint(member, isGroup)) {
//            throw exception(POINT_LOG_DELETE_ERROR);
//        }
////
//        if (!isGetPoint) {
//            member.setUsedPoint(member.getUsedPoint() + point);
//        }
//
//        if (isGroup) {
//            member.setGTotalPoint(member.getGTotalPoint() + totalPoint);
//            member.setGPoint(member.getGPoint() + currentPoint);
//        } else {
//            member.setMTotalPoint(member.getMTotalPoint() + totalPoint);
//            member.setMPoint(member.getMPoint() + currentPoint);
//        }
        // 打印调用栈
        memberMapper.updateById(member);
    }


    private void updateBatchMemberPoint(List<PointLogSaveReqVO> createReqVOs, List<StoreCardDO> storeCardDOS) {
        Map<String, List<PointLogSaveReqVO>> pointMap = CollectionUtils.convertMultiMap(createReqVOs, PointLogSaveReqVO::getMcode);
        storeCardDOS.forEach(storeCardDO -> {
            List<PointLogSaveReqVO> pointLogSaveReqVOS = pointMap.get(storeCardDO.getMcode());
            long point = pointLogSaveReqVOS.stream().mapToLong(PointLogSaveReqVO::getPoint).sum();
            storeCardDO.setPoint(storeCardDO.getPoint() + point);
            storeCardDO.setTotalPoint(storeCardDO.getTotalPoint() + point);
        });
        storeCardService.updateStoreCardBatch(storeCardDOS);
    }

    // 辅助方法，获取当前积分
//    private Long getCurrentPoint(MemberDO member, boolean isGroup) {
//        return isGroup ? member.getGPoint() : member.getMPoint();
//    }



}