package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogListReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.ConsumeLogDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会员消费日志 Service 接口
 *
 * <AUTHOR>
 */
public interface ConsumeLogService {

    /**
     * 创建会员消费日志
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createConsumeLog(@Valid ConsumeLogSaveReqVO createReqVO);

    /**
     * 获得会员消费日志
     *
     * @param
     * @return 会员消费日志
     */
    ConsumeLogDO getConsumeLog(String mcode, String hcode, String gcode);

    /**
     * 获得会员消费日志分页
     *
     * @param pageReqVO 分页查询
     * @return 会员消费日志分页
     */
    PageResult<ConsumeLogRespVO> getConsumeLogPage(ConsumeLogPageReqVO pageReqVO);

    /**
     * 获取会员消费日志列表
     */
    List<ConsumeLogDO> getConsumeLogList(ConsumeLogListReqVO pageReqVO);

}