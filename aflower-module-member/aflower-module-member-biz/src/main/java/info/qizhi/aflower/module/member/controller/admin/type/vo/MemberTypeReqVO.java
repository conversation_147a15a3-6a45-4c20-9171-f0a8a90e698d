package info.qizhi.aflower.module.member.controller.admin.type.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员类型 Request VO")
@Data
@ToString(callSuper = true)
public class MemberTypeReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String mtName;

    @Schema(description = "级别", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;

    @Schema(description = "是否有效;0：否 1：是")
    private String isEnable;


}