package info.qizhi.aflower.module.member.controller.app.user.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class AppMemberInfoAndTypeRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "30240")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员类型(会员级别);0:散客 ，其他为会员类型代码")
    private String mtCode;

    @Schema(description = "会员类型(会员级别)名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtName;

    @Schema(description = "会员权益信息")
    private List<MemberType> memberTypes ;

    @Data
    public static class MemberType {
        @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @ExcelProperty("会员类型(会员级别);0:散客 ，其他为会员类型代码")
        private String mtCode;

        @Schema(description = "会员类型(会员级别)名称", requiredMode = Schema.RequiredMode.REQUIRED)
        @ExcelProperty("会员类型(会员级别)名称")
        private String mtName;

        @Schema(description = "图标类型", requiredMode = Schema.RequiredMode.REQUIRED)
        private String imageType;

        @Schema(description = "会员的权益数量", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer benefitNum;

        @Schema(description = "升级享订房折扣", requiredMode = Schema.RequiredMode.REQUIRED)
        private String discount;

        @Schema(description = "会员权益信息")
        private List<MemberBenefit> memberBenefits;

        @Data
        public static class MemberBenefit {
            @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED)
            private String image;

            @Schema(description = "权益代码", requiredMode = Schema.RequiredMode.REQUIRED)
            private String benefitCode ;

            @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED)
            private String name;

            @Schema(description = "权益内容", requiredMode = Schema.RequiredMode.REQUIRED)
            private String value;

            @Schema(description = "状态;是否拥有 false:拥有, true:没拥有", requiredMode = Schema.RequiredMode.REQUIRED)
            private Boolean disabled;
        }
    }

}