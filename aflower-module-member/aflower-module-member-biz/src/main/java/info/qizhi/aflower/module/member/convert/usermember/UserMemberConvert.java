package info.qizhi.aflower.module.member.convert.usermember;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.MemberBenefitEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitRespVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule.UpRuleRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberInfoAndTypeRespVO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;

/**
 * @Author: TY
 * @CreateTime: 2024-08-12
 * @Description: 会员转换
 * @Version: 1.0
 */
@Mapper
public interface UserMemberConvert {

    UserMemberConvert INSTANCE = Mappers.getMapper(UserMemberConvert.class);

    default AppMemberInfoAndTypeRespVO MemberTypeRespVOToConvert(List<MemberTypeDO> typeList, List<UpRuleRespVO> upRuleList, List<MemberBenefitRespVO> typeBenefitList) {
        AppMemberInfoAndTypeRespVO memberTypeResp = new AppMemberInfoAndTypeRespVO();
        // 创建会员类型代码到会员类型对象的映射
        Map<String, AppMemberInfoAndTypeRespVO.MemberType> memberTypeMap = new HashMap<>();
        for (MemberTypeDO typeDO : typeList) {
            AppMemberInfoAndTypeRespVO.MemberType memberType = new AppMemberInfoAndTypeRespVO.MemberType();
            memberType.setMtCode(typeDO.getMtCode());
            memberType.setMtName(typeDO.getMtName());
            memberType.setImageType(typeDO.getImageType());
            memberType.setBenefitNum(0);

            // 初始化会员权益信息
            List<AppMemberInfoAndTypeRespVO.MemberType.MemberBenefit> memberBenefits = new ArrayList<>();
            typeBenefitList.forEach(memberBenefit -> {
                AppMemberInfoAndTypeRespVO.MemberType.MemberBenefit bean = BeanUtils.toBean(memberBenefit, AppMemberInfoAndTypeRespVO.MemberType.MemberBenefit.class);
                bean.setDisabled(BooleanEnum.TRUE.getCode());
                memberBenefits.add(bean);
            });
            memberBenefits.add(new AppMemberInfoAndTypeRespVO.MemberType.MemberBenefit().setBenefitCode(MemberBenefitEnum.VIEW_ALL.getCode())
                    .setName(MemberBenefitEnum.VIEW_ALL.getName()).setValue("更多权益").setDisabled(BooleanEnum.FALSE.getCode()));
            memberType.setMemberBenefits(memberBenefits);

            memberTypeMap.put(typeDO.getMtCode(), memberType);
        }

        List<AppMemberInfoAndTypeRespVO.MemberType> sortedMemberTypes = new ArrayList<>();
        Map<String, List<String>> upgradeMap = new HashMap<>(); // 会员等级升级映射
        Map<String, Integer> inDegree = new HashMap<>(); // 入度表

        // 1. 初始化图和入度
        for (UpRuleRespVO rule : upRuleList) {
            String source = rule.getSourceMtCode();
            String target = rule.getTargetMtCode();

            upgradeMap.computeIfAbsent(source, k -> new ArrayList<>()).add(target);
            inDegree.put(target, inDegree.getOrDefault(target, 0) + 1);
            inDegree.putIfAbsent(source, 0); // 确保所有等级都在入度表中
        }

        // 2. 拓扑排序（支持多个链条）
        Queue<String> queue = new LinkedList<>();

        // **这里不同点**：如果入度不为 0，仍然要加入队列（保证所有会员类型都遍历）
        for (String type : memberTypeMap.keySet()) {
            if (!inDegree.containsKey(type) || inDegree.get(type) == 0) {
                queue.offer(type); // 入度为 0，直接加入
            }
        }

        Set<String> addedTypes = new HashSet<>();

        while (!queue.isEmpty()) {
            String currentType = queue.poll();
            if (!addedTypes.contains(currentType) && memberTypeMap.containsKey(currentType)) {
                sortedMemberTypes.add(memberTypeMap.get(currentType));
                addedTypes.add(currentType);
            }

            if (upgradeMap.containsKey(currentType)) {
                for (String nextType : upgradeMap.get(currentType)) {
                    inDegree.put(nextType, inDegree.get(nextType) - 1);
                    if (inDegree.get(nextType) == 0 || !addedTypes.contains(nextType)) {
                        queue.offer(nextType);
                    }
                }
            }
        }

        return memberTypeResp.setMemberTypes(sortedMemberTypes);

    }
}
