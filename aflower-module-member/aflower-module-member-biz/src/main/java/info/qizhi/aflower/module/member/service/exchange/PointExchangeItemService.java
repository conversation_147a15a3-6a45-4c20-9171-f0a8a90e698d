package info.qizhi.aflower.module.member.service.exchange;

import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeItemPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeItemSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.exchange.PointExchangeItemDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * 积分兑换项目 Service 接口
 *
 * <AUTHOR>
 */
public interface PointExchangeItemService {

    /**
     * 创建积分兑换项目
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointExchangeItem(@Valid PointExchangeItemSaveReqVO createReqVO);

    /**
     * 更新积分兑换项目
     *
     * @param updateReqVO 更新信息
     */
    void updatePointExchangeItem(@Valid PointExchangeItemSaveReqVO updateReqVO);

    /**
     * 删除积分兑换项目
     *
     * @param id 编号
     */
    void deletePointExchangeItem(Long id);

    /**
     * 获得积分兑换项目
     *
     * @param id 编号
     * @return 积分兑换项目
     */
    PointExchangeItemDO getPointExchangeItem(Long id);

    /**
     * 获得积分兑换项目分页
     *
     * @param pageReqVO 分页查询
     * @return 积分兑换项目分页
     */
    PageResult<PointExchangeItemDO> getPointExchangeItemPage(PointExchangeItemPageReqVO pageReqVO);

}