//package info.qizhi.aflower.module.member.service.activity;
//
//import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityPageReqVO;
//import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivitySaveReqVO;
//import jakarta.validation.*;
//import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityDO;
//import info.qizhi.aflower.framework.common.pojo.PageResult;
//import info.qizhi.aflower.framework.common.pojo.PageParam;
//
///**
// * 充值活动 Service 接口
// *
// * <AUTHOR>
// */
//public interface RechargeActivityService {
//
//    /**
//     * 创建充值活动
//     *
//     * @param createReqVO 创建信息
//     * @return 编号
//     */
//    Long createRechargeActivity(@Valid RechargeActivitySaveReqVO createReqVO);
//
//    /**
//     * 更新充值活动
//     *
//     * @param updateReqVO 更新信息
//     */
//    void updateRechargeActivity(@Valid RechargeActivitySaveReqVO updateReqVO);
//
//    /**
//     * 删除充值活动
//     *
//     * @param id 编号
//     */
//    void deleteRechargeActivity(Long id);
//
//    /**
//     * 获得充值活动
//     *
//     * @param id 编号
//     * @return 充值活动
//     */
//    RechargeActivityDO getRechargeActivity(Long id);
//
//    /**
//     * 获得充值活动分页
//     *
//     * @param pageReqVO 分页查询
//     * @return 充值活动分页
//     */
//    PageResult<RechargeActivityDO> getRechargeActivityPage(RechargeActivityPageReqVO pageReqVO);
//
//}

package info.qizhi.aflower.module.member.service.activity;

import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityRespVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivitySaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityUpdateStatusReqVO;
import jakarta.validation.Valid;

import java.util.List;
/**
 * 充值活动 Service 接口
 *
 * <AUTHOR>
 */
public interface RechargeActivityService {

    /**
     * 创建充值活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRechargeActivity(@Valid RechargeActivitySaveReqVO createReqVO);

    /**
     * 更新充值活动
     *
     * @param updateReqVO 更新信息
     */
    void updateRechargeActivity(@Valid RechargeActivitySaveReqVO updateReqVO);

    /**
     *  修改充值活动状态
     */
    void updateRechargeActivityStatus(@Valid RechargeActivityUpdateStatusReqVO updateReqVO);

    /**
     * 获得充值活动
     *
     * @param
     * @return 充值活动
     */
    RechargeActivityRespVO getRechargeActivity(String gcode, String activityCode);

    /**
     * 获得充值活动列表
     */
    List<RechargeActivityRespVO> getRechargeActivityList(RechargeActivityReqVO reqVO);



}