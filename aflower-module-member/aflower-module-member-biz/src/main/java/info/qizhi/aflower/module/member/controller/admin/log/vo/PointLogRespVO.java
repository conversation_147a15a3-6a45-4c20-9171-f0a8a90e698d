package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员积分日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PointLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6431")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "积分发生门店代码;不是在门店发生，该字段为空0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("积分发生门店代码;不是在门店发生，该字段为空0")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("门店名称")
    private String hname;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号")
//    @MobileDesensitize
    private String phone;

    @Schema(description = "积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("积分")
    private Integer point;

    @Schema(description = "会员级别代码", example = "赵六")
    @ExcelProperty("会员级别代码")
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("会员级别名称")
    private String mtName;

    @Schema(description = "有效期;日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("有效期;日期")
    private String indate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1：有效")
    private String state;

    @Schema(description = "积分类型;0：获得积分 1：消费积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("积分类型;0：获得积分 1：消费积分")
    private String pointType;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime bizDate;

    @Schema(description = "场景;1. 手动增减积分 2 积分换房 3 消费获取 4 升级减去积分 5 积分兑换 6 积分清零 7 积分抵现  9 商城兑换增减积分", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("场景;1. 手动增减积分 2 积分换房 3 消费获取 4 升级减去积分 5 积分兑换 6 积分清零 7 积分抵现  9 商城兑换增减积分")
    private String scene;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "订单类型;0 入住订单，1 现付账", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("订单类型;0 入住订单，1 现付账")
    private String orderType;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建人")
    private String creator;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}