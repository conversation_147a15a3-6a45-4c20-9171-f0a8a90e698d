package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 会员升级规则分页 Request VO")
@Data
@ToString(callSuper = true)
public class UpRuleSimpleReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("源会员类型代码")
    private String sourceMtCode;
    private String sourceMtName;
}
