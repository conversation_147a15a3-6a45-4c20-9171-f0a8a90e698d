package info.qizhi.aflower.module.member.controller.admin.log;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.LevelLogSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.log.LevelLogDO;
import info.qizhi.aflower.module.member.service.log.LevelLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员级别变动日志")
@RestController
@RequestMapping("/member/level-log")
@Validated
public class LevelLogController {

    @Resource
    private LevelLogService levelLogService;

    @PostMapping("/create")
    @Operation(summary = "创建会员级别变动日志")
    @PreAuthorize("@ss.hasPermission('member:level-log:create')")
    public CommonResult<Long> createLevelLog(@Valid @RequestBody LevelLogSaveReqVO createReqVO) {
        return success(levelLogService.createLevelLog(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员级别变动日志(现付冲调)")
    @PreAuthorize("@ss.hasPermission('member:update:upgrade')")
    public CommonResult<LevelLogRespVO> getLevelLog(@Valid LevelLogReqVO reqVO) {
        LevelLogDO levelLog = levelLogService.getLevelLog(reqVO);
        return success(BeanUtils.toBean(levelLog, LevelLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得会员级别变动日志分页")
    @PreAuthorize("@ss.hasPermission('member:update:upgrade')")
    public CommonResult<PageResult<LevelLogRespVO>> getLevelLogPage(@Valid LevelLogPageReqVO pageReqVO) {
        PageResult<LevelLogRespVO> pageResult = levelLogService.getLevelLogPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出会员级别变动日志 Excel")
    @PreAuthorize("@ss.hasPermission('member:level-log:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportLevelLogExcel(@Valid LevelLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<LevelLogRespVO> list = levelLogService.getLevelLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "会员级别变动日志.xls", "数据", LevelLogRespVO.class,
                        BeanUtils.toBean(list, LevelLogRespVO.class));
    }

}