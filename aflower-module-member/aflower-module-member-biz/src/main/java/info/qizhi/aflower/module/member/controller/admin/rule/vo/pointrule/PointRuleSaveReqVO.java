package info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule;

import info.qizhi.aflower.framework.common.validation.PositiveNumber;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 积分规则配置新增/修改 Request VO")
@Data
public class PointRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29794")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "是否永久有效;0：有时间期限，1：永久有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{isForEver.notnull}")
    private String isForEver;

    @Schema(description = "有效期（月）")
    private Integer indateMonth;

    @Schema(description = "1元抵扣积分数")
    @PositiveNumber
    private Integer yuanPointNum;

    @Schema(description = "房费可获积分数;每消费1元获得的积分数，如果为0，则表示不积分")
    @PositiveNumber
    private Long roomRatePoint;

    @Schema(description = "消费可获积分数;每消费1元获得的积分数，如果为0，则表示不积分")
    @PositiveNumber
    private Long consumePoint;

    @Schema(description = "生日多倍积分")
    @PositiveNumber
    private BigDecimal birthdayPointTimes;

    @Schema(description = "会员日多倍积分")
    @PositiveNumber
    private BigDecimal memberDayPointTimes;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}