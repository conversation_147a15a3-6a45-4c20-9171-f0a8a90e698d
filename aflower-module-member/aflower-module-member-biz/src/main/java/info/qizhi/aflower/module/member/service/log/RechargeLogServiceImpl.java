package info.qizhi.aflower.module.member.service.log;

import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogListReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.RechargeLogSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.log.RechargeLogDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.log.RechargeLogMapper;

import java.util.List;

/**
 * 会员储值日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RechargeLogServiceImpl implements RechargeLogService {

    @Resource
    private RechargeLogMapper rechargeLogMapper;

    @Override
    public Long createRechargeLog(RechargeLogSaveReqVO createReqVO) {
        // 插入充值记录
        RechargeLogDO rechargeLog = BeanUtils.toBean(createReqVO, RechargeLogDO.class);
        rechargeLog.setPayMethod(createReqVO.getPayMethod());
        rechargeLogMapper.insert(rechargeLog);
        // 返回充值记录ID
        return rechargeLog.getId();
    }

    @Override
    public RechargeLogDO getRechargeLogByCashBillOrderNo(RechargeLogReqVO reqVO) {
        return rechargeLogMapper.selectByCashBillOrderNo(reqVO);
    }

    @Override
    public PageResult<RechargeLogDO> getRechargeLogPage(RechargeLogPageReqVO pageReqVO) {
        return rechargeLogMapper.selectPage(pageReqVO);
    }

    /**
     * 获得会员储值日志列表
     *
     * @param reqVO 列表查询
     * @return 会员储值日志列表
     */
    @Override
    public List<RechargeLogDO> getRechargeLogList(RechargeLogListReqVO reqVO) {
        return rechargeLogMapper.selectList(reqVO);
    }

}