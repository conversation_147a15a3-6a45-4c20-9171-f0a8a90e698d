package info.qizhi.aflower.module.member.controller.admin.log.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员消费日志 Request VO")
@Data
public class ConsumeLogListReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "单号")
    private String orderNo;

    @Schema(description = "报表操作员")
    private String operator;

    @Schema(description = "会员级别")
    private String mtCode;

    @Schema(description = "姓名,手机号")
    private String keyWords;

    @Schema(description = "支付方式")
    private String payMode;

    @Schema(description = "支付方式集合")
    private List<String> payModeList;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;



}
