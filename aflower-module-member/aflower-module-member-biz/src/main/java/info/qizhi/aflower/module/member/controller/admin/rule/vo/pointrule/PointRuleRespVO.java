package info.qizhi.aflower.module.member.controller.admin.rule.vo.pointrule;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 积分规则配置 Response VO")
@Data
public class PointRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29794")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "是否永久有效;0：有时间期限，1：永久有效", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isForEver;

    @Schema(description = "有效期（月）")
    private Integer indateMonth;

    @Schema(description = "1元抵扣积分数")
    private Integer yuanPointNum;

    @Schema(description = "房费可获积分数;每消费1元获得的积分数，如果为0，则表示不积分")
    private Long roomRatePoint;

    @Schema(description = "消费可获积分数;每消费1元获得的积分数，如果为0，则表示不积分")
    private Long consumePoint;

    @Schema(description = "生日多倍积分")
    private BigDecimal birthdayPointTimes;

    @Schema(description = "会员日多倍积分")
    private BigDecimal memberDayPointTimes;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}