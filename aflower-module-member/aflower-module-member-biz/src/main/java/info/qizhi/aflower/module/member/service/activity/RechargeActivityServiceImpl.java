package info.qizhi.aflower.module.member.service.activity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.RechargeChannelEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityRespVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivitySaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.activity.vo.RechargeActivityUpdateStatusReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberAndStoreCardRespVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityDO;
import info.qizhi.aflower.module.member.dal.dataobject.activity.RechargeActivityMerchantDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.dal.mysql.activity.RechargeActivityMapper;
import info.qizhi.aflower.module.member.dal.mysql.activity.RechargeActivityMerchantMapper;
import info.qizhi.aflower.module.member.service.member.MemberService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import info.qizhi.aflower.module.pms.api.channel.ChannelApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.*;

/**
 * 充值活动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RechargeActivityServiceImpl implements RechargeActivityService {

    @Resource
    private RechargeActivityMapper rechargeActivityMapper;
    @Resource
    private RechargeActivityMerchantMapper rechargeActivityMerchantMapper;
    @Resource
    private MemberTypeService memberTypeService;
    @Resource
    private ChannelApi channelApi;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    @Lazy
    private MemberService memberService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRechargeActivity(RechargeActivitySaveReqVO createReqVO) {
        // 判断起止日期大于当前日期
        if (createReqVO.getStartDate().isBefore(LocalDate.now()) || createReqVO.getEndDate().isBefore(LocalDate.now())) {
            throw exception(RECHARGE_ACTIVITY_DATE_ERROR);
        }
        // 插入充值活动
        RechargeActivityDO rechargeActivity = insertRechargeActivity(createReqVO);
        // 插入子表
        if (CollUtil.isNotEmpty(createReqVO.getRechargeActivityMerchants())) {
            List<RechargeActivityMerchantDO> activityMerchants = CollUtil.newArrayList();
            createReqVO.getRechargeActivityMerchants().forEach(merchantCode -> {
                RechargeActivityMerchantDO arSetMerchant = RechargeActivityMerchantDO.builder().activityCode(rechargeActivity.getActivityCode()).hcode(merchantCode).gcode(createReqVO.getGcode()).build();
                activityMerchants.add(arSetMerchant);
            });
            createRechargeActivityMerchantList(rechargeActivity.getActivityCode(), activityMerchants);
        }
        // 返回活动ID
        return rechargeActivity.getId();
    }

    // 插入充值活动
    private RechargeActivityDO insertRechargeActivity(RechargeActivitySaveReqVO createReqVO) {
        //活动名称去空格
        String trim = createReqVO.getActivityName().trim();
        // 判断 活动名称唯一
        RechargeActivityDO rechargeActivityDO = rechargeActivityMapper.selectOne(RechargeActivityDO::getActivityName, trim);
        if (rechargeActivityDO != null) {
            throw exception(RECHARGE_ACTIVITY_EXISTS);
        }

        RechargeActivityDO rechargeActivity = BeanUtils.toBean(createReqVO, RechargeActivityDO.class);
        //优惠卷json 关联优惠卷表 template_code 卷代码  num 张数
        if (createReqVO.getTickets() != null) {
            List<RechargeActivityDO.TicketsParameter> tickets = createReqVO.getTickets();
            //赠送卷的类型不能相同
            boolean hasDuplicates = CollectionUtils.hasDuplicates(tickets, RechargeActivityDO.TicketsParameter::getTemplateCode);
            if (hasDuplicates) {
                throw exception(RECHARGE_ACTIVITY_TICKETS_ERROR);
            }
            // 过滤掉数量大于0且选中了的优惠卷
            tickets = CollectionUtils.filterList(tickets, ticket -> ticket.getNum() > 0 && StrUtil.isNotBlank(ticket.getTemplateCode()));
            rechargeActivity.setTickets(tickets);
        }
        // 设置活动代码并插入数据库
        rechargeActivity.setActivityCode(IdUtil.getSnowflakeNextIdStr());
        rechargeActivityMapper.insert(rechargeActivity);
        return rechargeActivity;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRechargeActivity(RechargeActivitySaveReqVO updateReqVO) {
        // 校验存在
        RechargeActivityDO rechargeActivityDO = rechargeActivityMapper.selectOneByActivityCode(updateReqVO.getGcode(), updateReqVO.getActivityCode());
        if (rechargeActivityDO == null) {
            throw exception(RECHARGE_ACTIVITY_NOT_EXISTS);
        }
        // 更新
        RechargeActivityDO updateObj = BeanUtils.toBean(updateReqVO, RechargeActivityDO.class);
        // 过滤掉赠送优惠券数量为0的券
        updateObj.setTickets(CollectionUtils.filterList(updateReqVO.getTickets(), ticket -> ticket.getNum() > 0 && StrUtil.isNotBlank(ticket.getTemplateCode())));
        updateObj.setId(rechargeActivityDO.getId());
        rechargeActivityMapper.updateById(updateObj);
        // 更新子表
        if (CollUtil.isNotEmpty(updateReqVO.getRechargeActivityMerchants())) {
            List<RechargeActivityMerchantDO> activityMerchants = CollUtil.newArrayList();
            updateReqVO.getRechargeActivityMerchants().forEach(merchantCode -> {
                RechargeActivityMerchantDO arSetMerchant = RechargeActivityMerchantDO.builder().activityCode(updateReqVO.getActivityCode()).hcode(merchantCode).gcode(updateReqVO.getGcode()).build();
                activityMerchants.add(arSetMerchant);
            });
            updateRechargeActivityMerchantList(updateReqVO.getActivityCode(), activityMerchants);
        }
    }

    @Override
    public void updateRechargeActivityStatus(RechargeActivityUpdateStatusReqVO updateReqVO) {
        RechargeActivityDO rechargeActivityDO = rechargeActivityMapper.selectOneByActivityCode(updateReqVO.getGcode(), updateReqVO.getActivityCode());
        if (rechargeActivityDO == null) {
            throw exception(RECHARGE_ACTIVITY_NOT_EXISTS);
        }
        rechargeActivityMapper.updateById(RechargeActivityDO.builder().id(rechargeActivityDO.getId()).state(updateReqVO.getState()).build());
    }


    @Override
    public RechargeActivityRespVO getRechargeActivity(String gcode, String activityCode) {
        RechargeActivityDO rechargeActivityDO = rechargeActivityMapper.selectOneByActivityCode(gcode, activityCode);
        if (rechargeActivityDO == null) {
            throw exception(RECHARGE_ACTIVITY_NOT_EXISTS);
        }
        RechargeActivityRespVO rechargeActivity = BeanUtils.toBean(rechargeActivityDO, RechargeActivityRespVO.class);
        Map<String, List<String>> activityCodeToMerchantMap = buildActivityCodeToMerchantMap(gcode);
        rechargeActivity.setRechargeActivityMerchants(activityCodeToMerchantMap.get(activityCode));
        return rechargeActivity;
    }

    @Override
    public List<RechargeActivityRespVO> getRechargeActivityList(RechargeActivityReqVO reqVO) {
        // 查询活动列表
        List<RechargeActivityDO> rechargeActivityDOList = rechargeActivityMapper.selectList(reqVO);
        if (CollUtil.isEmpty(rechargeActivityDOList)) {
            return CollUtil.newArrayList();
        }
        // 构建会员级别代码到会员级别名称的映射
        Map<String, String> mtCodeToNameMap = buildMtCodeToNameMap(reqVO.getGcode());
        // 查询活动关联门店并构建活动代码到门店列表的映射
        Map<String, List<String>> activityCodeToMerchantMap = buildActivityCodeToMerchantMap(reqVO.getGcode());
        // 查询活动关联门店并构建活动代码到门店名称列表的映射
        Map<String, List<String>> activityCodeToMerchantNamesMap = buildActivityCodeToMerchantNamesMap(reqVO.getGcode());
        // 构建最终结果列表
        List<RechargeActivityRespVO> activityList = buildRechargeActivityRespVOList(rechargeActivityDOList, mtCodeToNameMap, activityCodeToMerchantNamesMap, activityCodeToMerchantMap);
        //如果会员级别代码不为空则对结果进行过滤只返回mts集合中带有会员级别代码的活动
        if (StrUtil.isNotBlank(reqVO.getMtCode())) {
            if (StrUtil.isNotBlank(reqVO.getMcode())) {
                MemberAndStoreCardRespVO member = memberService.getMemberAndStoreCard(new MemberReqVO().setGcode(reqVO.getGcode()).setMcode(reqVO.getMcode()).setHcode(reqVO.getHcode()));
                if (!Objects.equals(reqVO.getMtCode(), member.getMtCode())) {
                    throw exception(MEMBER_TPYE_ERROR);
                }
            }
            activityList = CollectionUtils.filterList(activityList, vo -> vo.getMts().contains(reqVO.getMtCode()));
        }
        // 如果门店代码不为空则对结果进行过滤只返回活动适用门店中带有该门店代码的活动
        if (StrUtil.isNotBlank(reqVO.getHcode())) {
            activityList = CollectionUtils.filterList(activityList, vo -> vo.getRechargeActivityMerchants().contains(reqVO.getHcode()));
        }
        // 如果渠道代码不为空则对结果进行过滤只返回channel集合中带有渠道代码的活动
        if (StrUtil.isNotBlank(reqVO.getChannel())) {
            activityList = CollectionUtils.filterList(activityList, vo -> vo.getChannels().contains(reqVO.getChannel()));
        }

        return activityList;
    }

    // 查询并构建会员级别代码到会员级别名称的映射
    private Map<String, String> buildMtCodeToNameMap(String gcode) {
        return memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(gcode))
                                .stream()
                                .collect(Collectors.toMap(MemberTypeDO::getMtCode, MemberTypeDO::getMtName));
    }

    // 构建门店代码到门店名称的映射
    private Map<String, String> buildHcodeToMerchantNameMap(List<MerchantRespDTO> merchantList) {
        return merchantList.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
    }

    // 查询活动关联门店并构建活动代码到门店列表的映射
    private Map<String, List<String>> buildActivityCodeToMerchantMap(String gcode) {
        List<RechargeActivityMerchantDO> rechargeActivityMerchant = rechargeActivityMerchantMapper.selectList(RechargeActivityMerchantDO::getGcode, gcode);
        return rechargeActivityMerchant
                .stream()
                .collect(Collectors.groupingBy(RechargeActivityMerchantDO::getActivityCode, Collectors.mapping(RechargeActivityMerchantDO::getHcode, Collectors.toList())));
    }

    // 查询活动关联门店并构建活动代码到门店名称列表的映射
    private Map<String, List<String>> buildActivityCodeToMerchantNamesMap(String gcode) {
        List<RechargeActivityMerchantDO> activityMerchants = rechargeActivityMerchantMapper.selectList(RechargeActivityMerchantDO::getGcode, gcode);

        Map<String, List<RechargeActivityMerchantDO>> activityCodeToMerchantsMap = activityMerchants
                .stream()
                .collect(Collectors.groupingBy(RechargeActivityMerchantDO::getActivityCode));

        Map<String, String> hcodeToMerchantNameMap = buildHcodeToMerchantNameMap(merchantApi.getMerchantList(gcode).getData());

        return activityCodeToMerchantsMap.entrySet()
                                         .stream()
                                         .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue()
                                                                                                    .stream()
                                                                                                    .map(RechargeActivityMerchantDO::getHcode)
                                                                                                    .map(hcodeToMerchantNameMap::get)
                                                                                                    .collect(Collectors.toList())));
    }

    // 构建最终结果列表
    private List<RechargeActivityRespVO> buildRechargeActivityRespVOList(
            List<RechargeActivityDO> rechargeActivityDOList
            , Map<String, String> mtCodeToNameMap
            , Map<String, List<String>> activityCodeToMerchantNamesMap
            , Map<String, List<String>> activityCodeToMerchantMap
    ) {
        return rechargeActivityDOList.stream().map(rechargeActivityDO -> {
            RechargeActivityRespVO rechargeActivityRespVO = BeanUtils.toBean(rechargeActivityDO, RechargeActivityRespVO.class);

            // 设置渠道名称列表
            List<String> channelNames = new ArrayList<>();
            List<String> channels = rechargeActivityRespVO.getChannels();
            channels.forEach(channelCode -> {
                channelNames.add(RechargeChannelEnum.getNameByCode(channelCode));
            });
            rechargeActivityRespVO.setChannelName(channelNames);

            // 设置会员级别名称列表
            List<String> mts = rechargeActivityRespVO.getMts();
            List<String> memberNames = mts.stream().map(mtCodeToNameMap::get).collect(Collectors.toList());
            rechargeActivityRespVO.setMtsName(memberNames);

            // 设置门店列表
            List<String> merchants = activityCodeToMerchantMap.get(rechargeActivityDO.getActivityCode());
            rechargeActivityRespVO.setRechargeActivityMerchants(merchants);
            // 设置门店名称列表
            List<String> merchantNames = activityCodeToMerchantNamesMap.getOrDefault(rechargeActivityDO.getActivityCode(), CollUtil.newArrayList());
            rechargeActivityRespVO.setMerchantName(merchantNames);


            return rechargeActivityRespVO;
        }).collect(Collectors.toList());

    }


    // ==================== 子表（充值活动参与门店） ====================


    private void createRechargeActivityMerchantList(String activityCode, List<RechargeActivityMerchantDO> list) {
        list.forEach(o -> o.setActivityCode(activityCode));
        rechargeActivityMerchantMapper.insertBatch(list);
    }

    private void updateRechargeActivityMerchantList(String activityCode, List<RechargeActivityMerchantDO> list) {
        deleteRechargeActivityMerchantByActivityCode(activityCode);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createRechargeActivityMerchantList(activityCode, list);
    }

    private void deleteRechargeActivityMerchantByActivityCode(String activityCode) {
        rechargeActivityMerchantMapper.deleteByActivityCode(activityCode);
    }

}