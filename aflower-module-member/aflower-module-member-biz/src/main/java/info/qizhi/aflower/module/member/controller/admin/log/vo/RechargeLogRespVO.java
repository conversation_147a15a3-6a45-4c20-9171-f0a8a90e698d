package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员储值日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RechargeLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13176")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生门店代码;不是在门店充值，该字段为空0")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("门店名称")
    private String hname;

    @Schema(description = "每次充值的代码;系统生成唯一", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("每次充值的代码;系统生成唯一")
    private String rechargeCode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码")
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("会员级别名称")
    private String mtName;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("姓名")
    // @NameDesensitize
    private String name;

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("手机号")
//    @MobileDesensitize
    private String phone;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式")
    private String payMethod;

    @Schema(description = "授权码;微信 支付宝 付款时的授权码")
    @ExcelProperty("授权码;微信 支付宝 付款时的授权码")
    private String qrcode;

    @Schema(description = "充值渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值渠道")
    private String rechargeChannel;

    @Schema(description = "储值卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("储值卡号")
    private String storeCardNo;

    @Schema(description = "充值赠送金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值赠送金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long giveMoney;

    @Schema(description = "充值活动代码;0表示没有参与活动", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值活动代码;0表示没有参与活动")
    private String activityCode;

    @Schema(description = "操作员")
    @ExcelProperty("操作员")
    private String operator;

    @Schema(description = "营业日")
    @ExcelProperty("营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}