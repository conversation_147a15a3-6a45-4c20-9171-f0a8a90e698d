package info.qizhi.aflower.module.member.controller.admin.type.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员类型简单类 Response VO")
@Data
public class MemberTypeSimpleRespVO {

    @Schema(description = "会员类型代码")
    private String mtCode;

    @Schema(description = "会员类型名称", example = "金牌会员")
    private String mtName;

    @Schema(description = "会员级别", example = "1")
    private String level;

}