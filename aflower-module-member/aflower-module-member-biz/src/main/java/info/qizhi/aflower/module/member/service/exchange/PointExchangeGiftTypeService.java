package info.qizhi.aflower.module.member.service.exchange;

import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeGiftTypePageReqVO;
import info.qizhi.aflower.module.member.controller.admin.exchange.vo.PointExchangeGiftTypeSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.member.dal.dataobject.exchange.PointExchangeGiftTypeDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * 积分兑换-礼品分类 Service 接口
 *
 * <AUTHOR>
 */
public interface PointExchangeGiftTypeService {

    /**
     * 创建积分兑换-礼品分类
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createPointExchangeGiftType(@Valid PointExchangeGiftTypeSaveReqVO createReqVO);

    /**
     * 更新积分兑换-礼品分类
     *
     * @param updateReqVO 更新信息
     */
    void updatePointExchangeGiftType(@Valid PointExchangeGiftTypeSaveReqVO updateReqVO);

    /**
     * 删除积分兑换-礼品分类
     *
     * @param id 编号
     */
    void deletePointExchangeGiftType(Long id);

    /**
     * 获得积分兑换-礼品分类
     *
     * @param id 编号
     * @return 积分兑换-礼品分类
     */
    PointExchangeGiftTypeDO getPointExchangeGiftType(Long id);

    /**
     * 获得积分兑换-礼品分类分页
     *
     * @param pageReqVO 分页查询
     * @return 积分兑换-礼品分类分页
     */
    PageResult<PointExchangeGiftTypeDO> getPointExchangeGiftTypePage(PointExchangeGiftTypePageReqVO pageReqVO);

}