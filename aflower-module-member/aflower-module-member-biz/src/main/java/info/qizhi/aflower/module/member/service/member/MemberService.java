package info.qizhi.aflower.module.member.service.member;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.member.controller.admin.log.vo.PointLogSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.*;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 会员信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MemberService {

    /**
     * 注册会员
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long register(@Valid MemberSaveReqVO createReqVO);

    /**
     * 查询会员，没有则注册会员
     * @param gcode
     * @param phone
     * @return
     */
    MemberRespVO createUserIfAbsent(String gcode, String phone, String unionId);

    /**
     * 更新会员信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMember(@Valid MemberStarSaveReqVO updateReqVO);

    /**
     * 更新会员密码
     * @param mcode
     * @param pwd
     */
    void updateMemberPwd(String mcode, String pwd);

    /**
     *
     * @param unionId
     * @param id
     */
    void updateAppMember(String unionId, Long id);

    /**
     * 更新会员信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMember2(@Valid MemberUpdateReqVO updateReqVO);

    /**
     * 会员升级<br>
     * 条件：<br>
     * 1. 判断会员是否存在且状态为正常<br>
     * 2. 判断目标级别要大于当前级别<br>
     * 3. 判断升级条件是否存在<br>
     * 流程：<br>
     * 1. 修改会员信息<br>
     * 2. 创建会员升级现付订单<br>
     * 3. 创建升级日志<br>
     *
     * @param updateReqVO 入参
     */
    void upgrade(@Valid MemberUpgradeReqVO updateReqVO);

    /**
     * 获得会员信息，不包括储值卡
     *
     * @param reqVO 入参
     * @return 会员信息
     */
    MemberRespVO getMember(MemberReqVO reqVO);

    /**
     * 获得用户信息根据id
     * @param id
     * @return
     */
    MemberRespVO getMemberById(Long id);

    /**
     * 获得会员信息和储值卡
     *
     * @return 会员信息和储值卡
     */
    MemberAndStoreCardRespVO getMemberAndStoreCard(MemberReqVO reqVO);

    /**
     * 获得会员信息未脱敏信息
     * @param gcode
     * @param mcode
     * @return
     */
    MemberAndStoreCardNoDesRespVO getMemberNoDes(String gcode, String mcode);

    /**
     * 获得会员信息和储值卡(当前门店如果是直营，只返回集团卡。如果是加盟，返回集团卡和门店卡)
     * @param reqVO 入参
     * @return 会员信息和储值卡
     */
    MemberAndStoreCardSimpleRespVO getMemberAndStoreCard(MemberCardReqVO reqVO);

    /**
     * 获得会员信息分页
     *
     * @param pageReqVO 分页查询
     * @return 会员信息分页
     */
    PageResult<MemberAndStoreCardRespVO> getMemberPage(MemberPageReqVO pageReqVO);

    /**
     * 获得会员信息列表
     *
     * @return 会员信息列表
     */
    List<MemberAndStoreCardRespVO> getMemberList(MemberListReqVO reqVO);

    /**
     * 充值<br>
     * 1. 直营店:会员充值直接充值到集团卡<br>
     * 2. 加盟店:会员充值，需要先判断是否有加盟店的储值卡，没有则需要先创建加盟店储值卡再充值到加盟店储值卡上<br>
     * 3. 充值需要根据充值活动，判断是否需要赠送积分、券等<br>
     * 4. 记录充值日志<br>
     *
     * @param rechargeReqVO 入参
     */
    void recharge(RechargeSaveReqVO rechargeReqVO);

    /**
     * 消费
     *
     * @param consumeReqVO 入参
     */
    void consume(ConsumeSaveReqVO consumeReqVO);

    /**
     * 积分
     */
    void createPoint(PointLogSaveReqVO createReqVO);

    /**
     * 批量创建积分
     */
    void createPointBatch(List<PointLogSaveReqVO> createReqVOs);

    /**
     * 修改会员状态
     *
     * @param updateReqVO 入参
     */
    void updateMemberStatus(@Valid MemberUpdateStatusReqVO updateReqVO);

    /**
     * 删除会员信息
     * @param gcode 集团代码
     * @param mcode 会员代码
     */
    void deleteMember(String gcode, String mcode);

    /**
     * 会员密码校验
     * @param gcode 集团代码
     * @param mcode 会员代码
     */
    void verifyMemberPassword(String gcode, String mcode, String password);

}