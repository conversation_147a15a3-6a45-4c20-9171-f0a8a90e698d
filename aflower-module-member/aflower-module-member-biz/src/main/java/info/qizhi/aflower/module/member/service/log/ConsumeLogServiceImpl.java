package info.qizhi.aflower.module.member.service.log;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.PayAccountEnum;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogListReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogPageReqVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogRespVO;
import info.qizhi.aflower.module.member.controller.admin.log.vo.ConsumeLogSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.member.dal.dataobject.log.ConsumeLogDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.member.dal.mysql.log.ConsumeLogMapper;

import java.util.List;

/**
 * 会员消费日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ConsumeLogServiceImpl implements ConsumeLogService {

    @Resource
    private ConsumeLogMapper consumeLogMapper;

    @Override
    public Long createConsumeLog(ConsumeLogSaveReqVO createReqVO) {
        // 插入消费记录
        ConsumeLogDO consumeLog = BeanUtils.toBean(createReqVO, ConsumeLogDO.class);
        consumeLog.setConsumeCode(IdUtil.getSnowflakeNextIdStr());
        consumeLogMapper.insert(consumeLog);
        // 返回消费记录ID
        return consumeLog.getId();
    }

    @Override
    public ConsumeLogDO getConsumeLog(String mcode, String hcode, String gcode) {
        return consumeLogMapper.selectOne(ConsumeLogDO::getMcode, mcode,ConsumeLogDO::getHcode, hcode,ConsumeLogDO::getGcode, gcode);
    }

    @Override
    public PageResult<ConsumeLogRespVO> getConsumeLogPage(ConsumeLogPageReqVO pageReqVO) {
        PageResult<ConsumeLogDO> result = consumeLogMapper.selectPage(pageReqVO);
        PageResult<ConsumeLogRespVO> bean = BeanUtils.toBean(result, ConsumeLogRespVO.class);
        bean.getList().forEach(consumeLogRespVO -> consumeLogRespVO.setPayMethodName(PayAccountEnum.getLabelByCode(consumeLogRespVO.getPayMethod())));
        return bean;
    }

    /**
     * 获取会员消费日志列表
     *
     */
    @Override
    public List<ConsumeLogDO> getConsumeLogList(ConsumeLogListReqVO reqVO) {
        return consumeLogMapper.selectList(reqVO);
    }

}