package info.qizhi.aflower.module.member.service.benefit;

import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitImageSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.BenefitImageDO;
import jakarta.validation.Valid;

import java.util.List;

public interface BenefitImageService {

    /**
     *批量创建
     * @param createReqVOs
     */
    void createBtach(@Valid List<MemberBenefitImageSaveReqVO> createReqVOs);

    /**
     * 获得集团下权益图标
     */
    List<BenefitImageDO> getList(String gcode);
}
