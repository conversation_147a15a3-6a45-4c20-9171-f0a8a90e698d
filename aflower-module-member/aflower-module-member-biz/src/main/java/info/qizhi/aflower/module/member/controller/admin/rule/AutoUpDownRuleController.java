package info.qizhi.aflower.module.member.controller.admin.rule;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleReqVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleRespVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule.AutoUpDownRuleSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.rule.AutoUpDownRuleDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.service.rule.AutoUpDownRuleService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 会员类型自动升降级规则")
@RestController
@RequestMapping("/member/auto-up-down-rule")
@Validated
public class AutoUpDownRuleController {

    @Resource
    private AutoUpDownRuleService autoUpDownRuleService;

    @Resource
    private MemberTypeService memberTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建会员类型自动升降级规则")
    @PreAuthorize("@ss.hasPermission('member:auto-up-down-rule:create')")
    public CommonResult<Long> createAutoUpDownRule(@Valid @RequestBody AutoUpDownRuleSaveReqVO createReqVO) {
        return success(autoUpDownRuleService.createAutoUpDownRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新会员类型自动升降级规则")
    @PreAuthorize("@ss.hasPermission('member:auto-up-down-rule:create')")
    public CommonResult<Boolean> updateAutoUpDownRule(@Valid @RequestBody AutoUpDownRuleSaveReqVO updateReqVO) {
        autoUpDownRuleService.updateAutoUpDownRule(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得会员类型自动升降级规则")
    @Parameter(name = "ruleCode", description = "规则代码", required = true, example = "1")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('member:auto-up-down-rule:query')")
    public CommonResult<AutoUpDownRuleRespVO> getAutoUpDownRule(@RequestParam("gcode") String gcode, @RequestParam("ruleCode") String ruleCode) {
        AutoUpDownRuleDO autoUpDownRule = autoUpDownRuleService.getAutoUpDownRule(ruleCode,gcode);
        return success(BeanUtils.toBean(autoUpDownRule, AutoUpDownRuleRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得会员类型自动升降级规则列表")
    //@PreAuthorize("@ss.hasPermission('member:auto-up-down-rule:query:list')")
    public CommonResult<List<AutoUpDownRuleRespVO>> getAutoUpDownRuleList(@Valid AutoUpDownRuleReqVO reqVO) {
        List<AutoUpDownRuleDO> listResult = autoUpDownRuleService.getAutoUpDownRuleList(reqVO);
        List<AutoUpDownRuleRespVO> respVOList = BeanUtils.toBean(listResult, AutoUpDownRuleRespVO.class);
        buildAutoUpDownRuleDO(reqVO.getGcode(), respVOList);
        return success(respVOList);
    }

    private void buildAutoUpDownRuleDO(String gcode, List<AutoUpDownRuleRespVO> respVOList) {
        if (CollUtil.isEmpty(respVOList)) {
            return;
        }
        List<MemberTypeDO> memberTypeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(gcode));
        if (CollUtil.isEmpty(memberTypeList)) {
            return;
        }
        Map<String, MemberTypeDO> memberTypeMap = CollectionUtils.convertMap(memberTypeList, MemberTypeDO::getMtCode);
        respVOList.forEach(respVO -> {
            if (memberTypeMap.containsKey(respVO.getSourceMtCode())) {
                respVO.setSourceMtName(memberTypeMap.get(respVO.getSourceMtCode()).getMtName())
                      .setIsForEver(memberTypeMap.get(respVO.getSourceMtCode()).getIsForEver())
                        .setIndateMonth(memberTypeMap.get(respVO.getSourceMtCode()).getIndateMonth())
                        .setTargetMtName(memberTypeMap.get(respVO.getTargetMtCode()).getMtName());
                if (StrUtil.isNotBlank(respVO.getAutoDownMtCode()) && NumberEnum.ONE.getNumber().equals(respVO.getIsAutoDown())){
                    respVO.setAutoDownMtName(memberTypeMap.get(respVO.getAutoDownMtCode()).getMtName());
                }
            }
        });
    }
}