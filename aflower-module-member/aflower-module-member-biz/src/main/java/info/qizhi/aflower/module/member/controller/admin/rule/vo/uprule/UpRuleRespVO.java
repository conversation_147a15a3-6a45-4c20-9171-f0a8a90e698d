package info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员升级规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UpRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7601")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "规则代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则代码")
    private String ruleCode;

    @Schema(description = "源会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("源会员类型代码")
    private String sourceMtCode;
    private String sourceMtName;

    @Schema(description = "目标会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标会员类型代码")
    private String targetMtCode;
    private String targetMtName;

    @Schema(description = "升级方式(积分兑换、现金支付、挂房账)")
    @ExcelProperty("升级方式")
    private String upMode;

    @Schema(description = "相应额度", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private String amount;

    @Schema(description = "升级渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}