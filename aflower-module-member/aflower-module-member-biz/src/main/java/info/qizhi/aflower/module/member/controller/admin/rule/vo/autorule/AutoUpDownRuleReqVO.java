package info.qizhi.aflower.module.member.controller.admin.rule.vo.autorule;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员类型自动升降级规则 Request VO")
@Data
@ToString(callSuper = true)
public class AutoUpDownRuleReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "规则代码")
    private String ruleCode;

    @Schema(description = "源会员类型代码")
    private String sourceMtCode;

}