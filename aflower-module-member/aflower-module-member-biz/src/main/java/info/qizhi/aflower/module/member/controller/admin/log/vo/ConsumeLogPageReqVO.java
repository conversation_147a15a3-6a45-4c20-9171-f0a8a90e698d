package info.qizhi.aflower.module.member.controller.admin.log.vo;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员消费日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConsumeLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "发生门店代码;不是在门店充值，该字段为空0")
    private String hcode;

    @Schema(description = "门店名称", example = "王五")
    private String hname;

    @Schema(description = "每次消费代码")
    private String consumeCode;

    @Schema(description = "会员代码")
    private String mcode;

    @Schema(description = "会员级别名称", example = "张三")
    private String mtName;

    @Schema(description = "姓名", example = "王五")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "金额")
    private Long fee;

    @Schema(description = "场景")
    private String scene;

    @Schema(description = "支付方式")
    private String payMode;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate beginBizDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endBizDate;

    @Schema(description = "消费类型")
    private String consumeType;


}