package info.qizhi.aflower.module.member.controller.admin.log.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员级别变动日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class LevelLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15230")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "发生门店代码;0表示没有发生在门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发生门店代码;0表示没有发生在门店")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员代码")
    private String mcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("姓名")
    private String name;

    @Schema(description = "源级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("源级别代码")
    private String sourceMtcode;

    @Schema(description = "源级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @ExcelProperty("源级别名称")
    private String sourceMtname;

    @Schema(description = "目标级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标级别代码")
    private String targetMtcode;

    @Schema(description = "目标级别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("目标级别名称")
    private String targetMtname;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime bizDate;

    @Schema(description = "变更类型;0:升级 1:降级 2:保级", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("变更类型;0:升级 1:降级 2:保级")
    private String changeType;

    @Schema(description = "支付类型;0: 现付 1：挂房账", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("支付类型;0: 现付 1：挂房账")
    private String payType;

    @Schema(description = "支付金额")
    @ExcelProperty("支付金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "付款方式")
    @ExcelProperty("付款方式")
    private String payMode;

    @Schema(description = "描述", example = "你说的对")
    @ExcelProperty("描述")
    private String remark;

    @Schema(description = "操作人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("操作人")
    private String creator;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店名称")
    private String hname;

    @Schema(description = "会员卡号（手机号）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员卡号")
    private String mcard;

}