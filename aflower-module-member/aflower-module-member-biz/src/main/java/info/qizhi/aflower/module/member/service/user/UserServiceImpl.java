package info.qizhi.aflower.module.member.service.user;

import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.web.core.util.WebFrameworkUtils;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitReqVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitRespVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberTypeBenefitReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberAndStoreCardRespVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberRespVO;
import info.qizhi.aflower.module.member.controller.admin.rule.vo.uprule.UpRuleRespVO;
import info.qizhi.aflower.module.member.controller.admin.type.vo.MemberTypeReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberInfoAndTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberPwdReqVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.AppMemberTypeRespVO;
import info.qizhi.aflower.module.member.controller.app.user.vo.UserStoreCardRespVO;
import info.qizhi.aflower.module.member.convert.usermember.UserMemberConvert;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.BenefitImageDO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.MemberTypeBenefitDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.member.service.benefit.BenefitImageService;
import info.qizhi.aflower.module.member.service.benefit.MemberBenefitService;
import info.qizhi.aflower.module.member.service.benefit.MemberTypeBenefitService;
import info.qizhi.aflower.module.member.service.member.MemberService;
import info.qizhi.aflower.module.member.service.rule.UpRuleService;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.MEMBER_NOT_EXISTS;

/**
 * 会员信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserServiceImpl implements UserService {

    @Resource
    private MemberTypeBenefitService memberTypeBenefitService;
    @Resource
    private MemberService memberService;
    @Resource
    private MemberBenefitService memberBenefitService;
    @Resource
    private UpRuleService upRuleService;
    @Resource
    private MemberTypeService memberTypeService;
    @Resource
    private BenefitImageService benefitImageService;

    @Override
    public UserStoreCardRespVO getMemberAndStoreCard(MemberReqVO reqVO) {
        MemberAndStoreCardRespVO member = memberService.getMemberAndStoreCard(reqVO);
        return BeanUtils.toBean(member, UserStoreCardRespVO.class);
    }

    @Override
    public AppMemberInfoAndTypeRespVO getMemberTypeList(String gcode) {
        MemberRespVO member = memberService.getMember(new MemberReqVO().setGcode(gcode).setPhone(WebFrameworkUtils.getUsername()));
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        // 获得系统权益列表
        List<MemberBenefitRespVO> typeBenefitList = memberBenefitService.getTypeBenefitList(new MemberBenefitReqVO().setGcode(gcode));
        // 获得权益图标
        List<BenefitImageDO> benefitImageList = benefitImageService.getList(gcode);
        Map<String, List<BenefitImageDO>> benefitImageListMap = CollectionUtils.convertMultiMap(benefitImageList, BenefitImageDO::getImageType);
        // 获得会员等级的权益列表
        List<MemberTypeBenefitDO> memberTypeBenefitRespVOS = memberTypeBenefitService.getTypeBenefitList(new MemberTypeBenefitReqVO().setGcode(gcode));
        Map<String, List<MemberTypeBenefitDO>> memberTypeBenefitListMap = CollectionUtils.convertMultiMap(memberTypeBenefitRespVOS, MemberTypeBenefitDO::getMtCode);
        // 获得会员升级列表
        List<UpRuleRespVO> upRuleList = upRuleService.getUpRuleList(gcode);
        // 获得会员类型列表
        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(gcode).setIsEnable(NumberEnum.ONE.getNumber()));
        // 初始化会员类型及权益
        AppMemberInfoAndTypeRespVO appMemberInfoAndTypeRespVO = UserMemberConvert.INSTANCE.MemberTypeRespVOToConvert(typeList, upRuleList, typeBenefitList);
        buildMemberInfo(appMemberInfoAndTypeRespVO, member, memberTypeBenefitListMap, benefitImageListMap);
        return appMemberInfoAndTypeRespVO;
    }

    @Override
    public AppMemberTypeRespVO getMemberType(String gcode) {
        MemberRespVO member = validateMember(gcode);
        AppMemberTypeRespVO memberTypeRespVO = new AppMemberTypeRespVO();

        List<MemberTypeDO> typeList = memberTypeService.getTypeList(new MemberTypeReqVO().setGcode(gcode));
        Map<String, MemberTypeDO> memberTypeDOMap = CollectionUtils.convertMap(typeList, MemberTypeDO::getMtCode);
        String mtCode = "";
        String mtName = "";
        if(ObjectUtil.isEmpty(member)){
            MemberTypeDO memberType = CollectionUtils.findFirst(typeList, memberTypeDO -> MemberTypeEnum.ORDINARY_MEMBER.getLabel().equals(memberTypeDO.getMtName()));
            mtCode = memberType.getMtCode();
            mtName = memberType.getMtName();
        }else {
            mtCode = member.getMtCode();
            mtName = memberTypeDOMap.getOrDefault(mtCode, new MemberTypeDO()).getMtName();
        }
        // 获得系统权益列表
        List<MemberBenefitRespVO> typeBenefitList = memberBenefitService.getTypeBenefitList(new MemberBenefitReqVO().setGcode(gcode).setIsSys(NumberEnum.ONE.getNumber()));
        Map<String, MemberBenefitRespVO> memberBenefitMap = CollectionUtils.convertMap(typeBenefitList, MemberBenefitRespVO::getBenefitCode);
        // 获得会员等级的权益列表
        List<MemberTypeBenefitDO> memberTypeBenefitRespVOS = memberTypeBenefitService.getTypeBenefitList(new MemberTypeBenefitReqVO()
                .setGcode(gcode).setMtCodes(List.of(mtCode)));
        Map<String, MemberTypeBenefitDO> memberTypeBenefitDOMap = CollectionUtils.convertMap(memberTypeBenefitRespVOS, MemberTypeBenefitDO::getBenefitCode);

       /* // 获得权益图标
        List<BenefitImageDO> benefitImageList = benefitImageService.getList(gcode);
        CollectionUtils.convertMap(benefitImageList)*/

        List<AppMemberTypeRespVO.MemberBenefit> list = new ArrayList<>();
        AppMemberTypeRespVO.MemberBenefit benefit = new AppMemberTypeRespVO.MemberBenefit();
        benefit.setBenefitCode(MemberBenefitEnum.BOOKING_DISCOUNT.getCode());
        benefit.setName(MemberBenefitEnum.BOOKING_DISCOUNT.getName());
        benefit.setImage(MemberBenefitImageEnum.BOOKING_DISCOUNT.getImage());
        benefit.setValue(memberTypeBenefitDOMap.getOrDefault(MemberBenefitEnum.BOOKING_DISCOUNT.getCode(), new MemberTypeBenefitDO()).getValue());
        list.add(benefit);

        memberTypeRespVO.setMemberBenefits(list);
        memberTypeRespVO.setMtCode(mtCode);
        memberTypeRespVO.setMtName(mtName);
        return memberTypeRespVO;
    }

    @NotNull
    private MemberRespVO validateMember(String gcode) {
        // 小程序用户名存储的手机号
        String phone = WebFrameworkUtils.getUsername();
        MemberRespVO member = memberService.getMember(new MemberReqVO().setGcode(gcode).setPhone(phone));

        return member;
    }

    @Override
    public void updatePwd(AppMemberPwdReqVO reqVO) {
        memberService.updateMemberPwd(reqVO.getMcode(), reqVO.getPwd());
    }

    private void buildMemberInfo(AppMemberInfoAndTypeRespVO appMemberInfoAndTypeRespVO, MemberRespVO member,
                                 Map<String, List<MemberTypeBenefitDO>> memberTypeBenefitListMap, Map<String, List<BenefitImageDO>> benefitImageListMap) {
        appMemberInfoAndTypeRespVO.setMcode(member.getMcode());
        appMemberInfoAndTypeRespVO.setMtCode(member.getMtCode());
        appMemberInfoAndTypeRespVO.setName(member.getName());

        // 升级享受的订房折扣
        String discount = "";

        List<AppMemberInfoAndTypeRespVO.MemberType> memberTypes = appMemberInfoAndTypeRespVO.getMemberTypes();
        for (int i = memberTypes.size() - 1; i >= 0; i--) {
            AppMemberInfoAndTypeRespVO.MemberType memberType = memberTypes.get(i);

            // 获得图标类型
            List<BenefitImageDO> typeBenefitImageList = benefitImageListMap.getOrDefault(memberType.getImageType(), new ArrayList<>());
            Map<String, BenefitImageDO> benefitImageDOMap = CollectionUtils.convertMap(typeBenefitImageList, BenefitImageDO::getBenefitCode);

            List<MemberTypeBenefitDO> memberTypeBenefitRespVOS = memberTypeBenefitListMap.get(memberType.getMtCode());


            int num = 0;
            for (AppMemberInfoAndTypeRespVO.MemberType.MemberBenefit memberBenefit : memberType.getMemberBenefits()) {

                if (ObjectUtil.isNotEmpty(memberTypeBenefitRespVOS)) {
                    Map<String, MemberTypeBenefitDO> typeBenefitDOMap = CollectionUtils.convertMap(memberTypeBenefitRespVOS, MemberTypeBenefitDO::getBenefitCode);
                    MemberTypeBenefitDO memberTypeBenefitDO = typeBenefitDOMap.get(memberBenefit.getBenefitCode());
                    if (memberTypeBenefitDO != null) {
                        memberBenefit.setValue(memberTypeBenefitDO.getValue());
                        memberBenefit.setDisabled(BooleanEnum.FALSE.getCode());
                        num++;

                        if (MemberBenefitEnum.BOOKING_DISCOUNT.getCode().equals(memberTypeBenefitDO.getBenefitCode())) {
                            memberType.setDiscount(discount);
                            discount = memberTypeBenefitDO.getValue();
                        }

                    }
                }

                memberBenefit.setImage(benefitImageDOMap.getOrDefault(memberBenefit.getBenefitCode(), new BenefitImageDO()).getImage());
            }

            memberType.setBenefitNum(num);


        }
    }
}