package info.qizhi.aflower.module.member.convert.member;

import info.qizhi.aflower.framework.common.enums.ConsumeSceneEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.member.controller.admin.log.vo.*;
import info.qizhi.aflower.module.member.controller.admin.member.vo.ConsumeSaveReqVO;
import info.qizhi.aflower.module.member.controller.admin.member.vo.MemberUpgradeReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.member.MemberDO;
import info.qizhi.aflower.module.member.dal.dataobject.type.MemberTypeDO;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 消费日志 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ConsumeLogConvert {
    ConsumeLogConvert INSTANCE = Mappers.getMapper(ConsumeLogConvert.class);

    /**
     * 更新会员集团总消费
     */
    default ConsumeLogSaveReqVO consumeLogMemberUpgradeConvert(MemberUpgradeReqVO reqVO) {
        ConsumeLogSaveReqVO consumeLogSaveReqVO = new ConsumeLogSaveReqVO();
        consumeLogSaveReqVO.setGcode(reqVO.getGcode());
        consumeLogSaveReqVO.setHcode(reqVO.getHcode());
        consumeLogSaveReqVO.setHname(reqVO.getHname());
        consumeLogSaveReqVO.setMcode(reqVO.getMcode());
        consumeLogSaveReqVO.setMtCode(reqVO.getSourceMtcode());
        consumeLogSaveReqVO.setFee(reqVO.getBuyFee());
        consumeLogSaveReqVO.setScene(ConsumeSceneEnum.MEMBER_UP.getCode());
        consumeLogSaveReqVO.setPayMethod(reqVO.getPayMethod());
        consumeLogSaveReqVO.setConsumeType(NumberEnum.ONE.getNumber());
        return consumeLogSaveReqVO;
    }

    default ConsumeLogSaveReqVO consumeLogConvert(ConsumeSaveReqVO reqVO, MemberDO member, MerchantRespDTO merchant, MemberTypeDO memberType) {
        return new ConsumeLogSaveReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(merchant.getHcode())
                .setHname(merchant.getHname())
                .setMcode(member.getMcode())
                .setMtCode(member.getMtCode())
                .setMtName(memberType.getMtName())
                .setName(member.getName())
                .setPhone(member.getPhone())
                .setOrderNo(reqVO.getOrderNo())
                .setOrderType(reqVO.getOrderType())
                .setFee(reqVO.getFee())
                .setScene(reqVO.getScene())
                .setStoreCardNo(reqVO.getStoreCardNo())
                .setPayMethod(reqVO.getPayMethod())
                .setConsumeType(reqVO.getConsumeType());
    }

}
