package info.qizhi.aflower.module.member.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Response VO")
@Data
public class AppMemberTypeRespVO {

    @Schema(description = "会员类型(会员级别);0:散客 ，其他为会员类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员类型(会员级别)名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtName;

    @Schema(description = "会员权益信息")
    private List<MemberBenefit> memberBenefits;

    @Data
    public static class MemberBenefit {
        @Schema(description = "图标", requiredMode = Schema.RequiredMode.REQUIRED)
        private String image;

        @Schema(description = "权益代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String benefitCode ;

        @Schema(description = "权益名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String name;

        @Schema(description = "权益内容", requiredMode = Schema.RequiredMode.REQUIRED)
        private String value;

        @Schema(description = "状态;是否拥有 false:拥有, true:没拥有", requiredMode = Schema.RequiredMode.REQUIRED)
        private Boolean disabled;
    }

}