package info.qizhi.aflower.module.member.service.benefit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.MemberBenefitEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitReqVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitRespVO;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.MemberBenefitDO;
import info.qizhi.aflower.module.member.dal.mysql.benefit.MemberBenefitMapper;
import info.qizhi.aflower.module.member.service.type.MemberTypeService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * 会员权益表;(member_benefit)表服务实现类
 * @date : 2025-3-8
 */
@Service
@Validated
public class MemberBenefitServiceImpl implements MemberBenefitService{
    @Resource
    private MemberBenefitMapper memberBenefitMapper;
    @Resource
    private MemberTypeService memberTypeService;

    @Override
    public void create(MemberBenefitSaveReqVO createReqVO) {
        // 插入
        MemberBenefitDO memberBenefitDO = BeanUtils.toBean(createReqVO, MemberBenefitDO.class);
        memberBenefitDO.setBenefitCode(IdUtil.getSnowflakeNextIdStr());
        memberBenefitMapper.insert(memberBenefitDO);
    }

    @Override
    public List<MemberBenefitRespVO> getTypeBenefitList(MemberBenefitReqVO reqVO) {
        List<MemberBenefitDO> memberBenefitDOS = memberBenefitMapper.selectList(reqVO);
        if(CollUtil.isEmpty(memberBenefitDOS)){
            for (MemberBenefitEnum memberBenefit : MemberBenefitEnum.values()){
                if(memberBenefit.getCode().equals(MemberBenefitEnum.VIEW_ALL.getCode())){
                    continue;
                }
                MemberBenefitDO memberBenefitDO = new MemberBenefitDO()
                        .setGcode(reqVO.getGcode())
                        .setBenefitCode(memberBenefit.getCode())
                        .setName(memberBenefit.getName())
                        .setValue(memberBenefit.getValue())
                        .setIsSys(NumberEnum.ONE.getNumber());

                memberBenefitDOS.add(memberBenefitDO);
            }

            memberBenefitMapper.insertBatch(memberBenefitDOS);
        }
        return BeanUtils.toBean(memberBenefitDOS, MemberBenefitRespVO.class);
    }

    @Override
    public void createBtach(List<MemberBenefitSaveReqVO> createReqVOs) {
        List<MemberBenefitDO> memberBenefitDOs = BeanUtils.toBean(createReqVOs, MemberBenefitDO.class);
        memberBenefitMapper.insertBatch(memberBenefitDOs);
    }
}
