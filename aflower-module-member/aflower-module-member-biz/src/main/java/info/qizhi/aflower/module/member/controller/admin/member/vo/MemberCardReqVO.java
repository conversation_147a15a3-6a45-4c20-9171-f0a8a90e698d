package info.qizhi.aflower.module.member.controller.admin.member.vo;

import info.qizhi.aflower.framework.common.validation.IdCard;
import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 会员信息 Request VO")
@Data
public class MemberCardReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "手机号码")
    @Mobile
    private String phone;

    @Schema(description = "证件号码")
    @IdCard
    private String idNo;
}