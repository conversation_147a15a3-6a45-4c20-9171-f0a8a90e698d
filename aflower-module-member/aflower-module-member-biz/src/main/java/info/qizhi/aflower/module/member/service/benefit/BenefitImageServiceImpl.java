package info.qizhi.aflower.module.member.service.benefit;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.MemberBenefitImageEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.controller.admin.benefit.vo.MemberBenefitImageSaveReqVO;
import info.qizhi.aflower.module.member.dal.dataobject.benefit.BenefitImageDO;
import info.qizhi.aflower.module.member.dal.mysql.benefit.BenefitImageMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Service
@Validated
public class BenefitImageServiceImpl implements BenefitImageService{
    @Resource
    private BenefitImageMapper benefitImageMapper;

    @Override
    public void createBtach(List<MemberBenefitImageSaveReqVO> createReqVOs) {
        List<BenefitImageDO> benefitImageDOS = BeanUtils.toBean(createReqVOs, BenefitImageDO.class);
        benefitImageMapper.insertBatch(benefitImageDOS);
    }

    @Override
    public List<BenefitImageDO> getList(String gcode) {
        List<BenefitImageDO> benefitImageDOS = benefitImageMapper.selectList(gcode);
        if(CollUtil.isEmpty(benefitImageDOS)){
            for (MemberBenefitImageEnum benefitImage : MemberBenefitImageEnum.values()) {
                BenefitImageDO memberBenefitImageSaveReqVO = new BenefitImageDO()
                        .setGcode(gcode)
                        .setImage(benefitImage.getImage())
                        .setImageType(benefitImage.getType())
                        .setBenefitCode(benefitImage.getCode());

                benefitImageDOS.add(memberBenefitImageSaveReqVO);
            }

            benefitImageMapper.insertBatch(benefitImageDOS);
        }

        return benefitImageDOS;
    }
}
