package info.qizhi.aflower.module.pay.service.notify;

import info.qizhi.aflower.framework.test.core.ut.BaseDbUnitTest;
import info.qizhi.aflower.module.pay.framework.job.config.PayJobConfiguration;
import info.qizhi.aflower.module.pay.service.refund.PayRefundServiceImpl;
import org.springframework.context.annotation.Import;


/**
 * {@link PayRefundServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({PayJobConfiguration.class, PayNotifyServiceImpl.class})
public class PayNotifyServiceTest extends BaseDbUnitTest {


}
