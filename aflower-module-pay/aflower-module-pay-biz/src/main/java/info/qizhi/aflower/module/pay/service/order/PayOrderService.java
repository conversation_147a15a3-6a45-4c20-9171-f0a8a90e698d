package info.qizhi.aflower.module.pay.service.order;

import com.baomidou.mybatisplus.extension.service.IService;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayOrderDO;

/**
 * 支付订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PayOrderService extends IService<PayOrderDO> {

    /**
     * 发起被扫支付
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO scan(ScanReqDTO reqDTO);

    /**
     * 发起会员卡支付
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO vip(VipReqDTO reqDTO);

    /**
     * 发起主扫下单接口
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO activeScan(ActiveScanReqDTO reqDTO);

    /**
     * 微信小程序支付
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO mini(MiniReqDTO reqDTO);

    /**
     * 查询支付订单
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO query(QueryOrderReqDTO reqDTO);

    /**
     * 通过商户订单号查询支付订单
     * @param orderNo 商户订单号
     * @return 支付订单信息
     */
    PayOrderDO getOneByOrderNo(String orderNo);

    /**
     * 修改退款金额
     *
     * @param orderNo     商户订单号
     * @param refundPrice 退款金额
     */
    void updateRefundPrice(String orderNo, Long refundPrice);

}
