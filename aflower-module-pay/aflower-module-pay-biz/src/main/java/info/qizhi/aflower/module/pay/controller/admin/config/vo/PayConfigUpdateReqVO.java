package info.qizhi.aflower.module.pay.controller.admin.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *
 * <AUTHOR>
 *
 */
@Schema(description = "支付配置 更新 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PayConfigUpdateReqVO extends PayConfigBaseVO {
    @Schema(description = "支付配置数据库ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{id.notnull}")
    private Long id;
}
