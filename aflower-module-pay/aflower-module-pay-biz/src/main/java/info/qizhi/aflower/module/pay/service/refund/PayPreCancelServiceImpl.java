package info.qizhi.aflower.module.pay.service.refund;

import cn.hutool.extra.spring.SpringUtil;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreCancelReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayPreOrderDO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayPreCancelDO;
import info.qizhi.aflower.module.pay.dal.mysql.refund.PayPreCancelMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.service.order.PayPreOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 预授权完成记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PayPreCancelServiceImpl implements PayPreCancelService {

    private static final String OPERATE_TYPE_PRE_CANCEL = "PRE_CANCEL";

    @Resource
    PayPreCancelMapper payPreCancelMapper;
    @Resource
    PayPreOrderService preOrderService;
    @Resource
    PayConfigService configService;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayPreCancelServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public PayRefundRespDTO preCancel(PreCancelReqDTO reqDTO) {
        try {
            PayPreOrderDO payPreOrderDO = preOrderService.validateAndGetPreOrder(reqDTO.getPayOrderNo());
            String channelCode = payPreOrderDO.getChannelCode();
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayRefundUnifiedReqDTO unifiedReqDTO = buildRefundUnifiedReqDTO(reqDTO, payPreOrderDO);
            PayRefundRespDTO respDTO = client.unifiedRefund(unifiedReqDTO);
            if (respDTO == null) {
                throw new ServiceException(-1, "预授权撤销失败！");
            }
            // 添加预授权撤销记录
            PayPreCancelDO cancelDO = buildCancelDO(reqDTO, respDTO, payPreOrderDO);
            payPreCancelMapper.insert(cancelDO);

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl preCancel error ======", e);
            throw new ServiceException(-1, "预授权撤销失败！");
        }
    }

    private PayRefundUnifiedReqDTO buildRefundUnifiedReqDTO(PreCancelReqDTO reqDTO, PayPreOrderDO preOrderDO) {
        PayRefundUnifiedReqDTO unifiedReqDTO = new PayRefundUnifiedReqDTO();
        unifiedReqDTO.setOperateType(OPERATE_TYPE_PRE_CANCEL);
        unifiedReqDTO.setAccountType(preOrderDO.getAccountType());
        unifiedReqDTO.setOutTradeNo(preOrderDO.getOrderNo());
        unifiedReqDTO.setChannelOrderNo(preOrderDO.getChannelOrderNo());
        unifiedReqDTO.setPayPrice(reqDTO.getPayPrice());
        unifiedReqDTO.setRefundPrice(reqDTO.getPayPrice());

        return unifiedReqDTO;
    }

    private PayPreCancelDO buildCancelDO(PreCancelReqDTO reqDTO, PayRefundRespDTO respDTO, PayPreOrderDO preOrderDO) {
        PayPreCancelDO cancelDO = new PayPreCancelDO();
        cancelDO.setHcode(reqDTO.getHcode());
        cancelDO.setCancelNo(respDTO.getOutRefundNo());
        cancelDO.setAccountType(preOrderDO.getAccountType());
        cancelDO.setStatus(respDTO.getStatus());
        cancelDO.setPayOrderNo(reqDTO.getPayOrderNo());
        cancelDO.setPayPrice(reqDTO.getPayPrice());
        cancelDO.setMerchantNo(respDTO.getMerchantNo());
        cancelDO.setChannelCancelNo(respDTO.getChannelRefundNo());
        cancelDO.setSuccessTime(respDTO.getSuccessTime());
        return cancelDO;
    }
}
