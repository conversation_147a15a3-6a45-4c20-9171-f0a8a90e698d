package info.qizhi.aflower.module.pay.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 *
 */
@TableName("pay_pre_cancel")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayPreCancelDO extends BaseDO {
    /**
     * 预授权撤销单编号，数据库自增
     */
    private Long id;
    /**
     * 酒店代码
     */
    private String hcode;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户预授权撤销单号
     *
     */
    private String cancelNo;
    /**
     * 钱包类型：微信-WECHAT；支付宝-ALIPAY
     */
    private String accountType;
    /**
     * 商户支付订单号
     *
     */
    private String payOrderNo;
    /**
     * 撤销状态
     *
     */
    private Integer status;
    /**
     * 支付金额，单位：分
     */
    private Long payPrice;
    /**
     * 渠道预授权撤销单号
     */
    private String channelCancelNo;
    /**
     * 成功时间
     */
    private LocalDateTime successTime;
}
