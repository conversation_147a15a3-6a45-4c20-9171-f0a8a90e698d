package info.qizhi.aflower.module.pay.controller.open.notify;

import com.alibaba.fastjson.JSONObject;
import jakarta.annotation.security.PermitAll;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/27 15:43
 */
@RestController
@RequestMapping("/pay/notify")
public class PayNotifyController {

    @PermitAll
    @PostMapping("/lakala")
    public String notifyLakala(@RequestBody JSONObject reqJson) {
        JSONObject respJson = new JSONObject();
        respJson.put("code", "SUCCESS");
        respJson.put("message", "执行成功");
        return JSONObject.toJSONString(respJson);
    }
}
