package info.qizhi.aflower.module.pay.controller.admin.config.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 支付配置 Base VO，提供给添加、修改、详细的子 VO 使用
 * <AUTHOR>
 */
@Data
public class PayConfigBaseVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "支付平台：FUIOUPAY-富友；ALIPAY-支付宝；WXPAY-微信支付", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{platform.notblank}")
    private String platform;

    @Schema(description = "机构号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orgNo;

    @Schema(description = "商户号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{merchantNo.notblank}")
    private String merchantNo;

    @Schema(description = "appid", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appid;

    @Schema(description = "商户私钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{privateKey.notblank}")
    private String privateKey;

    @Schema(description = "平台公钥", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{publicKey.notblank}")
    private String publicKey;

    @Schema(description = "订单号前缀", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderPrefix;

    @Schema(description = "是否集团配置", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isGroup;
}
