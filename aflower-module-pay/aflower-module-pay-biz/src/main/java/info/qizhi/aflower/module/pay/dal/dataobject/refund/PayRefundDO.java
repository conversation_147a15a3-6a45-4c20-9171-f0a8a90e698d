package info.qizhi.aflower.module.pay.dal.dataobject.refund;

import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 支付退款单 DO
 * 一个支付订单，可以拥有多个支付退款单
 *
 * <AUTHOR>
 */
@TableName("pay_refund")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayRefundDO extends BaseDO {

    /**
     * 退款单编号，数据库自增
     */
    private Long id;
    /**
     * 酒店代码
     */
    private String hcode;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户退款订单号
     *
     */
    private String refundNo;
    /**
     * 钱包类型：微信-WECHAT；支付宝-ALIPAY
     */
    private String accountType;
    /**
     * 商户支付订单号
     *
     */
    private String payOrderNo;
    /**
     * 退款状态
     *
     */
    private Integer status;
    /**
     * 支付金额，单位：分
     */
    private Long payPrice;
    /**
     * 退款金额，单位：分
     */
    private Long refundPrice;
    /**
     * 渠道退款单号
     *
     */
    private String channelRefundNo;
    /**
     * 账户端退款单号
     */
    private String accRefundNo;
    /**
     * 退款成功时间
     */
    private LocalDateTime successTime;


}
