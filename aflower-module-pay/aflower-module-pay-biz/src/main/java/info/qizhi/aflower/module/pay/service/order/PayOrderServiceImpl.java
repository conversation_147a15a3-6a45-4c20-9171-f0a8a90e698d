package info.qizhi.aflower.module.pay.service.order;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.exception.PayException;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayOrderDO;
import info.qizhi.aflower.module.pay.dal.mysql.order.PayOrderMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.util.ChannelUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;


/**
 * 支付订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PayOrderServiceImpl extends ServiceImpl<PayOrderMapper, PayOrderDO> implements PayOrderService {

    private static final String CHANNEL_CODE_SCAN_SUFFIX = "_scan";
    private static final String CHANNEL_CODE_VIP_SUFFIX = "_vip";

    @Resource
    PayOrderMapper payOrderMapper;
    @Resource
    PayConfigService configService;
    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayOrderServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public PayOrderRespDTO scan(ScanReqDTO reqDTO) {
        try {
            String accountType = validateAndGetAccountType(reqDTO.getAuthCode());
            String channelCode = reqDTO.getPlatform() + CHANNEL_CODE_SCAN_SUFFIX;
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayOrderUnifiedReqDTO unifiedReqDTO = buildUnifiedReqDTO(reqDTO, accountType);
            PayOrderRespDTO respDTO = client.unifiedOrder(unifiedReqDTO);


            if (respDTO == null) {
                throw new ServiceException(-1, "扫码支付失败！");
            }

            PayOrderDO payOrderDO = buildPayOrderDO(reqDTO, respDTO, channelCode);
            payOrderMapper.insert(payOrderDO);

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl payScan error ======", e);
            throw new ServiceException(-1, "扫码支付失败！");
        }
    }

    @Override
    public PayOrderRespDTO vip(VipReqDTO reqDTO) {
        String channelCode = reqDTO.getPlatform() + CHANNEL_CODE_VIP_SUFFIX;
        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

        // 调用三方接口
        PayOrderUnifiedReqDTO unifiedReqDTO = new PayOrderUnifiedReqDTO() {{
            setAccountType("VIP");
            setPrice(reqDTO.getPrice());
            setSubject("会员卡余额支付");
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("vipId", reqDTO.getVipId());
            setChannelExtras(channelExtras);

        }};
        PayOrderRespDTO respDTO = client.unifiedOrder(unifiedReqDTO);
        // 记录支付记录
        PayOrderDO payOrderDO = new PayOrderDO() {{
            setHcode(reqDTO.getHcode());
            setMerchantNo(respDTO.getMerchantNo());
            setOrderNo(respDTO.getOutTradeNo());
            setAccountType("VIP");
            setPrice(reqDTO.getPrice());
            setRefundPrice(0L);
            setChannelCode(channelCode);
            setAccOrderNo(respDTO.getAccTradeNo());
            setChannelOrderNo(respDTO.getChannelOrderNo());
            setSuccessTime(respDTO.getSuccessTime());
            setStatus(respDTO.getStatus());
        }};
        payOrderMapper.insert(payOrderDO);

        return respDTO;
    }

    @Override
    public PayOrderRespDTO activeScan(ActiveScanReqDTO reqDTO) {
        // 根据平台以及授权码得到支付渠道
        String channelCode = reqDTO.getPlatform() + "_active_scan";
        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);
        // 调用三方接口
        PayOrderUnifiedReqDTO unifiedReqDTO = new PayOrderUnifiedReqDTO() {{
            setAccountType(reqDTO.getAccountType());
            setPrice(reqDTO.getPrice());
            setSubject(reqDTO.getSubject());
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("transType", "NATIVE");
            setChannelExtras(channelExtras);

        }};
        PayOrderRespDTO respDTO = client.unifiedOrder(unifiedReqDTO);
        // 记录支付记录
        PayOrderDO payOrderDO = new PayOrderDO() {{
            setHcode(reqDTO.getHcode());
            setMerchantNo(respDTO.getMerchantNo());
            setAccountType(reqDTO.getAccountType());
            setOrderNo(respDTO.getOutTradeNo());
            setPrice(reqDTO.getPrice());
            setRefundPrice(0L);
            setNotifyUrl(reqDTO.getNotifyUrl());
            setChannelCode(channelCode);
            setChannelOrderNo(respDTO.getChannelOrderNo());
            setAccOrderNo(respDTO.getAccTradeNo());
            setThirdOrderNo(respDTO.getThirdOrderNo());
            setSuccessTime(respDTO.getSuccessTime());
            setStatus(respDTO.getStatus());
        }};
        payOrderMapper.insert(payOrderDO);

        return respDTO;
    }

    @Override
    public PayOrderRespDTO mini(MiniReqDTO reqDTO) {
        // 根据平台以及授权码得到支付渠道
        String channelCode = reqDTO.getPlatform() + "_active_scan";
        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);
        // 调用三方接口
        PayOrderUnifiedReqDTO unifiedReqDTO = new PayOrderUnifiedReqDTO() {{
            setAccountType(reqDTO.getAccountType());
            setPrice(reqDTO.getPrice());
            setSubject(reqDTO.getSubject());
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("transType", "MINI");
            channelExtras.put("user_id", reqDTO.getUserId());
            setChannelExtras(channelExtras);

        }};
        PayOrderRespDTO respDTO = client.unifiedOrder(unifiedReqDTO);
        // 记录支付记录
        PayOrderDO payOrderDO = new PayOrderDO() {{
            setHcode(reqDTO.getHcode());
            setMerchantNo(respDTO.getMerchantNo());
            setAccountType(reqDTO.getAccountType());
            setOrderNo(respDTO.getOutTradeNo());
            setPrice(reqDTO.getPrice());
            setRefundPrice(0L);
            setNotifyUrl(reqDTO.getNotifyUrl());
            setChannelCode(channelCode);
            setChannelOrderNo(respDTO.getChannelOrderNo());
            setAccOrderNo(respDTO.getAccTradeNo());
            setThirdOrderNo(respDTO.getThirdOrderNo());
            setSuccessTime(respDTO.getSuccessTime());
            setStatus(respDTO.getStatus());
        }};
        payOrderMapper.insert(payOrderDO);

        return respDTO;
    }

    @Override
    public PayOrderRespDTO query(QueryOrderReqDTO reqDTO) {

        if (reqDTO == null || StringUtils.isBlank(reqDTO.getOrderNo())) {
            throw new ServiceException(-1, "支付单号不能为空");
        }
        reqDTO.setOrderNo(reqDTO.getOrderNo().trim());

        PayOrderDO payOrderDO = validateAndGetOrderDO(reqDTO);

        String channelCode = reqDTO.getPlatform() + CHANNEL_CODE_SCAN_SUFFIX;
        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

        PayOrderGetReqDTO getReqDTO = buildGetReqDTO(payOrderDO);

        PayOrderRespDTO orderRespDTO = client.getOrder(getReqDTO);
        if (orderRespDTO == null) {
            throw new ServiceException(-1, "没找到该订单！");
        }

        // 更新订单的状态
        PayOrderDO updatePayOrder = getPayOrderDO(payOrderDO, orderRespDTO);
        payOrderMapper.updateById(updatePayOrder);

        // 避免小游支付没返回支付金额
        if (orderRespDTO.getPayPrice() == null) {
            orderRespDTO.setPayPrice(payOrderDO.getPrice());
        }
        orderRespDTO.setAccountType(payOrderDO.getAccountType());
        return orderRespDTO;

    }

    @NotNull
    private static PayOrderDO getPayOrderDO(PayOrderDO payOrderDO, PayOrderRespDTO orderRespDTO) {
        PayOrderDO updatePayOrder = new PayOrderDO();
        updatePayOrder.setId(payOrderDO.getId());
        updatePayOrder.setStatus(orderRespDTO.getStatus());
        updatePayOrder.setSuccessTime(orderRespDTO.getSuccessTime());
        updatePayOrder.setChannelOrderNo(orderRespDTO.getChannelOrderNo());
        updatePayOrder.setAccOrderNo(orderRespDTO.getAccTradeNo());
        updatePayOrder.setThirdOrderNo(orderRespDTO.getThirdOrderNo());
        updatePayOrder.setRefundPrice(orderRespDTO.getRefundPrice());
        return updatePayOrder;
    }

    @Override
    public PayOrderDO getOneByOrderNo(String orderNo) {
        return payOrderMapper.selectOne(PayOrderDO::getOrderNo, orderNo);
    }

    private String validateAndGetAccountType(String authCode) {
        String accountType = ChannelUtils.getAccountTypeByAuthCode(authCode);
        if (StringUtils.isBlank(accountType)) {
            throw new ServiceException(-1, "无效的授权码");
        }
        return accountType;
    }

    private PayOrderUnifiedReqDTO buildUnifiedReqDTO(ScanReqDTO reqDTO, String accountType) {
        PayOrderUnifiedReqDTO unifiedReqDTO = new PayOrderUnifiedReqDTO();
        unifiedReqDTO.setAccountType(accountType);
        unifiedReqDTO.setPrice(reqDTO.getPrice());
        unifiedReqDTO.setBody(reqDTO.getBody());
        unifiedReqDTO.setSubject(reqDTO.getSubject());
        unifiedReqDTO.setExpireTime(reqDTO.getExpireTime());

        Map<String, String> channelExtras = new HashMap<>();
        channelExtras.put("authCode", reqDTO.getAuthCode());
        unifiedReqDTO.setChannelExtras(channelExtras);
        return unifiedReqDTO;
    }

    private PayOrderDO buildPayOrderDO(ScanReqDTO reqDTO, PayOrderRespDTO respDTO, String channelCode) {
        PayOrderDO payOrderDO = new PayOrderDO();
        payOrderDO.setHcode(reqDTO.getHcode());
        payOrderDO.setMerchantNo(respDTO.getMerchantNo());
        payOrderDO.setOrderNo(respDTO.getOutTradeNo());
        payOrderDO.setAccountType(ChannelUtils.getAccountTypeByAuthCode(reqDTO.getAuthCode()));
        payOrderDO.setPrice(reqDTO.getPrice());
        payOrderDO.setChannelCode(channelCode);
        payOrderDO.setChannelOrderNo(respDTO.getChannelOrderNo());
        payOrderDO.setAccOrderNo(respDTO.getAccTradeNo());
        payOrderDO.setThirdOrderNo(respDTO.getThirdOrderNo());
        payOrderDO.setSuccessTime(respDTO.getSuccessTime());
        payOrderDO.setStatus(respDTO.getStatus());
        payOrderDO.setRefundPrice(0L);
        return payOrderDO;
    }

    @Override
    public void updateRefundPrice(String orderNo, Long refundPrice) {
        PayOrderDO orderDO = payOrderMapper.selectOne(new LambdaQueryWrapper<>() {{
            eq(PayOrderDO::getOrderNo, orderNo);
        }});
        orderDO.setRefundPrice(orderDO.getRefundPrice() + refundPrice);
        payOrderMapper.updateById(orderDO);
    }

    private PayOrderDO validateAndGetOrderDO(QueryOrderReqDTO reqDTO) {
        LambdaQueryWrapperX<PayOrderDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(PayOrderDO::getThirdOrderNo, reqDTO.getOrderNo());
        queryWrapper.eq(PayOrderDO::getChannelCode, reqDTO.getPlatform() + CHANNEL_CODE_SCAN_SUFFIX);
        PayOrderDO payOrderDO = payOrderMapper.selectOne(queryWrapper);

        if (payOrderDO == null) {
            throw new ServiceException(-1, "支付订单不存在");
        }
        return payOrderDO;
    }

    private PayOrderGetReqDTO buildGetReqDTO(PayOrderDO payOrderDO) {
        PayOrderGetReqDTO getReqDTO = new PayOrderGetReqDTO();
        getReqDTO.setAccountType(payOrderDO.getAccountType());
        getReqDTO.setOutTradeNo(payOrderDO.getOrderNo());
        getReqDTO.setChannelOrderNo(payOrderDO.getChannelOrderNo());
        return getReqDTO;
    }
}
