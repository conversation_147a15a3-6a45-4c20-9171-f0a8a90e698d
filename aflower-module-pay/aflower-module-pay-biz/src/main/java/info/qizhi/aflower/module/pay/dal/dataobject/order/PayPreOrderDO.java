package info.qizhi.aflower.module.pay.dal.dataobject.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.*;


/**
 * 预授权支付订单 DO
 *
 * <AUTHOR>
 */
@TableName("pay_pre_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class PayPreOrderDO extends PayOrderCommonDO {
    /**
     * 完成金额，单位：分
     */
    private Long finishPrice;

}
