package info.qizhi.aflower.module.pay.convert.base;

import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 
 * 
 * <AUTHOR>
 *
 */
@Mapper
public interface PayBaseConvert {
    PayBaseConvert INSTANCE = Mappers.getMapper(PayBaseConvert.class);

    PayOrderUnifiedReqDTO convert(ScanReqDTO bean);

    ScanRespDTO convert(PayOrderRespDTO bean);

    PayOrderUnifiedReqDTO convert(PreScanReqDTO bean);

    PreScanRespDTO convertPre(PayOrderRespDTO bean);

    PayRefundUnifiedReqDTO convert(RefundReqDTO bean);

    PayRefundUnifiedReqDTO convert(PreFinishReqDTO bean);

    PayRefundUnifiedReqDTO convert(PreCancelReqDTO bean);

    PayRefundUnifiedReqDTO convert(PreFinishCancelReqDTO bean);
}
