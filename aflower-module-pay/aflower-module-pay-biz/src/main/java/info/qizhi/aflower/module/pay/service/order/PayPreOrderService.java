package info.qizhi.aflower.module.pay.service.order;

import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreScanReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayPreOrderDO;

/**
 * 支付订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PayPreOrderService {

    /**
     * 预授权被扫
     * @param reqDTO 请求参数
     * @return 预授权支付订单信息
     */
    PayOrderRespDTO scan(PreScanReqDTO reqDTO);

    /**
     * 校验预授权支付订单
     * @param orderNo 商户订单号
     * @return 预授权信息
     */
    PayPreOrderDO validateAndGetPreOrder(String orderNo);

    int updateFinishPrice(String orderNo, Long finishPrice);



}
