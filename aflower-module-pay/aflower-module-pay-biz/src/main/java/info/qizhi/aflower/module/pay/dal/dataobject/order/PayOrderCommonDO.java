package info.qizhi.aflower.module.pay.dal.dataobject.order;

import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/30 16:04
 */
@Data
public class PayOrderCommonDO extends BaseDO {
    /**
     * 订单编号，数据库自增
     */
    private Long id;
    /**
     * 酒店代码
     */
    private String hcode;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 渠道编码
     *
     */
    private String channelCode;
    /**
     * 商户订单编号
     *
     */
    private String orderNo;
    /**
     * 钱包类型：微信-WECHAT；支付宝-ALIPAY
     */
    private String accountType;
    /**
     * 异步通知地址
     */
    private String notifyUrl;
    /**
     * 支付金额，单位：分
     */
    private Long price;
    /**
     * 支付状态
     *
     * 枚举 {@link PayOrderStatusRespEnum}
     */
    private Integer status;
    /**
     * 订单支付成功时间
     */
    private LocalDateTime successTime;
    /**
     * 回调时间
     */
    private LocalDateTime notifyTime;
    /**
     * 渠道订单号
     */
    private String channelOrderNo;
    /**
     * 账户端交易单号（微信、支付宝生成的交易单号）
     */
    private String accOrderNo;
    /**
     * 第三方单号
     */
    private String thirdOrderNo;
}
