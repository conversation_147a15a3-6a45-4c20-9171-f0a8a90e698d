package info.qizhi.aflower.module.pay.service.order;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.OrderReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreScanReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayOrderCommonDO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayPreOrderDO;
import info.qizhi.aflower.module.pay.dal.mysql.order.PayPreOrderMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.util.ChannelUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;


/**
 * 支付订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class PayPreOrderServiceImpl implements PayPreOrderService {

    private static final String CHANNEL_CODE_SUFFIX = "_pre_scan";

    @Resource
    PayPreOrderMapper payPreOrderMapper;
    @Resource
    PayConfigService configService;
    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayPreOrderServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public PayOrderRespDTO scan(PreScanReqDTO reqDTO) {
        try {
            String accountType = validateAndGetAccountType(reqDTO.getAuthCode());
            String channelCode = reqDTO.getPlatform() + CHANNEL_CODE_SUFFIX;
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayOrderUnifiedReqDTO unifiedReqDTO = buildUnifiedReqDTO(reqDTO, accountType);
            PayOrderRespDTO respDTO = client.unifiedOrder(unifiedReqDTO);

            if (respDTO == null) {
                throw new ServiceException(-1, "支付失败");
            }
            PayOrderCommonDO orderCommonDO = buildPayPreOrderDO(reqDTO, respDTO, channelCode);
            PayPreOrderDO payPreOrderDO = BeanUtils.toBean(orderCommonDO, PayPreOrderDO.class);

            payPreOrderMapper.insert(payPreOrderDO);

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl payPreScan error ======", e);
            throw new ServiceException(-1, "支付失败！");
        }
    }

    @Override
    public PayPreOrderDO validateAndGetPreOrder(String orderNo) {
        // 校验支付订单号
        PayPreOrderDO payPreOrderDO = payPreOrderMapper.selectOne(new LambdaQueryWrapperX<>(){{
            eq(PayPreOrderDO::getOrderNo, orderNo);
        }});
        if (payPreOrderDO == null) {
            throw new ServiceException(-1, "预授权支付订单不存在");
        }
        String channelCode = payPreOrderDO.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            throw new ServiceException(-1, "支付渠道不存在");
        }
        String accountType = payPreOrderDO.getAccountType();
        if (StringUtils.isBlank(accountType)) {
            throw new ServiceException(-1, "钱包类型不存在");
        }
        return payPreOrderDO;
    }

    private String validateAndGetAccountType(String authCode) {
        String accountType = ChannelUtils.getAccountTypeByAuthCode(authCode);
        if (StringUtils.isBlank(accountType)) {
            throw new ServiceException(-1, "无效的授权码");
        }
        return accountType;
    }

    private PayOrderUnifiedReqDTO buildUnifiedReqDTO(OrderReqDTO reqDTO, String accountType) {
        PayOrderUnifiedReqDTO unifiedReqDTO = new PayOrderUnifiedReqDTO();
        unifiedReqDTO.setAccountType(accountType);
        unifiedReqDTO.setPrice(reqDTO.getPrice());
        unifiedReqDTO.setBody(reqDTO.getBody());
        unifiedReqDTO.setSubject(reqDTO.getSubject());
        unifiedReqDTO.setExpireTime(reqDTO.getExpireTime());

        Map<String, String> channelExtras = new HashMap<>();
        channelExtras.put("authCode", reqDTO.getAuthCode());
        unifiedReqDTO.setChannelExtras(channelExtras);
        return unifiedReqDTO;
    }

    private PayPreOrderDO buildPayPreOrderDO(PreScanReqDTO reqDTO, PayOrderRespDTO respDTO, String channelCode) {
        PayPreOrderDO payPreOrderDO = new PayPreOrderDO();
        payPreOrderDO.setHcode(reqDTO.getHcode());
        payPreOrderDO.setMerchantNo(respDTO.getMerchantNo());
        payPreOrderDO.setOrderNo(respDTO.getOutTradeNo());
        payPreOrderDO.setAccountType(ChannelUtils.getAccountTypeByAuthCode(reqDTO.getAuthCode()));
        payPreOrderDO.setPrice(reqDTO.getPrice());
        payPreOrderDO.setChannelCode(channelCode);
        payPreOrderDO.setChannelOrderNo(respDTO.getChannelOrderNo());
        payPreOrderDO.setAccOrderNo(respDTO.getAccTradeNo());
        payPreOrderDO.setThirdOrderNo(respDTO.getThirdOrderNo());
        payPreOrderDO.setSuccessTime(respDTO.getSuccessTime());
        payPreOrderDO.setStatus(respDTO.getStatus());
        payPreOrderDO.setFinishPrice(0L);
        return payPreOrderDO;
    }


    @Override
    public int updateFinishPrice(String orderNo, Long finishPrice) {
        PayPreOrderDO preOrderDO = payPreOrderMapper.selectOne(new LambdaQueryWrapper<>() {{
            eq(PayPreOrderDO::getOrderNo, orderNo);
        }});
        preOrderDO.setFinishPrice(preOrderDO.getFinishPrice() + finishPrice);
        return payPreOrderMapper.updateById(preOrderDO);
    }

}
