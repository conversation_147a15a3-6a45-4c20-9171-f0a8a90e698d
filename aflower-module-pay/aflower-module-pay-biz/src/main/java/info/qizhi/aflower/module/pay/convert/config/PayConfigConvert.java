package info.qizhi.aflower.module.pay.convert.config;

import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigCreateReqVO;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigUpdateReqVO;
import info.qizhi.aflower.module.pay.dal.dataobject.config.PayConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface PayConfigConvert {
    PayConfigConvert INSTANCE = Mappers.getMapper(PayConfigConvert.class);

    PayConfigDO convert(PayConfigCreateReqVO bean);

    PayConfigDO convert(PayConfigUpdateReqVO bean);
}