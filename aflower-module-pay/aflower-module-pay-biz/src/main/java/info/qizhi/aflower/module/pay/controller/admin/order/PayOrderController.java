package info.qizhi.aflower.module.pay.controller.admin.order;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.enums.refund.PayRefundStatusRespEnum;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import info.qizhi.aflower.module.pay.service.order.PayOrderService;
import info.qizhi.aflower.module.pay.service.order.PayPreOrderService;
import info.qizhi.aflower.module.pay.service.refund.PayPreFinishCancelService;
import info.qizhi.aflower.module.pay.service.refund.PayPreFinishService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/27 14:44
 */
@RestController
@RequestMapping("pay/order")
public class PayOrderController {

    @Resource
    PayOrderService orderService;
    @Resource
    PayPreOrderService preOrderService;
    @Resource
    PayPreFinishService preFinishService;
    @Resource
    PayPreFinishCancelService preFinishCancelService;


    @PostMapping("/active-scan")
    @Operation(summary = "聚合主扫")
    CommonResult<ActiveScanRespDTO> activeScan(@RequestBody ActiveScanReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.activeScan(reqDTO);

        // 主扫只要下单成功了就返回成功, 通过ChannelCode来判断
        String channelErrorCode = orderRespDTO.getChannelErrorCode();
        String channelOrderNo = orderRespDTO.getChannelOrderNo();
        if (StringUtils.isNotBlank(channelErrorCode) || StringUtils.isBlank(channelOrderNo)) {
            return CommonResult.error(-1, "下单失败，失败原因：" + orderRespDTO.getChannelErrorMsg());
        }
        ActiveScanRespDTO respDTO = new ActiveScanRespDTO() {{
            setOrderNo(orderRespDTO.getOutTradeNo());
            setChannelOrderNo(orderRespDTO.getChannelOrderNo());
            setQrCode(orderRespDTO.getExtraParams().get("code"));
        }};
        return CommonResult.success(respDTO);
    }

    @PostMapping("/query")
    @Operation(summary = "查询")
    public CommonResult<QueryOrderRespDTO> query(@RequestBody QueryOrderReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.query(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            QueryOrderRespDTO respDTO = new QueryOrderRespDTO() {{
                setStatus(orderRespDTO.getStatus());
                setPlatform(reqDTO.getPlatform());
                setAccountType(orderRespDTO.getAccountType());
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setPayPrice(orderRespDTO.getPayPrice());
                setRefundPrice(orderRespDTO.getRefundPrice());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    @PostMapping("/pre-scan")
    CommonResult<PreScanRespDTO> preScan(@RequestBody PreScanReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = preOrderService.scan(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            PreScanRespDTO respDTO = new PreScanRespDTO() {{
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    @PostMapping("/pre-finish")
    CommonResult<PreFinishRespDTO> preFinish(@RequestBody PreFinishReqDTO reqDTO) {
        PayRefundRespDTO orderRespDTO = preFinishService.preFinish(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayRefundStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            PreFinishRespDTO respDTO = new PreFinishRespDTO() {{
                setFinishOrderNo(orderRespDTO.getOutRefundNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    @PostMapping("/pre-finish-cancel")
    CommonResult<PreFinishCancelRespDTO> preFinishCancel(@RequestBody PreFinishCancelReqDTO reqDTO) {
        PayRefundRespDTO orderRespDTO = preFinishCancelService.preFinishCancel(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayRefundStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            PreFinishCancelRespDTO respDTO = new PreFinishCancelRespDTO() {{
                setFinishCancelNo(orderRespDTO.getOutRefundNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }
}
