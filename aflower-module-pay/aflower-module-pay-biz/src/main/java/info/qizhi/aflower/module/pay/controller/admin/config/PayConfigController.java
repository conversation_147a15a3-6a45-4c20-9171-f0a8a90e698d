package info.qizhi.aflower.module.pay.controller.admin.config;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigCreateReqVO;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigRespVO;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigUpdateReqVO;
import info.qizhi.aflower.module.pay.dal.dataobject.config.PayConfigDO;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


/**
 * 
 * 支付配置
 * <AUTHOR>
 *
 */
@Tag(name = "支付配置")
@RestController
@RequestMapping("/pay/config")
@Validated
public class PayConfigController {

    @Resource
    PayConfigService payConfigService;


    @PostMapping("/add")
    @Operation(summary = "添加支付配置")
    CommonResult<Long> add(@RequestBody PayConfigCreateReqVO reqVO) {
        return CommonResult.success(payConfigService.addConfig(reqVO));
    }

    @PostMapping("/update")
    @Operation(summary = "更新支付配置")
    CommonResult<Boolean> update(PayConfigUpdateReqVO reqVO) {
        payConfigService.updateConfig(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/get-config")
    @Operation(summary = "获取支付配置")
    CommonResult<PayConfigRespVO> getConfig(String hcode, String platform) {
        PayConfigDO payConfig = payConfigService.getConfigByHcodeAndPlatform(hcode, platform);
        return CommonResult.success(BeanUtils.toBean(payConfig, PayConfigRespVO.class));
    }











}
