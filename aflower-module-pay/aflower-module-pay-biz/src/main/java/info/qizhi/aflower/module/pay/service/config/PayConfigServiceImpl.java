package info.qizhi.aflower.module.pay.service.config;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import info.qizhi.aflower.framework.pay.core.client.PayClientFactory;
import info.qizhi.aflower.framework.pay.core.client.impl.fuiou.FuiouScanPayClient;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigCreateReqVO;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigUpdateReqVO;
import info.qizhi.aflower.module.pay.convert.config.PayConfigConvert;
import info.qizhi.aflower.module.pay.dal.dataobject.config.PayConfigDO;
import info.qizhi.aflower.module.pay.dal.mysql.config.PayConfigMapper;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.lang.reflect.Field;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.framework.common.util.cache.CacheUtils.buildAsyncReloadingCache;
import static info.qizhi.aflower.module.pay.enums.ErrorCodeConstants.*;

/**
 * 
 * 支付配置 service 实现类
 * <AUTHOR>
 * @date 2024/05/15 22:02
 */
@Service
@Slf4j
@Validated
public class PayConfigServiceImpl implements PayConfigService {
    /**
     * {@link PayClient} 缓存，通过它异步清空 payClientFactory
     */
    @Getter
    private final LoadingCache<String, PayClient> clientCache = buildAsyncReloadingCache(Duration.ofSeconds(10L),
            new CacheLoader<String, PayClient>() {
                @NotNull
                @Override
                public PayClient load(@NotNull String key) {
                    // 校验 key 格式
                    if (!key.contains("_")) {
                        throw exception(CONFIG_INVALID_KEY_FORMAT);
                    }
                    // key = hcode + '_' + channelCode
                    int index = key.indexOf("_");
                    PayChannelEnum channelEnum = PayChannelEnum.getByCode(key.substring(index + 1));
                    Assert.notNull(channelEnum, String.format("支付渠道(%s) 为空", key.substring(index + 1)));
                    String[] codes = key.split("_");
                    PayConfigDO config = payConfigMapper.selectOne(new LambdaQueryWrapper<>(){{
                        eq(PayConfigDO::getHcode, codes[0]);
                        eq(PayConfigDO::getPlatform, channelEnum.getPlatform());
                    }});

                    if (config != null) {
                        PayClientConfig payClientConfig = createPayClientConfig(channelEnum.getConfigClass(), config);
                        payClientFactory.createOrUpdatePayClient(key, channelEnum, payClientConfig);
                    } else {
                        throw exception(CONFIG_NOT_FOUND);
                    }
                    return payClientFactory.getPayClient(key);

                }

                private PayClientConfig createPayClientConfig(Class<? extends PayClientConfig> configClass, PayConfigDO config) {
                    try {
                        Field[] fields = ReflectUtil.getFields(config.getClass());
                        PayClientConfig payClientConfig = ReflectUtil.newInstance(configClass);
                        for (Field field : fields) {
                            Field field1 = ReflectUtil.getField(payClientConfig.getClass(), field.getName());
                            if (field1 != null) {
                                ReflectUtil.setFieldValue(payClientConfig, field.getName(), ReflectUtil.getFieldValue(config, field));
                            }
                        }
                        return payClientConfig;
                    } catch (Exception e) {
                        log.error("Error creating PayClientConfig", e);
                        throw exception(CONFIG_CREATE_ERROR);
                    }
                }

            });

    @Resource
    private PayClientFactory payClientFactory;

    @Resource
    private PayConfigMapper payConfigMapper;

    private final Map<String, Object> cacheClearLock = new ConcurrentHashMap<>();


    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        payClientFactory.registerPayClientClass(PayChannelEnum.FUIOU_SCAN, FuiouScanPayClient.class);
    }

    @Override
    public Long addConfig(PayConfigCreateReqVO reqVO) {
        // 断言是否有重复的
        PayConfigDO dbConfig = getConfigByHcodeAndPlatform(reqVO.getHcode(), reqVO.getPlatform());
        if (dbConfig != null) {
            throw exception(CONFIG_EXIST_SAME_CONFIG_ERROR);
        }

        // 新增配置
        PayConfigDO config = PayConfigConvert.INSTANCE.convert(reqVO);
        payConfigMapper.insert(config);
        return config.getId();
    }

    @Override
    public void updateConfig(PayConfigUpdateReqVO updateReqVO) {
        // 校验存在
        PayConfigDO dbConfig = payConfigMapper.selectById(updateReqVO.getId());
        if (dbConfig == null) {
            throw exception(CONFIG_NOT_FOUND);
        }

        // 更新
        PayConfigDO config = PayConfigConvert.INSTANCE.convert(updateReqVO);
        payConfigMapper.updateById(config);

        // 清空缓存
        clearCaches(dbConfig.getHcode(), dbConfig.getPlatform());
    }

    @Override
    public PayConfigDO getConfigByHcodeAndPlatform(String hcode, String platform) {
        return payConfigMapper.selectOne(new LambdaQueryWrapper<>(){{
            eq(PayConfigDO::getHcode, hcode);
            eq(PayConfigDO::getPlatform, platform);
        }});
    }

    /**
     * 删除缓存
     *
     */
    private void clearCaches(String hcode, String platform) {
        List<PayChannelEnum> channelEnum = PayChannelEnum.getListByPlatform(platform);
        List<String> keys = channelEnum.stream().map(channel -> hcode + "_" + channel.getCode()).toList();

        // 避免并发清理缓存时的冲突
        synchronized (cacheClearLock.computeIfAbsent(hcode + "_" + platform, k -> new Object())) {
            clientCache.invalidateAll(keys);
        }
    }


    @Override
    public PayClient getPayClient(String key) {
        return clientCache.getUnchecked(key);
    }
}
