package info.qizhi.aflower.module.pay.service.notify;


import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayOrderDO;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.service.order.PayOrderService;
import info.qizhi.aflower.module.pay.util.ChannelUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import jakarta.validation.Valid;

import java.time.LocalDateTime;

/**
 * 支付通知 Core Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Valid
@Slf4j
public class PayNotifyServiceImpl implements PayNotifyService {

    @Resource
    PayOrderService payOrderService;
    @Resource
    PayConfigService configService;



    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayNotifyServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public JSONObject notifyLakala(JSONObject reqJson) {

        // 通过商户流水号查看此笔订单是不是系统产生的
        String orderNo = reqJson.getString("out_trade_no");
        PayOrderDO orderDO = payOrderService.getOneByOrderNo(orderNo);
        JSONObject resultJson = new JSONObject();
        resultJson.put("code", "SUCCESS");
        resultJson.put("message", "执行成功");
        if (orderDO == null) {
            resultJson.put("code", "FAIL");
            resultJson.put("message", "订单不存在");
            return resultJson;
        }
        // 如果是不是待支付状态，或者回调时间不为空，则直接返回成功
        if (!orderDO.getStatus().equals(PayOrderStatusRespEnum.WAITING.getStatus()) || orderDO.getNotifyTime() != null) {
            return resultJson;
        }
        String hcode = orderDO.getHcode();
        String channelCode = orderDO.getChannelCode();
        PayClient payClient = configService.getPayClient(hcode + "_" + channelCode);

        PayOrderRespDTO orderResp = payClient.parseOrderNotify(JSONObject.toJSONString(reqJson));
        if (orderResp == null) {
            resultJson.put("code", "FAIL");
            resultJson.put("message", "订单不存在");
            return resultJson;
        }

        // 执行后续接口
        String notifyUrl = orderDO.getNotifyUrl();
        if (StringUtils.isNotBlank(notifyUrl)) {
            // 后续逻辑再处理

        }

        // 成功再更新订单信息，表示已经回调过
        PayOrderDO updateOrderDO = new PayOrderDO();
        updateOrderDO.setId(orderDO.getId());
        updateOrderDO.setStatus(orderResp.getStatus());
        updateOrderDO.setAccOrderNo(orderResp.getAccTradeNo());
        updateOrderDO.setSuccessTime(orderResp.getSuccessTime());
        updateOrderDO.setNotifyTime(LocalDateTime.now());
        payOrderService.updateById(updateOrderDO);

        return resultJson;
    }
}
