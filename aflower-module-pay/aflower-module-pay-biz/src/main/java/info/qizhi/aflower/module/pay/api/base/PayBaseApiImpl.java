package info.qizhi.aflower.module.pay.api.base;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.enums.refund.PayRefundStatusRespEnum;
import info.qizhi.aflower.module.pay.api.base.dto.*;
import info.qizhi.aflower.module.pay.service.order.PayOrderService;
import info.qizhi.aflower.module.pay.service.order.PayPreOrderService;
import info.qizhi.aflower.module.pay.service.refund.PayPreCancelService;
import info.qizhi.aflower.module.pay.service.refund.PayPreFinishCancelService;
import info.qizhi.aflower.module.pay.service.refund.PayPreFinishService;
import info.qizhi.aflower.module.pay.service.refund.PayRefundService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 * 提供 RESTful API 接口，给 Feign 调用
 * 条码支付、退款、主扫统一下单
 * <AUTHOR>
 *
 */

@RestController
@Validated
public class PayBaseApiImpl implements PayBaseApi {
    @Resource
    PayOrderService orderService;
    @Resource
    PayRefundService refundService;
    @Resource
    PayPreOrderService preOrderService;
    @Resource
    PayPreFinishService preFinishService;
    @Resource
    PayPreCancelService preCancelService;
    @Resource
    PayPreFinishCancelService preFinishCancelService;


    /**
     * 条码支付
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<ScanRespDTO> scan(ScanReqDTO reqDTO) {

        PayOrderRespDTO orderRespDTO = orderService.scan(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            ScanRespDTO respDTO = new ScanRespDTO() {{
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        String errorMsg = StringUtils.isNotBlank(orderRespDTO.getChannelErrorMsg()) ? orderRespDTO.getChannelErrorMsg() : PayOrderStatusRespEnum.getName(orderRespDTO.getStatus());
        return CommonResult.error(-1, errorMsg);
    }

    @Override
    public CommonResult<ActiveScanRespDTO> activeScan(ActiveScanReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.activeScan(reqDTO);

        // 主扫只要下单成功了就返回成功, 通过ChannelCode来判断
        String channelErrorCode = orderRespDTO.getChannelErrorCode();
        String channelOrderNo = orderRespDTO.getChannelOrderNo();
        if (StringUtils.isNotBlank(channelErrorCode) || StringUtils.isBlank(channelOrderNo)) {
            return CommonResult.error(-1, "下单失败，失败原因：" + orderRespDTO.getChannelErrorMsg());
        }
        ActiveScanRespDTO respDTO = new ActiveScanRespDTO() {{
            setOrderNo(orderRespDTO.getOutTradeNo());
            setChannelOrderNo(orderRespDTO.getChannelOrderNo());
            setQrCode(orderRespDTO.getExtraParams().get("code"));
        }};
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<MiniRespDTO> mini(MiniReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.activeScan(reqDTO);

        // 主扫只要下单成功了就返回成功, 通过ChannelCode来判断
        String channelErrorCode = orderRespDTO.getChannelErrorCode();
        String channelOrderNo = orderRespDTO.getChannelOrderNo();
        if (StringUtils.isNotBlank(channelErrorCode) || StringUtils.isBlank(channelOrderNo)) {
            return CommonResult.error(-1, "下单失败，失败原因：" + orderRespDTO.getChannelErrorMsg());
        }
        MiniRespDTO respDTO = new MiniRespDTO() {{
            setOrderNo(orderRespDTO.getOutTradeNo());
            setChannelOrderNo(orderRespDTO.getChannelOrderNo());
            setPrepayId(orderRespDTO.getExtraParams().get("prepayId"));
            setPaySign(orderRespDTO.getExtraParams().get("paySign"));
            setAppId(orderRespDTO.getExtraParams().get("appId"));
            setTimeStamp(orderRespDTO.getExtraParams().get("timeStamp"));
            setNonceStr(orderRespDTO.getExtraParams().get("nonceStr"));
        }};
        return CommonResult.success(respDTO);
    }

    /**
     * 退款
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<RefundRespDTO> refund(RefundReqDTO reqDTO) {
        PayRefundRespDTO refundRespDTO = refundService.refund(reqDTO);
        // 退款只有明确的失败了才算失败，其余的默认成功
        if (PayRefundStatusRespEnum.isFailure(refundRespDTO.getStatus())) {
            return CommonResult.error(-1, "退款失败，失败原因：" + refundRespDTO.getChannelErrorMsg());
        }
        RefundRespDTO respDTO = new RefundRespDTO() {{
            setRefundNo(refundRespDTO.getOutRefundNo());
            setChannelRefundNo(refundRespDTO.getChannelRefundNo());
            setAccRefundNo(refundRespDTO.getAccRefundNo());
            setSuccessTime(refundRespDTO.getSuccessTime());
        }};
        return CommonResult.success(respDTO);
    }

    @Override
    public CommonResult<QueryOrderRespDTO> query(QueryOrderReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.query(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            QueryOrderRespDTO respDTO = new QueryOrderRespDTO() {{
                setPlatform(reqDTO.getPlatform());
                setStatus(orderRespDTO.getStatus());
                setAccountType(orderRespDTO.getAccountType());
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setPayPrice(orderRespDTO.getPayPrice());
                setRefundPrice(orderRespDTO.getRefundPrice());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    /**
     * 预授权条码支付
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<PreScanRespDTO> preScan(PreScanReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = preOrderService.scan(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            PreScanRespDTO respDTO = new PreScanRespDTO() {{
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    /**
     * 预授权完成
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<PreFinishRespDTO> preFinish(PreFinishReqDTO reqDTO) {
        PayRefundRespDTO refundRespDTO = preFinishService.preFinish(reqDTO);
        // 只有明确的成功才认为支付成功
        if (PayRefundStatusRespEnum.isSuccess(refundRespDTO.getStatus())) {
            PreFinishRespDTO respDTO = new PreFinishRespDTO() {{
                setFinishOrderNo(refundRespDTO.getOutRefundNo());
                setSuccessTime(refundRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, "预授权完成失败，失败原因：" + refundRespDTO.getChannelErrorMsg());
    }

    /**
     * 预授权取消
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<PreCancelRespDTO> preCancel(PreCancelReqDTO reqDTO) {
        PayRefundRespDTO refundRespDTO = preCancelService.preCancel(reqDTO);
        // 只有明确的失败才认为失败，其余默认成功
        if (PayRefundStatusRespEnum.isFailure(refundRespDTO.getStatus())) {
            return CommonResult.error(-1, "预授权撤销失败，失败原因：" + refundRespDTO.getChannelErrorMsg());
        }
        PreCancelRespDTO respDTO = new PreCancelRespDTO() {{
            setCancelOrderNo(refundRespDTO.getOutRefundNo());
            setSuccessTime(refundRespDTO.getSuccessTime());
        }};
        return CommonResult.success(respDTO);
    }

    /**
     * 预授权完成取消
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<PreFinishCancelRespDTO> preFinishCancel(PreFinishCancelReqDTO reqDTO) {
        PayRefundRespDTO refundRespDTO = preFinishCancelService.preFinishCancel(reqDTO);
        // 只有明确的失败才认为失败，其余默认成功
        if (PayRefundStatusRespEnum.isFailure(refundRespDTO.getStatus())) {
            return CommonResult.error(-1, "退款失败，失败原因：" + refundRespDTO.getChannelErrorMsg());
        }
        PreFinishCancelRespDTO respDTO = new PreFinishCancelRespDTO() {{
            setFinishCancelNo(refundRespDTO.getOutRefundNo());
            setSuccessTime(refundRespDTO.getSuccessTime());
        }};
        return CommonResult.success(respDTO);

    }

    /**
     * 会员卡支付
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<VipRespDTO> vip(VipReqDTO reqDTO) {
        PayOrderRespDTO orderRespDTO = orderService.vip(reqDTO);
        if (PayOrderStatusRespEnum.isSuccess(orderRespDTO.getStatus())) {
            VipRespDTO respDTO = new VipRespDTO() {{
                setOrderNo(orderRespDTO.getOutTradeNo());
                setChannelOrderNo(orderRespDTO.getChannelOrderNo());
                setAccOrderNo(orderRespDTO.getAccTradeNo());
                setSuccessTime(orderRespDTO.getSuccessTime());
            }};
            return CommonResult.success(respDTO);
        }
        return CommonResult.error(-1, orderRespDTO.getChannelErrorMsg());
    }

    /**
     * 会员卡退款
     * @param reqDTO
     * @return
     */
    @Override
    public CommonResult<RefundRespDTO> vipRefund(RefundVipReqDTO reqDTO) {
        PayRefundRespDTO refundRespDTO = refundService.vipRefund(reqDTO);
        // 退款只有明确的失败了才算失败，其余的默认成功
        if (PayRefundStatusRespEnum.isFailure(refundRespDTO.getStatus())) {
            return CommonResult.error(-1, "会员卡退款失败，失败原因：" + refundRespDTO.getChannelErrorMsg());
        }
        RefundRespDTO respDTO = new RefundRespDTO() {{
            setRefundNo(refundRespDTO.getOutRefundNo());
            setChannelRefundNo(refundRespDTO.getChannelRefundNo());
            setSuccessTime(refundRespDTO.getSuccessTime());
        }};
        return CommonResult.success(respDTO);
    }


}
