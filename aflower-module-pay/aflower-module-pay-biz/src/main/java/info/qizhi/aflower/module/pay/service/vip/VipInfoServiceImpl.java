package info.qizhi.aflower.module.pay.service.vip;

import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.module.pay.api.vip.dto.VipQueryReqDTO;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.util.ChannelUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;


/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/18 10:48
 */
@Service
public class VipInfoServiceImpl implements VipInfoService {

    @Resource
    PayConfigService configService;

    @Override
    public VipInfoRespDTO vipInfo(VipQueryReqDTO reqDTO) {

        // 根据平台以及授权码得到渠道
        String channelCode = reqDTO.getPlatform() + "_vip";

        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

        // 调用三方接口
        VipInfoReqDTO vipInfoReqDTO = new VipInfoReqDTO() {{
            setKeyword(reqDTO.getKeyword());
            setType(reqDTO.getType());
        }};


        return client.getVipInfo(vipInfoReqDTO);

    }
}
