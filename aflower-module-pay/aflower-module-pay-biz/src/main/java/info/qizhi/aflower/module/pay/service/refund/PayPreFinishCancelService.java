package info.qizhi.aflower.module.pay.service.refund;


import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreFinishCancelReqDTO;

/**
 * 预授权完成撤销记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PayPreFinishCancelService {

    /**
     * 发起预授权完成撤销
     * @param reqDTO 请求参数
     * @return 预授权完成撤销结果
     */
    PayRefundRespDTO preFinishCancel(PreFinishCancelReqDTO reqDTO);

}
