package info.qizhi.aflower.module.pay.service.refund;

import cn.hutool.extra.spring.SpringUtil;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreFinishCancelReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayFinishCancelDO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayPreFinishDO;
import info.qizhi.aflower.module.pay.dal.mysql.refund.PayFinishCancelMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.util.ChannelUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 预授权完成撤销记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PayPreFinishCancelServiceImpl implements PayPreFinishCancelService {

    private static final String OPERATE_TYPE_PRE_FINISH_CANCEL = "PRE_FINISH_CANCEL";

    @Resource
    PayFinishCancelMapper finishCancelMapper;
    @Resource
    PayPreFinishService preFinishService;
    @Resource
    PayConfigService configService;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayPreFinishCancelServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public PayRefundRespDTO preFinishCancel(PreFinishCancelReqDTO reqDTO) {
        try {
            PayPreFinishDO preFinishDO = validateAndGetPreFinish(reqDTO.getFinishOrderNo());
            String channelCode = preFinishDO.getChannelCode();
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayRefundUnifiedReqDTO unifiedReqDTO = buildRefundUnifiedReqDTO(reqDTO, preFinishDO);

            PayRefundRespDTO respDTO = client.unifiedRefund(unifiedReqDTO);
            if (respDTO == null) {
                throw new ServiceException(-1, "预授权完成取消失败！");
            }
            PayFinishCancelDO finishCancelDO = buildFinishCancel(reqDTO, respDTO, preFinishDO);
            finishCancelMapper.insert(finishCancelDO);

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl preFinishCancel error ======", e);
            throw new ServiceException(-1, "预授权完成撤销失败！");
        }
    }

    private PayPreFinishDO validateAndGetPreFinish (String finishNo) {
        PayPreFinishDO preFinishDO = preFinishService.getOneByFinishNo(finishNo);
        if (preFinishDO == null) {
            throw new ServiceException(-1, "预授权订单未完成，不能进行完成撤销");
        }
        return preFinishDO;
    }

    private PayRefundUnifiedReqDTO buildRefundUnifiedReqDTO(PreFinishCancelReqDTO reqDTO, PayPreFinishDO preFinishDO) {
        PayRefundUnifiedReqDTO unifiedReqDTO = new PayRefundUnifiedReqDTO();
        unifiedReqDTO.setOperateType(OPERATE_TYPE_PRE_FINISH_CANCEL);
        unifiedReqDTO.setAccountType(preFinishDO.getAccountType());
        unifiedReqDTO.setOutTradeNo(reqDTO.getFinishOrderNo());
        unifiedReqDTO.setChannelOrderNo(preFinishDO.getChannelFinishNo());
        unifiedReqDTO.setRefundPrice(reqDTO.getRefundPrice());

        return unifiedReqDTO;
    }

    private PayFinishCancelDO buildFinishCancel(PreFinishCancelReqDTO reqDTO, PayRefundRespDTO respDTO, PayPreFinishDO preFinishDO) {
        PayFinishCancelDO finishCancelDO = new PayFinishCancelDO();
        finishCancelDO.setHcode(reqDTO.getHcode());
        finishCancelDO.setMerchantNo(respDTO.getMerchantNo());
        finishCancelDO.setFinishCancelNo(respDTO.getOutRefundNo());
        finishCancelDO.setAccountType(preFinishDO.getAccountType());
        finishCancelDO.setFinishNo(reqDTO.getFinishOrderNo());
        finishCancelDO.setRefundPrice(reqDTO.getRefundPrice());
        finishCancelDO.setStatus(respDTO.getStatus());
        finishCancelDO.setChannelFinishCancelNo(respDTO.getChannelRefundNo());
        finishCancelDO.setSuccessTime(respDTO.getSuccessTime());
        return finishCancelDO;
    }

}
