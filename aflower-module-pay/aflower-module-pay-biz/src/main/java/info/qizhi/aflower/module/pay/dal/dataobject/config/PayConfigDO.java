package info.qizhi.aflower.module.pay.dal.dataobject.config;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;


/**
 * 支付配置 DO
 * 
 * <AUTHOR>
 * @date 2024/05/15 22:37
 */

@TableName(value = "pay_config", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class PayConfigDO extends BaseDO {


    private Long id;

    /**
     * 集团
     */
    private String gcode;
    /**
     * 门店编号
     */
    private String hcode;

    /**
     * 支付平台：FUIOU-富友；ALIPAY-支付宝；WXPAY-微信支付
     */
    private String platform;

    /**
     * 机构号
     */
    private String orgNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 终端号
     */
    private String termNo;

    /**
     * appid
     */
    private String appid;

    /**
     * 商户密钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 订单号前缀
     */
    private String orderPrefix;

    /**
     * 是否是集团配置
     */
    private String isGroup;


}
