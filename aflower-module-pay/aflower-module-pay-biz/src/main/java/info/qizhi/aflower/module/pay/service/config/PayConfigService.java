package info.qizhi.aflower.module.pay.service.config;

import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigCreateReqVO;
import info.qizhi.aflower.module.pay.controller.admin.config.vo.PayConfigUpdateReqVO;
import info.qizhi.aflower.module.pay.dal.dataobject.config.PayConfigDO;
import jakarta.validation.Valid;

/**
 * 
 * 支付配置
 * <AUTHOR>
 * @date 2024/05/15 22:02
 */
public interface PayConfigService {
    /**
     * 添加支付配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long addConfig(@Valid PayConfigCreateReqVO createReqVO);

    /**
     * 更新支付配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfig(@Valid PayConfigUpdateReqVO updateReqVO);

    /**
     * 根据条件获取渠道
     *
     * @param hcode      门店代码
     * @param platform   支付平台
     * @return 数量
     */
    PayConfigDO getConfigByHcodeAndPlatform(String hcode, String platform);


    /**
     * 获得指定编号的支付客户端
     *
     * @param key = hcode + '_' + channelCode
     * @return 支付客户端
     */
    PayClient getPayClient(String key);
}
