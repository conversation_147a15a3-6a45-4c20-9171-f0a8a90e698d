package info.qizhi.aflower.module.pay.service.refund;

import cn.hutool.extra.spring.SpringUtil;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.refund.PayRefundStatusRespEnum;
import info.qizhi.aflower.module.pay.api.base.dto.RefundReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.RefundVipReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayOrderDO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayRefundDO;
import info.qizhi.aflower.module.pay.dal.mysql.refund.PayRefundMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.service.order.PayOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.HashMap;
import java.util.Map;

/**
 * 退款订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PayRefundServiceImpl implements PayRefundService {
    @Resource
    PayRefundMapper payRefundMapper;
    @Resource
    PayOrderService orderService;
    @Resource
    PayConfigService configService;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayRefundServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public PayRefundRespDTO refund(RefundReqDTO reqDTO) {
        try {
            PayOrderDO payOrderDO = validateAndGetPayOrder(reqDTO.getPayOrderNo());
            String channelCode = payOrderDO.getChannelCode();
            if (StringUtils.isBlank(channelCode)) {
                throw new ServiceException(-1, "渠道不存在");
            }
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayRefundUnifiedReqDTO unifiedReqDTO = buildRefundUnifiedReqDTO(reqDTO, payOrderDO);
            PayRefundRespDTO respDTO = client.unifiedRefund(unifiedReqDTO);

            if (respDTO == null) {
                throw new ServiceException(-1, "退款失败");
            }

            PayRefundDO refundDO = buildRefundDO(reqDTO, respDTO, payOrderDO);
            payRefundMapper.insert(refundDO);

            if (PayRefundStatusRespEnum.isSuccess(respDTO.getStatus())) {
                orderService.updateRefundPrice(reqDTO.getPayOrderNo(), reqDTO.getRefundPrice());
            }

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl refund error ======", e);
            throw new ServiceException(-1, "退款失败！");
        }
    }

    @Override
    public PayRefundRespDTO vipRefund(RefundVipReqDTO reqDTO) {
        // 校验支付订单号
        PayOrderDO payOrderDO = orderService.getOneByOrderNo(reqDTO.getPayOrderNo());
        if (payOrderDO == null) {
            throw new ServiceException(700, "支付订单不存在");
        }

        // 用日志里面的支付渠道
        String channelCode = payOrderDO.getChannelCode();
        PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

        PayRefundUnifiedReqDTO unifiedReqDTO = new PayRefundUnifiedReqDTO() {{
            setOperateType("BAR_REFUND");
            setAccountType("VIP");
            setOutTradeNo(reqDTO.getPayOrderNo());
            setChannelOrderNo(payOrderDO.getChannelOrderNo());
            setPayPrice(reqDTO.getPayPrice());
            setRefundPrice(reqDTO.getRefundPrice());
            Map<String, String> channelExtras = new HashMap<>();
            channelExtras.put("vipId", reqDTO.getVipId());
            setChannelExtras(channelExtras);
        }};

        PayRefundRespDTO respDTO = client.unifiedRefund(unifiedReqDTO);
        // 添加退款记录
        PayRefundDO refundDO = new PayRefundDO() {{
            setHcode(reqDTO.getHcode());
            setRefundNo(respDTO.getOutRefundNo());
            setAccountType("VIP");
            setRefundPrice(reqDTO.getRefundPrice());
            setStatus(respDTO.getStatus());
            setPayOrderNo(reqDTO.getPayOrderNo());
            setPayPrice(reqDTO.getPayPrice());
            setMerchantNo(respDTO.getMerchantNo());
            setChannelRefundNo(respDTO.getChannelRefundNo());
            setSuccessTime(respDTO.getSuccessTime());
        }};
        payRefundMapper.insert(refundDO);
        // 退款成功后，更新支付订单的退款金额
        if (PayRefundStatusRespEnum.isSuccess(respDTO.getStatus())) {
            orderService.updateRefundPrice(reqDTO.getPayOrderNo(), reqDTO.getRefundPrice());
        }
        return respDTO;
    }

    private PayOrderDO validateAndGetPayOrder(String orderNo) {
        PayOrderDO payOrderDO = orderService.getOneByOrderNo(orderNo);
        if (payOrderDO == null) {
            throw new ServiceException(-1, "支付订单不存在");
        }
        String channelCode = payOrderDO.getChannelCode();
        if (StringUtils.isBlank(channelCode)) {
            throw new ServiceException(-1, "支付渠道不存在");
        }
        return payOrderDO;
    }

    private PayRefundUnifiedReqDTO buildRefundUnifiedReqDTO(RefundReqDTO reqDTO, PayOrderDO payOrderDO) {
        PayRefundUnifiedReqDTO unifiedReqDTO = new PayRefundUnifiedReqDTO();
        unifiedReqDTO.setAccountType(payOrderDO.getAccountType());
        unifiedReqDTO.setOutTradeNo(payOrderDO.getOrderNo());
        unifiedReqDTO.setChannelOrderNo(payOrderDO.getChannelOrderNo());
        unifiedReqDTO.setPayPrice(reqDTO.getPayPrice());
        unifiedReqDTO.setRefundPrice(reqDTO.getRefundPrice());

        return unifiedReqDTO;
    }

    private PayRefundDO buildRefundDO(RefundReqDTO reqDTO, PayRefundRespDTO respDTO, PayOrderDO payOrderDO) {
        PayRefundDO refundDO = new PayRefundDO();
        refundDO.setHcode(reqDTO.getHcode());
        refundDO.setRefundNo(respDTO.getOutRefundNo());
        refundDO.setAccountType(payOrderDO.getAccountType());
        refundDO.setRefundPrice(reqDTO.getRefundPrice());
        refundDO.setStatus(respDTO.getStatus());
        refundDO.setPayOrderNo(reqDTO.getPayOrderNo());
        refundDO.setPayPrice(reqDTO.getPayPrice());
        refundDO.setMerchantNo(respDTO.getMerchantNo());
        refundDO.setChannelRefundNo(respDTO.getChannelRefundNo());
        refundDO.setAccRefundNo(respDTO.getAccRefundNo());
        refundDO.setSuccessTime(respDTO.getSuccessTime());
        return refundDO;
    }

}
