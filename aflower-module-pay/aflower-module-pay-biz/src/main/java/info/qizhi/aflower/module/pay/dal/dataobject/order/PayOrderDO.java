package info.qizhi.aflower.module.pay.dal.dataobject.order;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 普通支付订单 DO
 *
 * <AUTHOR>
 */
@TableName("pay_order")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
public class PayOrderDO extends PayOrderCommonDO {
    /**
     * 退款金额，单位：分
     */
    private Long refundPrice;
}
