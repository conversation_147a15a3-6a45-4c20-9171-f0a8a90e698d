package info.qizhi.aflower.module.pay.dal.dataobject.refund;

import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 *
 */
@TableName("pay_pre_finish")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayPreFinishDO extends BaseDO {
    /**
     * 退款单编号，数据库自增
     */
    private Long id;
    /**
     * 酒店代码
     */
    private String hcode;
    /**
     * 支付的渠道代码
     */
    private String channelCode;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户预授权完成单号
     *
     */
    private String finishNo;
    /**
     * 钱包类型：微信-WECHAT；支付宝-ALIPAY
     */
    private String accountType;
    /**
     * 商户支付订单号
     *
     */
    private String payOrderNo;
    /**
     * 完成状态
     *
     */
    private Integer status;
    /**
     * 支付金额，单位：分
     */
    private Long payPrice;
    /**
     * 完成金额，单位：分
     */
    private Long finishPrice;
    /**
     * 渠道完成单号
     */
    private String channelFinishNo;
    /**
     * 完成时间
     */
    private LocalDateTime successTime;
}
