package info.qizhi.aflower.module.pay.service.refund;


import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreCancelReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayPreCancelDO;

/**
 * 预授权撤销记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PayPreCancelService {

    /**
     * 发起预授权撤销
     * @param reqDTO 请求参数
     * @return 预授权撤销结果
     */
    PayRefundRespDTO preCancel(PreCancelReqDTO reqDTO);


}
