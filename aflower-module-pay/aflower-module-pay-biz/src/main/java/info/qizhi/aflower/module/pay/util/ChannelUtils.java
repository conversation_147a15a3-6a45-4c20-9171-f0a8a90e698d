package info.qizhi.aflower.module.pay.util;

import info.qizhi.aflower.framework.pay.core.enums.order.fuiou.FuiouBusinessEnum;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 
 * 支付渠道工具类
 * <AUTHOR>
 * @date 2024/05/30 17:53
 */
public class ChannelUtils {

    /**
     * 微信授权码开头
     */
    private static final List<String> WECHAT_AUTHCODE_PREFIX_LIST = Arrays.asList("10","11","12","13","14","15");

    /**
     * 支付宝授权码开头
     */
    private static final List<String> ALIPAY_AUTHCODE_PREFIX_LIST = Arrays.asList("25","26","27","28","29","30");

    public static String getAccountTypeByAuthCode(String authCode) {
        String accountType = "";
        if (WECHAT_AUTHCODE_PREFIX_LIST.contains(authCode.substring(0, 2))) {
            accountType = "WECHAT";
        } else if (ALIPAY_AUTHCODE_PREFIX_LIST.contains(authCode.substring(0, 2))) {
            accountType = "ALIPAY";
        }
        return accountType;
    }

    public static String getChannelCode(String platform, String merchantOrderNo) {

        String channelCode = "";
        if ("fuiou".equals(platform)) {
            String orderNoSuffix = merchantOrderNo.substring(merchantOrderNo.length() - 3);
            channelCode = FuiouBusinessEnum.getByOrderSuffix(orderNoSuffix).getChannelCode();
        }
        return channelCode;
    }
}
