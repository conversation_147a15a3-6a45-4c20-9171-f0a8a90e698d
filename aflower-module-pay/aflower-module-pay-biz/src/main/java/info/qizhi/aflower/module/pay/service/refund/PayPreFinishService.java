package info.qizhi.aflower.module.pay.service.refund;


import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.PreFinishReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayPreFinishDO;

/**
 * 预授权完成记录 Service 接口
 *
 * <AUTHOR>
 */
public interface PayPreFinishService {

    /**
     * 预授权完成
     * @param reqDTO 请求参数
     * @return 预授权完成结果
     */
    PayRefundRespDTO preFinish(PreFinishReqDTO reqDTO);

    PayPreFinishDO getOneByFinishNo(String finishNo);


}
