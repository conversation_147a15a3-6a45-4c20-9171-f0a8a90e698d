package info.qizhi.aflower.module.pay.convert.order;

import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.ScanReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.ScanRespDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 支付订单 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface PayOrderConvert {

    PayOrderConvert INSTANCE = Mappers.getMapper(PayOrderConvert.class);

    PayOrderUnifiedReqDTO convert(ScanReqDTO bean);

    ScanRespDTO convert(PayOrderRespDTO bean);

}
