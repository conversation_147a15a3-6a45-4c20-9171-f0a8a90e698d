package info.qizhi.aflower.module.pay.api.vip;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.module.pay.api.vip.dto.VipQueryReqDTO;
import info.qizhi.aflower.module.pay.api.vip.dto.VipQueryRespDTO;
import info.qizhi.aflower.module.pay.service.vip.VipInfoService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/13 16:14
 */
@RestController
@Validated
public class VipInfoApiImpl implements VipInfoApi {

    @Resource
    VipInfoService vipInfoService;

    @Override
    public CommonResult<VipQueryRespDTO> query(VipQueryReqDTO reqDTO) {

        VipInfoRespDTO respDTO = vipInfoService.vipInfo(reqDTO);

        if (respDTO.getStatus() != 1) {
            return CommonResult.error(respDTO.getErrorCode(), respDTO.getErrorMsg());
        }
        return CommonResult.success(new VipQueryRespDTO() {{
            setVipId(respDTO.getVipId());
            setVipName(respDTO.getVipName());
            setMobile(respDTO.getMobile());
            setBalance(respDTO.getBalance());
            setGiveBalance(respDTO.getGiveBalance());
        }});
    }
}
