package info.qizhi.aflower.module.pay.service.refund;

import cn.hutool.extra.spring.SpringUtil;
import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.refund.PayRefundStatusRespEnum;
import info.qizhi.aflower.module.pay.api.base.dto.PreFinishReqDTO;
import info.qizhi.aflower.module.pay.dal.dataobject.order.PayPreOrderDO;
import info.qizhi.aflower.module.pay.dal.dataobject.refund.PayPreFinishDO;
import info.qizhi.aflower.module.pay.dal.mysql.refund.PayPreFinishMapper;
import info.qizhi.aflower.module.pay.dal.mysql.refund.PayRefundMapper;
import info.qizhi.aflower.module.pay.service.config.PayConfigService;
import info.qizhi.aflower.module.pay.service.order.PayPreOrderService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 预授权完成记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class PayPreFinishServiceImpl implements PayPreFinishService {

    private static final String OPERATE_TYPE_PRE_FINISH = "PRE_FINISH";

    @Resource
    PayPreFinishMapper payPreFinishMapper;
    @Resource
    PayPreOrderService preOrderService;
    @Resource
    PayConfigService configService;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private PayPreFinishServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }


    @Override
    public PayRefundRespDTO preFinish(PreFinishReqDTO reqDTO) {
        try {
            PayPreOrderDO payPreOrderDO = preOrderService.validateAndGetPreOrder(reqDTO.getPayOrderNo());
            String channelCode = payPreOrderDO.getChannelCode();
            PayClient client = configService.getPayClient(reqDTO.getHcode() + "_" + channelCode);

            PayRefundUnifiedReqDTO unifiedReqDTO = buildRefundUnifiedReqDTO(reqDTO, payPreOrderDO);
            PayRefundRespDTO respDTO = client.unifiedRefund(unifiedReqDTO);
            if (respDTO == null) {
                throw new ServiceException(-1, "预授权完成失败！");
            }

            PayPreFinishDO finishDO = buildFinishDO(reqDTO, respDTO, payPreOrderDO);
            payPreFinishMapper.insert(finishDO);
            // 预授权完成成功后，更新预授权支付订单的完成金额
            if (PayRefundStatusRespEnum.isSuccess(respDTO.getStatus())) {
                preOrderService.updateFinishPrice(reqDTO.getPayOrderNo(), reqDTO.getFinishPrice());
            }

            return respDTO;
        } catch (Exception e) {
            log.error("====== PayBaseServiceImpl preFinish error ======", e);
            throw new ServiceException(-1, "预授权完成失败！");
        }
    }

    private PayRefundUnifiedReqDTO buildRefundUnifiedReqDTO(PreFinishReqDTO reqDTO, PayPreOrderDO preOrderDO) {
        PayRefundUnifiedReqDTO unifiedReqDTO = new PayRefundUnifiedReqDTO();
        unifiedReqDTO.setOperateType(OPERATE_TYPE_PRE_FINISH);
        unifiedReqDTO.setAccountType(preOrderDO.getAccountType());
        unifiedReqDTO.setOutTradeNo(preOrderDO.getOrderNo());
        unifiedReqDTO.setChannelOrderNo(preOrderDO.getChannelOrderNo());
        unifiedReqDTO.setPayPrice(reqDTO.getPayPrice());
        unifiedReqDTO.setRefundPrice(reqDTO.getFinishPrice());
        return unifiedReqDTO;
    }

    private PayPreFinishDO buildFinishDO(PreFinishReqDTO reqDTO, PayRefundRespDTO respDTO, PayPreOrderDO preOrderDO) {
        PayPreFinishDO finishDO = new PayPreFinishDO();
        finishDO.setHcode(reqDTO.getHcode());
        finishDO.setChannelCode(preOrderDO.getChannelCode());
        finishDO.setFinishNo(respDTO.getOutRefundNo());
        finishDO.setAccountType(preOrderDO.getAccountType());
        finishDO.setFinishPrice(reqDTO.getFinishPrice());
        finishDO.setStatus(respDTO.getStatus());
        finishDO.setPayOrderNo(reqDTO.getPayOrderNo());
        finishDO.setPayPrice(reqDTO.getPayPrice());
        finishDO.setMerchantNo(respDTO.getMerchantNo());
        finishDO.setChannelFinishNo(respDTO.getChannelRefundNo());
        finishDO.setSuccessTime(respDTO.getSuccessTime());
        return finishDO;
    }

    @Override
    public PayPreFinishDO getOneByFinishNo(String finishNo) {
        return payPreFinishMapper.selectOne("finish_no", finishNo);
    }
}
