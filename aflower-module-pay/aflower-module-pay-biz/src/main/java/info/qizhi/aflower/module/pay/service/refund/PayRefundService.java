package info.qizhi.aflower.module.pay.service.refund;

import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.module.pay.api.base.dto.RefundReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.RefundVipReqDTO;

/**
 * 退款订单 Service 接口
 *
 * <AUTHOR>
 */
public interface PayRefundService {

    /**
     * 发起退款
     * @param reqDTO 退款请求参数
     * @return 退款结果
     */
    PayRefundRespDTO refund(RefundReqDTO reqDTO);

    /**
     * 发起会员卡退款
     * @param reqDTO 请求参数
     * @return 退款结果
     */
    PayRefundRespDTO vipRefund(RefundVipReqDTO reqDTO);

}
