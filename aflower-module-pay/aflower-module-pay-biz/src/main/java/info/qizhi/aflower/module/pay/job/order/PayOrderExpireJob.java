package info.qizhi.aflower.module.pay.job.order;

import info.qizhi.aflower.framework.tenant.core.job.TenantJob;
import info.qizhi.aflower.module.pay.service.order.PayOrderService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 支付订单的过期 Job
 *
 * 支付超过过期时间时，支付渠道是不会通知进行过期，所以需要定时进行过期关闭。
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class PayOrderExpireJob {

    @Resource
    private PayOrderService orderService;

    @XxlJob("payOrderExpireJob")
    @TenantJob // 多租户
    public void execute(String param) {

    }

}
