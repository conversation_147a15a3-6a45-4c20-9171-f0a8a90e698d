CREATE TABLE `pay_config`
(
    `id` bigint(20) NOT NULL AUTO INCREMENT COMMENT 'id',
    `gcode` varchar(32) NOT NULL COMMENT '集团代码',
    `hcode` varchar(32) NOT NULL COMMENT '门店代码',
    `platform` varchar(32) NOT NULL COMMENT '支付平台',
    `org_no` varchar(32) NOT NULL COMMENT '机构号',
    `merchant_no` varchar(32) NOT NULL COMMENT '商户号',
    `appid` varchar(32) DEFAULT NULL COMMENT 'appid',
    `private_key` text NOT NULL COMMENT'商户密钥',
    `public_key` text NOT NULL COMMENT '平台公钥',
    `order_prefix` varchar(32) DEFAULT NULL COMMENT '订单号前缀',
    `is_group` int DEFAULT '0' COMMENT '是否集团支付配置：0-否，1-是',
	`creator` varchar(32) DEFAULT NULL COMMENT '创建人',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY(`id`)
)ENGINE=InnODB DEFAULT CHARSET=utf8mb4 COMMENT='支付配置';

CREATE TABLE `pay_order`
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`      varchar(32) NOT NULL COMMENT '商户号',
    `channel_code`     varchar(32) NOT NULL COMMENT '渠道代码',
    `order_no`         varchar(64) NOT NULL COMMENT '商户订单号',
    `price`            bigint      NOT NULL COMMENT '支付金额，单位-分',
    `notify_url`       varchar(1024)        DEFAULT '' COMMENT '回调地址',
    `status`           tinyint     NOT NULL COMMENT '支付状态',
    `success_time`     datetime             DEFAULT NULL COMMENT '支付成功时间',
    `notify_time`      datetime             DEFAULT NULL COMMENT '回调时间',
    `refund_price`     bigint      NOT NULL DEFAULT 0 COMMENT '退款金额，单位-分',
    `channel_order_no` varchar(64)          DEFAULT NULL COMMENT '渠道订单号',
    `creator`          varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='普通支付记录表';


CREATE TABLE `pay_refund`
(
    `id`                 bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`        varchar(32) NOT NULL COMMENT '商户号',
    `refund_no`          varchar(64) NOT NULL COMMENT '商户退款单号',
    `refund_price`       bigint      NOT NULL COMMENT '退款金额，单位-分',
    `pay_order_no`       varchar(64) NOT NULL COMMENT '商户支付订单号',
    `pay_price`          bigint      NOT NULL COMMENT '支付金额，单位-分',
    `status`             tinyint     NOT NULL COMMENT '退款状态',
    `success_time`       datetime             DEFAULT NULL COMMENT '退款成功时间',
    `channel_refund_no`  varchar(64)          DEFAULT NULL COMMENT '渠道订单号',
    `creator`            varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='退款记录表';


CREATE TABLE `pay_pre_order`
(
    `id`               bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`      varchar(32) NOT NULL COMMENT '商户号',
    `channel_code`     varchar(32) NOT NULL COMMENT '渠道代码',
    `order_no`         varchar(64) NOT NULL COMMENT '商户订单号',
    `price`            bigint      NOT NULL COMMENT '支付金额，单位-分',
    `notify_url`       varchar(1024)        DEFAULT '' COMMENT '回调地址',
    `status`           tinyint     NOT NULL COMMENT '支付状态',
    `success_time`     datetime             DEFAULT NULL COMMENT '支付成功时间',
    `notify_time`      datetime             DEFAULT NULL COMMENT '回调时间',
    `finish_price`     bigint      NOT NULL DEFAULT 0 COMMENT '完成金额，单位-分',
    `channel_order_no` varchar(64)          DEFAULT NULL COMMENT '渠道订单号',
    `creator`          varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`      datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预授权支付记录表';

CREATE TABLE `pay_pre_finish`
(
    `id`                 bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`        varchar(32) NOT NULL COMMENT '商户号',
    `finish_no`          varchar(64) NOT NULL COMMENT '商户完成单号',
    `finish_price`       bigint      NOT NULL COMMENT '完成金额，单位-分',
    `pay_order_no`       varchar(64) NOT NULL COMMENT '商户支付订单号',
    `pay_price`          bigint      NOT NULL COMMENT '支付金额，单位-分',
    `status`             tinyint     NOT NULL COMMENT '完成状态',
    `channel_finish_no`  varchar(64)          DEFAULT NULL COMMENT '渠道完成单号',
    `creator`            varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预授权完成记录表';

CREATE TABLE `pay_pre_cancel`
(
    `id`                 bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`        varchar(32) NOT NULL COMMENT '商户号',
    `cancel_no`          varchar(64) NOT NULL COMMENT '商户预授权撤销单号',
    `pay_order_no`       varchar(64) NOT NULL COMMENT '商户支付订单号',
    `pay_price`          bigint      NOT NULL COMMENT '支付金额，单位-分',
    `status`             tinyint     NOT NULL COMMENT '撤销状态',
    `channel_finish_no`  varchar(64)          DEFAULT NULL COMMENT '渠道预授权撤销单号',
    `creator`            varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预授权撤销记录表';

CREATE TABLE `pay_finish_cancel`
(
    `id`                        bigint      NOT NULL AUTO_INCREMENT COMMENT 'id',
    `merchant_no`               varchar(32) NOT NULL COMMENT '商户号',
    `finish_cancel_no`          varchar(64) NOT NULL COMMENT '商户预授权完成撤销单号',
    `finish_no`                 varchar(64) NOT NULL COMMENT '商户预授权完成单号',
    `refund_price`              bigint      NOT NULL COMMENT '撤销退款金额，单位-分',
    `status`                    tinyint     NOT NULL COMMENT '完成撤销状态',
    `channel_finish_cancel_no`  varchar(64)          DEFAULT NULL COMMENT '渠道完成单号',
    `creator`                   varchar(32)          DEFAULT NULL COMMENT '创建人',
    `create_time`               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`               datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='预授权完成撤销记录表';