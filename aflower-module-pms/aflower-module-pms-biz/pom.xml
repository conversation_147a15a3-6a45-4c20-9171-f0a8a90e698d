<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>info.qizhi</groupId>
        <artifactId>aflower-module-pms</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aflower-module-pms-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        pms模块，主要实现酒店收银相关业务
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-env</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-seata</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-sms</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.spullara.mustache.java</groupId>
            <artifactId>compiler</artifactId>
            <version>0.9.10</version>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-pms-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-marketing-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-member-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-sse-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-pay-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-report-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-hiii-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-order-sync-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-biz-ip</artifactId>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-security</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-job-plus</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-protection</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-excel</artifactId>
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-monitor</artifactId>
        </dependency>

        <!-- 发票相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-biz-invoice</artifactId>
        </dependency>

        <!--工具类-->
        <dependency>
            <groupId>io.github.biezhi</groupId>
            <artifactId>TinyPinyin</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-erp-api</artifactId>
            <version>2.0.1-snapshot</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>