# Common validation messages
gcode.notblank=Group code cannot be empty
gcode.notempty=Group code cannot be empty
hcode.notblank=Store code cannot be empty
hcode.notempty=Store code cannot be empty
no.notblank=Account number cannot be empty
accType.notblank=Account type cannot be empty
accType.instringenum=Invalid account type
fee.notnull=Amount cannot be null
fee.min=Amount must be greater than 0
subCode.notblank=Payment subject code cannot be empty
payValue.notblank=payValue cannot be empty
payCode.size=Code length cannot exceed 32 characters
bankCardNo.size=Bank card number length cannot exceed 20 characters
accDetail.size=Detail length cannot exceed 128 characters
remark.size=Remark length cannot exceed 255 characters
remark.size200=Remark length cannot exceed 200 characters
remark.size128=Remark length cannot exceed 128 characters
togetherCode.notempty=Guest code cannot be empty
consume.notnull=Consumption information cannot be null
Consume.subCode.notempty=Consumption subject cannot be empty
Pay.bankCardNo.size=Bank card number length cannot exceed 20 characters
originalAccNo.notblank=Original account number cannot be empty
accountList.notempty=Account list cannot be empty
guestName.notblank=Guest name cannot be empty
isTeam.notblank=Team status cannot be empty
accNoList.notempty=Account number list cannot be empty
isBookAccount.instringenum=Invalid booking account transfer format
accountNo.notempty=Account number cannot be empty
isCreditCheckout.notempty=Credit checkout status cannot be empty
checkoutOrderTogethers.notempty=Checkout guest list cannot be empty
no.notempty=Order number cannot be empty
isMain.notempty=Main order status cannot be empty
roomFeeSubCode.notempty=Room fee generation method cannot be empty
orderTogethers.notempty=Guest list cannot be empty
rCode.notempty=Room code cannot be empty
rNo.notempty=Room number cannot be empty
hcodes.notempty=Cash account associated store codes cannot be empty
payNo.notempty=Settlement account cannot be empty
cleanerAccount.notempty=Cleaner account cannot be empty
paymentAmount.notnull=Payment amount cannot be null
paymentAmount.min=Payment amount must be greater than or equal to 0
clearedAmount.notnull=Cleared amount cannot be null
refundEarlyTotalAmount.notnull=Refund early total amount cannot be null
writeOffAmount.notnull=Write-off amount cannot be null
changeAmount.notnull=Change amount cannot be null
totalWriteOffAmount.notnull=Total write-off amount cannot be null
afterTaxAmount.notnull=After-tax amount cannot be null
invoiceAmount.notnull=Invoice amount cannot be null
orderNumber.notempty=Order number cannot be empty
outOrderNo.max=Order number length cannot exceed 30
bookingNumber.notempty=Booking number cannot be empty
mainOrderNumber.notempty=Main order number cannot be empty
cashBillOrderNumber.notempty=Cash bill order number cannot be empty
billNumber.notempty=Bill number cannot be empty
sourceRoomCode.notblank=Source room code cannot be empty
targetRoomCode.notblank=Target room code cannot be empty
externalOrderRemark.size=Order remark length cannot exceed 2555 characters
roomFee.notnull=Room fee cannot be null
roomFee.min=Room fee must be greater than or equal to 0
isTeam.notempty=Team main account status cannot be empty
orderType.notblank=Order type cannot be empty
payment.notnull=Payment cannot be null
paymentMethod.notblank=Payment method cannot be empty
bookingOrOrderNumber.notempty=Booking or order number cannot be empty
guestSourceType.notempty=Guest source type; Individual, Member, Company, Agency cannot be empty
subjectType.notempty=Subject type; 0: Expense subject, 1: Payment subject cannot be empty
isHide.notempty=Hidden status; 0: No, 1: Yes cannot be empty
shiftNo.notempty=Shift number cannot be empty
recorder.notempty=Recorder cannot be empty
bizDate.notnull=date cannot be null
mealDate.notnull=date cannot be null
transactionStatus.notempty=Status; 0: Unsettled, 1: Settled, 2: Reversed, 3: Transferred out cannot be empty
isRev.notempty=Reversal entry status; 0: No, 1: Yes cannot be empty
isTurnOutIn.notempty=Transfer entry status cannot be empty
payBizDate.notnull=Settlement business date cannot be empty
isSplitAccount.notempty=Split account status; 0: No, 1: Yes cannot be empty
convertDepositAccount.notempty=Account created from deposit-to-security transfer; 1: Yes, 0: No cannot be empty
updateShiftNo.notempty=Shift number for data update cannot be empty
operator.notempty=Operator cannot be empty
cashAccountCode.notempty=Cash account code cannot be empty
isVerified.instringenum=Verification status; 0: Not verified, 1: Verified
accountingOrBusinessDate.instringenum=Accounting date or business date; 0: Accounting date, 1: Business date
accountName.notempty=Account name cannot be empty
accountName.size=Account name length cannot exceed 30
subjectCode.notempty=Subject code cannot be empty
subjectName.notempty=Subject name cannot be empty
enableStatus.notnull=Enable status cannot be empty; 0: Inactive, 1: Active
enableStatus.instringenum=Enable status must be 0: Inactive or 1: Active
accountCode.notempty=Account code cannot be empty
status.notempty=Status cannot be empty
sourceStatus.notempty=Source status cannot be empty
targetStatus.notempty=Target status cannot be empty
unitCode.notempty=Unit or agency code, team/travel agency code cannot be empty
unitName.notempty=Unit or agency, team/travel agency name cannot be empty
creditAccountType.instringenum=Account type must be either Credit Account or Prepaid Account
creditPayDays.instringenum=Account period type must be either Fixed Period or Permanent Period
creditQuotaType.instringenum=Credit quota type must be either Unlimited or Limited
creditValidType.instringenum=Valid period type must be either Permanent or Fixed
creditMinusAcc.instringenum=Credit allowance type; 0: Not allowed, 1: Allowed
creditMinusAcc.notempty=Credit allowance status cannot be empty
manualAdditionType.instringenum=Manual addition of account set; 0: Manual, 1: Automatic
enableStatusChange.instringenum=Status change must be either {0}
accountId.notnull=Account receivable ID cannot be null
accNos.notempty=The account entries to be verified cannot be empty
isVerify.instringenum=Verification status; 0: No 1: Yes; default is No
stateRed.instringenum=Status must be 'red' for reversal
startTime.notnull=Start time cannot be null
endTime.notnull=End time cannot be null
isNightAudit.notempty=Night audit status cannot be empty; 0: No, 1: Yes
channelCode.notempty=Channel code cannot be empty
orderSource.notempty=Order source cannot be empty
orderSource.instringenum=Order source is not within predefined values
guestSrcType.notempty=Guest source type cannot be empty
guestSrcType.instringenum=Guest source type is not within predefined values
bookType.notempty=Booking type cannot be empty
bookType.instringenum=Booking type is not within predefined values
checkinType.instringenum=Check-in type is not within predefined values
hourCode.instringenum=Hour room code is not within predefined values
platform.instringenum=Platform code error
guestName.size=Guest name (Member name, Contracted unit, or Agency name) cannot exceed 32 characters
teamName.size=Team name (Required for team bookings) cannot exceed 32 characters
teamName.notempty=Team name cannot be empty
contractNo.size=Contract number (Required for team bookings) cannot exceed 50 characters
contact.notempty=Contact person is required; if team booking, this saves the contact name
contact.size=Contact name length cannot exceed 32 characters
checkinPerson.size=Check-in person's name length cannot exceed 32 characters
isSendSms.notempty=SMS sending option is required; 0: No, 1: Yes
isSendSms.instringenum=SMS sending option must be either 0: No or 1: Yes
outOrderNo.size=External order number must not exceed 32 characters
batchList.notempty=Batch list cannot be empty
batchNo.notempty=Batch number cannot be empty
days.notnull=Days cannot be null
planCheckinTime.notnull=Planned check-in time cannot be null
planCheckoutTime.notnull=Planned check-out time cannot be null
bookRoomTypeList.notempty=Booking room type list cannot be empty
roomTypeCode.notempty=Room type code cannot be empty
breakfastCount.notnull=Breakfast count cannot be null
breakfastCount.min=Breakfast count cannot be less than 0
priceType.invalid=Price type is not in the preset range
roomCount.min=Room count cannot be less than 1
roomCount.notnull=Room count cannot be null
dailyPriceList.notempty=Daily price list cannot be empty
physicalRoomTypeCode.notempty=Physical room type code cannot be empty
virtualRoomTypeCode.notempty=Virtual room type code cannot be empty
isPreOccupied.notempty=Pre-occupied status cannot be empty; 0: No, 1: Yes
date.notempty=Date cannot be empty
startDate.notnull=Start date cannot be empty
endDate.notnull=End date cannot be empty
priceDates.notnull=Effective date cannot be empty
priceDate.notnull=Price date cannot be empty
bookingPrice.notnull=Booking price cannot be empty
discountPrice.notnull=Discount price cannot be empty
phone.invalidFormat=Invalid phone number format
delayMinute.notnull=Delay checkout minutes cannot be null
orderState.instringenum=Status must be one of: Pending Confirmation, Checked In, No-show, Cancelled, Rejected, or Completed
bookingDays.min=Booking days cannot be less than 1
roomTypes.notempty=Room type list cannot be empty
roomNum.notnull=Room booking count cannot be empty
roomNum.min=Room booking count must be greater than 0
dayPrices.notempty=Date price list cannot be empty
priceType.notempty=Price type cannot be empty
rooms.notempty=Room list cannot be empty
orderNos.notempty=Booking room number list cannot be empty
breakfastCount.max=Breakfast count cannot exceed 20
customRoomRate.notnull=Custom room rate cannot be null
isMeetingRoom.notempty=Must specify whether it is a meeting room; 0: Guest Room, 1: Meeting Room
teamType.notempty=Team type cannot be empty
teamType.invalid=Invalid team type
orderState.invalid=Invalid status
unitAgentCode.notempty=Unit/Agent code cannot be empty
commissionStrategyCode.notempty=Commission strategy code cannot be empty
roomPrice.notnull=Room price cannot be empty
guestName.notempty=Guest name cannot be empty
verificationStatus.notempty=Status cannot be empty; 0: Not verified, 1: Verified
commissionIdList.notempty=Commission ID list cannot be empty
isG.notempty=\u662F\u5426\u96C6\u56E2\u4F63\u91D1\u7B56\u7565;0\uFF1A\u5426 1\uFF1A\u662F\u4E0D\u80FD\u4E3A\u7A7A
strategyName.notempty=Strategy name cannot be empty
strategyName.size=Strategy name cannot exceed 32 characters
strategyName.size30=Strategy name cannot exceed 30 characters
companyType.notempty=Company type; 0: Agent, 1: Contracted unit, cannot be empty
companyType.invalid=Company type; 0: Agent, 1: Contracted unit, is out of range
isGrt.notempty=Group room type; 0: No, 1: Yes, cannot be empty
isGrt.invalid=Group room type; 0: No, 1: Yes, is out of range
brokerageType.notempty=Brokerage name; 0: Room-night fixed commission, 1: Percentage commission, cannot be empty
brokerageValue.notnull=Target value; commission value cannot be empty
isEnable.notempty=Is valid; 0: invalid, 1: valid, cannot be empty
id.notnull=ID cannot be null
calendarCode.notempty=Calendar code cannot be empty
calendarName.notempty=Calendar name cannot be empty
scope.notempty=Scope; 0: Single store use, 1: Group shared cannot be empty
calendarMode.notempty=Selection mode; 0: By week, 1: By day cannot be empty
calendarDate.notempty=Date; JSON object storing each day's date in the calendar settings cannot be empty
isUse.notempty=Usage status cannot be empty; 0: Not used, 1: In use. When referenced by other business, status changes to 1. Only the end date can be modified while in use.
isReversed.notempty=Reversal status cannot be empty
channelName.notempty=Channel name cannot be empty
channelName.size=Channel name cannot exceed 30 characters
isG.invalid=Group channel option can only be 0 or 1
channelType.notempty=Type cannot be empty
channelType.invalid=Type must be 0: direct sale or 1: distribution
isSys.notempty=System initialization option cannot be empty
isSys.invalid=System initialization option can only be 0 or 1
channelHcode.notempty=Channel store code cannot be empty
name.notempty=Name cannot be empty
name.size=Name cannot exceed 250 characters
name.size10=Name cannot exceed 10 characters
name.size32=Name cannot exceed 32 characters
reason.notempty=Reason cannot be empty
reason.size=Reason cannot exceed 250 characters
orderReasonType.notempty=Order reason type cannot be empty
orderReasonType.invalid=Order reason type must be {value}
type.notempty=Type cannot be empty
code.notempty=Code cannot be empty
otaDirectMgmt.notempty=OTA prepaid order settlement method - Direct; 1: to group account, 0: to store account cannot be empty
otaDirectMgmt.instringenum=OTA prepaid order settlement method - Direct; must be 0 (store account) or 1 (group account)
otaJoin.notempty=OTA prepaid order settlement method - Franchise; 1: to group account, 0: to store account cannot be empty
otaJoin.instringenum=OTA prepaid order settlement method - Franchise; must be 0 (store account) or 1 (group account)
otaAuth.notempty=OTA prepaid order settlement method - Managed; 1: to group account, 0: to store account cannot be empty
otaAuth.instringenum=OTA prepaid order settlement method - Managed; must be 0 (store account) or 1 (group account)
crsBrokerage.notempty=Central reservation commission collection; 0: No 1: Yes cannot be empty
crsBrokerage.instringenum=Central reservation commission collection; must be 0 (No) or 1 (Yes)
brokerageMode.notempty=Commission collection method; 0: Per order amount, 1: Per revenue cannot be empty
brokerageMode.instringenum=Commission collection method; must be 0 (Per order amount) or 1 (Per revenue)
brokerageRatio.notnull=Commission collection ratio cannot be empty
brokerageRatio.min=Minimum commission collection ratio is 0.01
value.notnull=Value cannot be empty
paramType.notempty=Parameter type cannot be empty
merchantTypeEmpty=Merchant type cannot be empty
merchantTypeNameEmpty=Merchant type name cannot be empty
memberShareEmpty=Member information sharing cannot be empty
memberShareInvalid=Member information sharing enum value is incorrect
memberPointShareEmpty=Balance and points sharing cannot be empty
memberPointShareInvalid=Balance and points sharing enum value is incorrect
storeFeeRecordEmpty=Member stored value amount entry destination cannot be empty
storeFeeRecordInvalid=Member stored value amount entry destination enum value is incorrect
taxRate.notnull=Tax rate cannot be empty
isGroupTaxRate.incorrect=The setting for group tax rate can only be 0 or 1
roomStateConfig.notnull=Personalized configuration cannot be empty
sex.notempty=Gender cannot be empty
idType.notempty=ID type cannot be empty
idNo.notempty=ID number cannot be empty
isBlack.enum=Blacklist status can only be 0 (normal) or 1 (blacklisted)
isSms.notempty=SMS reception status cannot be empty
isSms.enum=SMS reception status can only be 0 (not receiving) or 1 (receiving)
isMember.notempty=Membership status cannot be empty
isMember.enum=Membership status can only be 0 (no) or 1 (yes)
gtOrlt.enum=Greater than or less than status can only be 0 (greater than) or 1 (less than)
deviceType.notempty=Device type code cannot be empty
brandName.notempty=Device brand name cannot be empty
deviceCode.notempty=Device code cannot be empty
deviceVerName.notempty=Device model name cannot be empty
status.invalid=Status must be 0  or 1
goodsCode.notempty=Goods code cannot be empty
goodsName.notempty=Goods name cannot be empty
price.notnull=Price cannot be empty
num.notnull=Quantity cannot be empty
totalPrice.notnull=Total price cannot be empty
accType.notempty=Account type cannot be empty, must be 0 (Room charge) or 1 (Immediate payment)
orderNo.notempty=Order number cannot be empty, set as order number for room charge, or 0 for immediate payment
accNo.notempty=Account number cannot be empty. For room charge, use acc_no from the account table, and for immediate payment, use acc_no from the acc_record table
indemnityName.notempty=Indemnity item name cannot be empty
indemnityName.size=Indemnity item name length cannot exceed 30 characters
thingCode.notempty=Category code cannot be empty
price.min=Price must be greater than or equal to 0
itemName.notempty=Item name cannot be empty
rentTime.notnull=Rental time cannot be empty
rentName.notempty=Item name cannot be empty
rentName.size=Item name length cannot exceed 30 characters
rentPrice.notnull=Rental price cannot be empty
rentPrice.min=Rental price must be greater than or equal to 0
indemnityPrice.notnull=Compensation price cannot be empty
indemnityPrice.min=Compensation price must be greater than or equal to 0
consultInd.notempty=Whether the front desk can negotiate the compensation price cannot be empty
consultInd.enum=Whether the front desk can negotiate the compensation price must be 0 or 1
state.notempty=Status cannot be empty
goodsName.toolong=Product name length cannot exceed 30 characters
cost.min=Cost must be greater than or equal to 0
unit.toolong=Specification length cannot exceed 30 characters
stock.notnull=Stock cannot be null
stock.min=Stock must be greater than or equal to 0
goodsType.invalid=Goods type must be 0 (product type) or 1 (compensation item type)
groupItemCategory.invalid=Group item category must be 0 (no) or 1 (yes)
regPhone.invalid=The enterprise registration phone format is incorrect
email.invalid=The email format is incorrect
invoiceTitleType.notempty=Invoice title type cannot be empty (0: individual, 1: enterprise, 2: organization)
invoiceType.notempty=Invoice type cannot be empty (0: VAT general invoice, 1: VAT special invoice)
invoiceCode.notempty=Invoice code cannot be empty
makeTime.notnull=Invoice date cannot be empty
invoiceNo.notempty=Invoice number cannot be empty
invoicePerson.notempty=Issuer cannot be empty
taxpayerId.notempty=Taxpayer ID cannot be empty
invoiceTitle.notempty=Invoice title cannot be empty
toAllDay.notempty=Whether hourly room converts to full-day room cannot be empty
toDirty.notempty=Whether to set overnight rooms as dirty cannot be empty
agentCredit.notempty=Agency order auto-credit during night audit cannot be empty
protocolCredit.notempty=Contract unit order auto-credit during night audit cannot be empty
otaCredit.notempty=OTA order auto-credit during night audit cannot be empty
auto.notempty=Auto audit setting cannot be empty
time.notnull=Automatic night audit time cannot be empty
notice.notnull=Daily advance night audit reminder (minutes) cannot be empty
checkinType.notempty=Check-in type cannot be empty
nightNum.notnull=Number of room nights cannot be empty
hname.notempty=Store name cannot be empty
checkinType.invalid=Invalid check-in type
days.min=Number of stay days cannot be less than 0
guarantyStyle.notempty=Guarantee method is required for agency guest source type
isIgnoreVd.invalid=Invalid empty room cleaning status
checkinTime.notnull=Check-in time cannot be null
guestSrcType.invalid=Invalid guest source type
guestCode.size=Guest associated account cannot exceed 32 characters
vipPrice.notnull=Manual price cannot be null
bkNum.min=Breakfast voucher quantity cannot be less than 0
roomBkNum.min=Room breakfast quantity cannot be less than 0
idType.invalid=Invalid ID type
bkNum.max=The complimentary breakfast quantity cannot exceed 99
persons.notempty=Guest information cannot be empty
continuePriceType.invalid=The type of continued stay price is incorrect
continuePriceType.notempty=Continue stay price type cannot be empty
togetherCodes.notempty=Guest cannot be empty
changeReason.notblank=Room change/upgrade reason cannot be empty
isFree.notblank=Free upgrade status cannot be empty
teamCode.notempty=Team code cannot be empty
secrecy.invalid=Secrecy must be 0 or 1
noDisturbing.invalid=Do not disturb must be 0 or 1
dayBks.notempty=Daily additional breakfast count cannot be empty
bkNum.notnull=The number of complimentary breakfasts cannot be empty
bkNum.max10=The number of complimentary breakfasts cannot be more than 10
buyBkNum.notnull=The number of purchased breakfasts cannot be empty
buyBkNum.min=The number of purchased breakfasts must be greater than or equal to 0
buyBkNum.max=The number of purchased breakfasts cannot be more than 100
refundBkNum.notnull=The number of refunded breakfasts cannot be empty
refundBkNum.min=The number of refunded breakfasts must be greater than or equal to 0
sex.invalid=Invalid gender
address.size=Address cannot exceed 255 characters
idNo.size=ID number cannot exceed 32 characters
seller.notempty=Seller cannot be empty
bindCode.notempty=Binding code cannot be empty
noType.notempty=Type of number cannot be empty
quickFilter.invalid=Quick filter can only be 0: Expected departure today, 1: Check-in today
timeType.invalid=Time type can only be 0: Departure time, 1: Check-in time
overbookRoomCount.notnull=Overbooking room count cannot be empty
overBookCode.notempty=Overbooking configuration code cannot be empty
rtState.notempty=Room status cannot be empty
roomQuantity.notnull=Room quantity cannot be empty
oversellNum.notnull=Oversell quantity cannot be empty
uniPrice.notempty=Unified price cannot be empty
rtCodeAndPrices.notnull=Room type price list cannot be null
increaseOrDecrease.notempty=Price adjustment cannot be empty
increaseOrDecrease.invalid=Price adjustment can only be 0 or 1
updatePrice.notnull=Retail price cannot be empty
optCode.notempty=Operation type code cannot be empty
optName.notempty=Operation type cannot be empty
shift.notempty=Shift cannot be empty
oldValue.notempty=Original value cannot be empty
nowValue.notempty=Current value cannot be empty
rtName.notempty=Room type name cannot be empty
gcode.size=Group code cannot exceed 30 characters
condition.notnull=Condition configuration cannot be empty
strategy.notnull=Strategy content cannot be empty
scope.notnull=Scope cannot be empty
hotelCodes.notnull=Participating hotels for price strategy cannot be empty
groupStrategy.notempty=Group strategy status cannot be empty
groupStrategy.invalid=Group strategy status can only be 0 or 1
strategyCode.notnull=Strategy code cannot be empty
startPriceNMin.min=Minimum value for charging base fee after N minutes of check-in is 0
allPriceNMin.min=Minimum value for charging full-day rent after N minutes of check-in is 0
overNMinCollect.min=Minimum value for charging after N minutes of early departure is 0
overNMinAllDay.min=Minimum value for charging full-day rent after N minutes of overtime is 0
overCollectStyle.notempty=Overtime charge method cannot be empty
overCollectStyle.invalid=Overtime charge method must be 0 (charge by half-day rent) or 1 (additional charge per hour)
approvalName.notempty=Custom price name cannot be empty
approvalName.size=Custom price name length cannot exceed 30 characters
discount.max=Discount cannot exceed 1
discount.min=Discount cannot be less than 0
markUp.max=Markup cannot exceed 99999999
markUp.min=Markup cannot be less than 0
markDown.max=Markdown cannot exceed 99999999
markDown.min=Markdown cannot be less than 0
retainTime.notnull=Retain time cannot be empty
checkOutTime.notnull=Check-out time cannot be empty
ruleType.notempty=Type cannot be empty
paName.notempty=Agency/Agreement name cannot be empty
paName.size=Agency/Agreement name cannot exceed 30 characters
shortName.size=Short name cannot exceed 30 characters
paType.notempty=Type cannot be empty
paType.invalid=Type must be 0: Agreement or 1: Agency
isShare.notempty=Share status cannot be empty
isShare.invalid=Share status can only be 0: Single-store use or 1: Group sharing
contact.size30=Contact name cannot exceed 30 characters
isHidePrice.notempty=Hide price on registration cannot be empty
isCredit.notempty=Credit permission cannot be empty
buildingOrFloorName.notempty=Building or floor name cannot be empty
buildingOrFloorName.size=Building or floor name cannot exceed 30 characters
parentCode.notempty=Building code for the floor cannot be empty
isFloor.notempty=Floor status cannot be empty
isFloor.invalid=Floor status must be 0:Building or 1:Floor
buildingCode.notblank=Building code cannot be empty
floorCode.notblank=Floor code cannot be empty
extNum.size=Extension number cannot exceed 20 characters
bedNum.max=Bed number cannot exceed 20
bedNum.min=Bed number cannot be less than 1
isLocked.invalid=Lock status must be 0: No or 1: Yes
lockedReason.size=Lock reason cannot exceed 255 characters
roomStatus.invalid=Room status must be VC, VD, OC, OD, or OO
repairReason.size=Repair reason cannot exceed 255 characters
rNo.size=Room number cannot exceed 30 characters
roomList.notempty=The room list cannot be empty
cleaner.notempty=The cleaner cannot be empty
url.notempty=The database path cannot be empty
content.notempty=The operation content cannot be empty
handle.notempty=The operation type cannot be empty
hourCode.notempty=The hourly room cannot be empty
hourCode.invalid=The hourly room code is incorrect
hourName.notempty=The hourly room name cannot be empty
hourName.size=The hourly room name cannot exceed 20 characters
rtName.size=Room type name cannot exceed 40 characters
basePrice.min=Base price must be greater than or equal to 0
basePrice.max=Base price must be less than or equal to 9999999
area.min=Area must be greater than or equal to 0
peopleNum.min=Number of people must be greater than or equal to 1
isRestRoom.invalid=Bathroom presence can only be 1 or 0
isVirtual.invalid=Virtual room can only be 1 or 0
isGroupRt.invalid=Group room type can only be 1 or 0
services.notempty=Provided services cannot be empty
shiftCode.notempty=Shift code cannot be empty
shiftName.notempty=Shift name cannot be empty
userId.notnull=User ID cannot be empty
idCard.notempty=ID card number cannot be empty
userType.notempty=Visitor type cannot be empty
failTime.notnull=Expiration time cannot be null








# ========== Constants ==========
CONSTANTS_NOT_EXISTS=Constant does not exist

# ========== Building and Floor ==========
BUILD_FLOOR_NOT_EXISTS=The specified building or floor information does not exist
BUILD_FLOOR_EXITS_CHILDREN=Cannot delete because there are sub-buildings or floors
BUILD_FLOOR_HAS_ROOM=Cannot delete because there are rooms in this building or floor
BUILD_FLOOR_PARENT_NOT_EXITS=The parent building or floor does not exist
BUILD_FLOOR_PARENT_ERROR=Cannot set this floor as its own parent building or floor
BUILD_FLOOR_NAME_DUPLICATE=The building or floor name already exists
BUILD_FLOOR_PARENT_IS_CHILD=Cannot set its own subfloor as the parent floor

# ========== Room ==========
ROOM_NOT_EXISTS=Room does not exist
ROOM_IS_OCCUPIED=Room/type {0} is already occupied, with a current stay, reservation, or maintenance plan
ROOM_NO_REPEAT=Room number cannot be duplicated
ROOM_LOG_NOT_EXISTS=Room log does not exist
ROOM_IS_BOOKED=Room {0} is already booked
ROOM_IS_INVALID=Room {0} status does not allow check-in
ROOM_IS_INVALID2=Room {0} is in Dirty status and cannot be checked in
IDNO_USER_EXISTS=ID number {0} has already checked in; one ID can check into only one room
CUSTOMER_IS_BLACK=ID number {0} is on the blacklist; check-in not allowed
DUPLICATION_USER_EXISTS=ID number {0} is duplicate entry
ROOM_STATE_CHANGE_ERROR=Current room status does not allow change
ROOM_STOP_ERROR=Room {0} is in use; cannot be disabled
ROOM_REPAIR_TIME_ERROR=Repair end time cannot be earlier than start time
ROOM_REPAIR_REASON_ERROR=Repair start time, end time, and reason cannot be empty
ROOM_REPAIR_TIME_ERROR2=Repair start time cannot be earlier than the current time
ROOM_REPAIR_TIME_ERROR3=The room is already in a non-repair status
ROOM_STATE_ALREADY_DIRTY=Current room status is already dirty
ROOM_LOCKED_ERROR=Cannot lock a room that is in use
ROOM_STATE_ALREADY_CLEAN=Current room status is already clean
ROOM_REPAIR_OVERLAP_ERROR=Repair time conflicts with order time; cannot set repair
ROOM_TYPE_NOT_MATCH=This room does not belong to the current room type
ROOM_TYPE_IS_OCCUPIED=Room type "{0}" is fully occupied during this period. Maximum booked rooms: {1}, maximum occupied rooms: {2}, maximum rooms under maintenance: {3}, total rooms: {4}, conflicting reservation ID: {5}, conflicting order ID: {6}, conflicting room numbers: {7}.

# ========== Room Cleaning Log ==========
ROOM_CLEAN_LOG_NOT_EXISTS=Room cleaning log does not exist

# ========== Room Type ==========
ROOM_TYPE_NOT_EXISTS=Room type does not exist
ROOM_TYPE_HAS_ROOM=There are rooms under this room type, so it cannot be disabled.
ROOM_TYPE_VIRTUAL_RELE_EXISTS=This virtual room type is already associated with a physical room type
ROOM_TYPE_NAME_EXISTS=This room type name already exists
ROOM_TYPE_BASE_PRICE_NOT_EXISTS=No room type price is set for this room type
ROOM_TYPE_CODE_NOT_NULL=Room type code cannot be null
ROOM_TYPE_IS_REFERENCE=This room type is used by the hotel and cannot be changed to a virtual room type

# ========== Physical and Virtual Room Type Association ==========
ROOM_TYPE_VIRTUAL_RELE_NOT_EXISTS=Association between physical and virtual room type does not exist

# ========== Hourly Room Type ==========
HOUR_ROOM_TYPE_NOT_EXISTS=No room type is set for the selected stay duration
HOUR_ROOM_TYPE_EXISTS={0} is already set and cannot be set again

# ========== Shift Settings ==========
SHIFT_TIME_NOT_EXISTS=Shift does not exist
SHIFT_TIME_CLOSE_ERROR=Cannot close all shifts
BIZ_DATE_NOT_EXISTS=Business date does not exist; please ensure correct business hours are configured in system settings before proceeding
SHIFT_TIME_NOT_EXISTS2=Current account is not logged in, operation not possible
SHIFT_TIME_NOT_EXISTS3=New business date cannot be earlier than {0}
SHIFT_TIME_NOT_EXISTS4=New business date cannot be later than {0}
SHIFT_TIME_LOST=Shift lost, please log in again to retrieve


# ========== Service Items ==========
SERVICE_ITEMS_NOT_EXISTS=Service item does not exist

# ========== Night Audit Room Count Settings ==========
ROOM_NIGHTS_NUM_SET_NOT_EXISTS=Night audit room count setting does not exist

# ========== Night Audit Settings ==========
NIGHT_AUDI_SET_NOT_EXISTS=Night audit setting does not exist
NIGHT_AUDI_ERROR_NOW_IS_NIGHT_AUDI_DAY={0} has already undergone night audit and cannot be audited again
NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR=Night audit setting does not exist; please set night audit first
NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR2=Night audit time can only be set between 0-12 AM
NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR3=Night audit start time cannot be later than end time
NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR1=Night audit time cannot be null
NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR4=The night audit time cannot be 0:00

# ========== Item Category ==========
THING_CLASS_NOT_EXISTS=Item category does not exist
THING_CLASS_REFERENCE_RETAIL_GOODS=There are items under this category, so it cannot be deleted
THING_CLASS_REFERENCE_INDEM_GOODS=Item category is referenced and cannot be deleted
THING_CLASS_NOT_ALLOW_DELETE=All categories are not allowed to be deleted

# ========== Retail Goods ==========
RETAIL_GOODS_NOT_EXISTS=Retail goods do not exist

# ========== Rental Items ==========
RENT_GOODS_NOT_EXISTS=Rental items do not exist

# ========== Compensation Items ==========
INDEMNITY_GOODS_NOT_EXISTS=Compensation items do not exist

# ========== User Parameter Settings ==========
USER_PARAM_CONFIG_NOT_EXISTS=User parameter settings do not exist

# ========== Group Parameter Settings ==========
GROUP_PARAM_CONFIG_NOT_EXISTS=Group parameter settings do not exist

# ========== Group Global Configuration ==========
GROUP_GLOBAL_CONFIG_NOT_EXISTS=Group global configuration does not exist

# ========== Hotel Parameter Settings ==========
HOTEL_PARAM_CONFIG_NOT_EXISTS=Hotel parameter settings do not exist

# ========== General Configuration ==========
GENERAL_CONFIG_NOT_EXISTS=General configuration does not exist


# ========== Group Store Tax Configuration ==========
TAX_CONFIG_NOT_EXISTS=The group store tax configuration does not exist
TAX_CONFIG_EXISTS=The tax rate configuration for the current subject already exists

# ========== Hotel Base Price ==========
PRICE_BASE_NOT_EXISTS=The hotel base price does not exist

# ========== Price Calendar (Listing Price) ==========
PRICE_CALENDAR_NOT_EXISTS=The price calendar (listing price) does not exist
PRICE_CALENDAR_START_DATE_NULL=The start date cannot be empty
PRICE_CALENDAR_START_DATE_END_DATE_INVALID=The start date cannot be later than the end date
PRICE_CALENDAR_START_DATE_BEFORE_NOW=The start date cannot be earlier than the current date
PRICE_CALENDAR_RT_CODE_AND_PRICE_NULL=Room type code and price cannot be empty
PRICE_CALENDAR_PRICE_NEGATIVE=Price must be greater than 0
PRICE_CALENDAR_START_DATE_TOO_SHORT=The start date cannot be later than the end date
PRICE_CALENDAR_PRICE_NOT_SET=Some room types have no base price set, please set it first
PRICE_CALENDAR_START_DATE_TOO_LONG=The set room price cannot exceed three months from the current date
PRICE_CALENDAR_PRICE_TOO_LOW=The selling price of {0} must be greater than or equal to 0

# ========== Price Calendar Modification Log ==========
PRICE_CALENDAR_LOG_NOT_EXISTS=The price calendar modification log does not exist

# ========== Oversell Calendar ==========
OVERSELL_CALENDAR_NOT_EXISTS=The oversell calendar does not exist

# ========== Control Room Calendar ==========
CONTROL_ROOM_CALENDAR_NOT_EXISTS=The control room calendar does not exist

# ========== Floating Room Price ==========
PRICE_FLOAT_NOT_EXISTS=The floating room price does not exist

# ========== Room Price Strategy ==========
PRICE_STRATEGY_NOT_EXISTS=The room price strategy does not exist
PRICE_STRATEGY_DATE_ERROR=The date must be later than the system date

# ========== Applicable Store for Room Price Strategy ==========
PRICE_STRATEGY_MERCHANT_NOT_EXISTS=The store applicable to the room price strategy does not exist

# ========== Channel ==========
CHANNEL_NOT_EXISTS=The channel does not exist

# ========== Calendar ==========
CALENDAR_NOT_EXISTS=The calendar does not exist

# ========== All-Day Room Billing Rule ==========
PRICE_ALL_DAY_RULE_NOT_EXISTS=The all-day room billing rule does not exist

# ========== Hourly Room Billing Rule ==========
PRICE_HOUR_RULE_NOT_EXISTS=The hourly room billing rule does not exist

# ========== Special Room (Day Room, Midnight Room) Billing Rule ==========
PRICE_SPECIAL_RULE_NOT_EXISTS=The special room (day room, midnight room) billing rule does not exist

# ========== Group Price Approval ==========
PRICE_APPROVAL_NOT_EXISTS=The group price approval does not exist

# ========== Protocol Unit / Agent ==========
PROTOCOL_AGENT_NOT_EXISTS=The protocol unit or agent does not exist
PROTOCOL_AGENT_CREATE_DATE_ERROR=The end date for the protocol unit or agent cannot be earlier than the current date
PROTOCOL_AGENT_CREATE_DATE_ERROR2=The start date for the protocol unit or agent cannot be later than the end date
PROTOCOL_AGENT_CREATE_DATE_ERROR3=Cross-business day modifications are not supported, as they affect night audits and performance statistics. Please disable this record and add a new one.

# ========== Protocol Unit / Agent - Customer Name Already Exists ==========
PROTOCOL_PANEM_EXISTS=The customer name for the protocol unit or agent already exists

# ========== Brokerage Strategy ==========
BROKERAGE_STRATEGY_NOT_EXISTS=The brokerage strategy does not exist

# ========== Customer History ==========
CUSTOMER_NOT_EXISTS=Customer history does not exist

# ========== Account Cannot Be Empty ==========
ACCOUNT_NOT_NULL=Account cannot be empty

# ========== Account ==========
ACCOUNT_NOT_EXISTS=The account does not exist
GOODS_DETAILS_NOT_EXISTS=The item or product details do not exist
GOODS_DETAILS_TOTAL_PRICE_NOT_EQUAL_FEE=The total price of the item or product details does not match the account amount
CONSUME_FEE_NOT_EQUAL_PAY_FEE=The consumption amount does not match the payment amount
ACCOUNT_SEPARATE_ERROR=The sum of the separated accounts should equal the original account
ACCOUNT_SEPARATE_ERROR_NO_PAY=The separated account cannot be empty
ACCOUNT_SEPARATE_ERROR_VERIFY=Only unsettled accounts can be separated
ACCOUNT_SEPARATE_ERROR_PAY=Only accounts with cash payment in progress can be separated
ACCOUNT_SEPARATE_ERROR_MEMBER_CARD=Accounts for the current consumption subject do not allow separation
ACCOUNT_TRANSFER_ERROR_ACCOUNT=The account to be transferred does not exist
ACCOUNT_TRANSFER_ERROR_ACCOUNT2=Only unsettled accounts can be transferred
ACCOUNT_TRANSFER_ERROR_ACCOUNT3=Hanging accounts are not allowed to be transferred
ACCOUNT_TRANSFER_ERROR_TEAM_STATE=The team to be transferred to is not in a checked-in state
ACCOUNT_TRANSFER_ERROR_TOGETHER_STATE=The guest to be transferred to is not in a checked-in state
ACCOUNT_TRANSFER_ERROR_ACCOUNT4=Cannot transfer the account to oneself
ACCOUNT_TRANSFER_ERROR_ACCOUNT5=Hanging accounts are not allowed to be transferred
ACCOUNT_TRANSFER_ERROR_RNO=The room number to be transferred to cannot be empty
ROOM_NOT_EXIST=The room number to be transferred to does not exist
ACCOUNT_RED_ERROR_ACCOUNT=The account does not exist
ACCOUNT_RED_ERROR_BIZ_DATE=Accounts past night audit cannot be adjusted
ACCOUNT_RED_ERROR_ACCOUNT2=Only unsettled accounts can be adjusted
ACCOUNT_RED_ERROR_BK_FEE=Breakfast cannot be adjusted
ACCOUNT_TYPE_NOT_EXISTS=Account type does not exist
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT=Selected account has been settled
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT2=There are settled accounts in the selected accounts
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT3=The selected account amount is not equal to the payment amount
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT4=AR account set does not exist
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT5=The current account set has expired
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT6=The current account set is not within the valid time range
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT7=Insufficient account balance, hanging accounts are not allowed
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT8=The current account set has expired
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT9=The current unit is not allowed to hang accounts
ACCOUNT_NOT_UNCLOSED=Pre-authorization has been completed
ACCOUNT_NOT_PRE_AUTH=It must be a pre-authorization to confirm
ACCOUNT_PRE_AUTH_FEE_ERROR=Payment amount cannot exceed the pre-authorization amount
ACCOUNT_PRE_AUTH_TYPE_ERROR=Payment type error
ACCOUNT_NOT_UNCLOSED_EXISTS=No unsettled accounts exist
ACCOUNT_NOT_CHECKED_EXISTS=No account for income selection
ACCOUNT_PAY_CODE_NOT_ALLOW=Checkout does not allow using pre-authorization
ACCOUNT_PAY_CODE_NOT_NULL=Payment code cannot be empty
ACCOUNT_TEAM_CLOSED=The team has settled, no income is allowed
ACCOUNT_ORDER_CHECK_OUT=The income account has checked out
ACCOUNT_TEAM_CLOSED2=During checkout, team {0} has settled, no need to settle again
ACCOUNT_ORDER_CLOSED={0} has checked out, cannot settle again
ACCOUNT_ORDER_CLOSED2=No guest order found
ACCOUNT_ORDER_NOT_EXISTS=Order does not exist
BIZ_CREDIT_CHECK_OUT_ERROR=The team has settled or has a hanging account
ORDER_CREDIT_CHECK_OUT_ERROR=The guest with a hanging account is not in a checked-in state, hanging accounts are not allowed
ACCOUNT_NOT_CURRENT_SHIFT=Only accounts of the current business day can be revoked
ACCOUNT_PART_CLOSE_ERROR_ACCOUNT10=Refund amount cannot exceed the order balance, current order balance: {0}
ACCOUNT_STATE_ERROR=Only unsettled accounts can be checked out
ORDER_BK_NUM_ERROR=The number of early check-outs cannot exceed the available number
ORDER_STATUS_NOT_ALLOW_CONSUME=Guest {0} has checked out, income is not allowed
SCAN_GUN_PAY_CODE_NOT_EXISTS=Scan gun payment code cannot be empty
SCAN_GUN_PAY_CODE_ERROR=Scan gun payment code error
PAY_FAILURE=Scan code payment failed, reason for failure: {0}
QUERY_FAILURE=query failed, reason for failure: {0}
PRE_FINISH_ERROR=Pre-authorization confirmation failed, reason for failure: {0}
PRE_CANCEL_ERROR=Pre-authorization cancellation failed, reason for failure: {0}
ACCOUNT_VERIFY_EXISTS=Verification failed, there are already verified accounts in the account
ACCOUNT_NOT_VERIFY=Revocation of verification failed, current account status is not verified
SCAN_GUN_PAY_ERROR=Scan code payment failed, reason for failure: {0}
ACCOUNT_STATUS_ERROR=Account status {0}, cannot refund
ACCOUNT_SUB_CODE_ERROR=Only accounts that support scan code payment can be refunded
ACCOUNT_SUB_TYPE_ERROR=Only payment subject accounts can be refunded
ACCOUNT_CREATE_TIME_ERROR=Payment time has exceeded {0} days, cannot refund
ACCOUNT_FEE_ERROR=Account amount must be greater than 0
STORE_CARD_CONSUME_FAIL=Stored value card consumption failed, reason: {0}
GENERALTE_FEE_TYPE_NOT_EXISTS=Error generating room charge type
ACCOUNT_STATUS_NOT_UNCLOSED=Account status is not unsettled, cannot split accounts
ACCOUNT_STATUS_NOT_UNCLOSED2=Only unsettled accounts can add remarks
ACCOUNT_FEE_NOT_EQUAL=Consumption account and payment account are not balanced
ACCOUNT_CARD_NOT_EXISTS=Card information not found, please click the query button to get member card information
MONEY_NOT_EQUAL=Currency mismatch, unable to process refund
PAY_TYPE_NOT_MATCH=Please select the {0} payment method set by the store
FAILURE_REASON=Reason for failure: {0}



# ========== Occupied Orders ==========
ORDER_NOT_EXISTS=The occupied order does not exist
CHECKIN_DAYS_ERROR=The check-in and departure times do not match the number of days
CHECKIN_TIME_ERROR=The check-in time should be earlier than the check-out time
CHECKOUT_TIME_ERROR=The check-out time should be later than the current time
HOUR_ROOM_CODE_NOT_NULL=Hourly room code cannot be null
CHECKIN_TIME_NOT_IN_RULE=According to system settings, check-in for hourly rooms is not allowed at this time
ORDER_IS_OUT_HOUSE=The guest in room {0} has checked out
ORDER_TYPE_NOT_SUPPORT_EARLY_OUT=Hourly rooms do not support early check-out
MERGE_ROOM_LAST_ROOM_OUT_HOUSE=There is only one occupied or pending order left in the current linked room, quitting linked room is not allowed
ROOM_NOT_CHECKIN=This room is not checked in, cohabitation is not allowed
BREAKFAST_NOT_EXISTS=Breakfast is not available
ORDER_AWAKEN_TIME_ERROR=The wake-up call time must be later than the current time.
ORDER_AWAKEN_TIME_ERROR2=he wake-up call time cannot be later than the scheduled departure time
NOTYPE_NO_SUPPORT=The operation is not supported for this order type.
RENT_LIST_NOT_DELETE=The rent cannot be deleted
RENT_LIST_RETURNED_NOT_ALLOW_COMPENSATION=Returned room order cannot perform compensation operation
RENT_LIST_RETURNED_NOT_ALLOW_DEL=Returned room order cannot perform delete operation
ORDER_NOT_CHANGE_ROOM_AFTER_OUT_HOUSE=Changing rooms is not allowed, please renew your stay first
ROOM_IS_REPAIR2=The repair time for room {0} conflicts with the check-in and check-out time, check-in is not allowed
ROOM_TYPE_NOT_SUPPORT_HOUR_ROOM=The room type to be changed does not support the current hourly room
ORDER_CONTINUE_IN_TIME_ERROR=The new check-out time cannot be earlier than the original check-out time
ROOM_IS_CHECKIN=Room {0} is currently occupied, check-in is not allowed
ORDER_NOT_JOIN_ORDER=Room {0} is not part of any linked room, no need to quit linked room
ORDER_IS_GROUP_ORDER=Room {0} is a group room, joining linked room is not allowed
ORDER_OCCUPIED=Room {0} is occupied, check-in is not allowed
ORDER_NOT_CHECKIN=Room {0} is not checked in, cohabitation is not allowed
ORDER_NOT_TOGETHER=Room {0} does not have guest information
ORDER_TYPE_NOT_EXISTS=Order type does not exist
ORDER_CHECKIN_TYPE_ERROR=Check-in type error
ORDER_TEAM_MAIN_ACCOUNT_NOT_CLOSED=The main account of the team has unclosed bills, the last occupied room cannot check out but can remain pending
ORDER_STATE_ERROR=Order state error, pending or checked-out orders can check in
ORDER_BIZ_DATE_ERROR=Cannot check in again across business days
ROOM_STATE_ERROR=The room is occupied, check-in is not allowed
ORDER_ROOM_IS_DISABLE=Room {0} has been disabled, operation is not allowed
NO_ORDER_PRICE=Order price does not exist
STORECARD_PAY_ERROR=Stored value card payment failed, reason: {0}
ORDER_TOGETHER_NO_EXIST=Please select the orders to check out
CHECKED_ORDER_CHECKOUT=The selected orders have already been checked out
ORDER_IS_NOT_CAN_QUIT_TEAM=The main account of the team has unclosed bills, the last occupied room cannot quit the team
IDNO_REQUIRED=ID number cannot be empty
GUEST_CODE_NULL=Please select an intermediary
GUEST_CODE_NULL2=Please select a contractual unit
GUEST_CODE_NULL3=Please select a member
HOUR_ROOM_TYPE_ERROR=Hourly room type does not exist
HOUR_ROOM_TYPE_ERROR2=Room {0} does not support the current hourly room type
ROOM_IS_LOCKED=Room {0} is locked, check-in is not allowed
ORDER_PRICE_IS_NULL=Order price does not exist
ORDER_CONTINUE_IN_PRICE_TYPE_ERROR=Renewal price type cannot be null
SYSTEM_ERROR=System error: {0}
ORDER_CONTINUE_IN_TIME_ERROR2=Hourly room renewal cannot span days
BOOK_NOT_ACCOUNT=There are rooms already checked in in the reservation, financial operations are not allowed in the reservation
# ========== Order Log ==========
ORDER_LOG_NOT_EXISTS=Order log does not exist
# ========== Order Price ==========
ORDER_PRICE_NOT_EXISTS=Order price does not exist
ORDER_PRICE_ROOM_LIST_EMPTY=Room list cannot be empty
ORDER_PRICE_ORDER_LIST_EMPTY=Order list cannot be empty
ORDER_TOGETHER_NOT_EXISTS=Guest order does not exist
ORDER_IS_GROUP=Room {0} is a group order, joining another group is not allowed
ORDER_IS_NOT_GROUP=Room {0} is not a group order, no need to quit
PRICE_NOT_EXISTS=Price does not exist for that day
ROOM_ORDER_NOT_EXISTS=Order for room {0} does not exist
ROOM_ORDER_PLEASE_CONTINE_IN=Please renew your stay before changing price
ORDER_NOT_JOIN=Room {0} does not have a linked room, cannot be modified to a main order
ORDER_IS_MAIN=Room {0} is already the main order, no need to modify
ORDER_TOGETHER_NOT_EXISTS2=Please select the orders to check out

# ========== Merchant Type Rule ==========
MERCHANT_TYPE_RULE_NOT_EXISTS=Merchant type rule does not exist

# ========== Cash Account Record ==========
ACC_RECORD_NOT_EXISTS=Cash account record does not exist
CASH_BILL_ORDER_NOT_ROOM_ACCOUNT=This order is not a cash account

# ========== Cash Account Set ==========
ACC_SET_NOT_EXISTS=Cash account set does not exist
ACCOUNT_CONSUME_ACCOUNT_NOT_EXISTS=Account does not exist or the number of accounts is incorrect
ACCOUNT_CONSUME_ACCOUNT_REDEEMED=Account has been redeemed, cannot redeem again

# ========== The Subject Cash Account Set Already Exists ==========
ACC_SET_EXISTS=The subject cash account set already exists

# ========== Accounts Receivable (Account Set) ==========
AR_SET_NOT_EXISTS=Accounts receivable (account set) does not exist
AR_SET_ACCOUNT_TYPE_ERROR=Accounts receivable (account set) subject type error
AR_SET_HANDLE_TYPE_ERROR=Accounts receivable (account set) operation type error
AR_SET_INVALID=Accounts receivable (account set) has been disabled, operation not allowed
AR_SET_CREDIT_DATE_ERROR=Current {0} is not within the validity period of accounts receivable (account set)
AR_SET_CREDIT_QUOTA_ERROR={0} does not allow negative account; to allow negative account, please modify the accounts receivable account set settings
AR_SET_CREDIT_MORE_QUOTA_ERROR=Account failure; the total amount of accounts exceeds the maximum limit {0}
AR_SET_CREDIT_MORE_QUOTA_ERROR2=Account failure; insufficient account balance, balance: {0}
AR_SET_NOT_EQUAL=This account has been verified and cannot be redeemed
AR_SET_NAME_EXISTS=Accounts receivable (account set) name already exists

# ========== Cash Bill Order ==========
CASH_BILL_ORDER_NOT_EXISTS=Cash bill order does not exist

# ========== Cash Bill Order Already Redeemed ==========
CASH_BILL_ORDER_IS_REV=Cash bill order has been redeemed

# ========== Account Cannot Be Redeemed ==========
CASH_BILL_ORDER_BIZ_DATE_NOT_EQUAL=This cash bill order was not generated on the current business day and cannot be redeemed

# ========== Business Day Record ==========
BIZ_DAY_NOT_EXISTS=Business day record does not exist

# ========== Credit Start and End Dates Cannot Be Empty ==========
PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_NULL=Credit start and end dates cannot be empty
PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_ERROR=Credit end date cannot be earlier than the start date
PROTOCOL_AGENT_CREATE_CREDIT_START_DATE_ERROR=Credit start date cannot be earlier than the current date

# ========== Billing Period Cannot Be Empty ==========
PROTOCOL_AGENT_CREATE_CREDIT_PAY_FIX_NULL=Billing period cannot be empty

# ========== Credit Settings Cannot Be Empty ==========
PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_ZERO_NULL=Credit settings cannot be empty

# ========== Credit Account Type Cannot Be Empty ==========
PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_NULL=Credit account type cannot be empty

# ========== Credit Quota Cannot Be Empty ==========
PROTOCOL_AGENT_CREATE_CREDIT_QUOTA_NULL=Credit quota cannot be empty

# ========== Historical Order ==========
ORDER_HIS_NOT_EXISTS=Historical order does not exist

# ========== Historical Order Price  ==========
ORDER_HIS_PRICE_NOT_EXISTS=Historical order price does not exist

# ========== Amount Cannot Be Negative  ==========
THE_AMOUNT_CANNOT_BE_NEGATIVE=Amount cannot be negative

# ========== Account Type Error  ==========
ACC_TYPE_ERROR=Account type error

# ========== Write-off Amount Error  ==========
VERIFY_FEE_ERROR=Write-off amount error

# ========== Update Failed ==========
UPDATE_FAILED=Update failed

# ========== Breakfast Setting ==========
HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_EXISTS=Breakfast setting does not exist
HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_OPEN=This hotel does not offer breakfast

# ========== Guest Code ==========
TOGETHERCODE_NOT_NULL=Guest code cannot be empty
TOGETHER_CODE_NOT_EXISTS=Guest information cannot be empty
MEMBER_GET_ERROR=Error retrieving member, details: {0}

# ========== Manual Entry ==========
IS_MANUAL_NOT_NULL=Manual entry status cannot be empty

# ========== Coupon Settings ==========
COUPON_MONEY_NOT_NULL=Coupon amount cannot be empty
COUPON_REBATE_NOT_NULL=Discount amount cannot be empty

# ========== Invoice Information ==========
INVOICE_CONFIG_EXISTS=Invoice config does exist
INVOICE_CONFIG_NOT_EXISTS=Invoice config does not exist
INVOICE_NOT_EXISTS=Invoice information does not exist
INVOICE_LOG_NOT_EXISTS=Invoice log does not exist
INVOICE_SERVICE_NOT_OPEN=Invoice service is not available
INVOICE_NOT_FOUND=Invoice information does not exist

# ========== Item Storage and Sales ==========
DEPOSIT_LIST_NOT_EXISTS=Stored item list does not exist
GOODS_SELL_RECORD_NOT_EXISTS=Sales record does not exist
LEAVE_LIST_NOT_EXISTS=Lost item list does not exist
RENT_LIST_NOT_EXISTS=Rental item list does not exist

# ========== Coupon Usage ==========
COUPON_CONSUME_NOT_ENOUGH=Insufficient spending limit to use this coupon
COUPON_NUM_NOT_ENOUGH=Maximum coupon usage for this order reached
COUPON_EXPIRED=Coupon has expired
COUPON_MULTIPLE_NOT_ALLOWED=Coupon usage exceeds the allowed limit
CHECKINTYPE_COUPON_NOT_AVAILABLE=Coupon cannot be used for this check-in type
RTCODE_COUPON_NOT_AVAILABLE=Coupon cannot be used for this room type

# ========== Room Fee ==========
RFEE_NOT_NULL=Individual room fee cannot be empty

# ========== Overbooking Configuration ==========
OVER_BOOK_NOT_EXISTS=Overbooking configuration does not exist
OVER_BOOK_EXISTS=Overbooking configuration already exists

# ========== Pricing and Billing Rules ==========
PRICE_CHARGE_RULE_NOT_EXISTS=Billing rule does not exist
BOOK_HOUR_CODE_REQUIRED=Please select check-in duration
BOOK_GUEST_SRC_CODE_REQUIRED=Guest source type cannot be empty
BOOK_OUT_ORDER_NO_REPEAT=Duplicate order: {0}

# ========== Booking Orders (Regular & Team Bookings) ==========
BOOK_NOT_EXISTS=Booking order does not exist
BOOK_PLAN_CHECKIN_TIME_AFTER_PLAN_CHECKOUT_TIME=Planned check-in time cannot be later than the check-out time
BOOK_PLAN_CHECKOUT_TIME_BEFORE_NOW=Planned check-out time cannot be earlier than the current time
BOOK_NO_CHECK_IN_ROOM=No rooms available for check-in under this booking
ORDER_PLAN_CHECKOUT_TIME_BEFORE_PLAN_CHECKIN_TIME=Check-out time cannot be earlier than check-in time
BOOK_OVER_NOT_CHECKIN=Completed bookings cannot be checked in
BOOK_NO_ARRANGE_ROOM=Please arrange rooms before check-in
BOOK_NO_CHECKIN_ROOM=Please select a room for check-in
BOOK_RETAIN_TIME_ERROR=Retain time must be within the planned check-in and check-out times
BOOK_CHECKIN_TIME_NOT_IN_RULE=Booking to check-in conversion can only occur within check-in and check-out range
BOOK_CHECKIN_TIME_NOT_IN_RULE2=Booking to check-in conversion cannot occur after check-out time
BOOK_STATUS_NOT_ALLOW_CONSUME=Current booking status does not allow billing
TEAM_STATUS_NOT_ALLOW_CONSUME=Current team status does not allow billing
ACCOUNT_TYPE_NOT_SUPPORT=Current account type does not support billing
BOOK_BATCH_EMPTY=Booking batch cannot be empty
BOOK_ROOM_IN_USED=Room {0} is occupied, check-in not allowed

# ========== Room Booking Arrangement ==========
BOOK_ROOM_NOT_EXISTS=Room booking arrangement does not exist
BOOK_ROOM_STATE_ERROR=Only unoccupied rooms can be deleted
BOOK_ROOM_STATE_NOT_ALLOW_DELETE=Cannot delete; batch includes {0} checked-in rooms
BOOK_ROOM_PRICE_NOT_NULL=Booking room type price cannot be empty
BOOK_ROOM_PRICE_NOT_MATCH=Price days do not match booking days
BOOK_ROOM_PRICE_NOT_POSITIVE=Price must be greater than zero
BOOK_ROOM_DAY_PRICE_REQUIRED=Daily price cannot be empty
BOOK_ROOM_NOT_ALLOW_ADD=Current booking status {0} does not allow adding rooms
BOOK_ROOM_LOCKED=Room {0} is locked and cannot be checked in
BOOK_ROOM_REPAIR=Room {0} has conflicting maintenance time, check-in not allowed
BOOK_ROOM_DIRTY=The room {0} is a dirty room and cannot be checked in. If dirty rooms are allowed for check-in, please go to [Settings] - [Store Parameters Settings] - [Front Desk] and set "Allow Dirty Room Check-in" to Yes.
BOOK_ROOM_TEAM_CODE_REQUIRED=Team booking arrangement does not exist
BOOK_ROOM_LAST_ROOM_NOT_ALLOW_DELETE=Cannot delete; last room cannot be deleted
BOOK_ROOM_BATCH_NOT_ALLOW_DELETE=Cannot delete; last batch cannot be deleted
titleFontSize.notnull=Title font size is required
titleFontSize.range=Title font size must be between 18 and 36
contentFontSize.notnull=Content font size is required
contentFontSize.range=Content font size must be between 18 and 36
showRows.notnull=Rows per screen is required
showRows.range=Rows per screen must be between 1 and 10
switchingTime.notnull=Switching time is required
switchingTime.range=Switching time must be between 0 and 60 seconds

# ========== Team ==========
TEAM_NOT_EXISTS=Team does not exist
TEAM_IS_NOT_CHECK_IN=Only checked-in teams can be joined
TEAM_ORDER_NOT_EXISTS=Team order does not exist

# ========== Team Guests ==========
TEAM_GUEST_NOT_EXISTS=Team guest does not exist
BOOK_MEMBER_NOT_EXISTS=Booking member does not exist
BOOK_PLAN_CHECKOUT_TIME_ERROR=Check-out time cannot be earlier than check-in time
BOOK_ROOM_TYPE_PRICE_NOT_EXISTS=Booking room type cannot be empty
BOOK_ROOM_TYPE_PRICE_NOT_ENOUGH=Insufficient inventory for booking room type {0}
BOOK_ROOM_IN_HOUSE_NOT_ALLOW_EDIT=Room {0} from {1} to {2} is occupied or booked, or has a maintenance plan
BOOK_ROOM_ARRANGE_CONFLICT=Check-in and check-out times conflict for room {0}
BOOK_STATE_NOT_ALLOW_CANCEL=Current booking status does not allow cancellation
BOOK_STATE_NOT_ALLOW_CANCEL_GUARANTY=Guaranteed bookings cannot be canceled
BOOK_STATE_NOT_ALLOW_EDIT=Current booking status does not allow modification
BOOK_PLAN_CHECKIN_TIME_NOT_IN_BOOK_TIME=Check-in and check-out times must be within booking duration
BOOK_STATE_NOT_ALLOW_RECOVER=Only canceled bookings can be restored
BOOK_PLAN_CHECKOUT_TIME_NOT_ALLOW_RECOVER=Cannot restore booking, check-out time has expired
BOOK_ROOM_BOOK_RT_NO_REQUIRED=Booking room type cannot be empty
BOOK_ROOM_BOOK_NO_REQUIRED=Booking order number cannot be empty
BOOK_ROOM_GCODE_REQUIRED=Group code cannot be empty
BOOK_ROOM_HCODE_REQUIRED=Store code cannot be empty
BOOK_PLAN_CHECKOUT_TIME_ERRORX=Check-out time cannot be earlier than the current time
BOOK_ROOM_OCCUPIED=Room {0} is occupied within the check-in and check-out period
BOOK_TEAM_NAME_EXISTS=Active team with the same name exists: {0}. Please change the team name to avoid confusion

# ========== Store Access ==========
MERCHANT_NO_VISIT_ERROR=No access permission for the current store
PMS_SERVICE_EXPIRED=Your service has expired on {0}. Please contact your account manager to renew or renew online.

# ========== Account Receivable ==========
AR_SET_CODE_NOT_NULL=Accounts receivable code cannot be empty

# ========== Member Level Log ==========
LEVEL_LOG_NOT_EXIST=Member level change log does not exist
LEVEL_LOG_NOT_EQUAL=Member level adjustments cannot skip levels

# ========== Order Type ==========
ORDER_TYPE_ERROR=Order is not a linked or team order
HOTEL_LOCK_EXISTS=Hotel lock exists
HOTEL_LOCK_NOT_EXISTS=Hotel lock does not exist

# ========== Device Information ==========
DEVICE_SET_NOT_EXISTS=Device information does not exist
DEVICE_SET_EXISTS=Model already exists under this door lock

# ========== Order and Date Validation ==========
ORDER_NOT_IS_MAIN=Incorrect order number; main order number required for linked or team order billing
DATE_ERROR=Start date cannot be later than the end date
DATE_OVER_THREE_MONTHS=Date range cannot exceed three months

# ========== Hotel Visitors ==========
ACCESS_NOT_EXISTS=Hotel visitor does not exist
ORDER_COUNT_EXCEED_LIMIT=Order count for store {0} exceeds 500. Please initialize at midnight
INIT_MEMBER_BIZ_DATA_FAIL=Failed to initialize member business data
INIT_REPORT_BIZ_DATA_FAIL=Failed to initialize report business data
INIT_ORDER_SYNC_BIZ_DATA_FAIL=Failed to initialize order synchronization business data

# ========== Store OTA Association ==========
MERCHANT_OTA_RELE_NOT_EXISTS=Store OTA association does not exist
INIT_BIZ_DATA_FAIL=Failed to initialize business data; only stores under preparation or construction can be initialized
INIT_BIZ_DATA_FAIL2=Failed to initialize business data; store is not yet activated
#==============SMS==================
SMS_TPL_NOT_ENABLE=The booking SMS template was not fully enabled and the SMS fails to be sent.

#==============SMS Sign==============
SMS_SIGN_NOT_ENABLE=No valid SMS signature
REDIS_KEY_NULL=Redis key require
ROOM_TYPE_REF_NOT_EXISTS=OTA Room Type Association Not Found
OTA_API_NOT_EXISTS=OTA_API is require
SMS_CODE_ERROR=Sms Code Error:{0}

# =========== \u50A8\u503C\u5361 ====================
MEMBER_PAY_ERROR=Member Pay Error:{0}

# =========== HOTEL Data =====================
HOTEL_DATA_COMBO_ERROR=The combo cannot be zero

#=============== \u9152\u5E97\u6570\u636E\u722C\u53D6======================
OTA_HOTEL_NOT_HAVE=This hotel is not in the inquiry list
OTA_CHANNEL_NOT_EXISTS = The hotel channel does not exist

ROOM_CARD_LOG_NOT_EXISTS=The room card log not exist
ORDER_TOGETHER_LAST_ONE_CANNOT_REMOVE=Only one guest in the room, cannot remove
ROOM_LOCK_NO_EMPTY=Lock number cannot be empty
ID_NO_IS_EMPTY=ID number cannot be empty
ROOM_TYPE_CHANGE_ERROR=This room has check-in or booking, cannot change room type
ROOM_CLEAN_LOG_ALREADY_CLEAN_ERROR=Housekeeping task has been started, room can only be set to clean after cleaning is completed.

ROOM_ALREADY_CLEAN_ERROR=Room has been cleaned, please do not repeat the operation
OTA_ROOM_TYPE_REF_NOT_FOUND=No associated PMS room type found
SHIFT_TIME_NOT_CONTINUOUS=Time is not continuous, please reset the time
SHIFT_TIME_OVERLAP=There is overlapping time, please reset the time
SHIFT_TIME_NOT_NULL=The start time or end time of enabled shifts cannot be empty
GUEST_SRC_TYPE_NOT_NULL=Guest source type cannot be empty
PROTOCOL_AGENT_BUILTIN_ERROR=Built-in agent, modification not allowed
AR_SET_IS_BUILTIN=Built-in account set, modification not allowed
MONEY_NOT_EQUAL2=Currency mismatch, cannot cancel booking
MONEY_NOT_NULL=Currency cannot be empty
ACCOUNT_RED_ERROR_VERIFY=This account has been verified, cannot be reversed, please unverify first
ACCOUNT_MERGE_NOT_SUPPORT=This account involves multiple checked-out rooms, checkout cancellation is not supported, please use account adjustment function for account processing.
ACCOUNT_REFUND_ERROR_ACCOUNT=Can only refund unclosed accounts
ACCOUNT_REFUND_ERROR_ACCOUNT2=Can only refund online payment accounts
ACCOUNT_REFUND_ERROR_ACCOUNT3=Refund amount cannot exceed refundable amount
ACCOUNT_REFUND_NOT_RED=Refund amount cannot be reversed
DEPOSIT_CONFIG_NOT_EXISTS=Deposit not configured
REFUND_AMOUNT_NEGATIVE=Refund amount cannot be negative
ACCOUNT_REFUND_AMOUNT_EQUAL_ZERO=This account has been fully refunded
ACCOUNT_EXISTS=Account already exists
PAYMENT_FAILED_INSERT_NOT_ALLOWED=Payment failed, account insertion not allowed
CONSUME_ACCOUNT_NOT_EQUAL=AR account amount cannot exceed consumption amount
ORDER_NOT_EXISTS_IN_DB=No lost accounts found
RETURN_ACCOUNT_NOT_RED=Return account cannot be adjusted
RETURN_ACCOUNT_NUM_ERROR=Return quantity exceeds refundable quantity
COUPON_ACCOUNT_NOT_TRANSFER=Coupon account cannot be transferred
SCAN_NOT_OPEN=Scan gun service not yet available
STORE_CARD_NOT_OPEN=Stored value card service not yet available
IDNO_DATE_ERROR=ID card date parsing error
ORDER_IS_AR_ACCOUNT=AR account has been completed, please do not repeat
ORDER_NOT_UPDATE_CHECKIN_TYPE=Night audit has passed, cannot modify check-in type
COUPON_ONLY_SUPPORTED=Only coupons are supported
ONLY_BOOK_ORDER_CAN_REMOVE=Only booked guests can be removed
BOOK_ACCOUNT_NOT_BALANCE=Account not balanced, please balance first
BOOK_ACCOUNT_PASSWORD_ERROR=Password error
BOOK_ORDER_EXISTS=Order already booked, please do not pay repeatedly
BOOK_ORDER_CANCEL_ERROR=Only pending payment orders can be cancelled, if you want to cancel, please contact the front desk
HOUSEKEEPING_CONFIG_NOT_EXISTS=Hotel housekeeping configuration does not exist
SERVICE_INTEGRATION_EXISTS=Current store has already opened this service
UNSUPPORTED_CHANNEL=Unsupported channel {0}
GET_ROOM_TYPE_FAIL=Failed to get room type {0}
HOTEL_REF_NOT_EXISTS=OTA hotel association does not exist
HOTEL_REF_ALREADY_EXISTS=Hotel has already associated with current channel
MERCHANT_NOT_FOUND=Merchant not found
DATE_MISSING=Date cannot be empty
TASK_NOT_EXISTS=Task does not exist
TASK_STATUS_NOT_CLEAN=Task status is not pending cleaning, cannot cancel
TASK_STATUS_NOT_UPDATE=Task status is not pending cleaning, cannot modify cleaner
TASK_LOGIN_EXPIRE=Login expired, please login again
TASK_STATUS_ERROR=Status error
TASK_STATUS_NOT_START=Task status is not pending cleaning, cannot start cleaning
TASK_STATUS_NOT_CLEANING=Please start cleaning task first
TASK_STATUS_NOT_AUDIT=Status is not pending inspection, cannot start audit
TASK_STATUS_NOT_CLEANING_OR_CLEANING=Current room already has cleaning task in progress
PRICE_PANEL_NOT_EXISTS=Price panel does not exist
PRICE_PANEL_BASE_NOT_EXISTS=Please set basic information first