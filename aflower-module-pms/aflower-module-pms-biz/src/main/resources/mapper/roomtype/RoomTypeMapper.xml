<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.roomtype.RoomTypeMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->


<!--    <select id="selectRoomTypeListByHcode" parameterType="info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO"-->
<!--            resultType="info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO">-->
<!--        select a.*,b.base_price from pb_room_type a, pb_room_type_base_price b-->
<!--        where  a.rt_code = b.rt_code and b.hcode = #{hcode} and b.gcode = #{gcode}-->
<!--        <if test="rtName != null">-->
<!--            and a.rt_name like concat('%',#{rtName},'%')-->
<!--        </if>-->
<!--        <if test="isEnable != null">-->
<!--            and a.is_enable = #{isEnable}-->
<!--        </if>-->
<!--        <if test="isVirtual != null">-->
<!--            and a.is_virtual = #{isVirtual}-->
<!--        </if>-->
<!--        <if test="isGRt != null">-->
<!--             and a.is_g_rt = #{isGRt}-->
<!--        </if>-->
<!--        order by a.id desc-->
<!--    </select>-->

</mapper>