<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.room.RoomMapper">

    <resultMap id="BaseResultMap" type="map">
        <result column="rtCode" property="rtCode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="roomCount" property="roomCount" typeHandler="org.apache.ibatis.type.IntegerTypeHandler"/>
    </resultMap>
    <!--统计房型的房间梳理-->
    <select id="statRoomTypeRoomCount" resultMap="BaseResultMap">
        select r.rt_code as rtCode, count(r.r_code) as roomCount from room r where r.hcode = #{hcode} and r.gcode =
        #{gcode} and r.is_enable = '1' group by r.rt_code
    </select>

    <update id="initRooms">
        update pb_room set state = 'VC',
        is_locked='0',
        locked_reason=null,
        is_cleaned='0',
        is_checked='2',
        repair_start_time=null,
        repair_end_time=null,
        repair_reason=null,
        last_checkout_time=null
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="batchUpdateLockNos">
        <foreach collection="roomLockList" item="item" separator=";">
            UPDATE pb_room
            SET lock_no = #{item.lockNo}, mac = #{item.mac}, lock_version = #{item.version}
            WHERE r_no = #{item.rNo} AND hcode = #{hcode}
        </foreach>
    </update>
</mapper>