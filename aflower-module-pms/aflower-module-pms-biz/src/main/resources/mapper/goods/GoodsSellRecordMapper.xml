<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.goods.GoodsSellRecordMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <resultMap id="goodsRecordMap"
               type="info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail.GoodsSellSummaryRespVO">
        <result column="gcode" property="gcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="hcode" property="hcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="goods_code" property="goodsCode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="goods_name" property="goodsName" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="category_name" property="thingName" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="total_price" property="totalPrice" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="num" property="num" typeHandler="org.apache.ibatis.type.IntegerTypeHandler"/>
        <result column="avg_price" property="avgPrice" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>


    <!--
统计某营业日账号的消费、付款合计，按照账号、账务类型进行统计
-->
    <select id="statRetailGoodsSummaryList" resultMap="goodsRecordMap">
        SELECT
        gcode,
        hcode,
        category_name,
        goods_code,
        goods_name,
        SUM(num) AS num,
        SUM(total_price) AS total_price,
        AVG(total_price / num) AS avg_price  <!-- 计算平均价格 -->
        FROM
        biz_goods_sell_record
        WHERE
        gcode = #{gcode}
        AND hcode = #{hcode}
        and record_type = '0'
        AND biz_date BETWEEN #{startDate} AND #{endDate}  <!-- 统计日期范围 -->
        <if test="thingCodes != null and thingCodes.size() > 0">
            AND category_id IN
            <foreach collection="thingCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
        GROUP BY
        gcode, hcode, goods_code,goods_name,category_name
    </select>

</mapper>