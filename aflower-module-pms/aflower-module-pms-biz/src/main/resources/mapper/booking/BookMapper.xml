<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.booking.BookMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BookResultMap" type="info.qizhi.aflower.module.pms.controller.admin.booking.vo.book.BookPageRespVO">
        <id property="id" column="id" />
        <result property="gcode" column="gcode" />
        <result property="hcode" column="hcode" />
        <result property="bookNo" column="book_no" />
        <result property="outOrderNo" column="out_order_no" />
        <result property="channelCode" column="channel_code" />
        <result property="channelName" column="channel_name" />
        <result property="contact" column="contact" />
        <result property="phone" column="phone" />
        <result property="checkinPerson" column="checkin_person" />
        <result property="checkinPhone" column="checkin_phone" />
        <result property="rNo" column="r_no" />
        <result property="rtCode" column="rt_code" />
        <result property="rtName" column="rt_name" />
        <result property="planCheckinTime" column="plan_checkin_time" />
        <result property="planCheckoutTime" column="plan_checkout_time" />
        <result property="price" column="price" />
        <result property="checkinType" column="checkin_type" />
        <result property="checkinTypeName" column="checkin_type_name" />
        <result property="guestSrcType" column="guest_src_type" />
        <result property="guestSrcTypeName" column="guest_src_type_name" />
        <result property="state" column="state" />
        <result property="remark" column="remark" />
        <result property="teamCode" column="team_code" />
        <result property="retainTime" column="retain_time" />
    </resultMap>

    <!-- 预订单查询 -->
    <select id="selectBookPage" resultMap="BookResultMap">
        SELECT *
        FROM
        biz_book b
        WHERE
        b.gcode = #{reqVO.gcode}
        AND b.hcode = #{reqVO.hcode}
        <if test="reqVO.guestSrcType != null and reqVO.guestSrcType != ''">
            AND b.guest_src_type = #{reqVO.guestSrcType}
        </if>

        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND b.checkin_type = #{reqVO.checkinType}
        </if>

        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            <choose>
                <when test="reqVO.channelCode == 'ctrip'">
                    AND b.channel_code IN ('ctrip', 'elong', 'qunar', 'ly', 'zx')
                </when>
                <otherwise>
                    AND b.channel_code = #{reqVO.channelCode}
                </otherwise>
            </choose>
        </if>

        <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
            AND b.guest_code = #{reqVO.guestCode}
        </if>

        <if test="reqVO.seller != null and reqVO.seller != ''">
            AND b.seller = #{reqVO.seller}
        </if>

        <if test="reqVO.state != null and reqVO.state != ''">
            AND b.state = #{reqVO.state}
        </if>

        <if test="reqVO.timeType != null and reqVO.timeType != '' and reqVO.timeType == 0 and reqVO.startTime!=null and reqVO.endTime != null
                and reqVO.startTime!='' and reqVO.endTime != ''">
            AND b.plan_checkout_time between #{reqVO.startTime} and #{reqVO.endTime}
        </if>

        <if test="reqVO.timeType != null and reqVO.timeType != '' and reqVO.timeType == 1">
            AND b.create_time between #{reqVO.startTime} and #{reqVO.endTime}
        </if>

        <if test="reqVO.timeType != null and reqVO.timeType != '' and reqVO.timeType == 2 and reqVO.startTime!=null and reqVO.endTime != null
                and reqVO.startTime!='' and reqVO.endTime != ''">
            AND b.plan_checkin_time between #{reqVO.startTime} and #{reqVO.endTime}
        </if>

        <if test="reqVO.creator != null and reqVO.creator != ''">
            AND b.creator = #{reqVO.creator}
        </if>

        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            b.contact LIKE CONCAT(#{reqVO.keyWords}, '%')
            or b.phone = #{reqVO.keyWords}
            or b.checkin_person = #{reqVO.keyWords}
            or b.checkin_phone = #{reqVO.keyWords}
            or b.book_no = #{reqVO.keyWords}
            or b.out_order_no = #{reqVO.keyWords}
            )
        </if>

        <if test="bookNos != null and bookNos.size() > 0">
            AND b.book_no IN
            <foreach collection="bookNos" item="bookNo" open="(" separator="," close=")">
                #{bookNo}
            </foreach>
        </if>

        <!-- 动态排序 -->
        <choose>
            <!-- 如果指定了排序字段 -->
            <when test="reqVO.sort != null and reqVO.sort != ''">
                ORDER BY
                <choose>
                    <when test="reqVO.sort.toLowerCase() == 'bookno' or reqVO.sort.toLowerCase() == 'book_no'">
                        b.book_no
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'contact'">
                        b.contact
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'phone'">
                        b.phone
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'plancheckintime' or reqVO.sort.toLowerCase() == 'plan_checkin_time'">
                        b.plan_checkin_time
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'plancheckouttime' or reqVO.sort.toLowerCase() == 'plan_checkout_time'">
                        b.plan_checkout_time
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'price'">
                        b.price
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'createtime' or reqVO.sort.toLowerCase() == 'create_time'">
                        b.create_time
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'updatetime' or reqVO.sort.toLowerCase() == 'update_time'">
                        b.update_time
                    </when>
                    <when test="reqVO.sort.toLowerCase() == 'state'">
                        b.state
                    </when>
                    <otherwise>
                        b.id
                    </otherwise>
                </choose>
                <choose>
                    <when test="reqVO.order != null and reqVO.order.toLowerCase() == 'asc'">
                        ASC
                    </when>
                    <otherwise>
                        DESC
                    </otherwise>
                </choose>
            </when>
            <!-- 特殊状态的默认排序 -->
            <when test="reqVO.state != null and reqVO.state != '' and reqVO.state == 'no_check_in'">
                ORDER BY b.plan_checkin_time ASC, b.id DESC
            </when>
            <!-- 默认排序 -->
            <otherwise>
                ORDER BY b.id DESC
            </otherwise>
        </choose>
    </select>

    <!-- 预订团队分页 -->
    <select id="selectTeamPage" resultMap="BookResultMap">
        SELECT *
        FROM
        biz_book b
        WHERE
        b.gcode = #{reqVO.gcode}
        AND b.hcode = #{reqVO.hcode}
        AND b.book_type = #{bookType}
        <if test="state != null and state != ''">
            AND b.state = #{state}
        </if>
        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND b.checkin_type = #{reqVO.checkinType}
        </if>
        <if test="reqVO.teamCode != null and reqVO.teamCode != ''">
            AND b.team_code = #{reqVO.teamCode}
        </if>
        <if test="reqVO.bookNo != null and reqVO.bookNo != ''">
            AND b.book_no = #{reqVO.bookNo}
        </if>
        <!--timeType==0 预抵时间 timeType==1 预离时间-->
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <if test="reqVO.timeType == '0'">
                AND b.plan_checkin_time between #{reqVO.startDate} and #{reqVO.endDate}
            </if>
            <if test="reqVO.timeType == '1'">
                AND b.plan_checkout_time between #{reqVO.startDate} and #{reqVO.endDate}
            </if>
        </if>
        ORDER BY b.id DESC
    </select>

    <!-- 今日预抵 -->
    <select id="selectBookPageToday" resultMap="BookResultMap">
        SELECT *
        FROM
        biz_book b
        WHERE
        b.gcode = #{reqVO.gcode}
        AND b.hcode = #{reqVO.hcode}
        AND b.plan_checkin_time >= #{nowStartOfDay}
        AND b.plan_checkin_time &lt; #{nowEndOfDay}
        AND b.state = #{state}
        <if test="reqVO.guestSrcType != null and reqVO.guestSrcType != ''">
            AND b.guest_src_type = #{reqVO.guestSrcType}
        </if>

        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND b.checkin_type = #{reqVO.checkinType}
        </if>

        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            AND b.channel_code = #{reqVO.channelCode}
        </if>

        <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
            AND b.guest_code = #{reqVO.guestCode}
        </if>

        <if test="reqVO.seller != null and reqVO.seller != ''">
            AND b.seller = #{reqVO.seller}
        </if>

        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            b.contact LIKE CONCAT(#{reqVO.keyWords}, '%')
            or b.phone = #{reqVO.keyWords}
            or b.checkin_person = #{reqVO.keyWords}
            or b.checkin_phone = #{reqVO.keyWords}
            or b.book_no = #{reqVO.keyWords}
            or b.out_order_no = #{reqVO.keyWords}
            )
        </if>

        <if test="bookNos != null and bookNos.size() > 0">
            AND b.book_no IN
            <foreach collection="bookNos" item="bookNo" open="(" separator="," close=")">
                #{bookNo}
            </foreach>
        </if>

        ORDER BY b.plan_checkin_time ASC, b.id DESC
    </select>

</mapper>
