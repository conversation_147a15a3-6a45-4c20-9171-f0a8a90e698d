<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.brokerage.BrokerageStrategyMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
    
    <resultMap id="BaseResultMap" type="info.qizhi.aflower.module.pms.dal.dataobject.brokerage.BrokerageStrategyDO">
        <id column="id" property="id" />
        <result column="gcode" property="gcode" />
        <result column="strategy_code" property="strategyCode" />
        <result column="strategy_name" property="strategyName" />
        <result column="is_enabled" property="isEnabled" />
        <result column="company_type" property="companyType" />
        <result column="brokerage_type" property="brokerageType" />
        <result column="brokerage_value" property="brokerageValue" />
        <result column="brokerage_level_code" property="brokerageLevelCode" />
        <result column="is_grt" property="isGrt" />
        <result column="is_g" property="isG" />
        <result column="is_enable" property="isEnable" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="rts" property="rts" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="channels" property="channels" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
    </resultMap>

    <select id="selectMerchantBrokerageStrategy" resultMap="BaseResultMap"
            parameterType="info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.strategy.BrokerageStrategyReqVO">
        select a.* from pa_brokerage_strategy a, pa_brokerage_strategy_merchant b
        where b.gcode = #{gcode} and b.hcode = #{hcode} and a.strategy_code = b.strategy_code
        <if test="strategyName != null and strategyName != ''">
            and a.strategy_name like concat('%',#{strategyName},'%')
        </if>
        <if test="companyType != null and companyType != ''">
            and a.company_type = #{companyType}
        </if>
        <if test="isEnable != null and isEnable!=''">
            and a.is_enable = #{isEnable}
        </if>
        order by a.id desc
    </select>
    
</mapper>