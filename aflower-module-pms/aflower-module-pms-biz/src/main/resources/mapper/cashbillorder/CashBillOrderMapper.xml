<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.cashbillorder.CashBillOrderMapper">
    <resultMap id="accRecordMap"
               type="info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordStatByBizDateRespVO">
        <result column="gcode" property="gcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="hcode" property="hcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="sub_code" property="subCode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="consume_total" property="consumeTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="payment_total" property="paymentTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>


    <!--
统计某营业日账号的消费、付款合计，按照账号、账务类型进行统计
-->
    <select id="statAccRecordByBizDate" resultMap="accRecordMap">
        SELECT
        gcode,hcode,sub_code,
        SUM(pay_fee) AS consume_total,
        SUM(buy_fee) AS payment_total
        FROM
        pf_cash_bill_order
        WHERE gcode=#{gcode} and hcode=#{hcode} and biz_date=#{bizDate}
        GROUP BY gcode,hcode,sub_code
    </select>


</mapper>