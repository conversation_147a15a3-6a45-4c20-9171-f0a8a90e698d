<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.account.AccountMapper">

    <resultMap id="togetherAccountMap"
               type="info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountStatRespVO">
        <result column="no" property="no" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="together_code" property="togetherCode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="consume_total" property="consumeTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="payment_total" property="paymentTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>

    <resultMap id="noAccountMap"
               type="info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountStatByBizDateRespVO">
        <result column="no" property="no" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="acc_type" property="accType" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="guest_name" property="guestName" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="consume_total" property="consumeTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="payment_total" property="paymentTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>

    <resultMap id="accountMap"
               type="info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountStatByTogetherCodesRespVO">
        <result column="consume_total" property="consumeTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="payment_total" property="paymentTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>

    <resultMap id="accRecordMap"
               type="info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccRecordStatByBizDateRespVO">
        <result column="gcode" property="gcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="hcode" property="hcode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="sub_code" property="subCode" typeHandler="org.apache.ibatis.type.StringTypeHandler"/>
        <result column="consume_total" property="consumeTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
        <result column="payment_total" property="paymentTotal" typeHandler="org.apache.ibatis.type.LongTypeHandler"/>
    </resultMap>

    <update id="updateBatchById">
        UPDATE biz_account
        <trim prefix="SET" suffixOverrides=",">
            <foreach collection="list" item="item" index="index">
                <if test="item.state != null">state = #{item.state},</if>
                <if test="item.payNo != null">pay_no = #{item.payNo},</if>
                <if test="item.payNo == null">pay_no = null,</if>
                <if test="item.payTime != null">pay_time = #{item.payTime},</if>
                <if test="item.payTime == null">pay_time = null,</if>
                <if test="item.payShiftNo != null">pay_shift_no = #{item.payShiftNo},</if>
                <if test="item.payShiftNo == null">pay_shift_no = null,</if>
                <if test="item.payer != null">payer = #{item.payer},</if>
                <if test="item.payer == null">payer = null,</if>
                <if test="item.handleShiftNo != null">handle_shift_no = #{item.handleShiftNo},</if>
            </foreach>
        </trim>
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>

    <!--
    按照单号、客单号统计消费、付款合计
    过滤掉预授权sub_code是bank_pre_auth和scan_gun_pre_auth的且state为closed的账务
    -->
    <select id="statTogetherAccountByNoList" resultMap="togetherAccountMap"
            parameterType="info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountStatReqVO">
        SELECT
        no,
        together_code,
        SUM(CASE WHEN sub_type = 'consume_account' THEN fee ELSE 0 END) AS consume_total,
        SUM(CASE WHEN sub_type = 'pay_account' THEN fee ELSE 0 END) AS payment_total
        FROM
        biz_account
        WHERE gcode=#{gcode} and hcode=#{hcode}
        <if test="noList != null and !noList.isEmpty()">
            and no in
            <foreach collection="noList" item="no" separator="," open="(" close=")">
                #{no}
            </foreach>
        </if>
        AND NOT (sub_code IN ('bank_pre_auth', 'scan_gun_pre_auth') AND state IN ('closed','cancel') )
        GROUP BY no,together_code
    </select>

    <!--
    统计客单未结的消费、付款金额合计
    -->
    <select id="statTogetherAccountByTogetherCodes" resultMap="accountMap"
            parameterType="info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountStatByTogetherCodesReqVO">
        SELECT
        SUM(CASE WHEN sub_type = 'consume_account' THEN fee ELSE 0 END) AS consume_total,
        SUM(CASE WHEN sub_type = 'pay_account' THEN fee ELSE 0 END) AS payment_total
        FROM
        biz_account
        WHERE gcode=#{gcode} and hcode=#{hcode} and state='open'
        <if test="togetherCodes != null and !togetherCodes.isEmpty()">
            and together_code in
            <foreach collection="togetherCodes" item="togetherCode" separator="," open="(" close=")">
                #{togetherCode}
            </foreach>
        </if>
        <if test="accNoList !=null and !accNoList.isEmpty()">
            and acc_no in
            <foreach collection="accNoList" item="accNo" separator="," open="(" close=")">
                #{accNo}
            </foreach>
        </if>
    </select>

    <!--
    统计某营业日账号的消费、付款合计，按照账号、账务类型进行统计
    -->
    <select id="statAccountByBizDate" resultMap="noAccountMap">
        SELECT
        no,acc_type,guest_name,
        SUM(CASE WHEN sub_type = 'consume_account' THEN fee ELSE 0 END) AS consume_total,
        SUM(CASE WHEN sub_type = 'pay_account' THEN fee ELSE 0 END) AS payment_total
        FROM
        biz_account
        WHERE gcode=#{gcode} and hcode=#{hcode} and biz_date=#{bizDate} and acc_type!=#{accType}
        AND sub_code NOT IN ('bank_pre_auth', 'scan_gun_pre_auth')
        GROUP BY no,acc_type,guest_name
    </select>

    <!--
统计某营业日账号的消费、付款合计，按照账号、账务类型进行统计
-->
    <select id="statAccRecordByBizDate" resultMap="accRecordMap">
        SELECT
        gcode,hcode,
        GROUP_CONCAT(CASE WHEN sub_type = 'consume_account' THEN sub_code END ORDER BY sub_code) AS sub_code,
        SUM(CASE WHEN sub_type = 'consume_account' THEN fee ELSE 0 END) AS consume_total,
        SUM(CASE WHEN sub_type = 'pay_account' THEN fee ELSE 0 END) AS payment_total
        FROM
        biz_account
        WHERE gcode=#{gcode} and hcode=#{hcode} and biz_date=#{bizDate} and acc_type=#{accType}
        GROUP BY gcode,hcode,pay_no
    </select>


    <!--
    统计当前班次下的结账业务，按照结账号批次统计
    -->
    <select id="getCloseAccountSumByTogetherCode"
            resultType="info.qizhi.aflower.module.pms.controller.admin.account.vo.CloseAccountRespVO">
        select pay_no, sum(fee) as fee,pay_biz_date,pay_time,pay_shift_no,payer from biz_account where
        gcode=#{gcode} and hcode=#{hcode} and together_code=#{togetherCode}
        and pay_shift_no=#{payShiftNo} and pay_biz_date=#{bizDate} and state='closed' and sub_type='pay_account' group by
        pay_no,pay_biz_date,pay_time,pay_shift_no,payer
    </select>

</mapper>