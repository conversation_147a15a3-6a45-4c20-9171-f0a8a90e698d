<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.arset.ArSetMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectArSetPageByMerchant" resultType="info.qizhi.aflower.module.pms.dal.dataobject.arset.ArSetDO">
        select a.* from pf_ar_set a
        <if test="hcode != null and hcode !=''">
            join pf_ar_set_merchant b on a.ar_set_code = b.ar_set_code
        </if>
        where a.gcode = #{gcode}
        <if test="hcode != null and hcode !=''">
            and b.hcode = #{hcode}
        </if>
        <if test="keyword != null and keyword !=''">
            and (a.ar_set_name like concat('%',#{keyword},'%') or a.unit_name like concat('%',#{keyword},'%'))
        </if>
        <if test="isEnable != null and isEnable !=''">
            and a.is_enable = #{isEnable}
        </if>
        <if test="creditAccType !=null and creditAccType !=''">
            and a.credit_acc_type = #{creditAccType}
        </if>
        order by a.id desc
    </select>
    
</mapper>