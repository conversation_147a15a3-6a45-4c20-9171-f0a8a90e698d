<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.order.OrderTogetherMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <select id="selectOrderTodayCheckout"
            resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO">
        SELECT *
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND checkout_time BETWEEN #{nowStart} AND #{nowEnd}
        AND order_no IN
        <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR r_no = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            and channel_code = #{reqVO.channelCode}
        </if>
        <if test="reqVO.guestSrcType != null and reqVO.guestSrcType != ''">
            and guest_src_type = #{reqVO.guestSrcType}
        </if>
        <if test="reqVO.rtCode != null and reqVO.rtCode != ''">
            and rt_code = #{reqVO.rtCode}
        </if>
        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            and checkin_type = #{reqVO.checkinType}
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
        ORDER BY id DESC
    </select>

    <select id="selectOrderTogetherPendingAccount"
            resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO">
        SELECT *
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND order_no IN
            <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
                 #{item}
            </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR rno = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
        LIMIT #{start}, #{pageSize}
    </select>

    <select id="selectOrderTogetherPendingAccountCount" resultType="Long">
        SELECT count(*)
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND order_no IN
        <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR rno = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
    </select>

    <select id="selectOrderTogetherCheckedOut"
            resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO">
        SELECT *
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND order_no IN
        <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR rno = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
        LIMIT #{start}, #{pageSize}
    </select>

    <select id="selectOrderTogetherCheckedOutCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND order_no IN
        <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR rno = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
    </select>


    <select id="selectOrderHisTogetherPendingAccount"
            resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO">
        SELECT *
        FROM biz_order_together
        where
        gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        AND order_no IN
        <foreach collection="reqVO.orderNo" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="reqVO.keyWords != null and reqVO.keyWords != ''">
            AND (
            name = #{reqVO.keyWords}
            OR phone = #{reqVO.keyWords}
            OR r_no = #{reqVO.keyWords}
            )
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != ''">
            <choose>
                <when test="reqVO.timeType == '0'">
                    AND checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == '1'">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
        <if test="reqVO.start != null and reqVO.start != '' reqVO.orderHisPageSize != null and reqVO.orderHisPageSize != '' ">
            LIMIT #{reqVO.start}, #{reqVO.orderHisPageSize}
        </if>
    </select>

    <update id="updateAwakenTime">
        UPDATE biz_order_together
        <trim prefix="SET" suffixOverrides=",">
                <if test="awakenTime != null">
                    awaken_time = #{awakenTime},
                </if>
                <if test="awakenTime == null">
                    awaken_time = null,
                </if>
            </trim>
        WHERE id = #{id}
    </update>
</mapper>