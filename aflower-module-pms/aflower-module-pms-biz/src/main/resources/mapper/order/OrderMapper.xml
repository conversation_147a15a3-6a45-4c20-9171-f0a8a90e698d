<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="info.qizhi.aflower.module.pms.dal.mysql.order.OrderMapper">
    <update id="updateBatchNull">
            UPDATE biz_order set r_code = null, r_no = null
            WHERE id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.id}
            </foreach>
    </update>

    <!-- 统计在住订单各个房型数 -->
    <select id="statRoomTypeCount" parameterType="info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderReqVO" resultType="info.qizhi.aflower.module.pms.controller.admin.order.vo.order.RtRoomNumVO">
        select rt_code as rtCode, count(1) as count from biz_order where gcode = #{gcode} and hcode = #{hcode}
        <if test="isMeetingRoom != null and isMeetingRoom !=''">
            and is_meeting_room = #{isMeetingRoom}
        </if>
        <if test="bookNo != null and bookNo !=''">
            and book_no = #{bookNo}
        </if>
        <if test="states != null and !states.isEmpty()">
            and state in
            <foreach collection="states" item="state" separator="," open="(" close=")">
                #{state}
            </foreach>
        </if>
        <if test="planCheckinTime != null">
            and (
            (plan_checkin_time &lt;= #{planCheckoutTime} and plan_checkout_time &gt;= #{planCheckinTime})
            )
        </if>
        group by rt_code
    </select>
<!--    在住订单-->
    <select id="selectOrderList" resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO">
        SELECT *
        FROM biz_order
        WHERE gcode = #{reqVO.gcode}
        AND hcode = #{reqVO.hcode}
        AND state = #{state}
        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            AND channel_code = #{reqVO.channelCode}
        </if>
        <if test="reqVO.rtCode != null and reqVO.rtCode != ''">
            AND rt_code = #{reqVO.rtCode}
        </if>
        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND checkin_type = #{reqVO.checkinType}
        </if>
        <if test="reqVO.guestSrcType != null and reqVO.guestSrcType != ''">
            AND guest_src_type = #{reqVO.guestSrcType}
        </if>
        <if test="reqVO.seller != null and reqVO.seller != ''">
            AND seller = #{reqVO.seller}
        </if>
        <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
            AND guest_code = #{reqVO.guestCode}
        </if>
        <if test="reqVO.quickFilter != null and reqVO.quickFilter != '' ">
            <choose>
                <when test="reqVO.quickFilter == 0">
                    AND plan_checkout_time BETWEEN #{nowStart} AND #{nowEnd}
                </when>
                <when test="reqVO.quickFilter == 1">
                    AND checkin_time BETWEEN #{nowStart} AND #{nowEnd}
                </when>
            </choose>
        </if>
        <if test="reqVO.timeType != null and reqVO.timeType != '' ">
            <choose>
                <when test="reqVO.timeType == 0">
                    AND plan_checkout_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
                <when test="reqVO.timeType == 1">
                    AND checkin_time BETWEEN #{reqVO.startTime} AND #{reqVO.endTime}
                </when>
            </choose>
        </if>
    </select>

    <!--    今日离店-->
    <select id="selectTodayCheckout" resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO">
        SELECT *
        FROM biz_order b
        WHERE
            b.gcode = #{reqVO.gcode}
        AND b.hcode = #{reqVO.hcode}
        AND b.state = #{state}
        AND b.checkout_time BETWEEN #{nowStart} AND #{nowEnd}
        <if test="reqVO.rtCode != null and reqVO.rtCode != ''">
            AND b.rt_code = #{reqVO.rtCode}
        </if>
        <if test="reqVO.guestSrcType != null and reqVO.guestSrcType != ''">
            AND b.guest_src_type = #{reqVO.guestSrcType}
        </if>
        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND b.checkin_type = #{reqVO.checkinType}
        </if>
        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            AND b.channel_code = #{reqVO.channelCode}
        </if>
        <if test="reqVO.seller != null and reqVO.seller != ''">
            AND b.seller = #{reqVO.seller}
        </if>
        <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
            AND b.guest_code = #{reqVO.guestCode}
        </if>
        ORDER BY b.id DESC
    </select>

    <!--    挂s账(退房未结)分页-->
    <select id="selectOfOrderPagePendingAccount" resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO">
        SELECT
        *
        FROM biz_order b
        WHERE
        b.gcode = #{reqVO.gcode}
        AND b.hcode = #{reqVO.hcode}
        AND b.state = #{state}
        <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
            AND b.channel_code = #{reqVO.channelCode}
        </if>
        <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
            AND b.checkin_type = #{reqVO.checkinType}
        </if>
        <if test="reqVO.rtCode != null and reqVO.rtCode != ''">
            AND b.rt_code = #{reqVO.rtCode}
        </if>
        <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
            AND b.guest_code = #{reqVO.guestCode}
        </if>

    </select>

<!--    已退房-->
    <select id="selectCheckedOutPage" resultType="info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO">
        SELECT
        *
        FROM biz_order b
        where
            b.gcode = #{reqVO.gcode}
            AND b.state = #{state}
            AND b.hcode = #{reqVO.hcode}

            <if test="reqVO.channelCode != null and reqVO.channelCode != ''">
                AND b.channel_code = #{reqVO.channelCode}
            </if>

            <if test="reqVO.checkinType != null and reqVO.checkinType != ''">
                AND b.checkin_type = #{reqVO.checkinType}
            </if>

            <if test="reqVO.rtCode != null and reqVO.rtCode != ''">
                AND b.rt_code = #{reqVO.rtCode}
            </if>

            <if test="reqVO.guestCode != null and reqVO.guestCode != ''">
                AND b.guest_code = #{reqVO.guestCode}
            </if>
    </select>

</mapper>