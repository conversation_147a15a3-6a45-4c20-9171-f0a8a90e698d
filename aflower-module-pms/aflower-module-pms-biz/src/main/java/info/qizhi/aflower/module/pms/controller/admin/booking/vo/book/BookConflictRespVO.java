package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.CustomMMDDHHMMDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 冲突单 Response VO")
@Data
public class BookConflictRespVO {
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "入住人姓名")
    // @NameDesensitize
    private String name;

    @Schema(description = "入住人电话")
//    @MobileDesensitize
    private String phone;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = CustomMMDDHHMMDateTimeSerializer.class)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonSerialize(using = CustomMMDDHHMMDateTimeSerializer.class)
    private LocalDateTime endTime;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    private String checkinType;

    @Schema(description = "入住类型名称", example = "2")
    private String checkinTypeName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String noType;

    @Schema(description = "占用间数")
    private Integer num;


}
