package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 实时房态 - 房间 Response VO")
@Data
public class Room {
    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "锁房号")
    private String lockNo;

    @Schema(description = "MAC地址")
    private String mac;

    @Schema(description = "门锁版本号")
    private String lockVersion;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称")
    private String rtName;

    @Schema(description = "楼栋代码")
    private String buildingCode;

    @Schema(description = "楼栋名称")
    private String buildingName;

    @Schema(description = "楼层代码")
    private String floorCode;

    @Schema(description = "楼层名称")
    private String floorName;

    @Schema(description = "分机号")
    private String extNum;

    @Schema(description = "房间特性")
    private List<String> feature;

    @Schema(description = "是否启用")
    private String isEnable;

    @Schema(description = "是否锁定")
    private String isLocked;

    @Schema(description = "房态")
    private String state;

    @Schema(description = "打扫状态")
    private String cleanState;

    @Schema(description = "维修开始时间;当状态是维修时")
    private LocalDateTime repairStartTime;

    @Schema(description = "维修结束时间;当状态是维修时")
    private LocalDateTime repairEndTime;

    @Schema(description = "维修原因;当状态是维修时")
    private String repairReason;

    @Schema(description = "锁定原因")
    private String lockedReason;

    @Schema(description = "是否已检查")
    private String isChecked;

    @Schema(description = "床数")
    private String bedNum;

    @Schema(description = "是否已扫 是否已扫(0.未扫 1.已扫 2.未通过)")
    private String isCleaned;

    @Schema(description = "备注")
    private String remark;
}
