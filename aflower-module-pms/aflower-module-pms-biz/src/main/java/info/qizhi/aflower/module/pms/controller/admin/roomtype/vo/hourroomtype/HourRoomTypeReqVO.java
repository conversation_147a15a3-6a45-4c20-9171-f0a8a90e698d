package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.hourroomtype;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 时租房房型 Request VO")
@Data
@ToString(callSuper = true)
public class HourRoomTypeReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "时租房代码")
    private String hourCode;

    @Schema(description = "房型代码")
    private String rtCode;

}