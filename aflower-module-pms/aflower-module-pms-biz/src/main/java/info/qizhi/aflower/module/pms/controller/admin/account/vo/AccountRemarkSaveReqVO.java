package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 修改账务备注 Request VO")
@Data
public class AccountRemarkSaveReqVO {

    @Schema(description = "accNo", requiredMode = Schema.RequiredMode.REQUIRED, example = "16784")
    @NotEmpty(message = "{accountNo.notempty}")
    private String accNo;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}