package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.future;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 远期房态 Response VO")
@Data
public class FutureRoomStatusRespVO {

    @Schema(description = "房间信息")
    private List<RoomInfo> roomInfos;

    @Schema(description = "每日可售数")
    private List<DateRoomInfo> dateRoomInfos;

    @Data
    public static class DateRoomInfo {
        @Schema(description = "日期")
        @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
        private LocalDate date;

        @Schema(description = "星期")
        private Integer week;

        @Schema(description = "可售房间数")
        private Integer availableRoomCount;
    }

    @Data
    public static class RoomInfo {
        @Schema(description = "房号代码")
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房号")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "房型名称")
        private String rtName;

        @Schema(description = "房态")
        private String state;

        @Schema(description = "是否锁房")
        private String isLocked;

        @Schema(description = "楼栋代码")
        private String buildingCode;

        @Schema(description = "楼层代码")
        private String floorCode;

        @Schema(description = "报修开始时间")
        private LocalDateTime repairStartTime;

        @Schema(description = "报修结束时间")
        private LocalDateTime repairEndTime;

        @Schema(description = "报修原因")
        private String repairReason;

        @Schema(description = "订单列表")
        private List<Order> orders;

        @Data
        public static class Order {
            @Schema(description = "订单号")
            private String orderNo;
            @Schema(description = "预订单号")
            private String bookNo;
            @Schema(description = "入住时间")
            private LocalDateTime checkinTime;
            @Schema(description = "预抵时间")
            private LocalDateTime planCheckinTime;
            @Schema(description = "预计离店时间")
            private LocalDateTime planCheckoutTime;
            @Schema(description = "担保方式")
            private String guarantyStyle;
            @Schema(description = "担保方式名称")
            private String guarantyStyleName;
            @Schema(description = "客源类型")
            private String guestSrcType;
            @Schema(description = "客源类型名称")
            private String guestSrcTypeName;
            @Schema(description = "客户姓名")
            private String name;
            @Schema(description = "客户电话")
            private String phone;
            @Schema(description = "付款金额")
            private Long payAmount;
            @Schema(description = "入住类型")
            private String checkinType;
            @Schema(description = "入住类型名称")
            private String checkinTypeName;
            @Schema(description = "订单状态 已预订: no_check_in、在住:check_in 、已离店:check_out、取消：cancel 预订单noshow或取消时、挂账: credit")
            private String state;
            @Schema(description = "渠道代码")
            private String channelCode;
            @Schema(description = "渠道")
            private String channelName;
        }
    }


}
