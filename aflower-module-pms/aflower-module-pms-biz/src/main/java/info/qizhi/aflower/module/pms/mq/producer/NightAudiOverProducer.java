package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.framework.common.enums.HotelMessageTypeEnum;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.HotelMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审完成生产者
 * @Version: 1.0
 */
@Slf4j
@Component
public class NightAudiOverProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param msg 消息内容
     * @param hcode 门店代码
     */
    public void sendNightAudiOverMessage(String hcode, String msg) {
        HotelMessage message = new HotelMessage().setHcode(hcode)
                .setType(HotelMessageTypeEnum.NIGHTAUDITOVER.getCode())
                .setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HOTEL_MESSAGE_QUEUE, message);
    }
}
