package info.qizhi.aflower.module.pms.controller.admin.booking.vo.team;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 团队分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeamPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "团队代码")
    private String teamCode;

    @Schema(description = "团队代码列表")
    private List<String> teamCodes;

    @Schema(description = "团队名称", example = "王五")
    private String teamName;

    @Schema(description = "团队类型;请看数据字典", example = "2")
    private String teamType;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "客源类型;请看数据字典", example = "2")
    private String guestSrcType;

    @Schema(description = "入住时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkinTime;

    @Schema(description = "预离时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planCheckoutTime;

    @Schema(description = "状态;请看数据字典")
    private String state;

    @Schema(description = "账务状态 open:未结 closed:已结. 团队主单退房结账完成后，账务状态变为closed")
    private String accState;

    @Schema(description = "结账时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] payTime;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "结账操作人")
    private String payOperator;

    @Schema(description = "客人账号;如果客源类别为会员 账号为会员的账号 为单位则为单位账号 为中介则为中介账号", example = "5438")
    private String guestCode;

    @Schema(description = "总消费")
    private Integer totalFee;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}