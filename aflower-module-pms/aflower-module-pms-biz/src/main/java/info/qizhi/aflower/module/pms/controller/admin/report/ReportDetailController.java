package info.qizhi.aflower.module.pms.controller.admin.report;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail.*;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.ardetail.ArDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.ardetail.ArDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.arentrydetail.ArEntryDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.arentrydetail.ArEntryDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.arverifydetail.ArVerifyDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.arverifydetail.ArVerifyDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.bk.BKDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.bk.BKReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.book.BookGuestReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.book.BookGuestReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast.BreakfastPrepareReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast.BreakfastPrepareReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast.BuyBkOrReturnBkDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast.BuyBkOrReturnBkDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail.GoodsSellDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail.GoodsSellDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail.GoodsSellSummaryReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail.GoodsSellSummaryReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.guest.GuestBalanceDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.guest.HotelGuestDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.guest.HotelGuestReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.guest.InhouseGuestBalanceReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.invoice.InvoiceDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.invoice.InvoiceDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.membercardselldetail.MemberCardSellDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.membercardselldetail.MemberCardSellDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.memberrechargedetail.MemberRechargeDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.memberrechargedetail.MemberRechargeDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.nightpriceprequalify.NightPricePrequalifyReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.nightpriceprequalify.NightPricePrequalifyReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean.RoomCleanDetailReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean.RoomCleanDetailReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean.RoomCleanSummaryReportRespVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail.SettleDetailReportReqVO;
import info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail.SettleDetailReportRespVO;
import info.qizhi.aflower.module.pms.service.report.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 报表")
@RestController
@RequestMapping("/pms/report")
@Validated
public class ReportDetailController {

    @Resource
    private GoodsSellDetailService goodsSellDetailService;

    @Resource
    private ArSellDetailService arSellDetailService;

    @Resource
    private SettleDetailService settleDetailService;

    @Resource
    private MemberCardSellDetailService memberCardSellDetailService;

    @Resource
    private MemberRechargeDetailService memberRechargeDetailService;

    @Resource
    private BreakfastPrepareReportService breakfastPrepareReportService;

    @Resource
    private NightPricePrequalifyReportService nightPricePrequalifyReportService;

    @Resource
    private ArVerifyDetailService arVerifyDetailService;

    @Resource
    private ArEntryDetailService arEntryDetailService;

    @Resource
    private BuyBkOrReturnBkDetailService buyBkOrReturnBkDetailService;

    @Resource
    private AccountDetailService accountDetailService;
    @Resource
    private RoomCleanReportService roomCleanReportService;
    @Resource
    private GuestReportService guestReportService;
    @Resource
    private BreakfastService breakfastService;
    @Resource
    private InvoiceReportService invoiceReportService;
    @Resource
    private BookReportService bookReportService;

    @GetMapping("/goods")
    @Operation(summary = "获得商品销售明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:goods')")
    public CommonResult<GoodsSellDetailReportRespVO> getGoodsSell(@Valid GoodsSellDetailReqVO reqVO) {
        GoodsSellDetailReportRespVO goodsSellDetail = goodsSellDetailService.getGoodsSellDetail(reqVO);
        return success(goodsSellDetail);
    }

    @GetMapping("/goods-summary")
    @Operation(summary = "获得商品销售汇总报表")
    @PreAuthorize("@ss.hasPermission('pms:report:goods-summary')")
    public CommonResult<GoodsSellSummaryReportRespVO> getGoodsSellSummaryReport(@Valid GoodsSellSummaryReqVO reqVO) {
        GoodsSellSummaryReportRespVO goodsSellDetail = goodsSellDetailService.getGoodsSellSummaryReport(reqVO);
        return success(goodsSellDetail);
    }

    @GetMapping("/ar-sell")
    @Operation(summary = "获得AR账发生明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:ar-sell')")
    public CommonResult<ArDetailReportRespVO> getArSell(@Valid ArDetailReqVO reqVO) {
        ArDetailReportRespVO arDetailReportRespVO = arSellDetailService.getArSellDetail(reqVO);
        return success(arDetailReportRespVO);
    }


    @GetMapping("/settle-detail")
    @Operation(summary = "获得前台结账报表")
    @PreAuthorize("@ss.hasPermission('pms:report:settle-detail')")
    public CommonResult<SettleDetailReportRespVO> getSettleDetailReport(@Valid SettleDetailReportReqVO reqVO) {
        SettleDetailReportRespVO settleDetailReportRespVO = settleDetailService.getSettleDetailReport(reqVO);
        return success(settleDetailReportRespVO);
    }

    @GetMapping("/member-card")
    @Operation(summary = "获得会员卡销售明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:member-card')")
    public CommonResult<MemberCardSellDetailReportRespVO> getMemberCardDetail(@Valid MemberCardSellDetailReqVO reqVO) {
        MemberCardSellDetailReportRespVO memberCardSellDetail = memberCardSellDetailService.getMemberCardSellDetailReport(reqVO);
        return success(memberCardSellDetail);
    }


    @GetMapping("/member-recharge")
    @Operation(summary = "获得会员充值&支付明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:member-recharge')")
    public CommonResult<MemberRechargeDetailReportRespVO> getMemberRechargeDetail(@Valid MemberRechargeDetailReqVO reqVO) {
        MemberRechargeDetailReportRespVO memberRechargeDetail = memberRechargeDetailService.getMemberRechargeDetail(reqVO);
        return success(memberRechargeDetail);
    }

    @GetMapping("/breakfast-prepare")
    @Operation(summary = "获得早餐备餐报表")
    @PreAuthorize("@ss.hasPermission('pms:report:breakfast-prepare')")
    public CommonResult<BreakfastPrepareReportRespVO> getBreakfastPrepareReport(@Valid BreakfastPrepareReqVO reqVO) {
        BreakfastPrepareReportRespVO breakfastPrepare = breakfastPrepareReportService.getBreakfastPrepareReport(reqVO);
        return success(breakfastPrepare);
    }

    @GetMapping("/night-prequalify")
    @Operation(summary = "获得夜审房价预审报表")
    @PreAuthorize("@ss.hasPermission('pms:report:night-prequalify')")
    public CommonResult<NightPricePrequalifyReportRespVO> getNightPricePrequalifyReport(@Valid NightPricePrequalifyReqVO reqVO) {
        NightPricePrequalifyReportRespVO nightPricePrequalifyReport = nightPricePrequalifyReportService.getNightPricePrequalifyReport(reqVO);
        return success(nightPricePrequalifyReport);
    }

    @GetMapping("/ar-verify")
    @Operation(summary = "获得AR核销明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:ar-verify')")
    public CommonResult<ArVerifyDetailReportRespVO> getArVerifyDetail(@Valid ArVerifyDetailReqVO reqVO) {
        ArVerifyDetailReportRespVO arVerifyDetailReport = arVerifyDetailService.getArVerifyDetail(reqVO);
        return success(arVerifyDetailReport);
    }

    @GetMapping("/ar-entry")
    @Operation(summary = "获得AR账收款明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:ar-entry')")
    public CommonResult<ArEntryDetailReportRespVO> getArEntryDetailReport(@Valid ArEntryDetailReqVO reqVO) {
        ArEntryDetailReportRespVO arEntryDetailReport = arEntryDetailService.getArEntryDetailReport(reqVO);
        return success(arEntryDetailReport);
    }

    @GetMapping("/buybk-or-returnbk")
    @Operation(summary = "获得购早/退早明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:buybk-or-returnbk')")
    public CommonResult<BuyBkOrReturnBkDetailReportRespVO> getArEntryDetailReport(@Valid BuyBkOrReturnBkDetailReqVO reqVO) {
        BuyBkOrReturnBkDetailReportRespVO buyBkOrReturnBkDetailReport = buyBkOrReturnBkDetailService.getArEntryDetailReport(reqVO);
        return success(buyBkOrReturnBkDetailReport);
    }

    @PostMapping("/pay")
    @Operation(summary = "获得付款明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:pay')")
    public CommonResult<PayAndConsumeDetailReportRespVO> getPayDetailReport(@Valid @RequestBody PayOrConsumeDetailReqVO reqVO) {
        PayAndConsumeDetailReportRespVO payDetailReport = accountDetailService.getPayDetailReport(reqVO);
        return success(payDetailReport);
    }

    @PostMapping("/consume")
    @Operation(summary = "获得消费明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:consume')")
    public CommonResult<PayAndConsumeDetailReportRespVO> getConsumeDetailReport(@Valid @RequestBody PayOrConsumeDetailReqVO reqVO) {
        PayAndConsumeDetailReportRespVO payDetailReport = accountDetailService.getConsumeDetailReport(reqVO);
        return success(payDetailReport);
    }

    @PostMapping("/room-clean")
    @Operation(summary = "获得房扫记录明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:room-clean')")
    public CommonResult<RoomCleanDetailReportRespVO> getRoomCleanDetailReport(@Valid @RequestBody RoomCleanDetailReqVO reqVO) {
        RoomCleanDetailReportRespVO roomCleanDetailReport =roomCleanReportService.getRoomCleanDetailReport(reqVO);
        return success(roomCleanDetailReport);
    }

    @PostMapping("/room-clean-summary")
    @Operation(summary = "获得房扫记录汇总报表")
    @PreAuthorize("@ss.hasPermission('pms:report:room-clean-summary')")
    public CommonResult<RoomCleanSummaryReportRespVO> getRoomCleanSummaryReport(@Valid @RequestBody RoomCleanDetailReqVO reqVO) {
        RoomCleanSummaryReportRespVO roomCleanSummaryReportRespVO =roomCleanReportService.getRoomCleanSummaryReport(reqVO);
        return success(roomCleanSummaryReportRespVO);
    }

    @GetMapping("/guest-balance")
    @Operation(summary = "获得当前在住客人余额报表")
    @PreAuthorize("@ss.hasPermission('pms:report:guest-balance')")
    public CommonResult<InhouseGuestBalanceReportRespVO> getInhouseGuestBalanceReport(@Valid GuestBalanceDetailReqVO reqVO) {
        InhouseGuestBalanceReportRespVO inhouseGuestBalanceReport =guestReportService.getInhouseGuestBalanceReport(reqVO);
        return success(inhouseGuestBalanceReport);
    }

    @GetMapping("/get-bk")
    @Operation(summary = "获得早餐备餐报表")
    @PreAuthorize("@ss.hasPermission('pms:report:get-bk')")
    public CommonResult<BKReportRespVO> getBreakfastReport(@Valid BKDetailReqVO reqVO) {
        BKReportRespVO breakfastReport = breakfastService.getBreakfastReport(reqVO);
        return success(breakfastReport);
    }

    @GetMapping("/get-invoice")
    @Operation(summary = "获得发票明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:get-invoice')")
    public CommonResult<InvoiceDetailReportRespVO> getInvoiceDetailReport(@Valid InvoiceDetailReqVO reqVO) {
        InvoiceDetailReportRespVO invoiceDetailReport = invoiceReportService.getInvoiceDetailReport(reqVO);
        return success(invoiceDetailReport);
    }

    @PostMapping("/transaction")
    @Operation(summary = "获得交易记录表")
    @PreAuthorize("@ss.hasPermission('pms:report:transaction')")
    public CommonResult<TransactionDetailReportRespVO> getTransactionReport(@Valid @RequestBody  TransactionDetailReqVO reqVO) {
        TransactionDetailReportRespVO transactionDetailReport = accountDetailService.getTransactionReport(reqVO);
        return success(transactionDetailReport);
    }

    @GetMapping("/hotel-guest")
    @Operation(summary = "获得住店客人明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:hotel-guest')")
    public CommonResult<HotelGuestReportRespVO> getHotelGuestDetailReport(@Valid HotelGuestDetailReqVO reqVO) {
        HotelGuestReportRespVO hotelGuestReportRespVO = guestReportService.getHotelGuestDetailReport(reqVO);
        return success(hotelGuestReportRespVO);
    }

    @GetMapping("/subject-summary")
    @Operation(summary = "获得酒店经营科目汇总月报")
    @PreAuthorize("@ss.hasPermission('pms:report:subject-summary')")
    public CommonResult<SubjectSummaryReportRespVO> getSubjectSummaryReport(@Valid SubjectSummaryReqVO reqVO) {
        SubjectSummaryReportRespVO subjectSummaryReport = accountDetailService.getSubjectSummaryReport(reqVO);
        return success(subjectSummaryReport);
    }

    @GetMapping("/red-adjust")
    @Operation(summary = "获得冲账调账明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:red-adjust')")
    public CommonResult<RedAndAdjustReportRespVO> getRedAndAdjustReport(@Valid RedAndAdjustReqVO reqVO) {
        RedAndAdjustReportRespVO report = accountDetailService.getRedAndAdjustReport(reqVO);
        return success(report);
    }

    @GetMapping("/transfer")
    @Operation(summary = "获得转账报表")
    @PreAuthorize("@ss.hasPermission('pms:report:transfer')")
    public CommonResult<TransferAccountReportRespVO> getTransferAccountReport(@Valid TransferAccountReqVO reqVO) {
        TransferAccountReportRespVO report = accountDetailService.getTransferAccountReport(reqVO);
        return success(report);
    }

    @GetMapping("/split")
    @Operation(summary = "获得拆账报表")
    @PreAuthorize("@ss.hasPermission('pms:report:split')")
    public CommonResult<SplitAccountReportRespVO> getSplitAccountReport(@Valid TransferAccountReqVO reqVO) {
        SplitAccountReportRespVO report = accountDetailService.getSplitAccountReport(reqVO);
        return success(report);
    }

    @GetMapping("/change/price")
    @Operation(summary = "获得人工修改房价明细报表")
    @PreAuthorize("@ss.hasPermission('pms:report:change-price')")
    public CommonResult<ChangePriceReportRespVO> getChangePriceReport(@Valid ChangePriceReqVO reqVO) {
        ChangePriceReportRespVO report = accountDetailService.getChangePriceReport(reqVO);
        return success(report);
    }

    @GetMapping("/book")
    @Operation(summary = "获得今日预抵客人报表")
    @PreAuthorize("@ss.hasPermission('pms:report:book')")
    public CommonResult<BookGuestReportRespVO> getBookGuestReport(@Valid BookGuestReqVO reqVO) {
        BookGuestReportRespVO report = bookReportService.getBookGuestReport(reqVO);
        return success(report);
    }
}
