package info.qizhi.aflower.module.pms.controller.admin.booking;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamRespVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.booking.TeamDO;
import info.qizhi.aflower.module.pms.service.booking.TeamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "biz - 团队")
@RestController
@RequestMapping("/pms/team")
@Validated
public class TeamController {

    @Resource
    private TeamService teamService;

    @PutMapping("/update")
    @Operation(summary = "更新团队")
    @PreAuthorize("@ss.hasPermission('pms:team:update')")
    public CommonResult<Boolean> updateTeam(@Valid @RequestBody TeamSaveReqVO updateReqVO) {
        teamService.updateTeamByTeamCode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除团队")
    @Parameter(name = "teamCode", description = "团队代码", required = true)
    @PreAuthorize("@ss.hasPermission('pms:team:delete')")
    public CommonResult<Boolean> deleteTeam(@RequestParam("teamCode") String teamCode) {
        teamService.deleteTeam(teamCode);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得团队")
    @Parameter(name = "teamCode", description = "团队代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:team:query')")
    public CommonResult<TeamRespVO> getTeam(@RequestParam("teamCode") String teamCode) {
        TeamDO team = teamService.getTeam(teamCode);
        return success(BeanUtils.toBean(team, TeamRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得团队分页")
    @PreAuthorize("@ss.hasPermission('pms:team:query')")
    public CommonResult<PageResult<TeamRespVO>> getTeamPage(@Valid TeamPageReqVO pageReqVO) {
        PageResult<TeamDO> pageResult = teamService.getTeamPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeamRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得团队列表(根据状态可以获取不同状态团队)")
    @PreAuthorize("@ss.hasPermission('pms:team:query')")
    public CommonResult<List<TeamRespVO>> getTeamList(@Valid TeamReqVO reqVO) {
        List<TeamDO> list = teamService.getTeamList(reqVO);
        return success(BeanUtils.toBean(list, TeamRespVO.class));
    }


}