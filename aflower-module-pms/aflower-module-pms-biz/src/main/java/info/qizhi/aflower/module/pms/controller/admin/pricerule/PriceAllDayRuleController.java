package info.qizhi.aflower.module.pms.controller.admin.pricerule;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleSaveReqVO;
import info.qizhi.aflower.module.pms.service.pricerule.PriceAllDayRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 全天房计费规则")
@RestController
@RequestMapping("/pms/price-all-day-rule")
@Validated
public class PriceAllDayRuleController {

    @Resource
    private PriceAllDayRuleService priceAllDayRuleService;

    @PutMapping("/update")
    @Operation(summary = "更新全天房计费规则")
    @PreAuthorize("@ss.hasPermission('pms:price-all-day-rule:update')")
    public CommonResult<Boolean> updatePriceAllDayRule(@Valid @RequestBody PriceAllDayRuleSaveReqVO updateReqVO) {
        priceAllDayRuleService.updatePriceAllDayRule(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得全天房计费规则")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:price-rule:query')")
    public CommonResult<PriceAllDayRuleRespVO> getPriceAllDayRule(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        PriceAllDayRuleRespVO priceAllDayRule = priceAllDayRuleService.getPriceAllDayRule(new PriceAllDayRuleReqVO().setGcode(gcode).setHcode(hcode));
        return success(priceAllDayRule);
    }


}