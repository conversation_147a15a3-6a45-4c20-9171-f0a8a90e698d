package info.qizhi.aflower.module.pms.controller.admin.hoteldeviceset;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.hoteldeviceset.vo.HotelDeviceSetReqVO;
import info.qizhi.aflower.module.pms.controller.admin.hoteldeviceset.vo.HotelDeviceSetRespVO;
import info.qizhi.aflower.module.pms.dal.dataobject.hoteldeviceset.HotelDeviceSetDO;
import info.qizhi.aflower.module.pms.service.hoteldeviceset.HotelDeviceSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备信息")
@RestController
@RequestMapping("/pms/hotel-device-set")
@Validated
public class HotelDeviceSetController {

    @Resource
    private HotelDeviceSetService hotelDeviceSetService;

    @GetMapping("/get-door")
    @Operation(summary = "获得门锁信息")
    @Parameters(value = {
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true),
            @Parameter(name = "lockVersion", description = "门锁版本号", required = true)
    })
    public CommonResult<HotelDeviceSetRespVO> getDeviceSet(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("lockVersion") String lockVersion) {
        HotelDeviceSetDO hotelDeviceSetDO = hotelDeviceSetService.getDeviceDoorLockSet(gcode, hcode,  lockVersion);
        return success(BeanUtils.toBean(hotelDeviceSetDO, HotelDeviceSetRespVO.class));
    }

    @GetMapping("/get-by-id")
    @Operation(summary = "通过id获得门锁设备信息")
    @Parameters(value = {
            @Parameter(name = "id", description = "id", required = true),
    })
    public CommonResult<HotelDeviceSetRespVO> getHotelDeviceById(@RequestParam(value = "id") Long id) {
        HotelDeviceSetRespVO hotelDeviceSetDO = hotelDeviceSetService.getDeviceById(id);
        return success(hotelDeviceSetDO);
    }

    @GetMapping("/get-door-locks")
    @Operation(summary = "获得酒店门锁列表")
    @Parameters(value = {
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true)
    })
    public CommonResult<List<HotelDeviceSetRespVO>> getDoorLocks(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        List<HotelDeviceSetRespVO> hotelDeviceSetDOList = hotelDeviceSetService.getDeviceDoorLockList(gcode, hcode);
        return success(hotelDeviceSetDOList);
    }

    @PutMapping("/update")
    @Operation(summary = "更新酒店门锁设备信息")
    public CommonResult<Boolean> updateHotelDevice(@RequestBody HotelDeviceSetReqVO reqVO) {
        hotelDeviceSetService.updateDevice(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除酒店门锁设备信息")
    public CommonResult<Boolean> deleteHotelDevice(@RequestParam(value = "id") Long id) {
        hotelDeviceSetService.deleteById(id);
        return success(true);
    }

    @PostMapping("/create")
    @Operation(summary = "创建酒店门锁设备信息")
    public CommonResult<Boolean> createHotelDevice(@RequestBody HotelDeviceSetReqVO reqVO) {
        hotelDeviceSetService.createDevice(reqVO);
        return success(true);
    }

}