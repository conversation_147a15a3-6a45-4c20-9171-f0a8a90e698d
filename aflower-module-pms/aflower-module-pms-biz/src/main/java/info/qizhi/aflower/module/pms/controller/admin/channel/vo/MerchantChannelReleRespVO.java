package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店OTA关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MerchantChannelReleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4861")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "渠道门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道门店代码")
    private String channelHcode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道代码")
    private String channelCode;

    @Schema(description = "状态;0：无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1:有效")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}