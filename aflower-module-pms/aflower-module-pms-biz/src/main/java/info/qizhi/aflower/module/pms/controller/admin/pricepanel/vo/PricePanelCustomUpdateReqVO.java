package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelCustomDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 房价牌(自定义房型房价) Request VO")
@Data
public class PricePanelCustomUpdateReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "自定义房型房价代码;系统生成 (唯一标识)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "门市价", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long price;

    @Schema(description = "会员价", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<PricePanelCustomDO.MemberPriceParameter> memberPrice;

}
