package info.qizhi.aflower.module.pms.controller.admin.initbizdata;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.initbizdata.vo.InitAgentReqVO;
import info.qizhi.aflower.module.pms.controller.admin.initbizdata.vo.InitBizDataReqVO;
import info.qizhi.aflower.module.pms.service.initbiz.InitPmsBizDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 初始化酒店营业数据")
@RestController
@RequestMapping("/pms/init-biz-data")
@Validated
public class InitBizDataController {

    @Resource
    private InitPmsBizDataService initPmsBizDataService;

    @PostMapping("/init")
    @Operation(summary = "初始化酒店营业数据(禁止执行，会删除酒店数据)")
//    @PreAuthorize("@ss.hasRole('super_admin')")
    public CommonResult<Boolean> createDeposit(@Valid @RequestBody InitBizDataReqVO reqVO) {
        initPmsBizDataService.initBizData(reqVO.getGcode(), reqVO.getHcode(), reqVO.getHname());
        return success(true);
    }

    @PostMapping("/init/stat/channel")
    @Operation(summary = "初始化订单统计渠道")
    //@PreAuthorize("@ss.hasRole('super_admin')")
    public CommonResult<Boolean> initChannel(@Valid @RequestBody InitAgentReqVO reqVO) {
        initPmsBizDataService.initChannel(reqVO);
        return success(true);
    }

}