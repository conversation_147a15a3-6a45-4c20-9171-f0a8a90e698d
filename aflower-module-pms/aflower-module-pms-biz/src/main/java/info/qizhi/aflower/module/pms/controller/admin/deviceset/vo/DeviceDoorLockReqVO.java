package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 设备信息 Request VO")
@Data
public class DeviceDoorLockReqVO {

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    private String  hcode;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceCode;

    @Schema(description = "设备品牌", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandName;

    @Schema(description = "设备品牌代码")
    @NotEmpty(message = "{deviceCode.notempty}")
    private String brandCode;

    @Schema(description = "设备型号名称", example = "张三")
    private String deviceVerName;

    @Schema(description = "设备型号代码")
    @NotEmpty(message = "{deviceVerName.notempty}")
    private String deviceVerCode;

}