package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 确认预授权 Request VO")
@Data
public class ConfirmPreAuthReqVO {

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accountNo.notempty}")
    private String accNo;

    @Schema(description = "实收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    @DecimalMin(value = "0", inclusive = false, message = "{fee.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

}