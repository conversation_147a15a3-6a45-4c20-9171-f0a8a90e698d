package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 预订单(普通预订团队预订) Response VO")
@Data
@ExcelIgnoreUnannotated
public class BookRespVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订单号")
    private String bookNo;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道名称")
    private String channelName;

    @Schema(description = "订单来源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单来源")
    private String orderSource;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("客源类型")
    private String guestSrcType;

    @Schema(description = "客源类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("客源类型名称")
    private String guestSrcTypeName;

    @Schema(description = "预订类型;DD_PT：(个人)普通订单  DD_TD：团队订单", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("预订类型;DD_PT：(个人)普通订单  DD_TD：团队订单")
    private String bookType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    @ExcelProperty("入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队")
    private String checkinType;

    @Schema(description = "入住类型名称", example = "2")
    @ExcelProperty("入住类型名称;")
    private String checkinTypeName;

    @Schema(description = "小时房代码;入住类型为时租房时，该字段有值")
    @ExcelProperty("小时房代码;入住类型为时租房时，该字段有值")
    private String hourCode;

    @Schema(description = "团队代码;团队预订时输入")
    private String teamCode;

    @Schema(description = "团队名称")
    @ExcelProperty("团队名称")
    private String teamName;

    @Schema(description = "团队类型")
    @ExcelProperty("团队类型")
    private String teamType;

    @Schema(description = "客人代码;会员代码、协议单位、中介代码")
    @ExcelProperty("客人代码;会员代码、协议单位、中介代码")
    private String guestCode;

    @Schema(description = "客人名称;会员姓名、协议单位、中介名称", example = "张三")
    @ExcelProperty("客人名称;会员姓名、协议单位、中介名称")
    private String guestName;

    @Schema(description = "合同号;团队预订时输入")
    @ExcelProperty("合同号;团队预订时输入")
    private String contractNo;

    @Schema(description = "预抵时间;普通预订存储，团队预订低离时间存储在预订房型中", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预抵时间;普通预订存储，团队预订低离时间存储在预订房型中")
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间;普通预订存储，团队预订低离时间存储在预订房型中")
    @ExcelProperty("预离时间;普通预订存储，团队预订低离时间存储在预订房型中")
    private LocalDateTime planCheckoutTime;

    @Schema(description = "预订人(联系人);如果是团队预订，这里保存团队的联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订人(联系人);如果是团队预订，这里保存团队的联系人姓名")
    private String contact;

    @Schema(description = "预订人电话;联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订人电话;联系电话")
    private String phone;

    @Schema(description = "入住人姓名")
    @ExcelProperty("入住人姓名")
    private String checkinPerson;

    @Schema(description = "入住人电话")
    @ExcelProperty("入住人电话")
    private String checkinPhone;

    @Schema(description = "担保方式")
    @ExcelProperty("担保方式")
    private String guarantyStyle;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态")
    private String state;

    @Schema(description = "状态名称")
    @ExcelProperty("状态名称")
    private String stateName;

    @Schema(description = "保留时间")
    @ExcelProperty("保留时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalDateTime retainTime;

    @Schema(description = "是否发短信;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否发短信;0：否 1：是")
    private String isSendSms;

    @Schema(description = "市场活动代码")
    @ExcelProperty("市场活动代码")
    private String marketActivityCode;

    @Schema(description = "市场活动名称", example = "王五")
    @ExcelProperty("市场活动名称")
    private String marketActivityName;

    @Schema(description = "销售员")
    @ExcelProperty("销售员")
    private String seller;

    @Schema(description = "外部订单号")
    @ExcelProperty("外部订单号")
    private String outOrderNo;

    @Schema(description = "订单备注", example = "随便")
    @ExcelProperty("订单备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码")
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型名称")
    private String rtName;

    @Schema(description = "房价(原价)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房价(原价)")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

}