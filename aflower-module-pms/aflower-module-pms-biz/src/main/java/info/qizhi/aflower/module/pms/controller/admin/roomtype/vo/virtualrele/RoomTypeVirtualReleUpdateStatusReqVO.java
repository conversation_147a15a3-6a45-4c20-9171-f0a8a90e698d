package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 物理房型与虚拟房型关联新增/修改 Request VO")
@Data
public class RoomTypeVirtualReleUpdateStatusReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "892")
    @NotNull(message = "{id.notnull}")
    private Long id;

    @Schema(description = "是否有效")
    @NotEmpty(message = "{isEnable.notempty}")
    @InStringEnum(value= BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

}