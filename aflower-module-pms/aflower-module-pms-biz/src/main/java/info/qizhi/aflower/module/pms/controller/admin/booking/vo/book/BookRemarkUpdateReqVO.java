package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单备注修改 Request VO")
@Data
public class BookRemarkUpdateReqVO {

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{bookNo.notblank}")
    private String bookNo;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "外部订单号")
    private String outOrderNo;

    @Schema(description = "备注")
    @Size(max = 1000, message = "{remark.size}")
    private String remark;

    @Schema(description = "外部订单备注")
    @Size(max = 2555, message = "{remark.size}")
    private String outOrderRemark;

}