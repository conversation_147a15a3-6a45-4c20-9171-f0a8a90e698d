package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 人民币押金 Response VO")
@Data
public class DepositAccountRespVO {

    @Schema(description = "付款科目代码")
    private String subCode;

    @Schema(description = "付款金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;
}