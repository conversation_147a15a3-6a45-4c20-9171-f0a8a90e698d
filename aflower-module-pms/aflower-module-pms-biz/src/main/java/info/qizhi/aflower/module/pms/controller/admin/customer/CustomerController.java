package info.qizhi.aflower.module.pms.controller.admin.customer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.customer.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.customer.CustomerDO;
import info.qizhi.aflower.module.pms.service.customer.CustomerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 客历")
@RestController
@RequestMapping("/pms/customer")
@Validated
public class CustomerController {

    @Resource
    @Lazy
    private CustomerService customerService;

    @PutMapping("/update")
    @Operation(summary = "更新客历")
    @PreAuthorize("@ss.hasPermission('pms:customer:update')")
    public CommonResult<Boolean> updateCustomer(@Valid @RequestBody CustomerSaveReqVO updateReqVO) {
        customerService.updateCustomer(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除客历")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:customer:delete')")
    public CommonResult<Boolean> deleteCustomer(@RequestParam("id") Long id) {
        customerService.deleteCustomer(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得客历")
    @Parameters({
        @Parameter(name = "id", description = "id数据号", required = true),
        @Parameter(name = "gcode", description = "集团代码", required = true),
        @Parameter(name = "hcode", description = "酒店代码", required = true)
    })
    @PreAuthorize("@ss.hasPermission('pms:customer:query:page')")
    public CommonResult<CustomerRespVO> getCustomer(@RequestParam("id") String id, @RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        CustomerRespVO customer = customerService.getCustomer(id, gcode, hcode);
        return success(customer);
    }

    @GetMapping("/get-by-id-no")
    @Operation(summary = "根据身份证号获得客历")
    @Parameters({
            @Parameter(name = "idNo", description = "证件号码", required = true),
            @Parameter(name = "gcode", description = "集团代码", required = true),
    })
//    @PreAuthorize("@ss.hasPermission('pms:customer:query:page')")
    public CommonResult<CustomerRespVO> getCustomerByIdNo(@RequestParam("gcode") String gcode, @RequestParam("idNo") String idNo ) {
        List<CustomerDO> customers = customerService.getCustomerListByIdNos(new CustomerReqVO().setGcode(gcode).setIdNos(List.of(idNo)));
        if (CollUtil.isNotEmpty(customers)) {
            return success(BeanUtils.toBean(customers.getFirst(), CustomerRespVO.class));
        }
        return success(null);
    }

    @GetMapping("/getCustomerNotExist")
    @Operation(summary = "获得无脱敏客历")
    @Parameters({
            @Parameter(name = "id", description = "id数据号", required = true),
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "酒店代码", required = true)
    })
    @PreAuthorize("@ss.hasPermission('pms:customer:query:page')")
    public CommonResult<CustomerNoDesRespVO> getCustomerNotExist(@RequestParam("id") String id, @RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        CustomerRespVO customer = customerService.getCustomer(id, gcode, hcode);
        return success(BeanUtils.toBean(customer, CustomerNoDesRespVO.class));
    }

    @GetMapping("/black")
    @Operation(summary = "获取黑名单客户列表", description = "用于查询指定客历黑名单")
    //@PreAuthorize("@ss.hasPermission('pms:customer:query:black')")
    public CommonResult<List<CustomerRespVO>> getBlackCustomers(@Valid CustomerBlackReqVO reqVO) {
        List<CustomerDO> customers = customerService.getBlackCustomerList(reqVO);
        return success(BeanUtils.toBean(customers, CustomerRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得客历分页")
    @PreAuthorize("@ss.hasPermission('pms:customer:query:page')")
    public CommonResult<PageResult<CustomerRespVO>> getCustomerPage(@Valid CustomerPageReqVO pageReqVO) {
        PageResult<CustomerDO> pageResult = customerService.getCustomerPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomerRespVO.class));
    }

    @GetMapping("/select-page")
    @Operation(summary = "获得客历分页")
    public CommonResult<PageResult<CustomerNoDesRespVO>> getCustomerSelectPage(@Valid CustomerPageReqVO pageReqVO) {
        PageResult<CustomerDO> pageResult = customerService.getCustomerSelectPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustomerNoDesRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得客历列表")
    //@PreAuthorize("@ss.hasPermission('pms:customer:query:page')")
    public CommonResult<List<CustomerNoDesRespVO>> getCustomerList(@Valid CustomerSelectReqVO reqVO) {
        List<CustomerDO> customerList = customerService.getCustomerList(reqVO);
        return success(BeanUtils.toBean(customerList, CustomerNoDesRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出客历 Excel")
    @PreAuthorize("@ss.hasPermission('pms:customer:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportCustomerExcel(@Valid CustomerPageReqVO pageReqVO,
                                    HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustomerDO> list = customerService.getCustomerPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "客历.xls", "数据", CustomerRespVO.class,
                BeanUtils.toBean(list, CustomerRespVO.class));
    }

}