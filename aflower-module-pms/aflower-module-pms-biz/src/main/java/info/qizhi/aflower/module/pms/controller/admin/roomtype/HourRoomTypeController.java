package info.qizhi.aflower.module.pms.controller.admin.roomtype;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.hourroomtype.HourRoomTypeReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.hourroomtype.HourRoomTypeRespVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.hourroomtype.HourRoomTypeSaveReqVO;
import info.qizhi.aflower.module.pms.service.roomtype.HourRoomTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 时租房房型")
@RestController
@RequestMapping("/pms/hour-room-type")
@Validated
public class HourRoomTypeController {

    @Resource
    private HourRoomTypeService hourRoomTypeService;

    @PostMapping("/create")
    @Operation(summary = "创建时租房房型")
    @PreAuthorize("@ss.hasPermission('pms:hour-room-type:create')")
    public CommonResult<Long> createHourRoomType(@Valid @RequestBody HourRoomTypeSaveReqVO createReqVO) {
        return success(hourRoomTypeService.createHourRoomType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新时租房房型")
    @PreAuthorize("@ss.hasPermission('pms:hour-room-type:create')")
    public CommonResult<Boolean> updateHourRoomType(@Valid @RequestBody HourRoomTypeSaveReqVO updateReqVO) {
        hourRoomTypeService.updateHourRoomType(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除时租房房型")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:hour-room-type:create')")
    public CommonResult<Boolean> deleteHourRoomType(@RequestParam("id") Long id) {
        hourRoomTypeService.deleteHourRoomType(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得时租房房型")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:hour-room-type:query, pms:hour-room-type:update')")
    public CommonResult<HourRoomTypeRespVO> getHourRoomType(@RequestParam("id") Long id) {
        HourRoomTypeRespVO hourRoomType = hourRoomTypeService.getHourRoomType(id);
        return success(hourRoomType);
    }

    @GetMapping("/list")
    @Operation(summary = "获得时租房房型列表")
    //@PreAuthorize("@ss.hasPermission('pms:hour-room-type:query:list')")
    public CommonResult<List<HourRoomTypeRespVO>> getHourRoomTypeList(@Valid HourRoomTypeReqVO reqVO) {
        List<HourRoomTypeRespVO> list = hourRoomTypeService.getHourRoomTypeList(reqVO);
        return success(list);
    }


}