package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 交班报表获取账务 Response VO")
@Data
public class HandoverReportAccountRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23518")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "账号(入账账号);预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "账务号;系统生成唯一标识，每笔账都生成一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String accType;

    @Schema(description = "房间代码;如果客人换房，需要记录原账务房间信息")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名;冗余做报表统计", example = "李四")
    private String guestName;

    @Schema(description = "客源类型;散客 会员 单位 中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String guestSrcType;

    @Schema(description = "客源类型名称;散客 会员 单位 中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String guestSrcTypeName;

    @Schema(description = "消费科目、付款方式代码;消费科目、付款方式代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称;消费科目名称、付款方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String subType;

    @Schema(description = "付款码;付款时的码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String payCode;

    @Schema(description = "现付账套名称")
    private String accSetName;

    @Schema(description = "金额;账务的金额,如果是冲调就为负数", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "业务详情")
    private String accDetail;

    @Schema(description = "入账班次")
    private String shiftNo;

    @Schema(description = "入账班次名称")
    private String shiftName;

    @Schema(description = "入账人;操作者账号")
    private String recorder;

    @Schema(description = "入账营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "是否集团账务；0:门店 1:集团")
    private String isGAcc;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}
