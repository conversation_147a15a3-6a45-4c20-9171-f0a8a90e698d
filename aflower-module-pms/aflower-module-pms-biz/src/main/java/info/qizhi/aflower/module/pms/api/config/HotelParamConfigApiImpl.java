package info.qizhi.aflower.module.pms.api.config;

import info.qizhi.aflower.framework.common.enums.ParamConfigTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.config.dto.hotel.HotelParamConfigBreakfastTicketRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam.HotelParamConfigReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.HotelParamConfigDO;
import info.qizhi.aflower.module.pms.service.config.HotelParamConfigService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class HotelParamConfigApiImpl implements HotelParamConfigApi {

    @Resource
    private HotelParamConfigService hotelParamConfigService;

    @Override
    public CommonResult<HotelParamConfigBreakfastTicketRespDTO> getHotelParamConfigBreakfastTicket(String gcode, String hcode) {
        HotelParamConfigDO hotelParamConfig = getHotelParamConfig(gcode, hcode, ParamConfigTypeEnum.PARAM_TYPE_BREAKFAST_TICKET.getParamType());
        return success(BeanUtils.toBean(hotelParamConfig, HotelParamConfigBreakfastTicketRespDTO.class));
    }

    private HotelParamConfigDO getHotelParamConfig(String gcode, String hcode, String paramType) {
        return hotelParamConfigService.getHotelParamConfig(new HotelParamConfigReqVO().setParamType(paramType)
                                                                                      .setGcode(gcode).setHcode(hcode));
    }

}
