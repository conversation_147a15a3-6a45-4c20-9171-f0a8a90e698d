package info.qizhi.aflower.module.pms.controller.admin.overbook;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.overbook.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.overbook.OverBookDO;
import info.qizhi.aflower.module.pms.service.overbook.OverBookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 超预订配置")
@RestController
@RequestMapping("/pms/over-book")
@Validated
public class OverBookController {

    @Resource
    private OverBookService overBookService;

    @PostMapping("/create")
    @Operation(summary = "创建超预订配置")
    @PreAuthorize("@ss.hasPermission('pms:over-book:create')")
    public CommonResult<Long> createOverBook(@Valid @RequestBody OverBookSaveReqVO createReqVO) {
        return success(overBookService.createOverBook(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新超预订配置")
    @PreAuthorize("@ss.hasPermission('pms:over-book:create')")
    public CommonResult<Boolean> updateOverBook(@Valid @RequestBody OverBookSaveReqVO updateReqVO) {
        overBookService.updateOverBook(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新超预订配置状态")
    @PreAuthorize("@ss.hasPermission('pms:over-book:create')")
    public CommonResult<Boolean> updateOverBookStatus(@Valid @RequestBody OverBookStatusReqVO reqVO) {
        overBookService.updateOverBookStatus(reqVO.getOverBookCode(), reqVO.getIsEnable());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得超预订配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:over-book:query')")
    public CommonResult<OverBookRespVO> getOverBook(@RequestParam("id") Long id) {
        OverBookDO overBook = overBookService.getOverBookById(id);
        return success(BeanUtils.toBean(overBook, OverBookRespVO.class));
    }

    @GetMapping("/count")
    @Operation(summary = "获得超预订配置的数量")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024"),
            @Parameter(name = "isEnable", description = "是否有效", required = true, example = "1")
    })
    public CommonResult<Long> getOverBookCount(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("isEnable") String isEnable) {
        Long count = overBookService.getOverBookCount(new OverBookReqVO().setGcode(gcode).setHcode(hcode).setIsEnable(isEnable));
        return success(count);
    }

    @GetMapping("/page")
    @Operation(summary = "获得超预订配置分页")
    @PreAuthorize("@ss.hasPermission('pms:over-book:query:page')")
    public CommonResult<PageResult<OverBookRespVO>> getOverBookPage(@Valid OverBookPageReqVO pageReqVO) {
        PageResult<OverBookRespVO> pageResult = overBookService.getOverBookPage(pageReqVO);
        return success(pageResult);
    }


}