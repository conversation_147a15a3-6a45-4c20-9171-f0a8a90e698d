package info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 协议单位、中介 简单模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProtocolAgentSimpleRespVO {

    @Schema(description = "中介/协议单位代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中介/协议单位代码")
    private String paCode;

    @Schema(description = "中介/协议单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("中介/协议单位名称")
    private String paName;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channel;

    @Schema(description = "中介等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellLevel;

}