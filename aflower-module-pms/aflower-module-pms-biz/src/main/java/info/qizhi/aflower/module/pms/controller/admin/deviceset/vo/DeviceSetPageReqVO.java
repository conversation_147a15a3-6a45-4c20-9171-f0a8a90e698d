package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;


import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 设备信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DeviceSetPageReqVO extends PageParam {

    @Schema(description = "设备类别代码", example = "2")
    private String deviceType;

    @Schema(description = "状态;0：无效 1：有效")
    private String state;

}