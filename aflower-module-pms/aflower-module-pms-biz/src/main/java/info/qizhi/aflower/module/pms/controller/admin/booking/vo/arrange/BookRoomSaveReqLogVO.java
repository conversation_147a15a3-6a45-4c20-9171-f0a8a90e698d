package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房新增/修改 Request VO")
@Data
public class BookRoomSaveReqLogVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "以抵离日期作为批次号:05-01/05-10", requiredMode = Schema.RequiredMode.REQUIRED)
    private String batchNo;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtCode;

      @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtName;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;



    @Schema(description = "预低时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckinTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckoutTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;



    @Schema(description = "每日房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{dayPrices.notempty}")
    private List<DayPrice> dayPrices;

    @Data
    public static class DayPrice {

        @Schema(description = "赠早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer bkNum;

        @Schema(description = "房包早餐数", requiredMode = Schema.RequiredMode.REQUIRED)
        private Integer roomBkNum;

        @Schema(description = "价格策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String priceStrategyCode;

        @Schema(description = "价格类型;0：放盘价 1：手工价", requiredMode = Schema.RequiredMode.REQUIRED)
        @InStringEnum(value = OrderStateEnum.class, message = "{priceType.invalid}")
        private String priceType;

        @Schema(description = "价格;价格单位为分", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long price;

        @Schema(description = "优惠价;价格单位为分", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long vipPrice;

        @Schema(description = "价格日期", requiredMode = Schema.RequiredMode.REQUIRED)
        private LocalDate priceDate;
    }

}