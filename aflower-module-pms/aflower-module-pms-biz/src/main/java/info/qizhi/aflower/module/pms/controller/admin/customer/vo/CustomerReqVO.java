package info.qizhi.aflower.module.pms.controller.admin.customer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 客历 Request VO")
@Data
public class CustomerReqVO {


    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "身份证号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> idNos;

}