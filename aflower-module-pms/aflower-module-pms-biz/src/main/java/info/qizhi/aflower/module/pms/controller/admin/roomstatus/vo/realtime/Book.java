package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: TY
 * @CreateTime: 2024-09-23
 * @Description: 预订
 * @Version: 1.0
 */
@Data
public class Book {

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rCode")
    private String rCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "预抵时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime planCheckinTime;

    @Schema(description = "X天达", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("xDay")
    private Integer xDay;

    @Schema(description = "预订类型;general:普通订单  group：团队订单", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookType;
}
