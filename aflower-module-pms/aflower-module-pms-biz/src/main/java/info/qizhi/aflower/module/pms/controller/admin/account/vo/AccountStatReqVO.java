package info.qizhi.aflower.module.pms.controller.admin.account.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 统计 Request VO")
@Data
public class AccountStatReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "单号列表,预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。" )
    private List<String> noList;

}
