package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系")
@Data
public class RtFeeRule {

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "起步价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "起步价不能为空")
    @Min(value = 0, message = "起步价不能小于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long startPrice;

    @Schema(description = "散客入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "散客入住时加收金额不能小于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long walkInAddPrice;

    @Schema(description = "会员入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "会员入住时加收金额不能小于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberAddPrice;

    @Schema(description = "中介入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "中介入住时加收金额不能小于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long agentAddPrice;

    @Schema(description = "协议公司入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "协议公司入住时加收金额不能小于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long protocolAddPrice;
}
