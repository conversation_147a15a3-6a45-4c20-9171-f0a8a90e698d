package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.pms.dal.dataobject.brokeragerecord.BrokerageRecordDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 返佣记录分页+总金额 Response VO" )
@Data
public class BrokerageRespVO {

    @Schema(description = "返佣记录分页")
    private PageResult<BrokerageRecordRespVO> PageResult;

    @Schema(description = "未核销佣金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long brokerage;

    @Schema(description = "佣金总额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long brokerageSum;

    @Schema(description = "已核销佣金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long brokerageSumVerify;


}
