package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 交班报表 Response VO")
@Data
public class HandoverReportRespVO {

    @Schema(description = "酒店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hname;

    @Schema(description = "酒店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "班次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

    @Schema(description = "所有账务付款科目集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HandoverReportAccountRespVO> paymentTypeAccounts;

    @Schema(description = "所有账务付款科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentTypeTotalFee;

    @Schema(description = "所有账务现金交款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long rmbPayTotalFee;

    @Schema(description = "所有账务消费科目集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HandoverReportAccountRespVO> consumptionTypeAccounts;

    @Schema(description = "所有账务消费科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionTypeTotalFee;

    @Schema(description = "付款科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> paymentDetails;

    @Schema(description = "消费科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> consumptionDetails;

    @Schema(description = "入账人收款明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RecorderDetail> recorderPaymentDetails;

    @Schema(description = "会员充值集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> memberRechargeAccounts;

    @Schema(description = "会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeTotalFee;

    @Schema(description = "门店会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    @JsonProperty("mMemberRechargeTotalFee")
    private Long mMemberRechargeTotalFee;

    @Schema(description = "集团会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    @JsonProperty("gMemberRechargeTotalFee")
    private Long gMemberRechargeTotalFee;

    @Schema(description = "会员充值账务现金收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeRmbPayTotalFee;

    @Schema(description = "本班收银员AR账核销收款分类汇总", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ArDetail> arDetails;

    @Data
    public static class Detail{

        @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subCode;

        @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subName;

        @Schema(description = "科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "分类代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String classCode;

        @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String className;

        @Schema(description = "账务集合", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<HandoverReportAccountRespVO> accounts;

    }

    @Data
    public static class RecorderDetail{

        @Schema(description = "入账人", requiredMode = Schema.RequiredMode.REQUIRED)
        private String recorder;

        @Schema(description = "入账人收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "入账人现金交款合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long memberRechargeRmbPayTotalFee;

        @Schema(description = "账务集合", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<Detail> accounts;

    }

    @Data
    public static class ArDetail{

        @Schema(description = "入账人", requiredMode = Schema.RequiredMode.REQUIRED)
        private String recorder;

        @Schema(description = "入账人收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "账务集合", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<AccOutInAccountDetail> accounts;

    }
}
