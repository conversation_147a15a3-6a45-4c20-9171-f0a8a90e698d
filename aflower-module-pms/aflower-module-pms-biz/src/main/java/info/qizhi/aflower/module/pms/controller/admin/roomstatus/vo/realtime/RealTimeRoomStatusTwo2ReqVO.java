package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 实时房态 Request VO")
@Data
public class RealTimeRoomStatusTwo2ReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "统计类型")
    @NotEmpty(message = "统计类型不能为空")
    private List<String> statTypes;

}
