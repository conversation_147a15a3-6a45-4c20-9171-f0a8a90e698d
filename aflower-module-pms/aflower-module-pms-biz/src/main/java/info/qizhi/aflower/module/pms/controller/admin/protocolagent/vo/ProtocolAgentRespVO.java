package info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.desensitize.core.slider.annotation.MobileDesensitize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.ArSetRespVO;
import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.InvoiceRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 协议单位、中介 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProtocolAgentRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4871")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "所属酒店代码")
    @ExcelProperty("所属酒店代码")
    private String belongHcode;

    @Schema(description = "所属酒店")
    @ExcelProperty("所属酒店")
    private String belongHname;

    @Schema(description = "中介/协议单位代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("中介/协议单位代码")
    private String paCode;

    @Schema(description = "中介/协议单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("中介/协议单位名称")
    private String paName;

    @Schema(description = "简称", example = "王五")
    @ExcelProperty("简称")
    private String shortName;

    @Schema(description = "法人;公司法人")
    @ExcelProperty("法人;公司法人")
    private String legalPerson;

    @Schema(description = "电话;公司电话")
    @ExcelProperty("电话;公司电话")
    private String telephone;

    @Schema(description = "地址;公司地址")
    @ExcelProperty("地址;公司地址")
    private String address;

    @Schema(description = "类型;0：协议单位 1：中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型;0：协议单位 1：中介")
    private String paType;

    @Schema(description = "渠道;中介才有,存储渠道代码")
    @ExcelProperty("渠道;中介才有,存储渠道代码")
    private String channel;

    @Schema(description = "是否共享;0:单店使用 1：集团共享；0时挂账的酒店范围就是单店", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否共享;0:单店使用 1：集团共享；0时挂账的酒店范围就是单店")
    private String isShare;

    @Schema(description = "联系人姓名")
    @ExcelProperty("联系人姓名")
    private String contact;

    @Schema(description = "联系人电话")
    @ExcelProperty("联系人电话")
    private String phone;

    @Schema(description = "联系人email")
    @ExcelProperty("联系人email")
    private String email;

    @Schema(description = "销售员")
    @ExcelProperty("销售员")
    private String seller;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始日期")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束日期")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDateTime endDate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1：有效")
    private String isEnable;

    @Schema(description = "登记单隐藏房价;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED, example = "17953")
    @ExcelProperty("登记单隐藏房价;0：否 1：是")
    private String isHidePrice;

    @Schema(description = "是否允许挂账;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否允许挂账;0：否 1：是")
    private String isCredit;

    @Schema(description = "销售等级")
    @ExcelProperty("销售等级")
    private String sellLevel;

    @Schema(description = "佣金等级")
    @ExcelProperty("佣金等级")
    private String commissionLevel;

    @Schema(description = "应收账套")
    private ArSetRespVO arSet;

    @Schema(description = "开票信息")
    private InvoiceRespVO invoice;

    @Schema(description = "是否系统初始化 1：是 0: 否")
    private String isSys;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}