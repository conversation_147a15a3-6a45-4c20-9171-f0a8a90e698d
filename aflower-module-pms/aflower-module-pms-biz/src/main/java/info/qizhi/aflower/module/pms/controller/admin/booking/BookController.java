package info.qizhi.aflower.module.pms.controller.admin.booking;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange.BookRoomTypePriceRespVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.book.*;
import info.qizhi.aflower.module.pms.service.booking.BookService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 预订单(普通预订团队预订)")
@RestController
@RequestMapping("/pms/book")
@Validated
public class BookController {

    @Resource
    @Lazy
    private BookService bookService;

    @PostMapping("/create")
    @Operation(summary = "创建预订单(普通预订团队预订)")
    @PreAuthorize("@ss.hasPermission('pms:book:create')")
    public CommonResult<String> createBook(@Valid @RequestBody BookSaveReqVO createReqVO) {
        return success(bookService.createBook(createReqVO));
    }

    @PostMapping("/sms-send")
    @Operation(summary = "发送短信")
    @PreAuthorize("@ss.hasPermission('pms:book:create')")
    public CommonResult<String> smsSend(@Valid @RequestBody BookSaveReqVO createReqVO) {
        return success(bookService.smsSend(createReqVO));
    }

    @PutMapping("/update-general-book")
    @Operation(summary = "更新预订单(普通预订)")
    @PreAuthorize("@ss.hasPermission('pms:book:update:update-general-book')")
    public CommonResult<Boolean> updateGeneralBook(@Valid @RequestBody BookSaveReqVO updateReqVO) {
        bookService.updateGeneralBook(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-base-team-book")
    @Operation(summary = "更新团队预订单基本信息")
    @PreAuthorize("@ss.hasPermission('pms:book:update:update-base-team-book')")
    public CommonResult<Boolean> updateBaseTeamBookInfo(@Valid @RequestBody TeamBookBaseUpdateReqVO updateReqVO) {
        bookService.updateBaseTeamBookInfo(updateReqVO);
        return success(true);
    }


    @PutMapping("/cancel")
    @Operation(summary = "取消订单")
    @PreAuthorize("@ss.hasPermission('pms:book:update:cancel')")
    public CommonResult<Boolean> cancelBook(@Valid @RequestBody BookUpdateStatusReqVO bookUpdateStatusReqVO) {
        bookService.cancelBook(bookUpdateStatusReqVO);
        return success(true);
    }

    @PutMapping("/recover-general")
    @Operation(summary = "恢复普通预订单")
    @PreAuthorize("@ss.hasPermission('pms:book:update:recover-general')")
    public CommonResult<Boolean> recoverGeneralBook(@Valid @RequestBody BookUpdateStatusReqVO bookUpdateStatusReqVO) {
        bookService.recoverGeneralBook(bookUpdateStatusReqVO);
        return success(true);
    }

    @GetMapping("/get/check-out-time")
    @Operation(summary = "获得退房时间")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<RightsRespVO> getRight(@Valid RightReqVO reqVO) {
        RightsRespVO checkOutResp = bookService.getRight(reqVO);
        return success(checkOutResp);
    }

    @GetMapping("/get/roomtype")
    @Operation(summary = "获得可预订的房型列表(房型、售价、可售数、可超数、预计间数、排房信息、赠早数)")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<List<BookRoomTypePriceRespVO>> getBookRoomTypeList(@Valid BookReqVO reqVO) {
        List<BookRoomTypePriceRespVO> bookRoomTypePriceRespVOList = bookService.getRoomTypePriceList(reqVO);
        return success(bookRoomTypePriceRespVOList);
    }


    @GetMapping("/get/upgrade/roomtype")
    @Operation(summary = "获得升级房型的可预订的房型列表(房型、可售数、可超数)")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<List<BookUpgradeRoomTypeRespVO>> getUpgradeBookRoomTypeList(@Valid BookSelectUpgradeReqVO reqVO) {
        //List<BookRoomTypePriceRespVO> bookRoomTypePriceRespVOList = bookService.getRoomTypePriceList(reqVO);
        List<BookUpgradeRoomTypeRespVO> bookRoomTypePriceList = bookService.getUpgradeBookRoomTypeList(reqVO);
        return success(bookRoomTypePriceList);
    }

    @PutMapping("/upgrade")
    @Operation(summary = "升级房型")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<Boolean> upgradeBookRoomType(@Valid @RequestBody BookUpgradeReqVO reqVO) {
        //List<BookRoomTypePriceRespVO> bookRoomTypePriceRespVOList = bookService.getRoomTypePriceList(reqVO);
        bookService.upgradeBookRoomType(reqVO);
        return success(true);
    }

    @GetMapping("/get/general")
    @Operation(summary = "获得预订单(普通预订)")
    @Parameter(name = "bookNo", description = "预订单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<GeneralBookAllDataRespVO> getGeneralBook(@RequestParam("bookNo") String bookNo) {
        GeneralBookAllDataRespVO book = bookService.getGeneralBookAllData(bookNo);
        return success(book);
    }

    @PutMapping("/update-remark-out-order-no")
    @Operation(summary = "修改预订单的备注、外部订单备注、外部订单号")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<Boolean> updateBookRemarkOutRemarkOutOrderNo(@Valid @RequestBody BookRemarkUpdateReqVO reqVO) {
        bookService.updateBookRemarkOutRemarkOutOrderNo(reqVO);
        return success(true);
    }

    @GetMapping("/get/book-no-des")
    @Operation(summary = "获得未脱敏预订单的手机号（可扩展）")
    @Parameter(name = "bookNo", description = "预订单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<BookNoDesRespVO> getGeneralBookNoDes(@RequestParam("bookNo") String bookNo) {
        BookNoDesRespVO book = bookService.getBookNoDesData(bookNo);
        return success(book);
    }

    @GetMapping("/get/team")
    @Operation(summary = "获得预订单(团队预订)")
    @Parameter(name = "bookNo", description = "预订单号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<TeamBookAllDataRespVO> getTeamBook(@RequestParam("bookNo") String bookNo) {
        TeamBookAllDataRespVO book = bookService.getTeamBookAllData(bookNo);
        return success(book);
    }

    @GetMapping("/page-room-state")
    @Operation(summary = "获得预订单分页-房态盘右侧")
    public CommonResult<PageResult<BookPageRespVO>> getRoomStateBookPage(@Valid BookPageReq2VO pageReqVO) {
        PageResult<BookPageRespVO> pageResult = bookService.getRoomStateBookPage(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/page")
    @Operation(summary = "获得预订单分页")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<PageResult<BookPageRespVO>> getBookPage(@Valid BookPageReqVO pageReqVO) {
        PageResult<BookPageRespVO> pageResult = bookService.getRoomStateBookPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/get/no-desensitize")
    @Operation(summary = "获得未脱敏预订单")
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<BookNoDesRespVO> getBookNoDes(@RequestParam("bookNo") String bookNo) {
        BookNoDesRespVO pageResult = BeanUtils.toBean(bookService.getBookByBookNo(bookNo), BookNoDesRespVO.class);
        return success(pageResult);
    }

    @GetMapping("/today-arrive")
    @Operation(summary = "获取今日预抵订单分页")
    @PreAuthorize("@ss.hasPermission('pms:book:query:today-arrive')")
    public CommonResult<PageResult<BookPageRespVO>> getTodayBookArrivePage(@Valid BookPageTodayReqVO reqVO) {
        PageResult<BookPageRespVO> todayBookPage = bookService.getTodayBookArrivePage(reqVO);
        return success(todayBookPage);
    }

    @GetMapping("/team")
    @Operation(summary = "预订团队订单分页")
    @PreAuthorize("@ss.hasPermission('pms:book:query:team')")
    public CommonResult<PageResult<BookPageRespVO>> getBookTeamPage(@Valid BookTeamPageReqVO reqVO) {
        PageResult<BookPageRespVO> bookTeamPage = bookService.getBookTeamPage(reqVO);
        return success(bookTeamPage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出预订单(普通预订团队预订) Excel")
    @PreAuthorize("@ss.hasPermission('pms:book:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportBookExcel(@Valid BookPageReqVO pageReqVO,
                                HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BookPageRespVO> list = bookService.getRoomStateBookPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "预订单(普通预订团队预订).xls", "数据", BookRespVO.class,
                BeanUtils.toBean(list, BookRespVO.class));
    }

//    @GetMapping("/point")
//    @Operation(summary = "积分测试")
//    public CommonResult<Boolean> getPoint(String gcode, String hcode, LocalDate bizDate, LocalDateTime LastBizDateTime) {
//        nightAudiReportService.nightAudiReport(gcode,hcode,bizDate,LastBizDateTime);
//        return success(true);
//    }

    @GetMapping("/conflict")
    @Operation(summary = "获取冲突单")
    @PreAuthorize("@ss.hasPermission('pms:book:query:team')")
    public CommonResult<PageResult<BookConflictRespVO>> getConflict(@Valid BookConflictReqVO reqVO) {
        List<BookConflictRespVO> bookConflictList = bookService.getConflict(reqVO);
        // 实现内存分页
        int total = bookConflictList.size();
        int pageNo = reqVO.getPageNo();
        int pageSize = reqVO.getPageSize();
        
        List<BookConflictRespVO> pageList = new ArrayList<>();
        if (total > 0) {
            int startIndex = (pageNo - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, total);
            if (startIndex < total) {
                pageList = bookConflictList.subList(startIndex, endIndex);
            }
        }
        
        PageResult<BookConflictRespVO> pageResult = new PageResult<>(pageList, (long) total);
        return success(pageResult);
    }

    @GetMapping("/page-merge-reception")
    @Operation(
        summary = "需要联房的预订单分页查询",
        description = "查询可以进行联房操作的预订单列表，包括预订中和待确认的预订单。" +
                     "支持按预订单号、房型、客人姓名、手机号、预抵日期、预离日期等条件筛选。" +
                     "返回的预订单可用于联房、加入联房等操作。"
    )
    @PreAuthorize("@ss.hasPermission('pms:book:query')")
    public CommonResult<PageResult<BookMergeReceptionRespVO>> getMergeReceptionBooks(@Valid BookMergeReceptionPageReqVO pageReqVO) {
        PageResult<BookMergeReceptionRespVO> pageResult = bookService.getMergeReceptionBookPage(pageReqVO);
        return success(pageResult);
    }


}