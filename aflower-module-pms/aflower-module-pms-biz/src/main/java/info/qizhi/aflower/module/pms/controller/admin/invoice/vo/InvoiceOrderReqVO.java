package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/23 11:41
 */
@Data
public class InvoiceOrderReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "酒店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "酒店代码不能为空")
    private String hcode;

    @Schema(description = "数电发票类型代码, 81:数电专票,82:数电普票")
    private String digitalInvoiceType;

    @Schema(description = "开票详情集合")
    @Valid
    @NotEmpty(message = "开票详情集合不能为空")
    private List<DetailInfo> detailInfoList;

    @Schema(description = "当前票关联的订单号,单个订单开票请传订单号,在联房里开票这里不传")
    private String orderNo;

    @Schema(description = "宾客代码,开票时针对的宾客单号")
    @NotBlank(message = "宾客代码不能为空")
    private String togetherCode;

    @Schema(description = "联房单号")
    @NotBlank(message = "联房单号不能为空")
    private String bindCode;

    @Schema(description = "回调地址")
    private String notifyUrl;

    @Data
    public static class DetailInfo {

        @Schema(description = "发票行性质 0-正常行；1-折扣行；2-被折扣行,这里默认传0")
        private String lineNature;

        @Schema(description = "税收分类编码,默认传：3070402000000000000")
        private String taxClassifyCode;

        @Schema(description = "金额（含税）")
        @NotEmpty(message = "金额不能为空")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long amount;
    }
}
