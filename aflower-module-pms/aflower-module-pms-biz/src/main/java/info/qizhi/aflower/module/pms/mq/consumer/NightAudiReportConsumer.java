package info.qizhi.aflower.module.pms.mq.consumer;

import info.qizhi.aflower.module.pms.mq.message.NightAudiReportMessage;
import info.qizhi.aflower.module.pms.service.report.NightAudiReportService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审消费者
 * @Version: 1.0
 */
@Component
@RabbitListener(queues = NightAudiReportMessage.QUEUE) // 重点：添加 @RabbitListener 注解，声明消费的 queue
@Slf4j
public class NightAudiReportConsumer {
    @Resource
    private NightAudiReportService nightAudiReportService;

    /**
     * 消费消息（执行夜审）
     *
     * @param message 消息
     */
    @RabbitHandler // 重点：添加 @RabbitHandler 注解，实现消息的消费
    public void onMessage(NightAudiReportMessage message) {
        //休眠300毫秒
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("[onMessage][消息内容({})]", message);
        nightAudiReportService.nightAudiReport(message.getGcode(), message.getHcode(), message.getBizDate(), message.getLastBizDateTime());
    }
}
