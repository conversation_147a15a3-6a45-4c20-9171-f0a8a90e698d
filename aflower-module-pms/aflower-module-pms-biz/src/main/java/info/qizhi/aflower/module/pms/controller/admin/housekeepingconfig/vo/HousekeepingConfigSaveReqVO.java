package info.qizhi.aflower.module.pms.controller.admin.housekeepingconfig.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 酒店房务配置新增/修改 Request VO")
@Data
public class HousekeepingConfigSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19681")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "门店代码不能为空")
    private String hcode;

    @Schema(description = "房扫是否需要审核;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "房扫是否需要审核不能为空")
    private String cleanAudit;

    @Schema(description = "业绩是否需要审核;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "业绩是否需要审核不能为空")
    private String performanceAudit;

}