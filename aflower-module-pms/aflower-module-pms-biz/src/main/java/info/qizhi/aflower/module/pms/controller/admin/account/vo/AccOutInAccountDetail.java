package info.qizhi.aflower.module.pms.controller.admin.account.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "biz - 交班报表获取单位中介出入账记录")
@Data
public class AccOutInAccountDetail {

    @Schema(description = "账套名称")
    private String arSetName;

    @Schema(description = "消费科目、付款方式代码;消费科目、付款方式代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称;消费科目名称、付款方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "发生金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;


}
