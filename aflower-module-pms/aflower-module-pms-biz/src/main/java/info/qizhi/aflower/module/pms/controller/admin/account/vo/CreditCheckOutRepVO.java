package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 挂账退房 Request VO")
@Data
public class CreditCheckOutRepVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "客人列表")
    @Valid
    @NotEmpty(message = "客人列表不能为空")
    private List<OrderTogether> orderTogethers;

    @Data
    public static class OrderTogether {

        @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{no.notempty}")
        private String no;

        @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "是否团队主账,0:否 1:是")
        @NotEmpty(message = "{isTeam.notempty}")
        @InStringEnum(value = BooleanEnum.class)
        private String isTeam;

    }

}