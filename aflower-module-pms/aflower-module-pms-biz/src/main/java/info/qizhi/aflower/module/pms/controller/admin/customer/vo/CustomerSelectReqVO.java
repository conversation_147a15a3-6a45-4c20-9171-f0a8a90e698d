package info.qizhi.aflower.module.pms.controller.admin.customer.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 客历查询 Request VO")
@Data
@ToString(callSuper = true)
public class CustomerSelectReqVO  {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "关键字查询", example = "张三")
    private String keyWords;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "证件号码集合")
    private List<String> idNos;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "黑名单;0正常 1拉黑")
    private String isBlack;

    @Schema(description = "入住次数", example = "3")
    private Integer checkinNum;

    @Schema(description = "是否会员 0: 否 1: 是")
    private String isMember;

    @Schema(description = "大于,小于 0: 大于 1: 小于")
    @InStringEnum(value = BooleanEnum.class,message = "{gtOrlt.enum}")
    private String gtOrlt;




}