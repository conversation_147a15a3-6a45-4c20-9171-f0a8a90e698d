package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 酒店经营科目汇总月报报表 Response VO")
@Data
public class SubjectSummaryReportRespVO {
    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "所选营业日期的月初", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "营业日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "付款科目今日发生合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentTodayFee;

    @Schema(description = "付款科目本月发生合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentMonthFee;

    @Schema(description = "消费科目今日发生合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionTodayFee;

    @Schema(description = "消费科目本月发生合计", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionMonthFee;

    @Schema(description = "付款科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> paymentDetails;

    @Schema(description = "消费科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> consumptionDetails;

    @Schema(description = "会员充值消费集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "memberRechargeConsumptionDetails")
    private List<Detail> memberRechargeConsumptionDetails;

    @Schema(description = "会员充值付款集合", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "memberRechargePaymentDetails")
    private List<Detail> memberRechargePaymentDetails;

    @Data
    public static class Detail{

        @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subCode;

        @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subName;

        @Schema(description = "今日发生费用", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long todayFee;

        @Schema(description = "本月发生费用", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long monthFee;

        @Schema(description = "分类代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String classCode;

        @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String className;

    }

}
