package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 开票信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11969")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "代码")
    @ExcelProperty("代码")
    private String invoiceCode;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("抬头类型;0:个人  1:企业 2:组织")
    private String invoiceTitleType;

    @Schema(description = "发票抬头", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票抬头")
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3344")
    @ExcelProperty("纳税人识别号")
    private String taxpayerId;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("发票类型;0:增值税普通发票 1：增值税专用发票")
    private String invoiceType;

    @Schema(description = "开户银行")
    @ExcelProperty("开户银行")
    private String bank;

    @Schema(description = "开户账号")
    @ExcelProperty("开户账号")
    private String bankNo;

    @Schema(description = "企业注册地址")
    @ExcelProperty("企业注册地址")
    private String regAddress;

    @Schema(description = "企业注册电话")
    @ExcelProperty("企业注册电话")
    private String regPhone;

    @Schema(description = "email")
    @ExcelProperty("email")
    private String email;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}