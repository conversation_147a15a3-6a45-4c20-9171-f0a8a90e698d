package info.qizhi.aflower.module.pms.mq.message;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审消息
 * @Version: 1.0
 */
@Data
public class NightAudiReportMessage implements Serializable {

    public static final String QUEUE = QueueConstants.NIGHT_AUDI_REPORT_MESSAGE_QUEUE;

    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @NotBlank(message = "酒店代码不能为空")
    private String hcode;

    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    private LocalDateTime lastBizDateTime;

}
