package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 新增账务 -  退货 Request VO")
@Data
public class ConsumeRefundGoodReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accNo.notblank}")
    private String accNo;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark;

    @Schema(description = "商品集合")
    private List<Goods> goodsList;

    @Data
    public static class Goods {
        @Schema(description = "商品代码")
        @JsonAlias("id")
        private String goodsCode;

        @Schema(description = "退货数量")
        @NotEmpty(message = "退货数量不能为空")
        private Integer num;
    }
}