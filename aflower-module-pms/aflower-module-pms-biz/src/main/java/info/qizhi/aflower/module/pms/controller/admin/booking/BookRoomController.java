package info.qizhi.aflower.module.pms.controller.admin.booking;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange.*;
import info.qizhi.aflower.module.pms.service.booking.BookRoomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "biz - 预订排房")
@RestController
@RequestMapping("/pms/book-room")
@Validated
public class BookRoomController {

    @Resource
    @Lazy
    private BookRoomService bookRoomService;

    @PostMapping("/add")
    @Operation(summary = "添加房间(团队预订单才可以调用)")
    @PreAuthorize("@ss.hasPermission('pms:book-room:create')")
    public CommonResult<Boolean> createBookRoom(@Valid @RequestBody BookRoomAddReqVO reqVO) {
        bookRoomService.addBookRooms(reqVO);
        return success(true);
    }

    @PutMapping("/arrange")
    @Operation(summary = "排房")
    @PreAuthorize("@ss.hasPermission('pms:book-room:update:arrange')")
    public CommonResult<Boolean> arrangeRoom(@Valid @RequestBody BookRoomArrangeSaveReqVO reqVO) {
        bookRoomService.arrangeRooms(reqVO);
        return success(true);
    }

    @PutMapping("/auto-arrange")
    @Operation(summary = "自动排房")
    @PreAuthorize("@ss.hasPermission('pms:book-room:update:arrange')")
    public CommonResult<List<BookRoomAutoArrangeRespVO>> autoArrangeRooms(@Valid @RequestBody BookRoomAutoArrangeReqVO reqVO) {
        List<BookRoomAutoArrangeRespVO> res = bookRoomService.autoArrangeRooms(reqVO);
        return success(res);
    }

    @PutMapping("/give-bk")
    @Operation(summary = "批量赠早")
    @PreAuthorize("@ss.hasPermission('pms:book-room:update')")
    public CommonResult<Boolean> batchGiveBk(@Valid @RequestBody BookRoomBatchGiveBkReqVO reqVO) {
        bookRoomService.batchGiveBkRoom(reqVO);
        return success(true);
    }

    @PutMapping("/update-price")
    @Operation(summary = "批量改价")
    @PreAuthorize("@ss.hasPermission('pms:book-room:update')")
    public CommonResult<Boolean> updatePrice(@Valid @RequestBody BookRoomBatchUpdatePriceReqVO reqVO) {
        bookRoomService.batchUpdateBookRoomPrices(reqVO);
        return success(true);
    }

    @PutMapping("/delete-book-room")
    @Operation(summary = "团队预订：移除单个房间")
    @PreAuthorize("@ss.hasPermission('pms:book:update')")
    public CommonResult<Boolean> deleteBookRoom(@Valid @RequestBody BookRoomDeleteReqVO reqVO) {
        bookRoomService.deleteTeamBookRoom(reqVO);
        return success(true);
    }

    @PutMapping("/batch-delete-book-room")
    @Operation(summary = "批次删除团队预订房间")
    @PreAuthorize("@ss.hasPermission('pms:book:update')")
    public CommonResult<Boolean> batchDeleteBookRoom(@Valid @RequestBody BookRoomBatchDeleteReqVO reqVO) {
        bookRoomService.batchDeleteTeamBookRoom(reqVO);
        return success(true);
    }

    @DeleteMapping("/clean-rno")
    @Operation(summary = "清除预订的房间信息")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:book-room:update')")
    public CommonResult<Boolean> cleanRoomInfo(@RequestParam("orderNo") String orderNo) {
        bookRoomService.deleteArrangeBookRoom(orderNo);
        return success(true);
    }

    @GetMapping("/can-book-rooms")
    @Operation(summary = "获得可预订的房间列表")
    public CommonResult<List<BookRoomArrangeRespVO>> getBookRoom(@Valid BookRoomTypeArrangeReqVO reqVO) {
        List<BookRoomArrangeRespVO> bookRoom = bookRoomService.getCanArrangeRooms(reqVO);
        return success(bookRoom);
    }


}