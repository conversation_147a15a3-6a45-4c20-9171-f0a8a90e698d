package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第一步 退房客单  Response VO")
@Data
public class OrderCheckOutGuestRespVO {

    @Schema(description = "绑定代码")
    private String bindCode;

    @Schema(description = "订单类型,general：普通订单 join：联房订单 group：团队订单")
    private String orderType;

    @Schema(description = "宾客订单列表")
    private List<OrderTogether> orderTogethers;

    @Data
    public static class OrderTogether {

        @Schema(description = "订单号, 如果是团队主账时，订单号为团队代码")
        private String no;

        @Schema(description = "宾客代码, 如果是团队主账时，宾客代码为团队代码")
        private String togetherCode;

        @Schema(description = "房间代码")
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房间号")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "订单号")
        private String orderNo;

        @Schema(description = "宾客姓名")
        private String name;

        @Schema(description = "是否主客人,0:否 1:是")
        private String isMain;

        @Schema(description = "是否团队主账,0:否 1:是")
        private String isTeam;

        @Schema(description = "是否默认选中,0:否 1:是")
        private String checked;

        @Schema(description = "客人状态, check_in：在住, credit:挂账")
        private String state;

        @Schema(description = "账务类型")
        private String accType;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "入住类型")
        private String checkinType;

        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "客源类型")
        private String guestSrcType;

        @Schema(description = "客源关联账号（会员、中介、单位）")
        private String guestCode;

        @Schema(description = "渠道代码")
        private String channelCode;
    }

}