package info.qizhi.aflower.module.pms.controller.admin.report.vo.guest;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员卡销售明细 Response VO")
@Data
public class InhouseGuestBalanceReportRespVO {
    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "开始日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", example = "报表操作员")
    private String operator;

    @Schema(description = "总房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "总消费合计", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeFee;

    @Schema(description = "总付款合计", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "总余额", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long balance;

    @Schema(description = "总预授权费用", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long preFee;

    @Schema(description = "余额")
    private List<InhouseGuestBalanceRespVO> list;
}
