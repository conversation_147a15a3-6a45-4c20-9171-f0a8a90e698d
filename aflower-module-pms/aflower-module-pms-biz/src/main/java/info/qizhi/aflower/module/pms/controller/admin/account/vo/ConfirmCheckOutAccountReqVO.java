package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.enums.GenerateFeeTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第二步 选中客单进入计算房价 Request VO")
@Data
public class ConfirmCheckOutAccountReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单类型,general：普通订单 join：联房订单 group：团队订单")
    private String orderType;

    @Schema(description = "生成房费类型;add_all_day:加收全天,add_half_day:加收半天,room_fee:系统自动计费,0:不加收房费,hand_input_room_fee:手工房费")
    @InStringEnum(value = GenerateFeeTypeEnum.class)
    @NotEmpty(message = "{subCode.notblank}")
    private String subCode;

    @Schema(description = "是否挂账退房操作,0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isCreditCheckout.notempty}")
    private String isCreditCheckout;

    @Schema(description = "退房结账列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    @NotEmpty(message = "{checkoutOrderTogethers.notempty}")
    private List<OrderTogether> orderTogethers;

    @Data
    public static class OrderTogether {

        @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "单号, 如果是团队主账时，订单号为团队代码")
        @NotEmpty(message = "{no.notempty}")
        private String no;

        @Schema(description = "是否团队主账,0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{isTeam.notblank}")
        private String isTeam;

        @Schema(description = "是否主客单,0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{isMain.notempty}")
        private String isMain;

        @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
        private String state;

    }

}