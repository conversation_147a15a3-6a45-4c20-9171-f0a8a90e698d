package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.enums.GuaranteeTypeEnum;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单 Request VO")
@Data
public class BookListReqVO {

    @Schema(description = "门店代码")
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "集团代码")
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "预订单号列表")
    private List<String> bookNos;

    @Schema(description = "订单状态")
    @InStringEnum(value = OrderStateEnum.class, message = "{orderState.invalid}")
    private String state;

    @Schema(description = "预离时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "担保方式")
    @InStringEnum(value = GuaranteeTypeEnum.class, message = "担保方式不正确")
    private String guarantyStyle;

    @Schema(description = "团队代码列表")
    private List<String> teamCodes;
}
