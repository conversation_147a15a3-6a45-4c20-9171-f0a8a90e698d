package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.*;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单(普通预订) Response VO")
@Data
public class GeneralBookAllDataRespVO {

    @Schema(description = "id", example = "8352")
    private Long id;

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "预订单号")
    private String bookNo;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称")
    private String channelName;

    @Schema(description = "订单来源")
    private String orderSource;

    @Schema(description = "订单来源名称")
    private String orderSourceName;

    @Schema(description = "客源类型", example = "2")
    private String guestSrcType;

    @Schema(description = "客源类型名称", example = "2")
    private String guestSrcTypeName;

    @Schema(description = "general:(个人)普通订单  group：团队订单", example = "2")
    private String bookType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    private String checkinType;

    @Schema(description = "入住类型名称")
    private String checkinTypeName;

    @Schema(description = "小时房代码;入住类型为时租房时，该字段有值")
    private String hourCode;

    @Schema(description = "客人代码;会员代码、协议单位、中介代码、团队代码")
    private String guestCode;

    @Schema(description = "客人名称;会员姓名、协议单位、中介名称、团队名称", example = "张三")
    // @NameDesensitize
    private String guestName;

    @Schema(description = "合同号;团队预订时输入")
    private String contractNo;

    @Schema(description = "预抵时间;普通预订存储，团队预订低离时间存储在预订房间中")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间;普通预订存储，团队预订低离时间存储在预订房间中")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "预订人(联系人);如果是团队预订，这里保存团队的联系人姓名")
    // @NameDesensitize
    private String contact;

    @Schema(description = "预订人电话;联系电话")
//    @MobileDesensitize
    private String phone;

    @Schema(description = "入住人姓名")
    // @NameDesensitize
    private String checkinPerson;

    @Schema(description = "入住人电话")
//    @MobileDesensitize
    private String checkinPhone;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "担保方式名称")
    private String guarantyStyleName;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "保留时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalDateTime retainTime;

    @Schema(description = "是否发短信;0：否 1：是")
    private String isSendSms;

    @Schema(description = "市场活动代码")
    private String marketActivityCode;

    @Schema(description = "市场活动名称", example = "王五")
    private String marketActivityName;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "销售员名称")
    private String sellerName;

    @Schema(description = "外部订单号")
    private String outOrderNo;

    @Schema(description = "订单备注", example = "随便")
    private String remark;

    @Schema(description = "外部订单备注")
    private String outOrderRemark;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "预订房间列表")
    private List<BookRoom> bookRooms;

    @Data
    public static class BookRoom{

        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "房型名称")
        private String rtName;

        @Schema(description = "订单号")
        private String orderNo;

        @Schema(description = "批次号")
        private String batchNo;

        @Schema(description = "购早数")
        private Integer buyBkNum;

        @Schema(description = "房间代码")
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房号")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "天数")
        private Integer days;

        @Schema(description = "预抵时间")
        @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
        private LocalDateTime planCheckinTime;

        @Schema(description = "预离时间")
        @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
        private LocalDateTime planCheckoutTime;

        @Schema(description = "订单状态")
        private String state;

        @Schema(description = "宾客代码")
        private String togetherCode;

        @Schema(description = "是否会议室;0:否 1:是")
        private String isMeetingRoom;

        @Schema(description = "每日房价")
        private List<RoomPkPrice> roomPkPrices;


        @Data
        public static class RoomPkPrice{
            @Schema(description = "赠早数")
            private Integer bkNum;

            @Schema(description = "房包早数")
            private Integer roomBkNum;

            @Schema(description = "价格策略代码")
            private String priceStrategyCode;

            @Schema(description = "价格类型;0：放盘价 1：手工价")
            private String priceType;

            @Schema(description = "价格;价格单位为分")
            @JsonSerialize(using = FenToYuanSerializer.class)
            private Long price;

            @Schema(description = "优惠价;价格单位为分")
            @JsonSerialize(using = FenToYuanSerializer.class)
            private Long vipPrice;

            @Schema(description = "价格日期")
            @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
            private LocalDate priceDate;

            @Schema(description = "星期几")
            private Integer week;
        }

    }

}