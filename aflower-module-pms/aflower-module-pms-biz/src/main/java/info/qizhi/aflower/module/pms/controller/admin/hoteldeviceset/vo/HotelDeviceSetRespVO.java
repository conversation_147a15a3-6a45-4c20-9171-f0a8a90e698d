package info.qizhi.aflower.module.pms.controller.admin.hoteldeviceset.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.module.pms.dal.dataobject.hoteldeviceset.HotelDeviceSetDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 门店设备信息 Request VO")
@Data
public class HotelDeviceSetRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceCode;

    @Schema(description = "品牌代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandCode;

    @Schema(description = "品牌名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brandName;

    @Schema(description = "设备类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceType;

    @Schema(description = "数据库路径")
    @JsonProperty(value = "url")
    private String url;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "端口号")
    private String port;

    @Schema(description = "发卡器类型")
    private String cardDispenserType;

    @Schema(description = "是否允许开反锁 0:否 1:是 (门锁有效)")
    private String allowLockOut;

    @Schema(description = "新卡替换旧卡 0:否 1:是 (门锁有效)")
    private String replaceCard;

    @Schema(description = "检查入住时间 0:否 1:是 (门锁有效)")
    private String checkTime;

    @Schema(description = "状态 1:正常 0:停用")
    private String state;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "配置信息")
    private List<HotelDeviceSetDO.ConfParameter> conf;

}