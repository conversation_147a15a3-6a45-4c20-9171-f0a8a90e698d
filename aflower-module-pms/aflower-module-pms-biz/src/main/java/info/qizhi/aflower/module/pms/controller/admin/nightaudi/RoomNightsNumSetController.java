package info.qizhi.aflower.module.pms.controller.admin.nightaudi;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum.RoomNightsNumSetPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum.RoomNightsNumSetRespVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum.RoomNightsNumSetSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.nightaudi.RoomNightsNumSetDO;
import info.qizhi.aflower.module.pms.service.nightaudi.RoomNightsNumSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 夜审房间数设置")
@RestController
@RequestMapping("/pms/room-nights-num-set")
@Validated
public class RoomNightsNumSetController {

    @Resource
    private RoomNightsNumSetService roomNightsNumSetService;

    @PostMapping("/create")
    @Operation(summary = "创建夜审房间数设置")
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:create')")
    public CommonResult<Long> createRoomNightsNumSet(@Valid @RequestBody RoomNightsNumSetSaveReqVO createReqVO) {
        return success(roomNightsNumSetService.createRoomNightsNumSet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新夜审房间数设置")
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:update')")
    public CommonResult<Boolean> updateRoomNightsNumSet(@Valid @RequestBody RoomNightsNumSetSaveReqVO updateReqVO) {
        roomNightsNumSetService.updateRoomNightsNumSet(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除夜审房间数设置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:delete')")
    public CommonResult<Boolean> deleteRoomNightsNumSet(@RequestParam("id") Long id) {
        roomNightsNumSetService.deleteRoomNightsNumSet(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得夜审房间数设置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:query')")
    public CommonResult<RoomNightsNumSetRespVO> getRoomNightsNumSet(@RequestParam("id") Long id) {
        RoomNightsNumSetDO roomNightsNumSet = roomNightsNumSetService.getRoomNightsNumSet(id);
        return success(BeanUtils.toBean(roomNightsNumSet, RoomNightsNumSetRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得夜审房间数设置分页")
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:query')")
    public CommonResult<PageResult<RoomNightsNumSetRespVO>> getRoomNightsNumSetPage(@Valid RoomNightsNumSetPageReqVO pageReqVO) {
        PageResult<RoomNightsNumSetDO> pageResult = roomNightsNumSetService.getRoomNightsNumSetPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RoomNightsNumSetRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出夜审房间数设置 Excel")
    @PreAuthorize("@ss.hasPermission('pms:room-nights-num-set:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportRoomNightsNumSetExcel(@Valid RoomNightsNumSetPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<RoomNightsNumSetDO> list = roomNightsNumSetService.getRoomNightsNumSetPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "夜审房间数设置.xls", "数据", RoomNightsNumSetRespVO.class,
                        BeanUtils.toBean(list, RoomNightsNumSetRespVO.class));
    }

}