package info.qizhi.aflower.module.pms.api.init;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.RandomUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.tenant.core.util.TenantUtils;
import info.qizhi.aflower.module.erp.api.product.category.ProductCategoryApi;
import info.qizhi.aflower.module.erp.api.product.category.dto.ErpProductCategorySaveReqDTO;
import info.qizhi.aflower.module.erp.api.product.unit.ProductUnitApi;
import info.qizhi.aflower.module.erp.api.product.unit.dto.ErpProductUnitSaveReqDTO;
import info.qizhi.aflower.module.marketing.api.couponconfig.CouponConfigApi;
import info.qizhi.aflower.module.marketing.api.sms.balance.SmsBalanceApi;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelSaveReqDTO;
import info.qizhi.aflower.module.pms.api.init.dto.GroupReqDTO;
import info.qizhi.aflower.module.pms.api.init.dto.MerchantReqDTO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccSetReqVO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccSetRespVO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.ArSetSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.groupparam.GroupParamConfigSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam.HotelParamConfigSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule.MerchantTypeRuleSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.goods.vo.thingclass.ThingClassSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.log.NightAudiLogSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set.NightAudiSetSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday.PriceAllDayRuleSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special.PriceSpecialRuleSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo.ProtocolAgentSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.floor.BuildFloorSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.serviceintegration.vo.ServiceIntegrationSaveVO;
import info.qizhi.aflower.module.pms.controller.admin.shift.vo.ShiftTimeSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.accset.AccSetDO;
import info.qizhi.aflower.module.pms.dal.dataobject.accset.AccSetMerchantDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.*;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.BreakfastTicket;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.Deposit;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.Front;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.ShiftMode;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelBackgroundDO;
import info.qizhi.aflower.module.pms.dal.dataobject.room.BuildFloorDO;
import info.qizhi.aflower.module.pms.service.accset.AccSetService;
import info.qizhi.aflower.module.pms.service.channel.ChannelService;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import info.qizhi.aflower.module.pms.service.config.GroupParamConfigService;
import info.qizhi.aflower.module.pms.service.config.HotelParamConfigService;
import info.qizhi.aflower.module.pms.service.config.MerchantTypeRuleService;
import info.qizhi.aflower.module.pms.service.goods.ThingClassService;
import info.qizhi.aflower.module.pms.service.housekeepingconfig.HousekeepingConfigService;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiLogService;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiSetService;
import info.qizhi.aflower.module.pms.service.pricepanel.PricePanelBackgroundService;
import info.qizhi.aflower.module.pms.service.pricerule.PriceAllDayRuleService;
import info.qizhi.aflower.module.pms.service.pricerule.PriceHourRuleService;
import info.qizhi.aflower.module.pms.service.pricerule.PriceSpecialRuleService;
import info.qizhi.aflower.module.pms.service.protocolagent.ProtocolAgentService;
import info.qizhi.aflower.module.pms.service.room.BuildFloorService;
import info.qizhi.aflower.module.pms.service.serviceintegration.ServiceIntegrationService;
import info.qizhi.aflower.module.pms.service.shift.ShiftTimeService;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.dict.dto.DictDataRespDTO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Lazy
public class InitPmsApiImpl implements InitPmsApi {

    private final String ALL_THING_CLASSIFY = "全部分类";
    private final String NIGHT_AUDI_LOG_REMARK = "此记录为系统录入，未保持营业日和自然日一致。";
    /**
     * 备用金
     */
    private final Long PETTY_CASH = 1000L;
    private final Integer DEPOSIT = 100;
    private final LocalTime BK_STARTTIME = LocalTime.of(7, 0);
    private final LocalTime BK_ENDTIME = LocalTime.of(10, 0);
    private final BigDecimal BK_COST = BigDecimal.valueOf(10);
    private final BigDecimal BK_PRICE = BigDecimal.valueOf(15);
    private final String[] NIGHT_AUDIT_TIME = new String[]{"03:00", "03:05", "03:10", "03:15", "03:20", "03:25", "03:30", "03:35", "03:40", "03:45", "03:50", "04:00", "04:05", "04:10", "04:15", "04:20", "04:25", "04:30", "04:35", "04:40", "04:45", "04:50", "04:55"};
    private final LocalTime HANDSTARTTIME = LocalTime.of(0, 0);
    private final LocalTime HANDENDTIME = LocalTime.of(12, 0);
    private final Integer MINUTES = 30;

    private final Integer START_PRICE_N_MIN = 30; // 入住N分钟后收起步费
    private final Integer ALL_PRICE_N_MIN = 60; // 入住N分钟后收全日租
    private final Integer OVER_N_MIN_COLLECT = 30; // 预离超过N分钟后收费
    private final Integer OVER_N_MIN_ALL_DAY = 60; // 预离超时N分钟后收全日租

    private final LocalTime START_TIME = LocalTime.of(8, 0);
    private final LocalTime END_TIME = LocalTime.of(22, 0);
    private final LocalTime RETAIN_TIME = LocalTime.of(23, 0);
    private final LocalTime CHECK_OUT_TIME = LocalTime.of(23, 0);
    private final String BUILDER_NAME = "1栋";

    private final String ONE = "one";

    private final String SOLUTION_PROVIDER = "qizhi";


    @Resource
    private DictDataApi dictDataApi;
    @Resource
    private ChannelService channelService;
    @Resource
    private GeneralConfigService generalConfigService;
    @Resource
    private GroupParamConfigService groupParamConfigService;
    @Resource
    private HotelParamConfigService hotelParamConfigService;
    @Resource
    private MerchantTypeRuleService merchantTypeRuleService;
    @Resource
    private SmsBalanceApi smsBalanceApi;
    @Resource
    private CouponConfigApi couponConfigApi;
    @Resource
    private ThingClassService thingClassService;
    @Resource
    private NightAudiLogService nightAudiLogService;
    @Resource
    private ShiftTimeService shiftTimeService;
    @Resource
    private PriceAllDayRuleService priceAllDayRuleService;
    @Resource
    private PriceHourRuleService priceHourRuleService;
    @Resource
    private PriceSpecialRuleService priceSpecialRuleService;
    @Resource
    private BuildFloorService buildFloorService;
    @Resource
    private NightAudiSetService nightAudiSetService;
    @Resource
    private AccSetService accSetService;
    @Resource
    private ProtocolAgentService protocolAgentService;
    @Resource
    private HousekeepingConfigService housekeepingConfigService;
    @Resource
    private ProductCategoryApi productCategoryApi;
    @Resource
    private ProductUnitApi productUnitApi;
    @Resource
    private PricePanelBackgroundService pricePanelBackgroundService;
    @Resource
    private ServiceIntegrationService serviceIntegrationService;

    @Override
    public CommonResult<Boolean> initGroup(GroupReqDTO groupReqDTO) {

        List<String> dictTypes = CollUtil.newArrayList(DictTypeEnum.DICT_TYPE_CHANNEL.getCode(), DictTypeEnum.DICT_TYPE_ROOM_STATUS.getCode(),
                DictTypeEnum.DICT_TYPE_PROTOCOL_LEVEL.getCode(), DictTypeEnum.DICT_TYPE_BROKERAGE_LEVEL.getCode(),
                DictTypeEnum.DICT_TYPE_AGENT_LEVEL.getCode(), DictTypeEnum.DICT_TYPE_MEMBERRULE.getCode(), DictTypeEnum.DICT_TYPE_SMS_TEMPLATE.getCode()
                , DictTypeEnum.DICT_TYPE_MEMBERCONFIG.getCode(), DictTypeEnum.DICT_TYPE_HOTEL_SERVICE.getCode(), DictTypeEnum.DICT_TYPE_HOTEL_TYPE.getCode());
        List<DictDataRespDTO> dictDataRespDTOList = dictDataApi.getDictDataListByDicTypes(dictTypes).getData();

        TenantUtils.execute(Long.parseLong(groupReqDTO.getGcode()), () -> {
            // 初始化渠道
            initGroupChannels(groupReqDTO, dictDataRespDTOList);
            // 初始化集团系统参数-订单状态原因
            initGroupOrderReasons(groupReqDTO);
            // 初始化集团系统参数-客户等级
            initGeneralConfigs(groupReqDTO, dictDataRespDTOList);
            // 初始化集团系统参数-房态状态
            initGroupRoomStatus(groupReqDTO, dictDataRespDTOList);
            // 初始化集团系统参数-会员配置
            initGroupMemberConfig(groupReqDTO, dictDataRespDTOList);
            // 初始化付款科目 消费科目
            initGroupPayAndConsumeAccountConfig(groupReqDTO);
            // TODO: 初始化门店管控里的全局配置

            // 初始化会员计划(积分规则)
            initGroupMemberRuleConfig(groupReqDTO, dictDataRespDTOList);
            // 初始化门店3种类型的相关规则
            initGroupMerchantRuleConfig(groupReqDTO);
            // 初始化业务逻辑(夜审业务逻辑、间夜数设置、订单配置、财务)
            initGroupBiz(groupReqDTO);
            // 初始化账套
            initAccSetGroup(groupReqDTO);
            // 初始化电子房价牌背景图
            initPricePanelBackground(groupReqDTO);
        });
        return CommonResult.success(true);
    }


    private void initPricePanelBackground(GroupReqDTO groupReqDTO) {
        List<PricePanelBackgroundDO> list = new ArrayList<>();
        for (PricePanelBackgroundEnum panelBackgroundEnum : PricePanelBackgroundEnum.values()) {
            PricePanelBackgroundDO panelBackgroundDO = new PricePanelBackgroundDO()
                    .setGcode(groupReqDTO.getGcode())
                    .setBackgroundCode(IdUtil.getSnowflakeNextIdStr())
                    .setBackgroundWebsite(panelBackgroundEnum.getUrl())
                    .setName(panelBackgroundEnum.getName())
                    .setLayoutType(panelBackgroundEnum.getType());

            list.add(panelBackgroundDO);
        }

        pricePanelBackgroundService.createPricePanelBackgroundBatch(list);
    }

    @Override
    public CommonResult<Boolean> initMerchant(MerchantReqDTO merchantDTO) {
        List<String> dictTypes = CollUtil.newArrayList(DictTypeEnum.DICT_TYPE_SHIFT.getCode(), DictTypeEnum.DICT_TYPE_ROOM_FEATURE.getCode());
        List<DictDataRespDTO> dictDataRespDTOList = dictDataApi.getDictDataListByDicTypes(dictTypes).getData();
        // 初始化物品分类
        initMerchantThingClasses(merchantDTO);
        // 初始化门店参数（前台 交班模式 收押配置 早餐券）
        initMerchantConfig(merchantDTO);
        // 初始化班次设置
        initMerchantShift(merchantDTO, dictDataRespDTOList);
        // 初始化夜审记录
        initMerchantNightAudiLog(merchantDTO);
        // 初始化营业日期 现付账&赔偿&商品是否允许修改金额 房间特征 & 预抵默认时间
        initMerchantGenerateConfig(merchantDTO, dictDataRespDTOList);
        // 初始化计费规则
        initPriceRule(merchantDTO);
        // 初始化楼栋
        initBuilder(merchantDTO);
        // 初始化中介 和 应收账套
        initAgent(merchantDTO);
        // 初始化门店与集团账套关联
        initAccSetMerchant(merchantDTO);
        // 初始化房务配置
        initHousekeepingConfig(merchantDTO);
        // 初始仓库配置
        initWarehouseConfig(merchantDTO);
        // 初始化物品单位
        initUnit(merchantDTO);
        // 初始化登录PMS服务
        initLoginService(merchantDTO);
        return CommonResult.success(true);
    }

    private void initLoginService(MerchantReqDTO merchantDTO) {
        serviceIntegrationService.createServiceIntegration(new ServiceIntegrationSaveVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode())
                .setSolutionProvider(SOLUTION_PROVIDER)
                .setSolutionType(ServiceTypeEnum.PMS.getCode())
                .setType(ServiceTypeEnum.PMS.getCode())
                .setState(BooleanEnum.TRUE.getValue())
                .setStartTime(LocalDate.now())
                .setEndTime(LocalDate.now().plusYears(100)));
    }

    private void initUnit(MerchantReqDTO merchantDTO) {
        List<ErpProductUnitSaveReqDTO> productUnitSaveReqDTOList = new ArrayList<>();
        List<String> units = CollUtil.newArrayList("个", "包", "箱", "袋", "条", "支", "盒","瓶","斤");
        for (String unit : units) {
            ErpProductUnitSaveReqDTO productUnitSaveReqDTO = new ErpProductUnitSaveReqDTO();
            productUnitSaveReqDTO.setGcode(merchantDTO.getGcode())
                    .setHcode(merchantDTO.getHcode())
                    .setName(unit)
                    .setStatus(CommonStatusEnum.ENABLE.getStatus());
            productUnitSaveReqDTOList.add(productUnitSaveReqDTO);
        }
        productUnitApi.batchCreateProductUnit(productUnitSaveReqDTOList);
    }

    private void initWarehouseConfig(MerchantReqDTO merchantDTO) {
        GeneralConfigSaveReqVO warehouseConfigVO = new GeneralConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(GeneralConfigTypeEnum.WAREHOUSE_CONFIG.getCode())
                .setName(GeneralConfigTypeEnum.WAREHOUSE_CONFIG.getName()).setValue(NumberEnum.ZERO.getNumber())
                .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.WAREHOUSE_CONFIG.getCode())
                .setParentCode(NumberEnum.ZERO.getNumber());

        generalConfigService.createGeneralConfig(warehouseConfigVO);
    }

    private void initHousekeepingConfig(MerchantReqDTO merchantReqDTO) {
        List<GeneralConfigSaveReqVO> list = new ArrayList<>();

        for (RoomCleanTaskEnum cleanTaskEnum : RoomCleanTaskEnum.values()) {
            GeneralConfigSaveReqVO cleanTaskConfigVO = new GeneralConfigSaveReqVO()
                    .setGcode(merchantReqDTO.getGcode())
                    .setHcode(merchantReqDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(cleanTaskEnum.getCode())
                    .setName(cleanTaskEnum.getName()).setValue(NumberEnum.ONE.getNumber())
                    .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.ROOM_CLEAN_TASK.getCode())
                    .setParentCode(NumberEnum.ZERO.getNumber());

            list.add(cleanTaskConfigVO);
        }
        for (RoomCleanHousekeepingEnum housekeepingEnum : RoomCleanHousekeepingEnum.values()) {
            GeneralConfigSaveReqVO housekeepingConfigVO = new GeneralConfigSaveReqVO()
                    .setGcode(merchantReqDTO.getGcode())
                    .setHcode(merchantReqDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(housekeepingEnum.getCode())
                    .setName(housekeepingEnum.getName()).setValue(NumberEnum.ZERO.getNumber())
                    .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.HOUSEKEEPING.getCode())
                    .setRemark(housekeepingEnum.getRemark())
                    .setParentCode(NumberEnum.ZERO.getNumber());

            list.add(housekeepingConfigVO);
        }
        generalConfigService.createGeneralConfigs(list);


        /*HousekeepingConfigSaveReqVO housekeepingConfigSaveReqVO = new HousekeepingConfigSaveReqVO();
        housekeepingConfigSaveReqVO.setHcode(merchantReqDTO.getHcode())
                .setGcode(merchantReqDTO.getGcode())
                .setCleanAudit(BooleanEnum.FALSE.getValue())
                .setPerformanceAudit(BooleanEnum.FALSE.getValue());
        housekeepingConfigService.createHousekeepingConfig(housekeepingConfigSaveReqVO);*/
    }


    /**
     * 初始化代理协议配置及对应账户设置
     * 
     * @param merchantDTO 商户请求参数，包含商户信息
     */
    public void initAgent(MerchantReqDTO merchantDTO) {
        // 遍历所有代理协议类型，创建对应的协议代理及账户设置
        for (ProtocolAgentEnum protocol : ProtocolAgentEnum.values()) {
            protocolAgentService.createProtocolAgent(new ProtocolAgentSaveReqVO().setBelongHcode(merchantDTO.getHcode())
                    .setChannel(protocol.getChannel())
                    .setEndDate(LocalDate.of(9999, 12, 31))
                    .setStartDate(LocalDate.now())
                    .setGcode(merchantDTO.getGcode())
                    .setIsCredit(NumberEnum.ONE.getNumber())
                    .setIsEnable(NumberEnum.ONE.getNumber())
                    .setIsHidePrice(NumberEnum.ZERO.getNumber())
                    .setIsShare(NumberEnum.ZERO.getNumber())
                    .setPaName(protocol.getName())
                    .setPaType(NumberEnum.ONE.getNumber())
                    .setSellLevel(ONE)
                    .setIsSys(NumberEnum.ONE.getNumber())
                    .setArSet(new ArSetSaveReqVO().setGcode(merchantDTO.getGcode())
                            .setArSetName(protocol.getName())
                            .setCreditAccType(NumberEnum.ZERO.getNumber())
                            .setCreditPayDays(NumberEnum.ONE.getNumber())
                            .setCreditPayFix(Integer.parseInt(NumberEnum.ZERO.getNumber()))
                            .setCreditQuotaType(NumberEnum.ZERO.getNumber())
                            .setCreditQuota(Long.parseLong(NumberEnum.ZERO.getNumber()))
                            .setCreditValidType(NumberEnum.ZERO.getNumber())
                            .setCreditMinusAcc(NumberEnum.ONE.getNumber())
                            .setIsEnable(NumberEnum.ONE.getNumber())
                            .setIsManual(NumberEnum.ZERO.getNumber())
                            .setIsSys(NumberEnum.ONE.getNumber())
                            .setPayAccountCode(protocol.getCode())
                            .setArSetMerchants(List.of(merchantDTO.getHcode()))));

        }

    }

    /**
     * 初始化门店现付账套关联关系
     *
     * @param merchantDTO
     */
    private void initAccSetMerchant(MerchantReqDTO merchantDTO) {
        // 1. 获取集团下所有的账套，创建门店关联所有账套
        List<AccSetRespVO> accSetRespVOList = accSetService.getAccSetList(new AccSetReqVO().setGcode(merchantDTO.getGcode()));
        if (CollUtil.isEmpty(accSetRespVOList)) {
            return;
        }
        List<AccSetMerchantDO> accSetMerchantDOList = CollUtil.newArrayList();
        accSetRespVOList.forEach(accSetRespVO -> {
            accSetMerchantDOList.add(new AccSetMerchantDO().setAccCode(accSetRespVO.getAccCode())
                    .setHcode(merchantDTO.getHcode())
                    .setGcode(merchantDTO.getGcode())
                    .setState(BooleanEnum.TRUE.getValue()));
        });
        accSetService.createAccSetMerchant(accSetMerchantDOList);
    }

    /**
     * 初始化集团现付账套 小商品，会员储值，会员卡，赔偿
     *
     * @param groupReqDTO
     */
    private void initAccSetGroup(GroupReqDTO groupReqDTO) {
        List<AccSetDO> accSetSaveReqVOList = CollUtil.newArrayList();
        List<ConsumeAccountEnum> consumeAccountEnumList = List.of(ConsumeAccountEnum.GOODS, ConsumeAccountEnum.MEMBER_RECHARGE, ConsumeAccountEnum.MEMBER_CARD, ConsumeAccountEnum.INDEMNITY_FEE);
        consumeAccountEnumList.forEach(consumeAccountEnum -> {
            accSetSaveReqVOList.add(new AccSetDO().setGcode(groupReqDTO.getGcode())
                    .setAccCode(IdUtil.getSnowflakeNextIdStr())
                    .setAccName(consumeAccountEnum.getLabel())
                    .setSubCode(consumeAccountEnum.getCode())
                    .setSubName(consumeAccountEnum.getLabel())
                    .setIsEnable(BooleanEnum.TRUE.getValue()));
        });
        accSetService.createAccSets(accSetSaveReqVOList);
    }

    /**
     * 初始化楼栋
     *
     * @param merchantDTO
     */
    private void initBuilder(MerchantReqDTO merchantDTO) {
        buildFloorService.createBuildFloor(new BuildFloorSaveReqVO().setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode())
                .setName(BUILDER_NAME).setIsFloor(NumberEnum.ZERO.getNumber()).setParentCode(BuildFloorDO.PARENT_CODE_ROOT));
    }

    /**
     * 初始化计费规则
     *
     * @param merchantDTO
     */
    private void initPriceRule(MerchantReqDTO merchantDTO) {
        // 初始化全天房计费规则
        priceAllDayRuleService.createPriceAllDayRule(new PriceAllDayRuleSaveReqVO()
                .setGcode(merchantDTO.getGcode()).setHcode(merchantDTO.getHcode())
                .setStartPriceNMin(START_PRICE_N_MIN).setAllPriceNMin(ALL_PRICE_N_MIN).setOverNMinCollect(OVER_N_MIN_COLLECT)
                .setOverNMinAllDay(OVER_N_MIN_ALL_DAY)
                .setOverCollectStyle(NumberEnum.ZERO.getNumber()));
        priceHourRuleService.createPriceHourRule(new PriceHourRuleSaveReqVO()
                .setGcode(merchantDTO.getGcode()).setHcode(merchantDTO.getHcode())
                .setStartTime(START_TIME).setEndTime(END_TIME).setRetainTime(RETAIN_TIME)
                .setStartPriceNMin(START_PRICE_N_MIN).setAllPriceNMin(ALL_PRICE_N_MIN)
                .setOverNMinAllDay(OVER_N_MIN_ALL_DAY).setOverCollectStyle(NumberEnum.ZERO.getNumber()));
        priceSpecialRuleService.createPriceSpecialRule(new PriceSpecialRuleSaveReqVO()
                .setGcode(merchantDTO.getGcode()).setHcode(merchantDTO.getHcode())
                .setStartTime(START_TIME).setEndTime(END_TIME).setCheckOutTime(CHECK_OUT_TIME)
                .setStartPriceNMin(START_PRICE_N_MIN).setAllPriceNMin(ALL_PRICE_N_MIN)
                .setOverNMinCollect(OVER_N_MIN_COLLECT)
                .setOverNMinAllDay(OVER_N_MIN_ALL_DAY).setOverCollectStyle(NumberEnum.ZERO.getNumber())
                .setRuleType(DictDataEnum.DICT_DATA_CHARGE_RULE_DAYTIME.getCode()));
        priceSpecialRuleService.createPriceSpecialRule(new PriceSpecialRuleSaveReqVO()
                .setGcode(merchantDTO.getGcode()).setHcode(merchantDTO.getHcode())
                .setStartTime(START_TIME).setEndTime(END_TIME).setCheckOutTime(CHECK_OUT_TIME)
                .setStartPriceNMin(START_PRICE_N_MIN).setAllPriceNMin(ALL_PRICE_N_MIN)
                .setOverNMinCollect(OVER_N_MIN_COLLECT)
                .setOverNMinAllDay(OVER_N_MIN_ALL_DAY).setOverCollectStyle(NumberEnum.ZERO.getNumber())
                .setRuleType(DictDataEnum.DICT_DATA_CHARGE_RULE_MIDNIGHT.getCode()));
    }

    private void initMerchantGenerateConfig(MerchantReqDTO merchantDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        GeneralConfigSaveReqVO bizDateConfigVO = new GeneralConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(GeneralConfigTypeEnum.BIZ_DATE.getCode())
                .setName(GeneralConfigTypeEnum.BIZ_DATE.getName()).setValue(LocalDate.now().toString())
                .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.BIZ_DATE.getCode())
                .setParentCode(NumberEnum.ZERO.getNumber());
        GeneralConfigSaveReqVO defaultPlanCheckinTimeConfigVO = new GeneralConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(GeneralConfigTypeEnum.DEFAULT_PLAN_CHECKIN_TIME.getCode())
                .setName(GeneralConfigTypeEnum.DEFAULT_PLAN_CHECKIN_TIME.getName()).setValue("15:00")
                .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.DEFAULT_PLAN_CHECKIN_TIME.getCode())
                .setParentCode(NumberEnum.ZERO.getNumber());
        GeneralConfigSaveReqVO cashModifyConfigVO = new GeneralConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(ParamConfigTypeEnum.PARAM_TYPE_CASH_MODIFY.getParamType())
                .setName(ParamConfigTypeEnum.PARAM_TYPE_CASH_MODIFY.getName()).setValue(NumberEnum.ONE.getNumber())
                .setIsEnable(NumberEnum.ONE.getNumber()).setType(ParamConfigTypeEnum.PARAM_TYPE_CASH_MODIFY.getParamType())
                .setParentCode(NumberEnum.ZERO.getNumber());
        GeneralConfigSaveReqVO howMinuteConfigVO = new GeneralConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setIsG(NumberEnum.ZERO.getNumber()).setCode(GeneralConfigTypeEnum.HOW_MINUTE.getCode())
                .setName(GeneralConfigTypeEnum.HOW_MINUTE.getName()).setValue(MINUTES.toString())
                .setIsEnable(NumberEnum.ONE.getNumber()).setType(GeneralConfigTypeEnum.HOW_MINUTE.getCode())
                .setParentCode(NumberEnum.ZERO.getNumber());
        List<GeneralConfigSaveReqVO> roomFeatureConfigVOList = CollUtil.newArrayList(bizDateConfigVO, cashModifyConfigVO, howMinuteConfigVO, defaultPlanCheckinTimeConfigVO);
        dictDataRespDTOList.stream().filter(dictDataRespDTO -> DictTypeEnum.DICT_TYPE_ROOM_FEATURE.getCode().equals(dictDataRespDTO.getDictType()))
                .forEach(dictDataRespDTO -> {
                    GeneralConfigSaveReqVO roomFeatureConfigVO = new GeneralConfigSaveReqVO()
                            .setGcode(merchantDTO.getGcode())
                            .setHcode(merchantDTO.getHcode())
                            .setIsG(NumberEnum.ZERO.getNumber())
                            .setCode(dictDataRespDTO.getCode())
                            .setName(dictDataRespDTO.getLabel())
                            .setValue(dictDataRespDTO.getCode())
                            .setIsEnable(NumberEnum.ONE.getNumber())
                            .setType(DictTypeEnum.DICT_TYPE_ROOM_FEATURE.getCode())
                            .setParentCode(NumberEnum.ZERO.getNumber());
                    roomFeatureConfigVOList.add(roomFeatureConfigVO);
                });
        generalConfigService.createGeneralConfigs(roomFeatureConfigVOList);
    }

    private void initMerchantNightAudiLog(MerchantReqDTO merchantDTO) {
        LocalDate today = LocalDate.now();
        // 初始化夜审记录
        NightAudiLogSaveReqVO nightAudiLogSaveReqVO = new NightAudiLogSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setBizDate(today.minusDays(1)).setStartTime(LocalDateTime.now().minusDays(1)).setEndTime(LocalDateTime.now())
                .setNaturalDate(today).setIsSuccess(NumberEnum.ONE.getNumber()).setRemark(NIGHT_AUDI_LOG_REMARK);
        nightAudiLogService.createNightAudiLog(nightAudiLogSaveReqVO);
    }

    /**
     * 初始化班次设置
     *
     * @param merchantDTO
     * @param dictDataRespDTOList
     */
    private void initMerchantShift(MerchantReqDTO merchantDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        List<ShiftTimeSaveReqVO> shiftTimeSaveReqVOList = CollUtil.newArrayList();
        // 将dictDataRespDTOList转换为shiftTimeSaveReqVOList
        dictDataRespDTOList.stream().filter(dictDataRespDTO -> DictTypeEnum.DICT_TYPE_SHIFT.getCode().equals(dictDataRespDTO.getDictType()))
                .forEach(dictDataRespDTO -> {
                    ShiftTimeSaveReqVO shiftTimeSaveReqVO = new ShiftTimeSaveReqVO()
                            .setGcode(merchantDTO.getGcode()).setHcode(merchantDTO.getHcode())
                            .setShiftCode(dictDataRespDTO.getCode()).setShiftName(dictDataRespDTO.getLabel()).setState(NumberEnum.ONE.getNumber())
                            .setStartTime(LocalTime.parse(StringUtils.split(dictDataRespDTO.getValue(), ",")[0]))
                            .setEndTime(LocalTime.parse(StringUtils.split(dictDataRespDTO.getValue(), ",")[1]));
                    shiftTimeSaveReqVOList.add(shiftTimeSaveReqVO);
                });
        shiftTimeService.createShiftTimes(shiftTimeSaveReqVOList);
    }

    private void initMerchantConfig(MerchantReqDTO merchantDTO) {
        // 初始化交班模式
        Map<String, Object> shiftHotelMap = new HashMap<>();
        HotelParamConfigSaveReqVO shiftHotelParamConfig = new HotelParamConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_SHIFT_MODE.getParamType());
        shiftHotelMap.put(ParamConfigTypeEnum.PARAM_TYPE_SHIFT_MODE.getParamType(), new ShiftMode().setShiftMode(ParamConfigTypeEnum.PARAM_TYPE_CASH_FLOW.getParamType()).setPettyCash(PETTY_CASH));
        shiftHotelParamConfig.setValue(shiftHotelMap);
        // 初始化前台
        Map<String, Object> frontMap = new HashMap<>();
        HotelParamConfigSaveReqVO frontHotelParamConfig = new HotelParamConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_FRONT.getParamType());
        frontMap.put(ParamConfigTypeEnum.PARAM_TYPE_FRONT.getParamType(), new Front().setDirtyAlert(NumberEnum.ONE.getNumber()).setIdOneRoom(NumberEnum.ONE.getNumber())
                .setNeedId(NumberEnum.ZERO.getNumber()).setOrderNotice(NumberEnum.ONE.getNumber()));
        frontHotelParamConfig.setValue(frontMap);
        // 初始化收押配置
        Map<String, Object> depositMap = new HashMap<>();
        HotelParamConfigSaveReqVO depositHotelParamConfig = new HotelParamConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_DEPOSIT.getParamType());
        depositMap.put(ParamConfigTypeEnum.PARAM_TYPE_DEPOSIT.getParamType(), new Deposit().setMode(NumberEnum.ZERO.getNumber()).setDeposit(DEPOSIT)
                .setOpenPay(NumberEnum.ZERO.getNumber()));
        depositHotelParamConfig.setValue(depositMap);

        // 初始化早餐券设置
        Map<String, Object> breakfastTicketMap = new HashMap<>();
        HotelParamConfigSaveReqVO breakfastTicketHotelParamConfig = new HotelParamConfigSaveReqVO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_BREAKFAST_TICKET.getParamType());
        breakfastTicketMap.put(ParamConfigTypeEnum.PARAM_TYPE_BREAKFAST_TICKET.getParamType(),
                new BreakfastTicket().setCost(BK_COST).setPrice(BK_PRICE).setStartTime(BK_STARTTIME).setEndTime(BK_ENDTIME).setOpen(NumberEnum.ONE.getNumber()));
        breakfastTicketHotelParamConfig.setValue(breakfastTicketMap);

        hotelParamConfigService.createHotelParamConfigs(Arrays.asList(frontHotelParamConfig, shiftHotelParamConfig, depositHotelParamConfig, breakfastTicketHotelParamConfig));
    }

    private void initMerchantThingClasses(MerchantReqDTO merchantDTO) {
        // 初始化赔偿物品类型
        List<ThingClassSaveReqVO> thingClassSaveReqVOList = CollUtil.newArrayList(
                new ThingClassSaveReqVO()
                        .setGcode(merchantDTO.getGcode())
                        .setHcode(merchantDTO.getHcode())
                        .setThingCode(IdUtil.getSnowflakeNextIdStr())
                        .setThingName(ALL_THING_CLASSIFY)
                        .setGoodsType(NumberEnum.ONE.getNumber())
                        .setIsSys(NumberEnum.ONE.getNumber())
                        .setIsG(NumberEnum.ZERO.getNumber())
        );
        thingClassService.createThingClasses(thingClassSaveReqVOList);
        // 初始化商品分类
        ErpProductCategorySaveReqDTO erpProductCategorySaveReqDTO = new ErpProductCategorySaveReqDTO()
                .setGcode(merchantDTO.getGcode())
                .setHcode(merchantDTO.getHcode())
                .setParentId(0L)
                .setName(ALL_THING_CLASSIFY)
                .setCode(IdUtil.getSnowflakeNextIdStr())
                .setSort(NumberEnum.ZERO.getNumberInt())
                .setStatus(NumberEnum.ZERO.getNumberInt());
        productCategoryApi.createProductUnit(erpProductCategorySaveReqDTO);
    }

    private void initGroupPayAndConsumeAccountConfig(GroupReqDTO groupReqDTO) {
        // 从字典表获取付款科目和消费科目
        List<DictDataRespDTO> dictDataRespDTOList = dictDataApi.getDictDataListByDictTypeAndStatus(null,
                NumberEnum.ONE.getNumberInt()).getData();
        // 初始化 可以入账的Credit科目
        GeneralConfigSaveReqVO payAccountConfigVO = new GeneralConfigSaveReqVO()
                .setCode(GeneralConfigTypeEnum.ALL_CREDIT_ACCOUNT.getCode()).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode())
                .setName(GeneralConfigTypeEnum.ALL_CREDIT_ACCOUNT.getName()).setValue(GeneralConfigTypeEnum.ALL_CREDIT_ACCOUNT.getCode())
                .setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setIsEnable(NumberEnum.ONE.getNumber())
                .setIsG(NumberEnum.ONE.getNumber()).setParentCode(NumberEnum.ZERO.getNumber());
        // 初始化 可以消费的Debit科目
        GeneralConfigSaveReqVO consumeAccountConfigVO = new GeneralConfigSaveReqVO()
                .setCode(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode()).setType(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode())
                .setName(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getName()).setValue(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode())
                .setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setIsEnable(NumberEnum.ONE.getNumber())
                .setIsG(NumberEnum.ONE.getNumber()).setParentCode(NumberEnum.ZERO.getNumber());
        List<GeneralConfigSaveReqVO> generalConfigSaveReqVOList = CollUtil.newArrayList(payAccountConfigVO, consumeAccountConfigVO);
        // 从dictDataRespDTOList中过滤出付款科目和消费科目
        List<DictDataRespDTO> payDictDataRespDTOS = CollectionUtils.filterList(dictDataRespDTOList,
                dictDataRespDTO -> dictDataRespDTO.getDictType().equals(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));
        List<DictDataRespDTO> consumeDictDataRespDTOS = CollectionUtils.filterList(dictDataRespDTOList,
                dictDataRespDTO -> dictDataRespDTO.getDictType().equals(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode()));
        // generalConfigSaveReqVOList添加payDictDataRespDTOS，并转换
        generalConfigSaveReqVOList.addAll(payDictDataRespDTOS.stream().map(dictDataRespDTO -> {
            GeneralConfigSaveReqVO generalConfigSaveReqVO = new GeneralConfigSaveReqVO();
            generalConfigSaveReqVO.setCode(dictDataRespDTO.getCode()).setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode())
                    .setName(dictDataRespDTO.getLabel()).setValue(dictDataRespDTO.getColorType())
                    .setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setIsEnable(NumberEnum.ONE.getNumber())
                    .setMode(dictDataRespDTO.getCssClass())
                    .setCurrencyUnit(dictDataRespDTO.getCurrencyUnit())
                    .setParentCode(GeneralConfigTypeEnum.ALL_CREDIT_ACCOUNT.getCode()).setIsG(NumberEnum.ONE.getNumber());
            return generalConfigSaveReqVO;
        }).toList());
        // generalConfigSaveReqVOList添加consumeDictDataRespDTOS，并转换
        generalConfigSaveReqVOList.addAll(consumeDictDataRespDTOS.stream().map(dictDataRespDTO -> {
            GeneralConfigSaveReqVO generalConfigSaveReqVO = new GeneralConfigSaveReqVO();
            generalConfigSaveReqVO.setCode(dictDataRespDTO.getCode()).setType(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode())
                    .setName(dictDataRespDTO.getLabel()).setValue(dictDataRespDTO.getValue())
                    .setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setIsEnable(NumberEnum.ONE.getNumber())
                    .setMode(dictDataRespDTO.getCssClass())
                    .setParentCode(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode()).setIsG(NumberEnum.ONE.getNumber());
            return generalConfigSaveReqVO;
        }).toList());
        generalConfigService.createGeneralConfigs(generalConfigSaveReqVOList);
    }

    private void initGroupMerchantRuleConfig(GroupReqDTO groupReqDTO) {
        List<MerchantTypeRuleSaveReqVO> merchantTypeRuleSaveReqVOS = CollUtil.newArrayList();
        merchantTypeRuleSaveReqVOS.add(new MerchantTypeRuleSaveReqVO().setGcode(groupReqDTO.getGcode()).setMerchantType(ManageTypeEnum.DIRECT.getCode())
                .setMerchantTypeName(ManageTypeEnum.DIRECT.getLabel())
                .setMemberPointShare(NumberEnum.ZERO.getNumber()).setMemberShare(NumberEnum.ONE.getNumber())
                .setStoreFeeRecord(NumberEnum.ZERO.getNumber()).setRemark(ManageTypeEnum.DIRECT.getLabel()));
        merchantTypeRuleSaveReqVOS.add(new MerchantTypeRuleSaveReqVO().setGcode(groupReqDTO.getGcode()).setMerchantType(ManageTypeEnum.JOIN.getCode())
                .setMerchantTypeName(ManageTypeEnum.JOIN.getLabel())
                .setMemberPointShare(NumberEnum.ZERO.getNumber()).setMemberShare(NumberEnum.ZERO.getNumber())
                .setStoreFeeRecord(NumberEnum.ONE.getNumber()).setRemark(ManageTypeEnum.JOIN.getLabel()));
        merchantTypeRuleSaveReqVOS.add(new MerchantTypeRuleSaveReqVO().setGcode(groupReqDTO.getGcode()).setMerchantType(ManageTypeEnum.DEPOSIT.getCode())
                .setMerchantTypeName(ManageTypeEnum.DEPOSIT.getLabel())
                .setMemberPointShare(NumberEnum.ZERO.getNumber()).setMemberShare(NumberEnum.ZERO.getNumber())
                .setStoreFeeRecord(NumberEnum.ONE.getNumber()).setRemark(ManageTypeEnum.DEPOSIT.getLabel()));
        merchantTypeRuleService.createMerchantTypeRules(merchantTypeRuleSaveReqVOS);
    }

    private void initGroupChannels(GroupReqDTO groupReqDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        // 从字典列表中过滤出渠道
        List<DictDataRespDTO> qdDictDataList = CollectionUtils.filterList(dictDataRespDTOList,
                dict -> (Objects.equals(dict.getDictType(), DictTypeEnum.DICT_TYPE_CHANNEL.getCode()) && Objects.equals(dict.getStatus(), NumberEnum.ONE.getNumberInt())));
        // 按照sort排序
        qdDictDataList.sort(Comparator.comparing(DictDataRespDTO::getSort)
                .thenComparing(DictDataRespDTO::getCode));
        List<ChannelSaveReqDTO> channelSaveReqDTOS = CollUtil.newArrayList();
        qdDictDataList.forEach(dictDataDO -> {
            channelSaveReqDTOS.add(new ChannelSaveReqDTO().setGcode(groupReqDTO.getGcode())
                    .setHcode(NumberEnum.ZERO.getNumber())
                    .setChannelType(NumberEnum.ONE.getNumber())
                    .setChannelName(dictDataDO.getLabel())
                    .setIsEnable(NumberEnum.ONE.getNumber())
                    .setIsG(NumberEnum.ONE.getNumber())
                    .setIsSys(NumberEnum.ONE.getNumber())
                    .setShortName(dictDataDO.getColorType())
                    .setChannelCode(dictDataDO.getCode()));
        });
        channelService.createChannels(channelSaveReqDTOS);
    }


    private void initGroupOrderReasons(GroupReqDTO groupReqDTO) {
        // 初始化订单原因
        List<GeneralConfigSaveReqVO> generalConfigSaveReqVOS = CollUtil.newArrayList();
        // 迭代OrderReasonEnum.map 获取所有订单原因 并赋值给GeneralConfigSaveReqVO 最后组装为list
        OrderReasonEnum.map.forEach((k, v) -> {
            v.forEach(reason -> generalConfigSaveReqVOS.add(new GeneralConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber())
                    .setName(reason).setValue(reason).setType(k).setParentCode(NumberEnum.ZERO.getNumber()).setIsG(NumberEnum.ONE.getNumber())
                    .setIsEnable(NumberEnum.ONE.getNumber())));
        });
        generalConfigService.createGeneralConfigs(generalConfigSaveReqVOS);
    }

    private void initGroupRoomStatus(GroupReqDTO groupReqDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        // 初始化房态
        GroupParamConfigSaveReqVO groupParamSaveReqVO = new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode())
                .setParamType(ParamConfigTypeEnum.PARAM_TYPE_ROOMCOLOR.getParamType());
        Colors colors = new Colors();
        List<Colors.Color> colorsList = CollUtil.newArrayList();
        // 过滤为room_status的字典
        List<DictDataRespDTO> ftDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_ROOM_STATUS.getCode()));
        ftDictDataList.forEach(dictDataDO -> {
            colorsList.add(new Colors.Color().setCode(dictDataDO.getCode()).setName(dictDataDO.getLabel()).setColor(dictDataDO.getColorType()));
        });
        colors.setColors(colorsList);
        Map<String, Object> value = new HashMap<>();
        value.put(ParamConfigTypeEnum.PARAM_TYPE_ROOMCOLOR.getParamType(), colors);
        groupParamSaveReqVO.setValue(value);
        groupParamConfigService.createGroupParamConfig(groupParamSaveReqVO);
    }

    private void initGroupMemberConfig(GroupReqDTO groupReqDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        // 初始化会员配置
        GroupParamConfigSaveReqVO groupParamSaveReqVO = new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode())
                .setParamType(ParamConfigTypeEnum.PARAM_TYPE_MEMBERCONFIG.getParamType());
        List<DictDataRespDTO> dictDataList = CollectionUtils.filterList(dictDataRespDTOList,
                dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_MEMBERCONFIG.getCode()));
        MemberConfig memberConfig = new MemberConfig();
        dictDataList.forEach(dictDataDO -> {
            if (DictDataEnum.DICT_DATA_MEMBERCONFIG_SMS_NOTICE.getCode().equals(dictDataDO.getCode())) {
                SmsNotice smsNotice = JsonUtils.parseObject(dictDataDO.getValue(), SmsNotice.class);
                memberConfig.setSmsNotice(smsNotice);
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_REGISTER_CHANGE_FEE.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setRegisterChangeFee(dictDataDO.getValue());
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_CANCEL_NUM.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setCancelNum(Integer.parseInt(dictDataDO.getValue()));
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_BOOK_MAX_NUM.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setBookMaxNum(Integer.parseInt(dictDataDO.getValue()));
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_BOOK_AHEAD_DAYS.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setBookAheadDays(Integer.parseInt(dictDataDO.getValue()));
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_PTS_COUPONS_TOGETHER.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setPtsCouponsTogether(dictDataDO.getValue());
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_MEMBER_INFO_SEARCH.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setMemberInfoSearch(dictDataDO.getValue());
            } else if (DictDataEnum.DICT_DATA_MEMBERCONFIG_POINTS_WITH_NON_OWNER_CARD.getCode().equals(dictDataDO.getCode())) {
                memberConfig.setPointsWithNonOwnerCard(dictDataDO.getValue());
            }
        });
        Map<String, Object> value = new HashMap<>();
        value.put(ParamConfigTypeEnum.PARAM_TYPE_MEMBERCONFIG.getParamType(), memberConfig);
        groupParamSaveReqVO.setValue(value);
        groupParamConfigService.createGroupParamConfig(groupParamSaveReqVO);
    }

    private void initGroupMemberRuleConfig(GroupReqDTO groupReqDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        GroupParamConfigSaveReqVO groupParamSaveReqVO = new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode())
                .setParamType(ParamConfigTypeEnum.PARAM_TYPE_MEMBERRULE.getParamType());
        List<DictDataRespDTO> dictDataList = CollectionUtils.filterList(dictDataRespDTOList,
                dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_MEMBERRULE.getCode()));
        Map<String, Object> value = new HashMap<>();
        dictDataList.forEach(dictDataDO -> {
            if (DictDataEnum.DICT_DATA_MEMBER_RULE.getCode().equals(dictDataDO.getCode())) {
                MemberPointRuleConfig memberPointRuleConfig = JsonUtils.parseObject(dictDataDO.getValue(), MemberPointRuleConfig.class);
                value.put(ParamConfigTypeEnum.PARAM_TYPE_MEMBERRULE.getParamType(), memberPointRuleConfig);
                groupParamSaveReqVO.setValue(value);
            }
        });
        groupParamConfigService.createGroupParamConfig(groupParamSaveReqVO);
    }


    private void initGeneralConfigs(GroupReqDTO groupReqDTO, List<DictDataRespDTO> dictDataRespDTOList) {
        // 从字典列表中过滤出三种级别的数据
        List<DictDataRespDTO> brokerageLevelDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_BROKERAGE_LEVEL.getCode()));
        List<DictDataRespDTO> agentLevelDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_AGENT_LEVEL.getCode()));
        List<DictDataRespDTO> protocolLevelDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_PROTOCOL_LEVEL.getCode()));
        List<DictDataRespDTO> serviceDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_HOTEL_SERVICE.getCode()));
        List<DictDataRespDTO> hotelTypeDictDataList = CollectionUtils.filterList(dictDataRespDTOList, dictDataRespDTO -> dictDataRespDTO.getDictType().equals(DictTypeEnum.DICT_TYPE_HOTEL_TYPE.getCode()));
        List<DictDataRespDTO> levelSaveReqVOS = CollUtil.newArrayList();
        levelSaveReqVOS.addAll(brokerageLevelDictDataList);
        levelSaveReqVOS.addAll(agentLevelDictDataList);
        levelSaveReqVOS.addAll(protocolLevelDictDataList);
        levelSaveReqVOS.addAll(serviceDictDataList);
        levelSaveReqVOS.addAll(hotelTypeDictDataList);
        List<GeneralConfigSaveReqVO> generalConfigSaveReqVOS = CollUtil.newArrayList();
        levelSaveReqVOS.forEach(dictDataDO -> {
            generalConfigSaveReqVOS.add(new GeneralConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setCode(dictDataDO.getCode()).setValue(dictDataDO.getValue())
                    .setName(dictDataDO.getLabel()).setType(dictDataDO.getDictType())
                    .setParentCode(NumberEnum.ZERO.getNumber()).setIsG(NumberEnum.ONE.getNumber()).setIsEnable(NumberEnum.ONE.getNumber()));
        });
        // 添加集团最晚退房时间
        generalConfigSaveReqVOS.add(new GeneralConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setHcode(NumberEnum.ZERO.getNumber()).setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setValue("14:00").setName(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getName())
                .setIsG(NumberEnum.ONE.getNumber()).setParentCode(NumberEnum.ZERO.getNumber()).setIsEnable(NumberEnum.ONE.getNumber())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode()));
        generalConfigService.createGeneralConfigs(generalConfigSaveReqVOS);
    }


    private void initGroupBiz(GroupReqDTO groupReqDTO) {
        // 夜审业务逻辑
        NightAudiSetSaveReqVO nightAudit = new NightAudiSetSaveReqVO().setGcode(groupReqDTO.getGcode()).setAuto(BooleanEnum.TRUE.getValue()).setTime(LocalTime.parse(RandomUtil.randomEle(NIGHT_AUDIT_TIME)))
                .setNotice(NumberEnum.TEN.getNumberInt()).setToDirty(BooleanEnum.TRUE.getValue())
                .setAgentCredit(BooleanEnum.FALSE.getValue()).setOtaCredit(BooleanEnum.FALSE.getValue()).setProtocolCredit(BooleanEnum.FALSE.getValue())
                .setToAllDay(BooleanEnum.TRUE.getValue()).setHandStartTime(HANDSTARTTIME).setHandEndTime(HANDENDTIME);
        nightAudiSetService.createNightAudiSet(BeanUtils.toBean(nightAudit, NightAudiSetSaveReqVO.class));
        // 间夜数设置
        Map<String, Object> nightNumMap = new HashMap<>();
        NightNum nightNum = new NightNum().setHand(NumberEnum.ONE.getNumber()).setAddAllDay(NumberEnum.ONE.getNumber())
                .setAddHalfDay(NumberEnum.ONEHALF.getNumber()).setAllDay(NumberEnum.ONE.getNumber()).setHour(NumberEnum.ONEHALF.getNumber());
        nightNumMap.put(ParamConfigTypeEnum.PARAM_TYPE_NIGHT_NUM.getParamType(), nightNum);

        // 业务逻辑中-订单配置
        Map<String, Object> orderMap = new HashMap<>();
        Order order = new Order().setMinOneHour(MINUTES).setContinuePrice(ContinuePriceEnum.LAST.getCode())
                .setSecrecy(NumberEnum.ONE.getNumber()).setCouponsActiveTogether(NumberEnum.ONE.getNumber());
        orderMap.put(ParamConfigTypeEnum.PARAM_TYPE_ORDER.getParamType(), order);

        // 业务逻辑中-财务
        Map<String, Object> financeMap = new HashMap<>();
        Finance finance = new Finance().setOrderBelong(NumberEnum.ZERO.getNumber()).setClassMix(NumberEnum.ZERO.getNumber());
        financeMap.put(ParamConfigTypeEnum.PARAM_TYPE_FINANCE.getParamType(), finance);

        List<GroupParamConfigSaveReqVO> groupParamConfigSaveReqVOS = CollUtil.newArrayList();
        groupParamConfigSaveReqVOS.add(new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_NIGHT_NUM.getParamType())
                .setValue(nightNumMap));
        groupParamConfigSaveReqVOS.add(new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_ORDER.getParamType())
                .setValue(orderMap));
        groupParamConfigSaveReqVOS.add(new GroupParamConfigSaveReqVO().setGcode(groupReqDTO.getGcode()).setParamType(ParamConfigTypeEnum.PARAM_TYPE_FINANCE.getParamType())
                .setValue(financeMap));
        groupParamConfigService.createGroupParamConfigs(groupParamConfigSaveReqVOS);

    }
}
