package info.qizhi.aflower.module.pms.controller.admin.report.vo.arverifydetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - AR账核销明细 Response VO")
@Data
public class ArVerifyDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "单位中介代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String unitCode;

    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String unitName;

    @Schema(description = "账户类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String creditAccType;

    @Schema(description = "账户类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String creditAccTypeName;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "AR账户-账套代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String arSetCode;

    @Schema(description = "AR账户-账套名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String arSetName;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rNo;

    @Schema(description = "发生营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "核销营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate verifyBizDate;

    @Schema(description = "核销时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime verifyTime;

    @Schema(description = "核销金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long verifyFee;

    @Schema(description = "发生金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "入账操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operater;

    @Schema(description = "入账操作员昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operaterName;

    @Schema(description = "核销操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String verifyOperater;

    @Schema(description = "核销操作员昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String verifyOperaterName;

    @Schema(description = "备注")
    private String remark;

}
