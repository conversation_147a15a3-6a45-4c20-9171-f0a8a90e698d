package info.qizhi.aflower.module.pms.api.order;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.order.dto.OrderTogetherReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderTogetherRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class OrderTogetherApiImpl implements OrderTogetherApi {

    @Resource
    private OrderTogetherService orderTogetherService;


    @Override
    public CommonResult<List<OrderTogetherRespDTO>> getOrderTogetherList(OrderTogetherReqDTO reqDTO) {
        List<OrderTogetherDO> orderTogetherList= validateOrderTogetherExists(reqDTO);
        return success(BeanUtils.toBean(orderTogetherList, OrderTogetherRespDTO.class));
    }

    private List<OrderTogetherDO> validateOrderTogetherExists(OrderTogetherReqDTO reqDTO) {
        List<OrderTogetherDO> orderTogetherList=new ArrayList<>();
        if(CollUtil.isNotEmpty(reqDTO.getTogetherCodes())){
            orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(reqDTO.getGcode()).setHcode(reqDTO.getHcode()).setTogetherCodes(reqDTO.getTogetherCodes()));
        }else {
            OrderTogetherReqVO bean = BeanUtils.toBean(reqDTO, OrderTogetherReqVO.class);
            bean.setState(OrderStateEnum.CHECK_IN.getCode());
            orderTogetherList= orderTogetherService.getOrderTogetherList(bean);
        }

        return orderTogetherList;
    }
}
