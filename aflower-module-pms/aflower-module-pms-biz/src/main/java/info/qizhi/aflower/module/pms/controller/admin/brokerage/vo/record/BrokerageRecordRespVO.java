package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import com.alibaba.excel.annotation.*;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 返佣记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BrokerageRecordRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24305")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "返佣代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("返佣代码")
    private String brokerageCode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "单位/中介代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单位/中介代码")
    private String paCode;

    @Schema(description = "产生佣金的策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产生佣金的策略代码")
    private String strategyCode;

    @Schema(description = "入住房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "25165")
    @ExcelProperty("房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomPrice;

    @Schema(description = "入住人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("入住人姓名")
    private String name;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String bizDate;

    @Schema(description = "外部订单号;来自OTA的订单号")
    @ExcelProperty("外部订单号;来自OTA的订单号")
    private String outOrderNo;

    @Schema(description = "状态;0:未核销 1:已核销", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0:未核销 1:已核销")
    private String state;

    @Schema(description = "核销人")
    @ExcelProperty("核销人")
    private String verifyUser;

    @Schema(description = "核销时间")
    @ExcelProperty("核销时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime verifyTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}