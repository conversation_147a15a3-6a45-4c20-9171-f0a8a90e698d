package info.qizhi.aflower.module.pms.controller.admin.housekeepingconfig.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 酒店房务配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class HousekeepingConfigRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19681")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "房扫是否需要审核;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房扫是否需要审核;0:否 1:是")
    private String cleanAudit;

    @Schema(description = "业绩是否需要审核;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业绩是否需要审核;0:否 1:是")
    private String performanceAudit;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}