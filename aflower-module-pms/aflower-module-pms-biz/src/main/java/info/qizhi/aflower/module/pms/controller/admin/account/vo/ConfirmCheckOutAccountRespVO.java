package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第二步 计算房价结果 Response VO")
@Data
public class ConfirmCheckOutAccountRespVO {

    @Schema(description = "单号")
    private String no;

    @Schema(description = "宾客代码")
    private String togetherCode;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "入住类型")
    private String checkinType;

    @Schema(description = "入住时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime checkinTime;

    @Schema(description = "退房时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime checkoutTime;

    @Schema(description = "已收房费之后")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "加收房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;


}