package info.qizhi.aflower.module.pms.controller.admin.overbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 超预订配置 Request VO")
@Data
@ToString(callSuper = true)
public class OverBookReqVO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "是否有效")
    private String isEnable;

}