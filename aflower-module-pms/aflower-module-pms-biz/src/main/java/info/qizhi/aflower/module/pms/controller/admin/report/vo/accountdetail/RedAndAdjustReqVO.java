package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 冲账调账明细报表 Response VO")
@Data
public class RedAndAdjustReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "操作员")
    private String recorder;

    @Schema(description = "开始日期", example = "2024-07-04")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    private LocalDate endDate;

}
