package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.predict;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "前台营业 - 房类预测 Response VO")
@Data
public class RoomPredictionRespVO {
    @Schema(description = "房型信息")
    private List<RoomInfo> roomInfos;

    @Schema(description = "总房型信息")
    private List<RoomInfo> totalRoomInfo;

    @Data
    public static class RoomInfo {
        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "房型名称")
        private String rtName;

        @Schema(description = "房型数量")
        private Integer rtNum;

        @Schema(description = "每日可售数")
        private List<DateRoomInfo> dateRoomInfos;
    }

    @Data
    public static class DateRoomInfo {
        @Schema(description = "日期")
        @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
        private LocalDate date;

        @Schema(description = "星期")
        private Integer week;

        @Schema(description = "维修房间数")
        private Integer repairRoomCount;

        @Schema(description = "可售数")
        private Integer canSellNum;

        @Schema(description = "已售数")
        private Integer soldNum;

        @Schema(description = "可超数")
        private Integer canOverNum;

        @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "168")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long price;

        @Schema(description = "ADR", requiredMode = Schema.RequiredMode.REQUIRED, example = "168")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long ADR;

        @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED, example = "168")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long revPar;

        @Schema(description = "已售数百分比")
        private BigDecimal soldPercentage;

        @Schema(description = "可售数百分比")
        private BigDecimal canSellPercentage;
    }



}
