package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务新增 - 预授权 Request VO")
@Data
public class PreAuthAccountSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号或订单号,预订时产生的账务存储预订单号，入住后产生的账务存储订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{no.notblank}")
    private String no;

    @Schema(description = "宾客代码，订单入账时需要")
    private String togetherCode;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= AccountTypeEnum.class, message = "{accType.instringenum}")
    private String accType;

    @Schema(description = "金额;账务的金额,如果是冲调就为负数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "消费科目代码;消费科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{consume.subCode.notempty}")
    private String subCode;

    @Schema(description = "业务详情")
    private String accDetail;

    @Schema(description = "预授权号;当支付方式为预授权时有效")
    private String preAuthNo;

    @Schema(description = "有效日期;当支付方式为预授权时有效")
    private LocalDate validDate;

    @Schema(description = "付款码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)")
    private String payCode;

    @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
    private String bankType;

    @Schema(description = "银行卡号")
    private String bankCardNo;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}