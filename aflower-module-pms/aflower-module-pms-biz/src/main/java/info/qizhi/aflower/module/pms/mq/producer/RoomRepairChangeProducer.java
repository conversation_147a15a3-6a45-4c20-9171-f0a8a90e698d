package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomRepairChangeMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 维修消息生产者
 */

@Slf4j
@Component
public class RoomRepairChangeProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送房间维修消息
     *
     */
    public void sendRealRoomStateMessage(String hcode, String repairType,String msg) {
        RoomRepairChangeMessage message = new RoomRepairChangeMessage();
        message.setHcode(hcode);
        message.setRepairType(repairType);
        message.setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.ROOM_REPAIR_CHANGE_MESSAGE_QUEUE, message);
    }

}
