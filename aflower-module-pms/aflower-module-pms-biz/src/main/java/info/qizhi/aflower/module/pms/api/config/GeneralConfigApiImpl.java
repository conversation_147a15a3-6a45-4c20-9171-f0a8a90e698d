package info.qizhi.aflower.module.pms.api.config;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.GeneralConfigTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReqDTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigReq2VO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.GeneralConfigReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GeneralConfigDO;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * @Author: TY
 * @CreateTime: 2024-06-26
 * @Description: 通用配置
 * @Version: 1.0
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class GeneralConfigApiImpl implements GeneralConfigApi {

    @Resource
    private GeneralConfigService generalConfigService;

    @Override
    public CommonResult<LocalDate> getBizDay(String hcode) {
        return success(generalConfigService.getBizDate(hcode));
    }

    @Override
    public CommonResult<Map<String, LocalDate>> getAllBizDay(String gcode) {
        List<GeneralConfigDO> list = generalConfigService.getGeneralConfigList(new GeneralConfigReqVO().setGcode(gcode).setType(GeneralConfigTypeEnum.BIZ_DATE.getCode()));
        Map<String, LocalDate> map = new HashMap<>();
        if (CollUtil.isNotEmpty(list)) {
            map = CollectionUtils.convertMap(list, GeneralConfigDO::getCode, config->{
                return LocalDate.parse(config.getValue());
            });
        }
        return success(map);
    }

    @Override
    public CommonResult<GeneralConfigRespDTO> getCheckOutTimeConfig(String gcode) {
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(gcode, GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode());
        return success(BeanUtils.toBean(generalConfig, GeneralConfigRespDTO.class));
    }

    @Override
    public CommonResult<GeneralConfigRespDTO> getGeneralConfig(GeneralConfigReq2DTO req) {
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(BeanUtils.toBean(req, GeneralConfigReq2VO.class));
        return success(BeanUtils.toBean(generalConfig, GeneralConfigRespDTO.class));
    }

    @Override
    public CommonResult<List<GeneralConfigRespDTO>> getReceiptList(GeneralConfigReqDTO req) {
        List<GeneralConfigDO> generalConfigList = generalConfigService.getGeneralConfigList(new GeneralConfigReqVO().setGcode(req.getGcode()).setHcode(NumberEnum.ZERO.getNumber())
                .setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode()));

        // 过滤出 currencyUnit 不为空，且 code 以 "receipt" 结尾的记录
        List<GeneralConfigRespDTO> filteredList = generalConfigList.stream()
                .filter(config -> config.getCurrencyUnit() != null && !config.getCurrencyUnit().isEmpty())
                .filter(config -> config.getCode() != null && config.getCode().endsWith("receipt"))
                .map(config -> BeanUtils.toBean(config, GeneralConfigRespDTO.class))
                .collect(Collectors.toList());

        return success(BeanUtils.toBean(filteredList, GeneralConfigRespDTO.class));
    }

    @Override
    public CommonResult<String> getWarehouseConfig(String gcode, String hcode) {
        return success(generalConfigService.getWarehouseConfig(gcode, hcode));
    }
}
