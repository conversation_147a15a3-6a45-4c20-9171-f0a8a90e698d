package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.DecToIntSerializer;
import info.qizhi.aflower.framework.common.pojo.ImageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 房价牌 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PricePanelRespVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "酒店名称;与门店代码无关联", example = "王五")
    @ExcelProperty("酒店名称;与门店代码无关联")
    private String hname;

    @Schema(description = "酒店名称字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("酒店名称字号")
    private Integer hnameFontSize;

    @Schema(description = "是否展示门店logo;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否展示门店logo;0:关闭 1：开启")
    private String isShow;

    @Schema(description = "酒店提示")
    @ExcelProperty("酒店提示")
    private String reminder;

    @Schema(description = "是否滚动播放;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否滚动播放;0:关闭 1：开启")
    private String isRolling;

    @Schema(description = "酒店提示字号")
    @ExcelProperty("酒店提示字号")
    private Integer reminderFontSize;

    @Schema(description = "房型代码;存多个房型级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码;存多个房型级别代码")
    private String rtCode;

    @Schema(description = "标题字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题字号")
    @JsonSerialize(using = DecToIntSerializer.class)
    private Integer titleFontSize;

    @Schema(description = "版面类型;0:横版 1：竖版", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("版面类型;0:横版 1：竖版")
    private String layoutType;

   /* @Schema(description = "模板地址")
    @ExcelProperty("模板地址")
    private String templateWebsite;*/

    @Schema(description = "背景图片代码")
    @ExcelProperty("背景图片代码")
    private String backgroundCode;

    @Schema(description = "背景图片地址")
    @ExcelProperty("背景图片地址")
    private String backgroundWebsite;

    @Schema(description = "门店Logo")
    private List<ImageVO> logo;

    @Schema(description = "酒店房型展示图片")
    private List<ImageVO> houseImage;

    @Schema(description = "内容字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("内容字号")
    @JsonSerialize(using = DecToIntSerializer.class)
    private Integer contentFontSize;

    @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码;存多个会员级别代码")
    private String mtCode;

   /* @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码;存多个会员级别代码")
    private List<MemberTypeRespDTO> memberType;*/

    @Schema(description = "展示行数;每屏房型展示行数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("展示行数;每屏房型展示行数")
    private Integer showRows;

    @Schema(description = "切换时间;翻页切换时间,不切换为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("切换时间;翻页切换时间,不切换为0")
    private Integer switchingTime;

    @Schema(description = "滚动时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rollTime;

    @Schema(description = "二维码文案", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("二维码文案")
    private String tips;

    @Schema(description = "二维码图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "QRImage")
    @ExcelProperty("二维码图片")
    private List<ImageVO> QRImage;

    @Schema(description = "业务开通状态;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务开通状态;0:关闭 1：开启")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /*@Schema(description = "房型详情")
    private List<PricePanelContentBO> pricePanelContent;*/

   /* @Schema(description = "集团logo地址")
    private String logo;*/

}