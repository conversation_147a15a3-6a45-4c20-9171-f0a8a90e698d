package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 新增账务 - 消费 Request VO")
@Data
public class SplitAccountReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "原账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{originalAccNo.notblank}")
    private String  accNo;

    @Schema(description = "拆后的账", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "accountList.notempty")
    private List<Account> accountList;

    @Data
    public static class Account {

        @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{fee.notnull}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "税后金额,前端无需传，是方便后端计算用", requiredMode = Schema.RequiredMode.REQUIRED)
        private Long afterTaxFee;

        @Schema(description = "备注")
        @Size(max = 255, message = "{remark.size}")
        private String remark;
    }

}