package info.qizhi.aflower.module.pms.controller.admin.customer.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 客历新增/修改 Request VO")
@Data
public class CustomerSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22512")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{name.notempty}")
    @Length(max = 32, message = "{名称不能超过32}")
    private String name;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{sex.notempty}")
    private String sex;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{idType.notempty}")
    private String idType;

    @Schema(description = "证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{idNo.notempty}")
    private String idNo;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "电话")
    @Mobile
    private String phone;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "黑名单;0正常 1拉黑", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class,message =  "{isBlack.enum}")
    private String isBlack;

    @Schema(description = "拉黑原因", example = "不香")
    private String blackReason;

    @Schema(description = "是否接收短信;0：不接收 1：接收", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isSms.notempty}")
    @InStringEnum(value = BooleanEnum.class,message =  "{isSms.enum}")
    private String isSms;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "入住次数", example = "3")
    private Integer checkinNum;

    @Schema(description = "是否会员;0: 否 1: 是")
    @NotEmpty(message = "{isMember.notempty}")
    @InStringEnum(value = BooleanEnum.class,message =  "{isMember.enum}")
    private String isMember;

    @Schema(description = "最后一次入住门店名称", example = "张三")
    private String lastMerchant;

    @Schema(description = "最后一次入住时间", example = "2022-02-02 00:00:00")
    private LocalDateTime lastCheckinTime;

}