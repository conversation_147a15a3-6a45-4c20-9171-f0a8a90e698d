package info.qizhi.aflower.module.pms.controller.admin.account.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 统计 Response VO")
@Data
public class AccountStatByTogetherCodesRespVO {

    @Schema(description = "消费合计" )
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeTotal;

    @Schema(description = "付款合计" )
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentTotal;

}
