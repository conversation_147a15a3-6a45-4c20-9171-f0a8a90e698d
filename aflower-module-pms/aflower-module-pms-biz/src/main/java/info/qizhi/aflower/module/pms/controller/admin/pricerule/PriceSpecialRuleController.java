package info.qizhi.aflower.module.pms.controller.admin.pricerule;

import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special.PriceSpecialRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special.PriceSpecialRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special.PriceSpecialRuleSaveReqVO;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.module.pms.service.pricerule.PriceSpecialRuleService;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 特殊房(白天房午夜房)计费规则")
@RestController
@RequestMapping("/pms/price-special-rule")
@Validated
public class PriceSpecialRuleController {

    @Resource
    private PriceSpecialRuleService priceSpecialRuleService;

    @PutMapping("/update")
    @Operation(summary = "更新特殊房(白天房午夜房)计费规则")
    @PreAuthorize("@ss.hasPermission('pms:price-special-rule:update')")
    public CommonResult<Boolean> updatePriceSpecialRule(@Valid @RequestBody PriceSpecialRuleSaveReqVO updateReqVO) {
        priceSpecialRuleService.updatePriceSpecialRule(updateReqVO);
        return success(true);
    }

    @GetMapping("/get/daytime")
    @Operation(summary = "获得白天房计费规则")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:price-special-rule:query')")
    public CommonResult<PriceSpecialRuleRespVO> getPriceSpecialRuleDaytime(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        PriceSpecialRuleRespVO priceSpecialRule = priceSpecialRuleService.getPriceSpecialRuleDayTime(new PriceSpecialRuleReqVO().setGcode(gcode).setHcode(hcode));
        return success(priceSpecialRule);
    }

    @GetMapping("/get/midnight")
    @Operation(summary = "获得午夜房计费规则")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:price-special-rule:query')")
    public CommonResult<PriceSpecialRuleRespVO> getPriceSpecialRuleMidNight(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        PriceSpecialRuleRespVO priceSpecialRule = priceSpecialRuleService.getPriceSpecialRuleMidNight(new PriceSpecialRuleReqVO().setGcode(gcode).setHcode(hcode));
        return success(priceSpecialRule);
    }

}