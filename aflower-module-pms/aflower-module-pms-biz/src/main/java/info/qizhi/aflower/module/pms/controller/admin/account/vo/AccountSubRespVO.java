package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务科目 Request VO")
@Data
@ToString(callSuper = true)
public class AccountSubRespVO {

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;
    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;
    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String subType;
}