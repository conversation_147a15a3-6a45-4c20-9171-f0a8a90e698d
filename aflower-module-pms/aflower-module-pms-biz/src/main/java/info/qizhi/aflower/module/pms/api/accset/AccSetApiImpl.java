package info.qizhi.aflower.module.pms.api.accset;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.accset.dto.AccSetReqDTO;
import info.qizhi.aflower.module.pms.api.accset.dto.AccSetRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccSetReqVO;
import info.qizhi.aflower.module.pms.controller.admin.accset.vo.AccSetRespVO;
import info.qizhi.aflower.module.pms.service.accset.AccSetService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class AccSetApiImpl implements AccSetApi {

    @Resource
    private AccSetService accSetService;


    @Override
    public CommonResult<AccSetRespDTO> getAccSetBySubcode(String subCode, String gcode) {
        AccSetRespVO accSet = accSetService.getAccSetBySubCode(subCode, gcode);
        return success(BeanUtils.toBean(accSet, AccSetRespDTO.class));
    }

    @Override
    public CommonResult<List<AccSetRespDTO>> getAccSetList(AccSetReqDTO reqVO) {
        List<AccSetRespVO> list = accSetService.getAccSetList(BeanUtils.toBean(reqVO, AccSetReqVO.class));
        return success(BeanUtils.toBean(list, AccSetRespDTO.class));
    }

    @Override
    public CommonResult<String> getMerchantAccCodeBySubCode(String subCode, String gcode, String hcode) {
        return success(accSetService.getMerchantAccCodeBySubCode(subCode, gcode, hcode));
    }


}
