package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务新增 - 付款 Request VO")
@Data
public class PayAccountSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private String id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{no.notblank}")
    private String no;

    @Schema(description = "宾客代码，订单入账时需要")
    private String togetherCode;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单 cash:现付账订单", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = AccountTypeEnum.class, message = "{accType.instringenum}")
    @NotBlank(message = "{accType.notblank}")
    private String accType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{PayAccountSaveReqVO.fee.notnull}")
    // TODO 暂时注释掉，可以让AR帐输入负数
//    @Min(value = 1, message = "{fee.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{subCode.notblank}")
    private String subCode;

    @Schema(description = "支付类型值 1为收款，-1为退款", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotBlank(message = "{payValue.notblank}")
    private String payValue;

    @Schema(description = "付款码;付款方式：支付宝码、微信码、预授权码、储值卡号、账套代码(AR账)")
    @Size(max = 32, message = "{payCode.size}")
    private String payCode;

    //----银行卡支付--->>>//
    @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
    private String bankType;

    @Schema(description = "银行卡号")
    @Size(max = 20, message = "{PayAccountSaveReqVO.bankCardNo.size}")
    private String bankCardNo;
    //<<<----银行卡支付---//

    @Schema(description = "有效日期;当支付方式为预授权时有效")
    private LocalDate validDate;

    //----会员卡支付--->>>//
    @Schema(description = "会员代码，如果是储值卡支付，需要传会员代码")
    private String mcode;

    @Schema(description = "手机号，如果是储值卡支付,需要传会员手机号")
    private String phone;

    @Schema(description = "储值卡号")
    private String storeCardNo;

    @Schema(description = "验证类型")
    private Integer verifyMode;

    @Schema(description = "会员卡密码,当付款方式为储值卡时需要")
    private String pwd;

    @Schema(description = "验证码")
    private String smsCode;
    //<<<----会员卡支付---//

    @Schema(description = "业务详情")
    @Size(max = 128, message = "{accDetail.size}")
    private String accDetail;

    @Schema(description = "状态，与消费账务同时产生时，该付款科目状态为已结，前端可不传")
    private String state;

    @Schema(description = "结账号,前端不需要传，后端调用pay接口时使用")
    private String payNo;

    @Schema(description = "备注")
    @Size(max = 128, message = "{remark.size128}")
    private String remark;

    @Schema(description = "货币单位")
    private String currencyUnit;

    @Schema(description = "是否发送短信")
    @InStringEnum(value = BooleanEnum.class, message = "{isSendSms.instringenum}")
    private String isSendSms;

    @Schema(description = "来源")
    private String source;

    @Schema(description = "是否为转出转入的账务;-1: 为转出的账务, 0:默认， 1:为转入账务")
    private String isTurnOutIn;

    @Schema(description = "标签;多个标签用逗号分隔 如：拆账，并账")
    private String tags;

    @Schema(description = "客单信息，前端不传")
    private OrderTogether orderTogether;

    @Data
    public static class OrderTogether {
        @Schema(description = "房间代码")
        @JsonProperty("rCode")
        private String rCode;

        @Schema(description = "房间号")
        @JsonProperty("rNo")
        private String rNo;

        @Schema(description = "宾客姓名")
        private String guestName;

        @Schema(description = "客源类型")
        private String guestSrcType;
    }

}