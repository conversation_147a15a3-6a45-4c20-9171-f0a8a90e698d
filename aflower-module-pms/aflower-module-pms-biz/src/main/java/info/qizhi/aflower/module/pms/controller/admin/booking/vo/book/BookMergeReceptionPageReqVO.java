package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 需要联房的预订单查询请求VO
 * 
 * <AUTHOR>
 */
@Schema(description = "biz - 需要联房的预订单 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookMergeReceptionPageReqVO extends PageParam {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号", example = "BK202401010001")
    private String bookNo;

    @Schema(description = "房型代码", example = "STD")
    private String rtCode;

    @Schema(description = "客人姓名", example = "张三")
    private String guestName;

    @Schema(description = "客人手机号", example = "13800138000")
    private String phone;

    @Schema(description = "预订单状态", example = "no_check_in", allowableValues = {"no_check_in", "be_confirm"})
    private String state;

    @Schema(description = "外部订单号", example = "EXT202401010001")
    private String outOrderNo;

    @Schema(description = "关键词搜索(客人姓名/手机号)", example = "张三")
    private String keyword;

}
