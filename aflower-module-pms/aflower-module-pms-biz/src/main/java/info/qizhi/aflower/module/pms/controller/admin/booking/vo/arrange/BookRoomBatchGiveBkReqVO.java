package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房-批量赠早 Request VO")
@Data
public class BookRoomBatchGiveBkReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号")
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "批次号")
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "预订排房号列表")
    @NotEmpty(message = "{orderNos.notempty}")
    private List<String> orderNos;

    @Schema(description = "赠早数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{breakfastCount.notnull}")
    @Min(value = 0, message = "{breakfastCount.min}")
    @Max(value = 20, message = "{breakfastCount.max}")
    private Integer bkNum;

}