package info.qizhi.aflower.module.pms.controller.admin.invoice;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.*;
import info.qizhi.aflower.module.pms.service.invoice.InvoiceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 开票记录")
@RestController
@RequestMapping("/pms/invoice-log")
@Validated
public class InvoiceLogController {

    @Resource
    private InvoiceLogService invoiceLogService;

    @PostMapping("/create")
    @Operation(summary = "创建开票记录")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:create')")
    public CommonResult<Long> createInvoiceLog(@Valid @RequestBody InvoiceLogSaveReqVO createReqVO) {
        return success(invoiceLogService.createInvoiceLog(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新开票记录")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:create')")
    public CommonResult<Boolean> updateInvoiceLog(@Valid @RequestBody InvoiceLogSaveReqVO updateReqVO) {
        invoiceLogService.updateInvoiceLog(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-state")
    @Operation(summary = "置开票状态为作废")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:create')")
    public CommonResult<Boolean> updateInvoiceLogState(@RequestParam("id") Long id) {
        invoiceLogService.updateInvoiceLogState(id);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除开票记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:delete')")
    public CommonResult<Boolean> deleteInvoiceLog(@RequestParam("id") Long id) {
        invoiceLogService.deleteInvoiceLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得开票记录")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:query')")
    public CommonResult<InvoiceLogRespVO> getInvoiceLog(@RequestParam("id") Long id) {
        InvoiceLogRespVO invoiceLog = invoiceLogService.getInvoiceLog(id);
        return success(BeanUtils.toBean(invoiceLog, InvoiceLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得开票记录分页")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:create')")
    public CommonResult<PageResult<InvoiceLogRespVO>> getInvoiceLogPage(@Valid InvoiceLogPageReqVO pageReqVO) {
        PageResult<InvoiceLogRespVO> pageResult = invoiceLogService.getInvoiceLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InvoiceLogRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得开票记录及总金额")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:create')")
    public CommonResult<InvoiceLogMoneyRespVO> getTotalInvoiceMoney(@Valid InvoiceLogReqVO reqVO) {
        InvoiceLogMoneyRespVO invoiceMoney = invoiceLogService.getTotalInvoiceMoney(reqVO);
        return success(invoiceMoney);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出开票记录 Excel")
    @PreAuthorize("@ss.hasPermission('pms:invoice-log:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportInvoiceLogExcel(@Valid InvoiceLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceLogRespVO> list = invoiceLogService.getInvoiceLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "开票记录.xls", "数据", InvoiceLogRespVO.class,
                        BeanUtils.toBean(list, InvoiceLogRespVO.class));
    }

}