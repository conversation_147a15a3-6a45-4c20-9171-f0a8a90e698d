package info.qizhi.aflower.module.pms.controller.admin.config.vo.groupparam;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.HourRoomRuleConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 集团参数设置-业务逻辑 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GroupParamConfigHourRoomRespVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("参数类型")
    private String paramType;

    @Schema(description = "集团参数设置 钟点房配置", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团参数设置 钟点房配置")
    private HourRoomRuleConfig value;

}