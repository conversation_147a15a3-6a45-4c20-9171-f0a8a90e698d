package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备信息 Request VO")
@Data
public class DeviceDoorLock2ReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    private Long id;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceType;

}