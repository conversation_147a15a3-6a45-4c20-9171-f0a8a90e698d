package info.qizhi.aflower.module.pms.controller.admin.roomtype;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.room.RoomReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.*;
import info.qizhi.aflower.module.pms.dal.dataobject.room.RoomDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.room.RoomService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 房型")
@RestController
@RequestMapping("/pms/room-type")
@Validated
public class RoomTypeController {

    @Resource
    private RoomTypeService roomTypeService;

    @Resource
    private RoomService roomService;

    @PostMapping("/create")
    @Operation(summary = "创建房型")
    @PreAuthorize("@ss.hasPermission('pms:room-type:create, pms:room-type:base:create')")
    public CommonResult<Long> createRoomType(@Valid @RequestBody RoomTypeSaveReqVO createReqVO) {
        return success(roomTypeService.createRoomType(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新房型")
    @PreAuthorize("@ss.hasPermission('pms:room-type:create,pms:room-type:base:create')")
    public CommonResult<Boolean> updateRoomType(@Valid @RequestBody RoomTypeSaveReqVO updateReqVO) {
        roomTypeService.updateRoomType(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新房型状态")
    @PreAuthorize("@ss.hasPermission('pms:room-type:create, pms:room-type:base:create')")
    public CommonResult<Boolean> updateRoomTypeStatus(@Valid @RequestBody RoomTypeUpdateStatusReqVO reqVO) {
        roomTypeService.updateRoomTypeStatus(reqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得房型")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query, pms:room-type:base:update')")
    public CommonResult<RoomTypeRespVO> getRoomType(@Valid RoomTypeReqVO reqVO) {
        RoomTypeRespVO roomType = roomTypeService.getRoomTypeAndBasePrice(reqVO);
        return success(roomType);
    }

    @GetMapping("/list")
    @Operation(summary = "获得房型列表,不含房间数、门市价")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query:list')")
    public CommonResult<List<RoomTypeRespVO>> getRoomTypeList(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeDO> pageResult = roomTypeService.getRoomTypeList(reqVO, false);
        return success(BeanUtils.toBean(pageResult, RoomTypeRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得房型简单列表", description = "只包含房型代码和房型名称,用于前端列表")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query')")
    public CommonResult<List<RoomTypeSimpleRespVO>> getRoomTypeSimpleList(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeDO> list = roomTypeService.getRoomTypeList(reqVO, true);
        return success(BeanUtils.toBean(list, RoomTypeSimpleRespVO.class));
    }

    @GetMapping("/list-virtual")
    @Operation(summary = "获得虚拟房型列表")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query')")
    public CommonResult<List<RoomTypeRespVO>> getVirtualRoomTypeList(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeDO> list = roomTypeService.getVirtualRoomTypeList(reqVO);
        return success(BeanUtils.toBean(list, RoomTypeRespVO.class));
    }

    @GetMapping("/list-physics")
    @Operation(summary = "获得物理房型列表")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query')")
    public CommonResult<List<RoomTypeRespVO>> getPhysicsRoomTypeList(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeDO> list = roomTypeService.getPhysicsRoomTypeList(reqVO);
        return success(BeanUtils.toBean(list, RoomTypeRespVO.class));
    }


    @GetMapping("/list-room-num")
    @Operation(summary = "获得房型列表,含房间数、门市价")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query')")
    public CommonResult<List<RoomTypeRespVO>> getRoomTypeIncludeRoomNum(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeRespVO> list = roomTypeService.getBasePriceRoomTypeList(reqVO, false);
        includeRoomNumber(list, reqVO.getGcode(), reqVO.getHcode());
        return success(list);
    }

    @GetMapping("/list-room-num-include-grt")
    @Operation(summary = "获得房型列表,含房间数、门市价，包含集团房型")
    //@PreAuthorize("@ss.hasPermission('pms:room-type:query:list-room-num-include-grt')")
    public CommonResult<List<RoomTypeRespVO>> getRoomTypeIncludeRoomNumGrt(@Valid RoomTypeReqVO reqVO) {
        List<RoomTypeRespVO> list = roomTypeService.getBasePriceRoomTypeList(reqVO, false);
        includeRoomNumber(list, reqVO.getGcode(), reqVO.getHcode());
        return success(list);
    }


    private void includeRoomNumber(List<RoomTypeRespVO> list, String gcode, String hcode) {
        if(CollUtil.isEmpty(list)) {
            return;
        }
        // 获取门店下所有房间列表
        List<RoomDO> roomDOList = roomService.getRoomList(RoomReqVO.builder().gcode(gcode).hcode(hcode).isEnable(BooleanEnum.TRUE.getValue()).build());
        // 将roomDOList转换为以房型代码为key, value为房间数的map
        Map<String, Long> roomNumMap = roomDOList.stream()
                .collect(Collectors.groupingBy(RoomDO::getRtCode, Collectors.counting()));
        // 循环遍历roomTypeRespVOS, 并将每个房型的房间数加入到roomTypeRespVO中
        list.forEach(roomTypeRespVO -> {
            Long roomNum = roomNumMap.get(roomTypeRespVO.getRtCode());
            roomTypeRespVO.setRoomNum(roomNum == null ? 0L : roomNum);
        });
    }

}