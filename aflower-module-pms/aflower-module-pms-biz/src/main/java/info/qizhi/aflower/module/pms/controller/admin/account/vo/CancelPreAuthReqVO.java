package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 取消预授权 Request VO")
@Data
public class CancelPreAuthReqVO {

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accountNo.notempty}")
    private String accNo;

}