package info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 协议单位、中介分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProtocolAgentPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "所属酒店代码")
    private String belongHcode;

    @Schema(description = "中介/协议单位名称", example = "张三")
    private String paName;

    @Schema(description = "类型;0：协议单位 1：中介", example = "2")
    private String paType;

    @Schema(description = "状态;0：无效 1：有效")
    private String isEnable;

    @Schema(description = "0:单店使用 1：集团共享；0时挂账的酒店范围就是单店")
    private String isShare;

}