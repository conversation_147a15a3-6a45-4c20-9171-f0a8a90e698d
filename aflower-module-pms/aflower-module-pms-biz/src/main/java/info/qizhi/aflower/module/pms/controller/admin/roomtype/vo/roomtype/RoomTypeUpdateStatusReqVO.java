package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


@Schema(description = "管理后台 - 更新房型状态 Request VO")
@Data
public class RoomTypeUpdateStatusReqVO {

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "是否有效;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{enableStatus.notnull}")
    @InStringEnum(value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;


}