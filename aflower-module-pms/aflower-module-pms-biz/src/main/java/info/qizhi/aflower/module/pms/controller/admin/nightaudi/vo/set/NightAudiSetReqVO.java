package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 夜审设置 Request VO")
@Data
public class NightAudiSetReqVO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "自动审核;0：手动夜审 1：自动夜审")
    private String auto;

    @Schema(description = "每日自动夜审开始时间", example = "04: 00")
    private LocalTime autoStartTime;

    @Schema(description = "每日自动夜审结束时间", example = "05: 00")
    private LocalTime autoEndTime;

}