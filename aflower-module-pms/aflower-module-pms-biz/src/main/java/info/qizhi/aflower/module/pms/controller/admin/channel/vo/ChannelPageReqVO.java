package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 渠道分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChannelPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "渠道名称", example = "芋艿")
    private String channelName;

    @Schema(description = "是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "渠道类型;0:直营 1：分销")
    private String channelType;

}