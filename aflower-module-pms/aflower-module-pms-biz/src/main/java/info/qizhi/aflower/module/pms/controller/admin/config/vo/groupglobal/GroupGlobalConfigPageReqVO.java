package info.qizhi.aflower.module.pms.controller.admin.config.vo.groupglobal;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 集团全局配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class GroupGlobalConfigPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "集团入账商户号")
    private String accountNo;

    @Schema(description = "ota预付订单结算方式-直营;1：入账到集团账户 0：门店账户")
    private String otaDirectMgmt;

    @Schema(description = "ota预付订单结算方式-加盟;1：入账到集团账户 0：门店账户")
    private String otaJoin;

    @Schema(description = "ota预付订单结算方式-托管;1：入账到集团账户 0：门店账户")
    private String otaAuth;

    @Schema(description = "会员购卡成本;json格式，对应每个会员类型，物理卡的成本价格")
    private String cardCost;

    @Schema(description = "是否收取中央预订佣金;0:否 1：是")
    private String crsBrokerage;

    @Schema(description = "收取佣金方式;0: 按单笔订单金额 1: 按营收收取")
    private String brokerageMode;

    @Schema(description = "收取佣金比例;比如：0.12表示12%的佣金收取")
    private BigDecimal brokerageRatio;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}