package info.qizhi.aflower.module.pms.controller.admin.protocolagent;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.protocolagent.ProtocolAgentDO;
import info.qizhi.aflower.module.pms.service.protocolagent.ProtocolAgentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 协议单位、中介")
@RestController
@RequestMapping("/pms/protocol-agent")
@Validated
@Lazy
public class ProtocolAgentController {

    @Resource
    @Lazy
    private ProtocolAgentService protocolAgentService;

    @PostMapping("/create")
    @Operation(summary = "创建协议单位、中介")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:create')")
    public CommonResult<Long> createProtocolAgent(@Valid @RequestBody ProtocolAgentSaveReqVO createReqVO) {
        return success(protocolAgentService.createProtocolAgent(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新协议单位、中介")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:create')")
    public CommonResult<Boolean> updateProtocolAgent(@Valid @RequestBody ProtocolAgentSaveReqVO updateReqVO) {
        protocolAgentService.updateProtocolAgent(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新协议单位、中介状态")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:create')")
    public CommonResult<Boolean> updateProtocolAgentStatus(@Valid @RequestBody ProtocolAgentUpdateStatusReqVO reqVO) {
        protocolAgentService.updateProtocolAgentStatus(reqVO.getPaCode(), reqVO.getIsEnable(),reqVO.getGcode());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得协议单位、中介")
    @Parameter(name = "paCode", description = "协议单位、中介代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:query:page')")
    public CommonResult<ProtocolAgentRespVO> getProtocolAgent(@RequestParam("paCode") String paCode) {
        ProtocolAgentRespVO protocolAgentRespVO = protocolAgentService.protocolAgentRespVO(paCode);
        return success(protocolAgentRespVO);
    }

    @GetMapping("/page")
    @Operation(summary = "获得协议单位、中介分页")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:query:page')")
    public CommonResult<PageResult<ProtocolAgentRespVO>> getProtocolAgentPage(@Valid ProtocolAgentPageReqVO pageReqVO) {
        PageResult<ProtocolAgentRespVO> pageResult = protocolAgentService.getProtocolAgentPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "获得协议单位、中介列表")
    //@PreAuthorize("@ss.hasPermission('pms:protocol-agent:query')")
    public CommonResult<List<ProtocolAgentRespVO>> getProtocolAgentList(@Valid ProtocolAgentReqVO reqVO) {
        List<ProtocolAgentDO> list = protocolAgentService.getProtocolAgentList(reqVO);
        return success(BeanUtils.toBean(list, ProtocolAgentRespVO.class));
    }

    @GetMapping("/list-simple")
    @Operation(summary = "获得协议单位、中介列表(只包括代码,名称和渠道代码)用于下拉列表及绑定渠道")
    //@PreAuthorize("@ss.hasPermission('pms:protocol-agent:query')")
    public CommonResult<List<ProtocolAgentSimpleRespVO>> getProtocolAgentListSimple(@Valid ProtocolAgentReqVO reqVO) {
        List<ProtocolAgentDO> list = protocolAgentService.getProtocolAgentList(reqVO);
        return success(BeanUtils.toBean(list, ProtocolAgentSimpleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出协议单位、中介 Excel")
    @PreAuthorize("@ss.hasPermission('pms:protocol-agent:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportProtocolAgentExcel(@Valid ProtocolAgentPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProtocolAgentRespVO> list = protocolAgentService.getProtocolAgentPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "协议单位、中介.xls", "数据", ProtocolAgentRespVO.class,
                list);
    }


}