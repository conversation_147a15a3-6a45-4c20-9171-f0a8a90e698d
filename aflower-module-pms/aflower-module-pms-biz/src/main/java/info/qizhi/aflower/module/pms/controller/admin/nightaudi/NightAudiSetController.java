package info.qizhi.aflower.module.pms.controller.admin.nightaudi;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set.NightAudiSetRespVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set.NightAudiSetSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.nightaudi.NightAudiSetDO;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 夜审设置")
@RestController
@RequestMapping("/pms/night-audi-set")
@Validated
public class NightAudiSetController {

    @Resource
    private NightAudiSetService nightAudiSetService;

    @PutMapping("/update")
    @Operation(summary = "更新夜审设置")
    @PreAuthorize("@ss.hasPermission('pms:night-audi-set:update, pms:general-config:create')")
    public CommonResult<Boolean> updateNightAudiSet(@Valid @RequestBody NightAudiSetSaveReqVO updateReqVO) {
        nightAudiSetService.updateNightAudiSet(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得夜审设置")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:night-audi-set:query, pms:general-config:query, pms:hotel-param-config:query:get:deposit')")
    public CommonResult<NightAudiSetRespVO> getNightAudiSet(@RequestParam("gcode") String gcode) {
        NightAudiSetDO nightAudiSet = nightAudiSetService.getNightAudiSet(gcode);
        return success(BeanUtils.toBean(nightAudiSet, NightAudiSetRespVO.class));
    }


}