package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.HourRoomEnum;
import info.qizhi.aflower.framework.common.enums.OrderSrcEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 散客预订 Request VO")
@Data
@ToString(callSuper = true)
public class RightReqVO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "入住类型")
    private String checkinType;

    @Schema(description = "时租房代码")
    @InStringEnum(value = HourRoomEnum.class, message = "{hourCode.instringenum}")
    private String hourCode;

    @Schema(description = "客源类型")
    @InStringEnum(value = GuestSrcTypeEnum.class, message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "会员类型代码,当客源为会员时，需要知道该会员的会员类型代码")
    private String mtCode;

    @Schema(description = "订单来源")
    @InStringEnum(value = OrderSrcEnum.class, message = "{orderSource.instringenum}")
    private String orderSource;

    @Schema(description = "计划入住时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate planCheckinTime;

    @Schema(description = "计划离店时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate planCheckoutTime;

}
