package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 交易记录报表 Response VO")
@Data
public class TransactionDetailReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "日期类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotEmpty(message = "日期类型不能为空")
    private String timeType;

    @Schema(description = "关键字查询条件；包含字段订单号，姓名,房号,交易单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String keyWords;

    @Schema(description = "关键字查询条件；包含字段订单号，姓名,房号,交易单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "营业日")
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String startDate;

    @Schema(description = "结束日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String endDate;

}
