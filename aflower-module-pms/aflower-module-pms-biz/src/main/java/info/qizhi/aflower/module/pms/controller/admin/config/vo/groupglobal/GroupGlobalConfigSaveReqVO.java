package info.qizhi.aflower.module.pms.controller.admin.config.vo.groupglobal;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GroupGlobalConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

@Schema(description = "管理后台 - 集团全局配置新增/修改 Request VO")
@Data
public class GroupGlobalConfigSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20344")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "集团入账商户号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accountNo.notempty}")
    private String accountNo;

    @Schema(description = "ota预付订单结算方式-直营;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{otaDirectMgmt.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{otaDirectMgmt.instringenum}")
    private String otaDirectMgmt;

    @Schema(description = "ota预付订单结算方式-加盟;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{otaJoin.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{otaJoin.instringenum}")
    private String otaJoin;

    @Schema(description = "ota预付订单结算方式-托管;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{otaAuth.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{otaAuth.instringenum}")
    private String otaAuth;

    @Schema(description = "会员购卡成本,对应每个会员类型，物理卡的成本价格")
    private List<GroupGlobalConfigDO.CardCost> cardCosts;

    @Schema(description = "是否收取中央预订佣金;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{crsBrokerage.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{crsBrokerage.instringenum}")
    private String crsBrokerage;

    @Schema(description = "收取佣金方式;0: 按单笔订单金额 1: 按营收收取", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{brokerageMode.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{brokerageMode.instringenum}")
    private String brokerageMode;

    @Schema(description = "收取佣金比例;比如：0.12表示12%的佣金收取", requiredMode = Schema.RequiredMode.REQUIRED, example = "0.12")
    @NotNull(message = "{brokerageRatio.notnull}")
    @DecimalMin(value = "0.01", message = "{brokerageRatio.min}")
    private BigDecimal brokerageRatio;

}