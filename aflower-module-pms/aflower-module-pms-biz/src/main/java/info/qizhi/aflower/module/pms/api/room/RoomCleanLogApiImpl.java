package info.qizhi.aflower.module.pms.api.room;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.room.dto.RoomCleanLogSaveReqDTO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.cleanlog.RoomCleanLogSaveReqVO;
import info.qizhi.aflower.module.pms.service.room.RoomCleanLogService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Lazy
public class RoomCleanLogApiImpl implements RoomCleanLogApi{
    @Resource
    private RoomCleanLogService roomCleanLogService;

    @Override
    public CommonResult<Boolean> createRoomCleanLogs(List<RoomCleanLogSaveReqDTO> createReqVOs) {
        roomCleanLogService.createRoomCleanLogs(BeanUtils.toBean(createReqVOs, RoomCleanLogSaveReqVO.class));
        return success(true);
    }
}
