package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 预订排房每日早餐房价新增/修改 Request VO")
@Data
public class BookRoomBkPriceSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNumber.notempty}")
    private String orderNo;

    @Schema(description = "优惠价;价格单位为分", requiredMode = Schema.RequiredMode.REQUIRED, example = "9955")
    @NotNull(message = "优惠价;价格单位为分不能为空")
    @Min(value = 0, message = "优惠价;价格单位为分必须大于等于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long vipPrice;

    @Schema(description = "预订价格(原价);价格单位为分", requiredMode = Schema.RequiredMode.REQUIRED, example = "13760")
    @NotNull(message = "预订价格(原价);价格单位为分不能为空")
    @Min(value = 0, message = "预订价格(原价);价格单位为分必须大于等于0")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long price;

    @Schema(description = "价格类型;0：放盘价 1：手工价", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "价格类型;0：放盘价 1：手工价不能为空")
    private String priceType;

    @Schema(description = "赠早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{breakfastCount.notnull}")
    @Min(value = 0, message = "赠早餐份数必须大于等于0")
    private Integer bkNum;

    @Schema(description = "房包早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "房包早餐份数不能为空")
    @Min(value = 0, message = "房包早餐份数必须大于等于0")
    private Integer roomBkNum;

    @Schema(description = "房价策略代码;房价策略代码")
    private String priceStrategyCode;

    @Schema(description = "价格日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{priceDate.notnull}")
    private LocalDate priceDate;

    @Schema(description = "星期几", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer week;

}