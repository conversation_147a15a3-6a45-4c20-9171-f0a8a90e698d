package info.qizhi.aflower.module.pms.controller.admin.booking.vo.team;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 团队客人分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TeamGuestPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "预订单号")
    private String bookNo;

    @Schema(description = "关联book_room_type表book_rt_no;关联book_room_type表book_rt_no")
    private String bookRtNo;

    @Schema(description = "房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名", example = "张三")
    private String guestName;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "性别;0女 1男 2保密")
    private String sex;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "证件类型", example = "1")
    private String idType;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "地址")
    private String addr;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}