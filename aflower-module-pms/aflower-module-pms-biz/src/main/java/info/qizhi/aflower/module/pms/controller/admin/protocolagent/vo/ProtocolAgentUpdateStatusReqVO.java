package info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


@Schema(description = "管理后台 - 协议单位、中介新增/修改 Request VO")
@Data
public class ProtocolAgentUpdateStatusReqVO {

    @Schema(description = "paCode", requiredMode = Schema.RequiredMode.REQUIRED, example = "4871")
    @NotEmpty(message = "paCode不能为空")
    private String paCode;

    @Schema(description = "是否有效")
    @NotEmpty(message = "{isEnable.notempty}")
    @InStringEnum(value= BooleanEnum.class, message = "是否有效只能是0或1")
    private String isEnable;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "4871")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

}