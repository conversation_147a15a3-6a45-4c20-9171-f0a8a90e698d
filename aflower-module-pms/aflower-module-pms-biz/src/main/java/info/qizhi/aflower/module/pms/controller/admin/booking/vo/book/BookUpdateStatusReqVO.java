package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.enums.PlatformEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单(普通预订团队预订)取消 Request VO")
@Data
public class BookUpdateStatusReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "平台")
    @InStringEnum(value = PlatformEnum.class, message = "{platform.instringenum}")
    private String platform;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{status.notempty}")
    @InStringEnum(value = OrderStateEnum.class, message = "{state.invalid}")
    private String state;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "结算罚金")
    @Min(value = 0L, message = "{roomFee.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long penalty;

    @Schema(description = "货币单位")
    private String currencyUnit;


}