package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/22 14:43
 */
@Data
public class InvoiceConfigSaveReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "电子税务局登录账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "电子税务局登录账号不能为空")
    private String taxLoginNo;

    @Schema(description = "销方名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "销方名称不能为空")
    private String sellerName;

    @Schema(description = "销方纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "销方纳税人识别号不能为空")
    private String sellerTaxpayerId;

    @Schema(description = "销方地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellerAddress;

    @Schema(description = "销方电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellerPhone;

    @Schema(description = "销方开户银行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellerBankName;

    @Schema(description = "销方开户账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellerBankNo;
}
