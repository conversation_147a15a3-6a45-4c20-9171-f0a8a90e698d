package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.HotelMessageTypeEnum;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.HotelMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 叫醒服务消息生产者
 */

@Slf4j
@Component
public class WalkUpServiceProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送叫醒服务消息
     *
     */
    public void sendWalkPuServiceMessage(String hcode,String msg) {
        HotelMessage message = new HotelMessage().setHcode(hcode)
                .setType(HotelMessageTypeEnum.WAKEUP.getCode())
                .setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HOTEL_MESSAGE_QUEUE, message);
    }

} 
