package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 计费规则新增/修改 Request VO")
@Data
public class PriceChargeRuleSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "规则类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "规则类型不能为空")
    private String ruleType;

    @Schema(description = "小时房代码;当规则类型为时租房时才有值")
    private String hourCode;

    @Schema(description = "房型代码")
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtCode;

    @Schema(description = "起步价", requiredMode = Schema.RequiredMode.REQUIRED, example = "13468")
    private Long startPrice;

    @Schema(description = "散客入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "18558")
    private Long walkInAddPrice;

    @Schema(description = "会员入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "28693")
    private Long memberAddPrice;

    @Schema(description = "中介入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "2233")
    private Long agentAddPrice;

    @Schema(description = "协议公司入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "13767")
    private Long protocolAddPrice;

}