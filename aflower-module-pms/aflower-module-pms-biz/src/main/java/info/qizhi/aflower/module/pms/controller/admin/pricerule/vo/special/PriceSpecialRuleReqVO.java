package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 特殊房(白天房午夜房)计费规则分页 Request VO")
@Data
@ToString(callSuper = true)
public class PriceSpecialRuleReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;
}