package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeMinuteDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 添加房间 Request VO")
@Data
public class BookRoomAddReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "渠道代码")
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "预订天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 1, message = "{bookingDays.min}")
    private Integer days;

    @Schema(description = "预低时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckinTime.notnull}")
    @JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckoutTime.notnull}")
    @JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "房型列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypes.notempty}")
    private List<RoomType> roomTypes;

    @Data
    public static class RoomType {

        @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{roomTypeCode.notempty}")
        private String rtCode;

        @Schema(description = "房型预订数", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{roomNum.notnull}")
        @Min(value = 1, message = "{roomNum.min}")
        private Integer roomNum;

        @Schema(description = "是否会议室;0:客房 1:会议室")
        private String isMeetingRoom;

        @Schema(description = "房间列表", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<BookRoom> bookRooms;

        @Schema(description = "日期价格列表", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{dayPrices.notempty}")
        private List<DayPrice> dayPrices;

        @Data
        public static class BookRoom {
            @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotEmpty(message = "{rCode.notempty}")
            @JsonProperty(value = "rCode")
            private String rCode;

            @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotEmpty(message = "{rNo.notempty}")
            @JsonProperty(value = "rNo")
            private String rNo;
        }

        @Data
        public static class DayPrice {

            @Schema(description = "日期", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotNull(message = "{date.notempty}")
            private LocalDate priceDate;

            @Schema(description = "周几", requiredMode = Schema.RequiredMode.REQUIRED)
            private Integer week;

            @Schema(description = "赠早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
            @Min(value = 0, message = "{breakfastCount.min}")
            private Integer bkNum;

            @Schema(description = "房包早餐数")
            private Integer roomBkNum;

            @Schema(description = "预订价格,单位元", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotEmpty(message = "{bookingPrice.notnull}")
            @JsonDeserialize(using = YuanToFenDeserializer.class)
            private Long price;

            @Schema(description = "优惠价,单位元", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotEmpty(message = "{discountPrice.notnull}")
            @JsonDeserialize(using = YuanToFenDeserializer.class)
            private Long vipPrice;

            @Schema(description = "价格类型;0：放盘价 1：手工价", requiredMode = Schema.RequiredMode.REQUIRED)
            @InStringEnum(value = OrderStateEnum.class, message = "{priceType.invalid}")
            @NotEmpty(message = "{priceType.notempty}")
            private String priceType;

            @Schema(description = "价格策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
            private String priceStrategyCode;
        }
    }


}