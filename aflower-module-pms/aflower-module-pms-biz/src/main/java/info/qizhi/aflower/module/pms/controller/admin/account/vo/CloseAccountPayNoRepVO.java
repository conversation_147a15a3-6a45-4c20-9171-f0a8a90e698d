package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 撤销结账 Request VO")
@Data
public class CloseAccountPayNoRepVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "结账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{payNo.notempty}")
    private String payNo;

}