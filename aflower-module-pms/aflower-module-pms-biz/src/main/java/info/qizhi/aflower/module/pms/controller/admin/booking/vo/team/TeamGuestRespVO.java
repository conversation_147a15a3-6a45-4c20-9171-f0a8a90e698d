package info.qizhi.aflower.module.pms.controller.admin.booking.vo.team;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 团队客人 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TeamGuestRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22623")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订单号")
    private String bookNo;

    @Schema(description = "关联book_room_type表book_rt_no;关联book_room_type表book_rt_no")
    @ExcelProperty("关联book_room_type表book_rt_no;关联book_room_type表book_rt_no")
    private String bookRtNo;

    @Schema(description = "房间代码")
    @ExcelProperty("房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @ExcelProperty("房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名", example = "张三")
    @ExcelProperty("客人姓名")
    private String guestName;

    @Schema(description = "拼音")
    @ExcelProperty("拼音")
    private String pinyin;

    @Schema(description = "性别;0女 1男 2保密")
    @ExcelProperty("性别;0女 1男 2保密")
    private String sex;

    @Schema(description = "手机号码")
    @ExcelProperty("手机号码")
    private String phone;

    @Schema(description = "证件类型", example = "1")
    @ExcelProperty("证件类型")
    private String idType;

    @Schema(description = "证件号码")
    @ExcelProperty("证件号码")
    private String idNo;

    @Schema(description = "地址")
    @ExcelProperty("地址")
    private String addr;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}