package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import info.qizhi.aflower.framework.common.validation.Telephone;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 开票信息新增/修改 Request VO")
@Data
public class InvoiceSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11969")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "发票代码")
    @Size(max = 30, message = "发票代码长度不能超过30")
    private String invoiceCode;

    @Schema(description = "发票类型;0:公司专票 1:公司普票 2:个人普票", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{invoiceTitleType.notempty}")
    private String invoiceTitleType;

    @Schema(description = "发票抬头", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "3344")
    private String taxpayerId;

    @Schema(description = "开户银行")
    private String bank;

    @Schema(description = "开户账号")
    private String bankNo;

    @Schema(description = "企业注册地址")
    private String regAddress;

    @Schema(description = "企业注册电话")
    @Telephone(message = "{regPhone.invalid}")
    private String regPhone;

    @Schema(description = "email")
    @Email(message = "{email.invalid}")
    private String email;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}