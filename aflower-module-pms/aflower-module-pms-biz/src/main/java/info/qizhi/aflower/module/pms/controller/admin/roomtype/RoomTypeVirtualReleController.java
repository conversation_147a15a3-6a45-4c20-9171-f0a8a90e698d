package info.qizhi.aflower.module.pms.controller.admin.roomtype;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele.RoomTypeVirtualReleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele.RoomTypeVirtualReleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele.RoomTypeVirtualReleSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele.RoomTypeVirtualReleUpdateStatusReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeVirtualReleDO;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeVirtualReleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 物理房型与虚拟房型关联")
@RestController
@RequestMapping("/pms/room-type-virtual-rele")
@Validated
public class RoomTypeVirtualReleController {

    @Resource
    private RoomTypeVirtualReleService roomTypeVirtualReleService;

    @PostMapping("/create")
    @Operation(summary = "创建物理房型与虚拟房型关联")
    @PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:create')")
    public CommonResult<Long> createRoomTypeVirtualRele(@Valid @RequestBody RoomTypeVirtualReleSaveReqVO createReqVO) {
        return success(roomTypeVirtualReleService.createRoomTypeVirtualRele(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新物理房型与虚拟房型关联")
    @PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:create')")
    public CommonResult<Boolean> updateRoomTypeVirtualRele(@Valid @RequestBody RoomTypeVirtualReleSaveReqVO updateReqVO) {
        roomTypeVirtualReleService.updateRoomTypeVirtualRele(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新物理房型与虚拟房型关联状态")
    @PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:create')")
    public CommonResult<Boolean> updateRoomTypeVirtualReleStatus(@Valid @RequestBody RoomTypeVirtualReleUpdateStatusReqVO reqVO) {
        roomTypeVirtualReleService.updateRoomTypeVirtualReleStatus(reqVO.getId(), reqVO.getIsEnable());
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除物理房型与虚拟房型关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:create')")
    public CommonResult<Boolean> deleteRoomTypeVirtualRele(@RequestParam("id") Long id) {
        roomTypeVirtualReleService.deleteRoomTypeVirtualRele(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得物理房型与虚拟房型关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:query')")
    public CommonResult<RoomTypeVirtualReleRespVO> getRoomTypeVirtualRele(@RequestParam("id") Long id) {
        RoomTypeVirtualReleDO roomTypeVirtualRele = roomTypeVirtualReleService.getRoomTypeVirtualRele(id);
        return success(BeanUtils.toBean(roomTypeVirtualRele, RoomTypeVirtualReleRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得物理房型与虚拟房型关联列表")
    //@PreAuthorize("@ss.hasPermission('pms:room-type-virtual-rele:query')")
    public CommonResult<List<RoomTypeVirtualReleRespVO>> getRoomTypeVirtualReleList(@Valid RoomTypeVirtualReleReqVO reqVO) {
        List<RoomTypeVirtualReleRespVO> list = roomTypeVirtualReleService.getRoomTypeVirtualReleList(reqVO);
        return success(list);
    }


}