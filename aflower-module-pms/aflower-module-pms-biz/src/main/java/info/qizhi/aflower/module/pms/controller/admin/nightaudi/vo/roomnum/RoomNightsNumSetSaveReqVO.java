package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 夜审房间数设置新增/修改 Request VO")
@Data
public class RoomNightsNumSetSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19936")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{checkinType.notempty}")
    private String checkinType;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightNum.notnull}")
    private BigDecimal nightNum;

}