package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 付款/消费明细报表 Response VO")
@Data
public class SubjectSummaryReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "报表操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "科目类型;0: 消费科目 1：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String subType;

    @Schema(description = "营业日")
    @NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

}
