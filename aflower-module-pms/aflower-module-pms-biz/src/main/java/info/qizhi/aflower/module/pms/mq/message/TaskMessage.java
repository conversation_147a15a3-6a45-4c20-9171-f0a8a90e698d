package info.qizhi.aflower.module.pms.mq.message;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审消息
 * @Version: 1.0
 */
@Data
public class TaskMessage implements Serializable {

    public static final String QUEUE = QueueConstants.ROOM_CLEAN_TASK_MESSAGE_QUEUE;

    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @NotEmpty(message = "酒店代码不能为空")
    private String hcode;

    @Schema(description = "房型代码集合")
    private List<String> rCodes;

    @NotEmpty(message = "类型")
    private String type;


}
