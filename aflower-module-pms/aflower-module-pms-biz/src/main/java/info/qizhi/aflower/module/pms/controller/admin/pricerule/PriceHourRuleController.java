package info.qizhi.aflower.module.pms.controller.admin.pricerule;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour.PriceHourRuleSaveReqVO;
import info.qizhi.aflower.module.pms.service.pricerule.PriceHourRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 时租房计费规则")
@RestController
@RequestMapping("/pms/price-hour-rule")
@Validated
public class PriceHourRuleController {

    @Resource
    private PriceHourRuleService priceHourRuleService;

    @PutMapping("/update")
    @Operation(summary = "更新时租房计费规则")
    @PreAuthorize("@ss.hasPermission('pms:price-hour-rule:update')")
    public CommonResult<Boolean> updatePriceHourRule(@Valid @RequestBody PriceHourRuleSaveReqVO updateReqVO) {
        priceHourRuleService.updatePriceHourRule(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得时租房计费规则")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:price-rule:query')")
    public CommonResult<PriceHourRuleRespVO> getPriceHourRule(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        PriceHourRuleRespVO priceHourRule = priceHourRuleService.getPriceHourRule(new PriceHourRuleReqVO().setGcode(gcode).setHcode(hcode), true);
        return success(priceHourRule);
    }

}