package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.hour;

import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 时租房计费规则 Request VO")
@Data
@ToString(callSuper = true)
public class PriceHourRuleReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;
}