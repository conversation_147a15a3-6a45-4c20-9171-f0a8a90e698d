package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.module.pms.mq.message.NightAudiMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审生产者
 * @Version: 1.0
 */
@Slf4j
@Component
public class NightAudiProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     */
    public void sendNightAudiMessage(String gcode, String hcode) {
        NightAudiMessage message = new NightAudiMessage().setGcode(gcode).setHcode(hcode);
        rabbitTemplate.convertAndSend(NightAudiMessage.QUEUE, message);
    }
}
