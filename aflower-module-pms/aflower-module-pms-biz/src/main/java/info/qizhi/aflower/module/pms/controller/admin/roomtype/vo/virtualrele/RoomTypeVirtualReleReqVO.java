package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 物理房型与虚拟房型关联 Request VO")
@Data
@ToString(callSuper = true)
public class RoomTypeVirtualReleReqVO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "是否有效")
    private String isEnable;


}