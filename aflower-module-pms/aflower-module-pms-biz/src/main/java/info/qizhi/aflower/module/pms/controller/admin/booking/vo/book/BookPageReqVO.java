package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.enums.CheckInTypeEnum;
import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 预订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookPageReqVO extends PageParam {

    @Schema(description = "门店代码")
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "集团代码")
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "客源类型", example = "2")
    @InStringEnum(value = GuestSrcTypeEnum.class, message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型只能是会议团、旅行团、自用、免费、时租房、长包", example = "2")
    @InStringEnum(value = CheckInTypeEnum.class, message = "{checkinType.instringenum}")
    private String checkinType;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "协议单位、中介代码", example = "张三")
    private String guestCode;

    @Schema(description = "关键字查询条件；包含字段订单号，外部订单号，姓名，手机号")
    private String keyWords;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "状态")
    @InStringEnum(value = OrderStateEnum.class, message = "{orderState.instringenum}")
    private String state;

    @Schema(description = "时间类型； 0：预离时间，1：创建时间，2：预抵时间")
    private String timeType;

    @Schema(description = "开始时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private String startTime;

    @Schema(description = "结束时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private String endTime;

    @Schema(description = "创建者")
    private String creator;

    @Schema(description = "排序字段")
    private String sort;

    @Schema(description = "排序方式，asc升序，desc降序")
    private String order;

    @Schema(description = "关联预订单")
    private List<String> bookNo;
}
