package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.BookingOrderMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class BookingOrderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    public void sendBookingOrderMessage(String gcode, String hcode, List<String> bookNo, List<String> orderNo, String actionType, String togetherType) {
        BookingOrderMessage message = new BookingOrderMessage();
        message.setGcode(gcode);
        message.setHcode(hcode);
        message.setBookNo(bookNo);
        message.setOrderNo(orderNo);
        message.setActionType(actionType);
        message.setToogetherType(togetherType);
        rabbitTemplate.convertAndSend(QueueConstants.BOOKING_ORDER_MESSAGE_QUEUE, message);
    }
}
