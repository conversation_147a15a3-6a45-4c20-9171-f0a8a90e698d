package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.module.pms.mq.message.NightAudiReportMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审生产者
 * @Version: 1.0
 */
@Slf4j
@Component
public class NightAudiReportProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param gcode   集团代码
     * @param hcode   门店代码
     * @param bizDate 营业日期
     */
    public void sendNightAudiReportMessage(String gcode, String hcode, LocalDate bizDate, LocalDateTime lastBizDateTime) {
        NightAudiReportMessage message = new NightAudiReportMessage().setGcode(gcode)
                                                                     .setHcode(hcode)
                                                                     .setLastBizDateTime(lastBizDateTime)
                                                                     .setBizDate(bizDate);
        rabbitTemplate.convertAndSend(NightAudiReportMessage.QUEUE, message);
    }
}
