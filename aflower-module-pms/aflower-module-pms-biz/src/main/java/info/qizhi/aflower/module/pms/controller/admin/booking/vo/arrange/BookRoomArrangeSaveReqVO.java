package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房新增 Request VO")
@Data
public class BookRoomArrangeSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号")
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "批次号")
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "排房列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    @NotEmpty(message = "{rooms.notempty}")
    private List<Room> rooms;

    @Data
    public static class Room {
        @Schema(description = "订单号,针对单个预订进行排房需要传", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{orderNumber.notempty}")
        private String orderNo;

        @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "rNo")
        private String rNo;

    }
}