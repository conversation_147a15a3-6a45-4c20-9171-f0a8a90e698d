package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店OTA关联新增/修改 Request VO")
@Data
public class MerchantChannelReleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4861")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "渠道门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{channelHcode.notempty}")
    private String channelHcode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "状态;0：无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{status.notempty}")
    private String state;

}