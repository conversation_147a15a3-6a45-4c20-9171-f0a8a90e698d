package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房-批量改价 Request VO")
@Data
public class BookRoomBatchUpdatePriceReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号")
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "批次号")
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "房型代码")
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtCode;

    @Schema(description = "每日房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{dayPrices.notempty}")
    private List<DayPrice> dayPrices;

    @Data
    public static class DayPrice {
        @Schema(description = "自定义房价", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{customRoomRate.notnull}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long vipPrice;

        @Schema(description = "价格日期", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{priceDate.notnull}")
        private LocalDate priceDate;
    }

}