package info.qizhi.aflower.module.pms.controller.admin.account.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 统计 Request VO")
@Data
public class AccountStatByTogetherCodesReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "宾客代码" )
    private List<String> togetherCodes;

    @Schema(description = "账单号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> accNoList;

}
