package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.module.pms.controller.admin.config.vo.groupglobal.GroupGlobalConfigRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.groupglobal.GroupGlobalConfigSaveReqVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import info.qizhi.aflower.framework.common.pojo.CommonResult;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.module.pms.service.config.GroupGlobalConfigService;

@Tag(name = "管理后台 - 集团全局配置")
@RestController
@RequestMapping("/pms/group-global-config")
@Validated
public class GroupGlobalConfigController {

    @Resource
    private GroupGlobalConfigService groupGlobalConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建集团全局配置")
    @PreAuthorize("@ss.hasPermission('pms:group-global-config:create')")
    public CommonResult<Long> createGroupGlobalConfig(@Valid @RequestBody GroupGlobalConfigSaveReqVO createReqVO) {
        return success(groupGlobalConfigService.createGroupGlobalConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新集团全局配置")
    @PreAuthorize("@ss.hasPermission('pms:group-global-config:update')")
    public CommonResult<Boolean> updateGroupGlobalConfig(@Valid @RequestBody GroupGlobalConfigSaveReqVO updateReqVO) {
        groupGlobalConfigService.updateGroupGlobalConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得集团全局配置")
    @Parameter(name = "gcode", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:group-global-config:query')")
    public CommonResult<GroupGlobalConfigRespVO> getGroupGlobalConfig(@RequestParam("gcode") String gcode) {
        GroupGlobalConfigRespVO groupGlobalConfig = groupGlobalConfigService.getGroupGlobalConfigRespVO(gcode);
        return success(groupGlobalConfig);
    }

}