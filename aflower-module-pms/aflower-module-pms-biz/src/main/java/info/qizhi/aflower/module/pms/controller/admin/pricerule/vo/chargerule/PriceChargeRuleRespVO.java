package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 计费规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PriceChargeRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28504")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "规则类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("规则类型")
    private String ruleType;

    @Schema(description = "小时房代码;当规则类型为时租房时才有值")
    @ExcelProperty("小时房代码;当规则类型为时租房时才有值")
    private String hourCode;

    @Schema(description = "房型代码")
    @ExcelProperty("房型代码")
    private String rtCode;

    @Schema(description = "起步价", requiredMode = Schema.RequiredMode.REQUIRED, example = "13468")
    @ExcelProperty("起步价")
    private Long startPrice;

    @Schema(description = "散客入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "18558")
    @ExcelProperty("散客入住时加收金额")
    private Long walkInAddPrice;

    @Schema(description = "会员入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "28693")
    @ExcelProperty("会员入住时加收金额")
    private Long memberAddPrice;

    @Schema(description = "中介入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "2233")
    @ExcelProperty("中介入住时加收金额")
    private Long agentAddPrice;

    @Schema(description = "协议公司入住时加收金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "13767")
    @ExcelProperty("协议公司入住时加收金额")
    private Long protocolAddPrice;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}