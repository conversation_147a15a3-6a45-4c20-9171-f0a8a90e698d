package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.HotelMessageTypeEnum;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.HotelMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * OTA订单提醒 消息生产者
 */

@Slf4j
@Component
public class OTAOrderAlertProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * OTA订单提醒服务消息
     *
     */
    public void sendOTAOrderAlertMessage(String hcode, String msg) {
        HotelMessage message = new HotelMessage().setHcode(hcode)
                .setType(HotelMessageTypeEnum.OTA_ORDER.getCode())
                .setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HOTEL_MESSAGE_QUEUE, message);
    }

    /**
     * OTA订单取消提醒服务消息
     * @param hcode
     * @param msg
     */
    public void sendOTAOrderCancelAlertMessage(String hcode, String msg) {
        HotelMessage message = new HotelMessage().setHcode(hcode)
                .setType(HotelMessageTypeEnum.OTA_ORDER_CANCEL.getCode())
                .setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HOTEL_MESSAGE_QUEUE, message);
    }

} 
