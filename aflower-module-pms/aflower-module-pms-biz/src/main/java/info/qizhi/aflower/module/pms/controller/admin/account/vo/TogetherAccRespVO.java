package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 在住宾客账号 Response VO")
@Data
public class TogetherAccRespVO {

    @Schema(description = "账号", example = "1")
    private String no;

    @Schema(description = "宾客代码")
    private String togetherCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "账户姓名", example = "李四")
    private String name;

    @Schema(description = "账户类型", example = "book,group,general")
    private String accType;

}