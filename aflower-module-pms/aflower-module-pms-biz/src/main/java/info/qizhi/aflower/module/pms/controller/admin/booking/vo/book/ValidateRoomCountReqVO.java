package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 验证房量 Request VO")
@Data
@ToString(callSuper = true)
public class ValidateRoomCountReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "渠道代码")
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "批次列表")
    @NotEmpty(message = "{batchList.notempty}")
    private List<Batch> batches;

    @Data
    public static class Batch {
        @Schema(description = "计划入住时间")
        @NotNull(message = "{planCheckinTime.notnull}")
        @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
        private LocalDateTime planCheckinTime;

        @Schema(description = "计划离店时间")
        @NotNull(message = "{planCheckoutTime.notnull}")
        @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
        private LocalDateTime planCheckoutTime;

        @Schema(description = "房型预订数")
        @NotEmpty(message = "{roomNum.notnull}")
        private List<BookRoomType> bookRoomTypes;

        @Data
        public static class BookRoomType {

            @Schema(description = "订单号")
            private String orderNo;

            @Schema(description = "房型代码")
            @NotEmpty(message = "{roomTypeCode.notempty}")
            private String rtCode;

            @Schema(description = "房号")
            private String rNo;

            @Schema(description = "房型预订数")
            @NotNull(message = "{roomNum.notnull}")
            @Min(value = 0, message = "房型预订数不能小于0")
            private Integer roomNum;

            @Schema(description = "排房列表")
            @Valid
            private List<BookRoom> bookRooms;


            @Schema(description = "预订房间")
            @Data
            public static class BookRoom {

                @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{rCode.notempty}")
                @JsonProperty(value = "rCode")
                private String rCode;

                @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{rNo.notempty}")
                @JsonProperty(value = "rNo")
                private String rNo;

                @Schema(description = "选中的房间是不是已有预订单占用;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{isPreOccupied.notempty}")
                private String preOccupied;

                @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
                private String state;

            }
        }
    }
}
