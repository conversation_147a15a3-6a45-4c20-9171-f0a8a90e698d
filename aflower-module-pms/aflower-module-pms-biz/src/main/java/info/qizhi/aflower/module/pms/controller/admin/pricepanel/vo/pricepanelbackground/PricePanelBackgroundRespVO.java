package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepanelbackground;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 房价牌背景图片 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PricePanelBackgroundRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31580")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "背景图片代码;系统生成 (唯一标识)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("背景图片代码;系统生成 (唯一标识)")
    private String backgroundCode;

    @Schema(description = "背景图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("背景图片地址")
    private String backgroundWebsite;

    @Schema(description = "版面类型;0:横版 1：竖版", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版面类型;0:横版 1：竖版")
    private String layoutType;

    @Schema(description = "图片名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("图片名称")
    private String name;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}