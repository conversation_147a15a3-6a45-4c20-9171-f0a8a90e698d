package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "biz - 账务 - 交易 Request VO")
@Data
@ToString(callSuper = true)
public class TransactionPageReqVO extends PageParam {
    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "订单号")
    private String no;

    @Schema(description = "顾客姓名")
    private String guestName;

    @Schema(description = "房号")
    private String rNo;

    @Schema(description = "交易单号")
    private String outOrderNo;

    @Schema(description = "交易金额")
    private BigDecimal fee;

    @Schema(description = "交易开始时间")
    private LocalDateTime startTime;

    @Schema(description = "交易结束时间")
    private LocalDateTime endTime;
}
