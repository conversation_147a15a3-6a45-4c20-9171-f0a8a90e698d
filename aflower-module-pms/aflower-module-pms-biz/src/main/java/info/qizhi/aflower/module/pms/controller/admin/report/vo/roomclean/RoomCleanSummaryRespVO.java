package info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 房扫汇总 Response VO")
@Data
public class RoomCleanSummaryRespVO {
    @Schema(description = "清洁工账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String cleaner;

    @Schema(description = "清洁工名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String cleanerName;

    @Schema(description = "房扫房型数量汇总列表")
    private List<RoomCleanSummaryData> dataList;

}
