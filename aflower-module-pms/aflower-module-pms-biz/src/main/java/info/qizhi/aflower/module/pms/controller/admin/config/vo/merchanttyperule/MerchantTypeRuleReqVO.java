package info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店类型规则分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MerchantTypeRuleReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店类型", example = "JM")
    private String merchantType;

}