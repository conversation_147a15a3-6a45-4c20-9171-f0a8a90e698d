package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 新增账务 -  退货 Request VO")
@Data
public class ConsumeGoodRespVO {
    @Schema(description = "商品代码")
    private String goodsCode;

    @Schema(description = "商品名称")
    private String goodsName;

    @Schema(description = "可退数量")
    private Integer RemainCount;

    @Schema(description = "商品单价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;
}