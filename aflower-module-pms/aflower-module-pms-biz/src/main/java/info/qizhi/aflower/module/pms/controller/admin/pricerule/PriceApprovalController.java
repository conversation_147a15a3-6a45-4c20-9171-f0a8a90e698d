package info.qizhi.aflower.module.pms.controller.admin.pricerule;

import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval.PriceApprovalRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval.PriceApprovalSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval.PriceApprovalUpdateStatusReqVO;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import java.util.*;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.module.pms.dal.dataobject.pricerule.PriceApprovalDO;
import info.qizhi.aflower.module.pms.service.pricerule.PriceApprovalService;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 集团价格审批")
@RestController
@RequestMapping("/pms/price-approval")
@Validated
public class PriceApprovalController {

    @Resource
    private PriceApprovalService priceApprovalService;

    @PostMapping("/create")
    @Operation(summary = "创建集团价格审批")
    @PreAuthorize("@ss.hasPermission('pms:price-approval:create')")
    public CommonResult<Long> createPriceApproval(@Valid @RequestBody PriceApprovalSaveReqVO createReqVO) {
        return success(priceApprovalService.createPriceApproval(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新集团价格审批")
    @PreAuthorize("@ss.hasPermission('pms:price-approval:update')")
    public CommonResult<Boolean> updatePriceApproval(@Valid @RequestBody PriceApprovalSaveReqVO updateReqVO) {
        priceApprovalService.updatePriceApproval(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新集团价格审批状态", description = "传入id和isEnable")
    @PreAuthorize("@ss.hasPermission('pms:price-approval:update')")
    public CommonResult<Boolean> updatePriceApprovalStatus(@Valid @RequestBody PriceApprovalUpdateStatusReqVO updateReqVO) {
        priceApprovalService.updatePriceApprovalStatus(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得集团价格审批")
    @Parameter(name = "approvalCode", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:price-approval:query')")
    public CommonResult<PriceApprovalRespVO> getPriceApproval(@RequestParam("approvalCode") String approvalCode) {
        PriceApprovalDO priceApproval = priceApprovalService.getPriceApproval(approvalCode);
        return success(BeanUtils.toBean(priceApproval, PriceApprovalRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得集团价格审批列表")
    @PreAuthorize("@ss.hasPermission('pms:price-approval:query')")
    public CommonResult<List<PriceApprovalRespVO>> getPriceApprovalList(@RequestParam("gcode") String gcode) {
        List<PriceApprovalDO> list = priceApprovalService.getPriceApprovalList(gcode);
        return success(BeanUtils.toBean(list, PriceApprovalRespVO.class));
    }


}