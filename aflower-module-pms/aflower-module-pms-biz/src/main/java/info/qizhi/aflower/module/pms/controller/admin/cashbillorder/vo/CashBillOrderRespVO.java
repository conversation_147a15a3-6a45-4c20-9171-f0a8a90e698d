package info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 现付账订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CashBillOrderRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20244")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String mcode;

    @Schema(description = "现付账订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("现付账订单号")
    private String cashBillOrderNo;

    @Schema(description = "付款方式")
    @ExcelProperty("付款方式")
    private String payMethod;

    @Schema(description = "科目名称")
    private String subName;

    @Schema(description = "现付账套代码")
    @ExcelProperty("现付账套代码")
    private String accCode;

    @Schema(description = "付款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("付款金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "消费金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("消费金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long buyFee;

    @Schema(description = "AR账账户")
    @ExcelProperty("AR账账户")
    private String arSetCode;

    @Schema(description = "银行卡号")
    @ExcelProperty("银行卡号")
    private String bankNo;

    @Schema(description = "银行代码")
    @ExcelProperty("银行代码")
    private String bankType;

    @Schema(description = "结账时间")
    @ExcelProperty("结账时间")
    private LocalDateTime payTime;

    @Schema(description = "结账交班号")
    @ExcelProperty("结账交班号")
    private String payShiftNo;

    @Schema(description = "交班名称")
    private String shiftName;

    @Schema(description = "是否被冲调;0:否 1：是")
    @ExcelProperty("是否被冲调;0:否 1：是")
    private String isRev;

    @Schema(description = "操作人")
    @ExcelProperty("操作人")
    private String operator;

    @Schema(description = "现付账购买商品内容（存商品信息）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("现付账购买商品内容（存商品信息）")
    private String buyContent;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "营业日期")
    @ExcelProperty("营业日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

}