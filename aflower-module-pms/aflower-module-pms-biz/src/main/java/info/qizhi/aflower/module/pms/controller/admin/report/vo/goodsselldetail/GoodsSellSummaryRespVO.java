package info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 小商品销售汇总 Response VO")
@Data
public class GoodsSellSummaryRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "商品分类代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingCode;

    @Schema(description = "商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingName;

    @Schema(description = "商品代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsName;

    @Schema(description = "平均价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgPrice;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer num;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalPrice;

}
