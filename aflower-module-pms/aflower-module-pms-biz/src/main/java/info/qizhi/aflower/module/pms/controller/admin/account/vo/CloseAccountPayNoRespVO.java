package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 已结账务 Response VO")
@Data
public class CloseAccountPayNoRespVO {

    @Schema(description = "结账号")
    private String payNo;

    @Schema(description = "金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "科目代码")
    private String subCode;

    @Schema(description = "科目名称")
    private String subName;

    @Schema(description = "科目类型 consume_account: 消费科目 pay_account：付款科目")
    private String subType;

}