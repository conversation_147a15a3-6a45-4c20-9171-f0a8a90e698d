package info.qizhi.aflower.module.pms.controller.admin.report.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 今日预抵客人明细项 VO")
@Data
public class BookGuestRespVO {
    @Schema(description = "预订单号")
    private String bookNo;

    @Schema(description = "超链接")
    private String url;

    @Schema(description = "预抵信息")
    private List<BookGuestDetailRespVO> bookList;
} 