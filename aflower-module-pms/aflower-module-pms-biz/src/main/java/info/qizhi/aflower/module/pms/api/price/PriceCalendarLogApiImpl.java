package info.qizhi.aflower.module.pms.api.price;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.price.dto.PriceCalendarLogSaveReqDTO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricecalendarlog.PriceCalendarLogSaveReqVO;
import info.qizhi.aflower.module.pms.service.price.PriceCalendarLogService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class PriceCalendarLogApiImpl implements PriceCalendarLogApi{
    @Resource
    private PriceCalendarLogService priceCalendarLogService;

    @Override
    public CommonResult<Long> createPriceCalendarLog(PriceCalendarLogSaveReqDTO createReqVO) {
        return success(priceCalendarLogService.createPriceCalendarLog(BeanUtils.toBean(createReqVO, PriceCalendarLogSaveReqVO.class)));
    }

    @Override
    public CommonResult<Boolean> createPriceCalendarLogBatch(List<PriceCalendarLogSaveReqDTO> createReqVO) {
        priceCalendarLogService.createPriceCalendarLogBatch(BeanUtils.toBean(createReqVO, PriceCalendarLogSaveReqVO.class));
        return success(true);
    }
}
