package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 需要联房的预订单响应VO
 * 
 * <AUTHOR>
 */
@Schema(description = "biz - 需要联房的预订单 Response VO")
@Data
public class BookMergeReceptionRespVO {

    @Schema(description = "预订单ID", example = "1001")
    private Long id;

    @Schema(description = "预订单号", example = "BK202401010001")
    private String bookNo;

    @Schema(description = "预抵时间", example = "2024-01-01 14:00:00")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间", example = "2024-01-02 12:00:00")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "房型代码", example = "STD")
    private String rtCode;

    @Schema(description = "房型名称", example = "标准间")
    private String rtName;

    @Schema(description = "预订单状态", example = "no_check_in", allowableValues = {"no_check_in", "be_confirm", "cancel", "noshow"})
    private String state;

    @Schema(description = "预订单状态名称", example = "预订中")
    private String stateName;

    @Schema(description = "联系人姓名", example = "张三")
    private String contact;

    @Schema(description = "联系人手机号", example = "13800138000")
    private String phone;

}
