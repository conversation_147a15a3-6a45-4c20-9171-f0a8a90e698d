package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务 Response VO")
@Data
public class AccountRoomFeeRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23518")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bizDate;

    @Schema(description = "账号(入账账号);预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "消费科目、付款方式代码;消费科目、付款方式代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称;消费科目名称、付款方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String subType;

    @Schema(description = "是否已经使用了优惠券 0：否，1：是", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String isUsed;

    @Schema(description = "券号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String couponCode;

    @Schema(description = "金额;账务的金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

}