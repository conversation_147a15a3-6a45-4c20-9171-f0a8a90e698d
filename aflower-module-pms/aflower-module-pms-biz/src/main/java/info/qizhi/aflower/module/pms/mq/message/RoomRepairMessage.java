package info.qizhi.aflower.module.pms.mq.message;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 房间维修消息
 * <AUTHOR>
 */

@Data
public class RoomRepairMessage implements Serializable {

    public static final String QUEUE = QueueConstants.ROOM_REPAIR_MESSAGE_QUEUE; // 重点：需要增加消息对应的 Queue

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "变更类型; 0:创建,1:移除")
    private String repairType;

    @Schema(description = "维修消息")
    private String msg;

}
