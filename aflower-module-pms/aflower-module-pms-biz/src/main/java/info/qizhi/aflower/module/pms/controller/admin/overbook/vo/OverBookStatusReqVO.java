package info.qizhi.aflower.module.pms.controller.admin.overbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 超预订配置 Request VO")
@Data
@ToString(callSuper = true)
public class OverBookStatusReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "超预订配置代码")
    @NotEmpty(message = "{overBookCode.notempty}")
    private String overBookCode;

    @Schema(description = "是否有效")
    @NotEmpty(message = "{isEnable.notempty}")
    private String isEnable;

}