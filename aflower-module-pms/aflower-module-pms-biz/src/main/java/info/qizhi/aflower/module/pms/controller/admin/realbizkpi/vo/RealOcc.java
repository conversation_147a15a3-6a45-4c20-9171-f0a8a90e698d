package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "小程序首页 - 实时出租率 Response VO")
@Data
public class RealOcc {

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称")
    private String rtName;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal occ;

    @Schema(description = "已售房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long sold;

    @Schema(description = "总房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long totalRooms;
}
