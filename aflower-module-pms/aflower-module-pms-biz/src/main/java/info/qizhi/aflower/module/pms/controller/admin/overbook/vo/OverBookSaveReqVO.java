package info.qizhi.aflower.module.pms.controller.admin.overbook.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 超预订配置新增/修改 Request VO")
@Data
public class OverBookSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22459")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "超预订代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String overBookCode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypeCode.notempty}")
    private String rtCode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "超预订间数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{overbookRoomCount.notnull}")
    private Integer num;

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isEnable.notempty}")
    private String isEnable;

}