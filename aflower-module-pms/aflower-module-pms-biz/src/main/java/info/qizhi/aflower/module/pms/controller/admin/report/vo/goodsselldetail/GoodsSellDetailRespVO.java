package info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 小商品销售明细 Response VO")
@Data
public class GoodsSellDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "商品分类代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingCode;

    @Schema(description = "商品分类名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String thingName;

    @Schema(description = "商品代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsCode;

    @Schema(description = "商品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsName;

    @Schema(description = "单价", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer num;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "超链接", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rNo;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "售卖渠道")
    private String sellChannel;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "入账人昵称")
    private String recorderName;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;

    @Schema(description = "班次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "班次名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftName;

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "账务类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accType;

}
