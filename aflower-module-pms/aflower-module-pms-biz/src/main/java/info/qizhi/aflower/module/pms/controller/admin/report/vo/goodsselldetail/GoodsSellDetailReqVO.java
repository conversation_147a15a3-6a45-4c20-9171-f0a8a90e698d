package info.qizhi.aflower.module.pms.controller.admin.report.vo.goodsselldetail;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 小商品销售明细 Request VO")
@Data
public class GoodsSellDetailReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "报表操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "商品分类代码列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> thingCodes;

    @Schema(description = "商品代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsCode;

    @Schema(description = "商品代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String goodsName;

    @Schema(description = "售卖渠道")
    private String sellChannel;

    @Schema(description = "班次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "营业日")
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

}
