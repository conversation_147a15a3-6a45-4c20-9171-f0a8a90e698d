package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 房价牌模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PricePanelTemplateRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3340")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "模板代码;系统生成 (唯一标识)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板代码;系统生成 (唯一标识)")
    private String templateCode;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("模板名称")
    private String templateName;

    @Schema(description = "模板地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板地址")
    private String templateWebsite;

    @Schema(description = "版面类型;0:横版 1：竖版", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("版面类型;0:横版 1：竖版")
    private String layoutType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}