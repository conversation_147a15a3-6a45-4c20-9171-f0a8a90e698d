package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "biz - 账务补录 Request VO")
@Data
public class RecordingAccountRespVO {
    @Schema(description = "交易单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outOrderNo;

    @Schema(description = "交易金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

}
