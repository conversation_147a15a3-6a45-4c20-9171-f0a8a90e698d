package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 设备信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceBrandRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    @ExcelProperty("id")
    private Long id;

    /*@Schema(description = "设备品牌代码")
    @ExcelProperty("设备品牌代码")
    private String brandCode;*/

    @Schema(description = "设备品牌名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("设备品牌名称")
    private String brandName;


}