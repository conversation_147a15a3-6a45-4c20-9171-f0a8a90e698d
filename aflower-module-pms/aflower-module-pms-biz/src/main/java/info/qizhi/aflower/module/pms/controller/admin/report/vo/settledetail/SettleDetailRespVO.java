package info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 宾客明细报表[固化] Response VO")
@Data
@ExcelIgnoreUnannotated
public class SettleDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32651")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "订单号")
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "超链接")
    @ExcelProperty("超链接")
    private String url;

    @Schema(description = "房号")
    @ExcelProperty("房号")
    private String rNo;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "客人姓名", example = "赵六")
    @ExcelProperty("客人姓名")
    private String name;

    @Schema(description = "房型代码")
    @ExcelProperty("房型代码")
    private String rtCode;

    @Schema(description = "房型")
    @ExcelProperty("房型")
    private String rtName;

    @Schema(description = "客源")
    @ExcelProperty("客源")
    @JsonProperty("gSrc")
    private String gSrc;

    @Schema(description = "客源名称")
    @JsonProperty("gSrcName")
    @ExcelProperty("客源名称")
    private String gSrcName;

    @Schema(description = "入住类型")
    @ExcelProperty("入住类型")
    private String inType;

    @Schema(description = "入住类型名称")
    @ExcelProperty("入住类型名称")
    private String inTypeName;

    @Schema(description = "入住时间")
    @ExcelProperty("入住时间")
    private LocalDateTime checkInTime;

    @Schema(description = "离店时间")
    @ExcelProperty("离店时间")
    private LocalDateTime checkOutTime;

    @Schema(description = "结账时间")
    @ExcelProperty("结账时间")
    private LocalDateTime payTime;

    @Schema(description = "结账日期")
    @ExcelProperty("结账日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate payBizDate;

    @Schema(description = "账务类型")
    @ExcelProperty("账务类型")
    private String payType;

    @Schema(description = "房费")
    @ExcelProperty("房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubFee;

    @Schema(description = "优惠卷抵扣")
    @ExcelProperty("优惠卷抵扣")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long couponDeduction;

    @Schema(description = "餐饮")
    @ExcelProperty("餐饮")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubCate;

    @Schema(description = "小商品")
    @ExcelProperty("小商品")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubGoods;

    @Schema(description = "会员卡")
    @ExcelProperty("会员卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubCard;

    @Schema(description = "会议室")
    @ExcelProperty("会议室")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubMeetroom;

    @Schema(description = "其他")
    @ExcelProperty("其他")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consSubOth;

    @Schema(description = "人民币现金(收款)")
    @ExcelProperty("人民币现金(收款)")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubCash;

    @Schema(description = "人民币押金")
    @ExcelProperty("人民币押金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubDeposit;

    @Schema(description = "现金退款")
    @ExcelProperty("现金退款")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubCashBack;

    @Schema(description = "银行卡")
    @ExcelProperty("银行卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubBank;

    @Schema(description = "储值卡")
    @ExcelProperty("储值卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubStoreCard;

    @Schema(description = "AR账")
    @ExcelProperty("AR账")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubAr;

    @Schema(description = "微信")
    @ExcelProperty("微信")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubWx;

    @Schema(description = "支付宝")
    @ExcelProperty("支付宝")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubAlpay;

    @Schema(description = "信用住")
    @ExcelProperty("信用住")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubCredit;

    @Schema(description = "其他")
    @ExcelProperty("其他")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paySubOth;

    @Schema(description = "合并结账")
    @ExcelProperty("合并结账")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long merge;

    @Schema(description = "结账操作员")
    @ExcelProperty("结账操作员")
    private String payOperator;

    @Schema(description = "结账操作员昵称")
    private String payOperatorName;

    @Schema(description = "付款科目账务")
    @ExcelProperty("付款科目账务")
    private List<SubCodeAccount> payAccountList;

    @Schema(description = "付款科目账务")
    @ExcelProperty("付款科目账务")
    private List<SubCodeAccount> consumeAccountList;

}