package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 返佣记录新增/修改 Request VO")
@Data
public class BrokerageRecordSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "24305")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "返佣代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String brokerageCode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNumber.notempty}")
    private String orderNo;

    @Schema(description = "单位/中介代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{unitAgentCode.notempty}")
    private String paCode;

    @Schema(description = "产生佣金的策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{commissionStrategyCode.notempty}")
    private String strategyCode;

    @Schema(description = "入住房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{rNo.notempty}")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "25165")
    @NotNull(message = "{roomPrice.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long roomPrice;

    @Schema(description = "入住人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{guestName.notempty}")
    private String name;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    @NotNull(message = "{fee.notnull}")
    private Long fee;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "外部订单号;来自OTA的订单号")
    private String outOrderNo;

    @Schema(description = "状态;0:未核销 1:已核销", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{verificationStatus.notempty}")
    private String state;

    @Schema(description = "核销人")
    private String verifyUser;

    @Schema(description = "核销时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime verifyTime;

}