package info.qizhi.aflower.module.pms.api.order;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.order.dto.*;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.his.OrderHisListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.his.OrderHisPageResp;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.*;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.service.order.OrderService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class OrderApiImpl implements OrderApi {
    @Resource
    @Lazy
    private OrderService orderService;


    @Override
    public CommonResult<List<OrderRespDTO>> getOrderListForSms(OrderReqDTO reqDTO) {
        return success(BeanUtils.toBean(orderService.smsGetClientInfoList(reqDTO.getGcode(), reqDTO.getHcode()), OrderRespDTO.class));
    }

    @Override
    public CommonResult<OrderPersonRespDTO> getGuestInfo(String togetherCode) {
        return success(BeanUtils.toBean(orderService.getGuestInfo(togetherCode), OrderPersonRespDTO.class));
    }

    @Override
    public CommonResult<OrderRespDTO> getOrderByOrderNo(String orderNo) {
        OrderDO orderByOrderNo = orderService.getOrderByOrderNo(orderNo);
        return success(BeanUtils.toBean(orderByOrderNo, OrderRespDTO.class));
    }

    @Override
    public CommonResult<List<OrderRespDTO>> getOrderList(OrderReqDTO reqDTO) {
        List<OrderDO> orderList = orderService.getOrderList(BeanUtils.toBean(reqDTO, OrderReqVO.class));
        return success(BeanUtils.toBean(orderList, OrderRespDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateOrderUseBreakfast(OrderBreakfastReqDTO reqDTO) {
        orderService.updateOrder(BeanUtils.toBean(reqDTO, OrderDO.class));
        return success(true);
    }

    @Override
    public CommonResult<List<OrderHisRespDTO>> getOrderHisByCheckoutState(OrderHisListReqDTO reqVO) {
        OrderHisListReqVO bean = BeanUtils.toBean(reqVO, OrderHisListReqVO.class);
        List<OrderHisPageResp> orderHis = orderService.getPendingAccountForSms(bean);
        return success(BeanUtils.toBean(orderHis, OrderHisRespDTO.class));
    }

    @Override
    public CommonResult<Boolean> batchCheckIn(BookBatchCheckInReqDTO reqVO) {
        orderService.bookTurnCheckIn(BeanUtils.toBean(reqVO, BookBatchCheckInReqVO.class));
        return success(true);
    }

    @Override
    public CommonResult<String> createOrder(CheckInReqDTO reqVo) {
        return success(orderService.directCheckIn(BeanUtils.toBean(reqVo, CheckInReqVO.class)));
    }

    @Override
    public CommonResult<Boolean> createOrderTogether(OrderAddTogetherSaveReqDTO createReqVO) {
        orderService.addTogether(BeanUtils.toBean(createReqVO, OrderAddTogetherSaveReqVO.class));
        return success(true);
    }

    @Override
    public CommonResult<Boolean> continueIn(OrderContinueInReqDTO updateReqVO) {
        orderService.continueIn(BeanUtils.toBean(updateReqVO, OrderContinueInReqVO.class));
        return success(true);
    }
}
