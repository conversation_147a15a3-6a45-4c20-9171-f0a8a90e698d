package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.module.pms.mq.message.TaskMessage;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审生产者
 * @Version: 1.0
 */
@Slf4j
@Component
public class TaskProducer {
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    private GeneralConfigService generalService;

    /**
     * 发送消息
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param rCodes 房间代码
     * @param type 具体类型
     */
    public void sendRoomCleanTaskMessage(String gcode, String hcode, List<String> rCodes, String type) {
        TaskMessage message = new TaskMessage().setGcode(gcode).setHcode(hcode)
                .setRCodes(rCodes).setType(type);
        rabbitTemplate.convertAndSend(TaskMessage.QUEUE, message);
    }
}
