package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.bo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 房价牌 - 房型详情 BO")
@Data
public class PricePanelContentBO {

    @Schema(description = "房型代码")
    private String rtCode;

/*    @Schema(description = "是否自定义房型房价;0:否 1：是")
    private String isCustom;*/

    @Schema(description = "自定义房型房价代码;")
    private String customCode;

    @Schema(description = "房型名称")
    private String name;

    @Schema(description = "门市价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "会员价")
    private List<MemberPrice> memberPrice;

    @Data
    public static class MemberPrice  {
        @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String mtCode;

        @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String name;

        @Schema(description = "会员房价", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long price;

    }

}
