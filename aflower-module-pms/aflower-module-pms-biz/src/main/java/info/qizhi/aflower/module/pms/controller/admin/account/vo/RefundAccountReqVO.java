package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 退款 Request VO")
@Data
public class RefundAccountReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "备注")
    @NotEmpty(message = "备注不能为空")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "退款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "账务号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accNoList.notempty}")
    private List<String> accNoList;

}