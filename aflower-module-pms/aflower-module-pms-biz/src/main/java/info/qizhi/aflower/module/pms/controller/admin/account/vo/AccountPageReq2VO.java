package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.enums.NoTypeEnum;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountPageReq2VO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "宾客代码列表")
    private List<String> togetherCodes;

    @Schema(description = "账号(入账账号)列表")
    private List<String> noList;

    @Schema(description = "账号类型")
    @InStringEnum(value= NoTypeEnum.class)
    private String noType;

    @Schema(description = "科目代码列表")
    private List<String> subCodes;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

}