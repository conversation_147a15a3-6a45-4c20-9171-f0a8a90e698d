package info.qizhi.aflower.module.pms.controller.admin.serviceitems.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 服务项目新增/修改 Request VO")
@Data
public class ServiceItemsSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4575")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "提供的服务", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{services.notempty}")
    private String services;

}