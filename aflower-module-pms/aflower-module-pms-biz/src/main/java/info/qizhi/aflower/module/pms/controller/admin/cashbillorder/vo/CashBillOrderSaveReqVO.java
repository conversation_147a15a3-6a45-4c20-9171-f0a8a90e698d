package info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 现付账订单新增/修改 Request VO")
@Data
public class CashBillOrderSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "是否挂房账; 0:否, 1:是")
    private String isRoomAccount;

    @Schema(description = "购买内容")
    private String buyContent;

    @Schema(description = "现付账套代码")
    @NotEmpty(message = "{cashAccountCode.notempty}")
    private String accCode;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "现付账订单创建路径, 门店:'lobby', 小程序:'mini_app'")
    private String path;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单 cash:现付账订单", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = AccountTypeEnum.class, message = "{accType.instringenum}")
    @NotEmpty(message = "{accType.notblank}")
    private String accType;

    @Schema(description = "备注", example = "随便")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "消费信息")
    @NotNull(message = "{consume.notnull}")
    private Consume consume;

    @Schema(description = "付款信息")
    private Pay pay;

    @Schema(description = "商品明细")
    private List<GoodsDetail> goodsDetails;

    @Schema(description = "赔偿信息")
    private List<IndemnityGoodsDetail> indemnityGoodsDetails;

    @Data
    public static class Consume {

        @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{fee.notnull}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "消费科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{consume.subCode.notempty}")
        private String subCode;

        @Schema(description = "业务详情")
        @Size(max = 128, message = "{accDetail.size}")
        private String accDetail;
    }

    @Data
    public static class Pay {
        @Schema(description = "付款码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)")
        private String payCode;

        //----会员卡支付--->>>//
        @Schema(description = "会员代码，如果是储值卡支付，需要传会员代码")
        private String mcode;

        @Schema(description = "手机号，如果是储值卡支付,需要传会员手机号")
        private String phone;

        @Schema(description = "会员卡密码,当付款方式为储值卡时需要")
        private String pwd;
        //<<<----会员卡支付---//

        @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{fee.notnull}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{subCode.notblank}")
        private String subCode;

        //----银行卡支付--->>>//
        @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
        private String bankType;

        @Schema(description = "银行卡号")
        @Size(max = 20, message = "{bankCardNo.size}")
        private String bankCardNo;
        //<<<----银行卡支付---//

        @Schema(description = "有效日期;当支付方式为预授权时有效")
        private LocalDate validDate;

    }

    @Data
    public static class GoodsDetail {

        @Schema(description = "商品分类代码")
        private String thingCode;

        @Schema(description = "商品分类名称")
        private String categoryName;

        @Schema(description = "商品代码")
        private String goodsCode;

        @Schema(description = "商品名称")
        private String goodsName;

        @Schema(description = "商品数量")
        private Integer num;

        @Schema(description = "商品单价")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long price;
    }

    @Data
    public static class IndemnityGoodsDetail {

        @Schema(description = "物品分类代码")
        private String thingCode;

        @Schema(description = "物品代码")
        private String indemnityCode;

        @Schema(description = "物品名称")
        private String indemnityName;

        @Schema(description = "物品数量")
        private Integer num;

        @Schema(description = "物品单价")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long price;
    }

}