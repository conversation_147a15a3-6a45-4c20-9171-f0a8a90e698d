package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import info.qizhi.aflower.module.pms.dal.dataobject.deviceset.DeviceSetDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备信息新增/修改 Request VO")
@Data
public class DeviceSetSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deviceCode;

    @Schema(description = "设备类别代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{deviceType.notempty}")
    private String deviceType;

    @Schema(description = "设备类别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String deviceName;

    @Schema(description = "设备品牌代码")
    private String brandCode;

    @Schema(description = "设备品牌名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{brandName.notempty}")
    private String brandName;

    @Schema(description = "设备型号代码")
    private String deviceVerCode;

    @Schema(description = "设备型号名称")
    private String deviceVerName;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "端口号")
    private String port;

    @Schema(description = "发卡器类型")
    private String cardDispenserType;

    @Schema(description = "配置信息")
    private List<DeviceSetDO.ConfParameter> conf;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isEnable.notempty}")
    private String state;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}