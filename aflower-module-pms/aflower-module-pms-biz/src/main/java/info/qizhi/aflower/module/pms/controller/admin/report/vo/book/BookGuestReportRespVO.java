package info.qizhi.aflower.module.pms.controller.admin.report.vo.book;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_TWO;

@Schema(description = "管理后台 - 今日预抵客人报表 Response VO")
@Data
public class BookGuestReportRespVO {
    @Schema(description = "门店名称", example = "酒店练习环境 (砺学院)")
    private String hname;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "预抵日期", example = "2025-05-21")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate bookDate;

    @Schema(description = "最后查询时间", example = "2025-05-21 16:07:58")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "预付款合计", example = "208")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long prepayAmountSum;

    @Schema(description = "今日预抵客人列表")
    private List<BookGuestRespVO> list;
} 