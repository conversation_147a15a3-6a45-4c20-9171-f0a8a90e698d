package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.CouponEnum;
import info.qizhi.aflower.framework.common.enums.OrderTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 退房结账 Request VO")
@Data
public class PayCheckOutAccountReqVO {
    @Schema(description = "集团代码")
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单类型,general：普通订单 join：联房订单 group：团队订单")
    @InStringEnum(value = OrderTypeEnum.class)
    @NotBlank(message = "{orderType.notblank}")
    private String orderType;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "是否发送短信")
    @InStringEnum(value = BooleanEnum.class, message = "{isSendSms.instringenum}")
    private String isSendSms;

    @Schema(description = "验证类型")
    private Integer verifyMode;

    @Schema(description = "验证码")
    private String smsCode;

    @Schema(description = "是否自动创建任务")
    private String isAutoTask;

    @Schema(description = "客人列表")
    @Valid
    @NotEmpty(message = "客人列表不能为空")
    private List<OrderTogether> orderTogethers;

    @Schema(description = "付款")
    @Valid
    @NotNull(message = "{payment.notnull}")
    private Pay pay;

    @Schema(description = "优惠券付款信息")
    @Valid
    @NotNull(message = "{payment.notnull}")
    private List<PayInfo> payInfoList;

    @Data
    public static class OrderTogether {

        @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{no.notempty}")
        private String no;

        @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "是否被选中为入账账号,0:否 1:是")
        @InStringEnum(value = BooleanEnum.class)
        private String isRecord;

        @Schema(description = "是否团队主账,0:否 1:是")
        @NotBlank(message = "{isTeam.notempty}")
        @InStringEnum(value = BooleanEnum.class)
        private String isTeam;

        @Schema(description = "是否主客单,0:否 1:是")
        @NotBlank(message = "{isMain.notempty}")
        @InStringEnum(value = BooleanEnum.class)
        private String isMain;
    }

    @Data
    public static class Pay {
        @Schema(description = "付款科目代码")
        @NotBlank(message = "{subCode.notblank}")
        private String subCode;

        @Schema(description = "付款方式,1:收款 -1:退款")
        @NotBlank(message = "{paymentMethod.notblank}")
        private String payMode;

        @Schema(description = "付款金额")
        @NotNull(message = "{paymentAmount.notnull}")
        @Min(value = 0L, message = "{paymentAmount.min}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "付款时的码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)")
        private String payCode;

        // 银行卡------

        @Schema(description = "银行类型;建设银行、招商银行....")
        private String bankType;

        @Schema(description = "银行卡号")
        private String bankCardNo;

        // ------银行卡

        // 会员支付--------

        @Schema(description = "会员代码,储值卡支付时需要")
        private String mcode;

        @Schema(description = "储值卡号")
        private String storeCardNo;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "会员卡密码")
        private String pwd;

        // --------会员支付

    }

    @Data
    public static class PayInfo {

        @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{PayAccountSaveReqVO.fee.notnull}")
        // TODO 暂时注释掉，可以让AR帐输入负数
//    @Min(value = 1, message = "{fee.min}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{subCode.notblank}")
        private String subCode;

        @Schema(description = "支付类型值 1为收款，-1为退款", requiredMode = Schema.RequiredMode.REQUIRED)
        //@NotBlank(message = "{payValue.notblank}")
        private String payValue;

        @Schema(description = "付款码;付款方式：支付宝码、微信码、预授权码、储值卡号、账套代码(AR账)")
        @Size(max = 32, message = "{payCode.size}")
        private String payCode;

        @Schema(description = "业务详情")
        @Size(max = 128, message = "{accDetail.size}")
        private String accDetail;

        @Schema(description = "备注")
        @Size(max = 128, message = "{remark.size128}")
        private String remark;

        @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
        private String accNo;

        @Schema(description = "券代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String templateCode;

        @Schema(description = "券名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
        private String templateName;

        @Schema(description = "券号", requiredMode = Schema.RequiredMode.REQUIRED)
        private String couponCode;

        @Schema(description = "券类型;voucher  代金券, discount 折扣券, free 免房券,breakfast 早餐券", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        @NotEmpty(message = "{couponType.invalid}")
        @InStringEnum(value = CouponEnum.class, message = "{couponType.invalid}")
        private String couponType;

        @Schema(description = "金额;代金券金额，其他券该值为0")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long money;

        @Schema(description = "折扣;折扣券折扣，其他券值为1")
        @Max(value = 1, message = "折扣不能大于1")
        private BigDecimal rebate;

        @Schema(description = "状态;0：未赠送 1:已送待使用 2：已使用, 3：作废", requiredMode = Schema.RequiredMode.REQUIRED)
        private String state;
    }


}