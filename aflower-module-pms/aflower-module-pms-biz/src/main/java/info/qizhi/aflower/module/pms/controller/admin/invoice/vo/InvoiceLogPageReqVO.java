package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 开票记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoiceLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "发票代码")
    private String invoiceCode;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "开票时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] makeTime;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "开票金额")
    private Integer invoiceMoney;

    @Schema(description = "开票人")
    private String invoicePerson;

    @Schema(description = "发票抬头")
    private String invoiceTitle;

    @Schema(description = "税金")
    private Integer tax;

    @Schema(description = "纳税人识别号", example = "15374")
    private String taxpayerId;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}