package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
@Schema(description = "管理后台 - 渠道简单模型 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChannelSimpleRespVO {

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("渠道名称")
    private String channelName;


}