package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomTypeChangeMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 *  房型变更生产者
 */
@Slf4j
@Component
public class RoomTypeChangeProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送房型变更消息
     *
     */
    public void sendRealRoomStateMessage(String hcode, String changeType,String msg) {
        RoomTypeChangeMessage message = new RoomTypeChangeMessage();
        message.setHcode(hcode);
        message.setChangeType(changeType);
        message.setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.ROOM_TYPE_CHANGE_MESSAGE_QUEUE, message);
    }
}
