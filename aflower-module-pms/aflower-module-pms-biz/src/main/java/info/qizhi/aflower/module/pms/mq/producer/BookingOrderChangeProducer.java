package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.BookingOrderChangeMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 订单信息生产者
 */

@Slf4j
@Component
public class BookingOrderChangeProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    public void sendBookingOrderChangeMessage(String hcode, String actionType, String msg) {
        BookingOrderChangeMessage message = new BookingOrderChangeMessage();
        message.setHcode(hcode);
        message.setActionType(actionType);
        message.setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.BOOKING_ORDER_CHANGE_MESSAGE_QUEUE, message);
    }
}
