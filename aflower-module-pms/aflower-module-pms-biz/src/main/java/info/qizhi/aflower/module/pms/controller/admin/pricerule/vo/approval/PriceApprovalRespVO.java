package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 集团价格审批 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PriceApprovalRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25451")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "审批代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("审批代码")
    private String approvalCode;

    @Schema(description = "自定义价格名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("自定义价格名称")
    private String approvalName;

    @Schema(description = "房价折扣", example = "31599")
    @ExcelProperty("房价折扣")
    private BigDecimal discount;

    @Schema(description = "涨价")
    @ExcelProperty("涨价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long markUp;

    @Schema(description = "降价")
    @ExcelProperty("降价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long markDown;

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有效")
    private String isEnable;

}