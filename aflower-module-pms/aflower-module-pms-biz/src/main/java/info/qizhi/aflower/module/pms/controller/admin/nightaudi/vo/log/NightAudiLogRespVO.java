package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 夜审记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NightAudiLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3963")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "营业日yyyy-MM-dd", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日yyyy-MM-dd")
    private LocalDate bizDate;

    @Schema(description = "夜审开始时间（也是断账时间）精确到时分秒", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("夜审开始时间（也是断账时间）精确到时分秒")
    private LocalDateTime startTime;

    @Schema(description = "夜审结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("夜审结束时间")
    private LocalDateTime endTime;

    @Schema(description = "自然日（yyyy-MM-dd）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自然日（yyyy-MM-dd）")
    private LocalDate naturalDate;

    @Schema(description = "夜审是否成功;0失败 1成功", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("夜审是否成功;0失败 1成功")
    private String isSuccess;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}