package info.qizhi.aflower.module.pms.controller.admin.overbook.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 超预订配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class OverBookRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22459")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码")
    private String rtCode;
    private String rtName;

    @Schema(description = "超预订代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String overBookCode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道代码")
    private String channelCode;
    private String channelName;

    @Schema(description = "超预订间数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("超预订间数")
    private Integer num;

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有效")
    private String isEnable;

    @Schema(description = "创建者", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建者")
    private String creator;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}