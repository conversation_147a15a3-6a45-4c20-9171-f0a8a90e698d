package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomRepairMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * 维修消息生产者
 * <AUTHOR>
 */

@Slf4j
@Component
public class RoomRepairProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送房间维修消息
     *
     */
    public void sendRealRoomStateMessage(String gcode, String hcode, String rcode, String repairType) {
        RoomRepairMessage message = new RoomRepairMessage();
        message.setGcode(gcode);
        message.setHcode(hcode);
        message.setRcode(rcode);
        message.setRepairType(repairType);
        rabbitTemplate.convertAndSend(QueueConstants.ROOM_REPAIR_MESSAGE_QUEUE, message);
    }

}
