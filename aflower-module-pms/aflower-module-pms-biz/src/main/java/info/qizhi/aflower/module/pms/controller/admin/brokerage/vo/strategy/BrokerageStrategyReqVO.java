package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.strategy;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 佣金策略 Request VO")
@Data
@ToString(callSuper = true)
public class BrokerageStrategyReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "策略名称", example = "李四")
    private String strategyName;

    @Schema(description = "公司类型;0:中介 1:协议单位", example = "2")
    private String companyType;

    @Schema(description = "是否有效;0:无效 1:有效")
    private String isEnable;


}