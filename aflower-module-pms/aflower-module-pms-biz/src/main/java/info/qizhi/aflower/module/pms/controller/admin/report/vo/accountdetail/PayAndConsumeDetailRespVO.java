package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 付款/消费明细 Response VO")
@Data
public class PayAndConsumeDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", example = "门店代码示例")
    private String no;

    @Schema(description = "订单号类别", example = "门店代码示例")
    private String noType;

    @Schema(description = "超链接", example = "门店代码示例")
    private String url;

    @Schema(description = "房号", example = "门店代码示例")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "姓名", example = "门店代码示例")
    private String guestName;

    @Schema(description = "金额", example = "门店代码示例")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "客源")
    private String guestSrcType;

    @Schema(description = "客源", example = "门店代码示例")
    private String guestSrcTypeName;

    @Schema(description = "创建渠道")
    @ExcelProperty("创建渠道")
    private String createChannel;

    @Schema(description = "创建渠道名称")
    @ExcelProperty("创建渠道名称")
    private String createChannelName;

    @Schema(description = "统计渠道")
    @ExcelProperty("统计渠道")
    private String statChannel;

    @Schema(description = "统计渠道名称")
    @ExcelProperty("统计渠道名称")
    private String statChannelName;

    @Schema(description = "营业日", example = "门店代码示例")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "操作时间", example = "2024-07-04")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "班次", example = "报表操作员")
    private String shiftName;

    @Schema(description = "操作员", example = "门店代码示例")
    private String  recorder;

    @Schema(description = "操作员昵称", example = "门店代码示例")
    private String  recorderName;

    @Schema(description = "备注", example = "报表操作员")
    private String remark;

}
