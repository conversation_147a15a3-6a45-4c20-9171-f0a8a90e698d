package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 选中返佣记录 Request VO")
@Data
public class BrokerageRecordChooseReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "返佣id列表")
    @NotNull(message = "{commissionIdList.notempty}")
    private List<Long> idList;

}
