package info.qizhi.aflower.module.pms.controller.admin.report.vo.membercardselldetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 会员卡销售明细 Response VO")
@Data
public class MemberCardSellDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "会员姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mcode;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员级别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtName;

    @Schema(description = "会员卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cardNo;

    @Schema(description = "外卡号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String outCardNo;

    @Schema(description = "办卡方式")
    private String cardRegisterType;

    @Schema(description = "办卡方式名称")
    private String cardRegisterTypeName;

    @Schema(description = "获取方式")
    private String getMethod;

    @Schema(description = "支付方式")
    private String payType;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "支付积分", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer payPoint;

    @Schema(description = "销售门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellHcode;

    @Schema(description = "销售门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellHcodeName;

    @Schema(description = "售卖渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sellChannel;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "操作时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "操作员昵称")
    private String operatorName;

    @Schema(description = "销售员昵称")
    private String sellerName;

}
