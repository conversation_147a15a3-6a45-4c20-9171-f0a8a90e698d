package info.qizhi.aflower.module.pms.controller.admin.report.vo.book;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 今日预抵客人明细项 VO")
@Data
public class BookGuestDetailRespVO {
    @Schema(description = "序号")
    private Integer serialNo;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "联系人")
    private String name;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "预抵时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "房型")
    private String roomType;

    @Schema(description = "房间数")
    private Integer roomCount;

    @Schema(description = "预分配房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomPrice;

    @Schema(description = "预付款")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long prepayAmount;

    @Schema(description = "保留时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime retainTime;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "担保方式名称")
    private String guarantyStyleName;

    @Schema(description = "订单来源")
    private String orderSource;

    @Schema(description = "订单来源名称")
    private String orderSourceName;

    @Schema(description = "客源")
    private String guestSrcType;

    @Schema(description = "客源名称")
    private String guestSrcTypeName;

    @Schema(description = "会员级别/公司")
    private String levelOrCompanyName;

    @Schema(description = "备注")
    private String remark;
} 