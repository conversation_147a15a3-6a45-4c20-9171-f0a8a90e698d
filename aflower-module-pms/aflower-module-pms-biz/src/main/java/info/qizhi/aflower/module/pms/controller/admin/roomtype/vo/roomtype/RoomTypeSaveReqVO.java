package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.ImageVO;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeBedDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 房型新增/修改 Request VO")
@Data
public class RoomTypeSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6025")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @Size(max = 40, message = "{rtName.size}")
    @NotEmpty(message = "{rtName.notempty}")
    private String rtName;

    @Schema(description = "房型简称", example = "王五")
    @Size(max = 30, message = "{shortName.size}")
    private String shortName;

    @Schema(description = "基础价格", example = "168")
    @DecimalMin(value = "0", message = "{basePrice.min}")
    @DecimalMax(value = "9999999", message = "{basePrice.max}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long basePrice;

    @Schema(description = "面积", example = "20")
    @DecimalMin(value = "0", message = "{area.min}")
    private BigDecimal area;

    @Schema(description = "容纳人数", example = "2")
    @DecimalMin(value = "1", message = "{peopleNum.min}")
    private Integer peopleNum;

    @Schema(description = "是否有窗;是否有窗(0是，1否，2部分有窗)", example = "1")
    private String isWindow;

    @Schema(description = "是否有卫生间;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InStringEnum(value = BooleanEnum.class, message = "{isRestRoom.invalid}")
    private String isRestRoom;

    @Schema(description = "是否虚拟房;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    @InStringEnum(value = BooleanEnum.class, message = "{isVirtual.invalid}")
    private String isVirtual;

    @Schema(description = "是否集团房型;1是 0否，1时hcode为空", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InStringEnum(value = BooleanEnum.class, message = "{isGroupRt.invalid}")
    private String isGRt;

    @Schema(description = "是否有效;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @InStringEnum(value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String intro;

    @Schema(description = "图片")
    private List<ImageVO> pics;

    @Schema(description = "房型的床型列表")
    private List<RoomTypeBedDO> roomTypeBeds;

}