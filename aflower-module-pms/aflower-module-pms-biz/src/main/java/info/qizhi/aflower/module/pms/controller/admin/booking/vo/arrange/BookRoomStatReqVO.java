package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeMinuteDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订房间 统计房型房间数 Request VO")
@Data
public class BookRoomStatReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "预低日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckinTime.notnull}")
    @JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckoutTime.notnul}")
    @JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "是否会议室;0:客房 1:会议室", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "是否会议室不能为空")
    private String isMeetingRoom;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> states;

}