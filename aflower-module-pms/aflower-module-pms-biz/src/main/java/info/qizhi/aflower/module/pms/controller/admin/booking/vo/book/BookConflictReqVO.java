package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 在住订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookConflictReqVO extends PageParam {

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime endTime;

}