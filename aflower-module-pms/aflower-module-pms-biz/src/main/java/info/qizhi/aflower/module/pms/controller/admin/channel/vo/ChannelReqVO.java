package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 渠道 Request VO")
@Data
@ToString(callSuper = true)
public class ChannelReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码，如果不为空，查询集团和门店创建的渠道，否则只查询集团创建的渠道")
    private String hcode;

    @Schema(description = "渠道名称", example = "芋艿")
    private String channelName;

    @Schema(description = "是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "渠道类型;0:直营 1：分销")
    private String channelType;

}