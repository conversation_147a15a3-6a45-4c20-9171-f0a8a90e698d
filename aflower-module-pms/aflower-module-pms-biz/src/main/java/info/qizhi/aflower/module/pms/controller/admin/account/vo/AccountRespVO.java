package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务 Response VO")
@Data
public class AccountRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "23518")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "账号(入账账号);预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "账务号;系统生成唯一标识，每笔账都生成一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String accType;

    @Schema(description = "宾客代码;关联宾客表代码,用于区分账务属于那个宾客。账务类型为订单时需要保存宾客代码,为团队主单时保存团队代码，否则默认为0", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "房间代码;如果客人换房，需要记录原账务房间信息")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名;冗余做报表统计", example = "李四")
    private String guestName;

    @Schema(description = "客源类型;散客 会员 单位 中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String guestSrcType;

    @Schema(description = "消费科目、付款方式代码;消费科目、付款方式代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称;消费科目名称、付款方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String subType;

    @Schema(description = "金额;账务的金额,如果是冲调就为负数", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "货币单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencyUnit;

    @Schema(description = "税后金额;根据税率计算后的税后金额,消费科目才需要。", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long afterTaxFee;

    @Schema(description = "业务详情")
    private String accDetail;

    @Schema(description = "是否隐藏;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isHide;

    @Schema(description = "入账班次")
    private String shiftNo;

    @Schema(description = "入账人;操作者账号")
    private String recorder;

    @Schema(description = "入账营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "标签;标签，如：拆账，转出，并账")
    private String tags;

    @Schema(description = "是否可以冲调;0否 1是 夜审后的账务不能冲 已结账务不能冲 转出账务不能冲", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isCanRev;

    @Schema(description = "是否为冲调产生的账务;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isRev;

    @Schema(description = "冲调账号;当该条账务被冲调时，需要把冲调账务的账号记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private String revAccNo;

    @Schema(description = "是否为转出转入的账务;-1: 为转出的账务, 0:默认， 1:为转入账务", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isTurnOutIn;

    @Schema(description = "原转出账号;当该条账务为转入账时，需要记录转出的账号（no）")
    private String turnOutNo;

    @Schema(description = "原转出宾客代码;当该条账务为转入账时，需要记录转出的宾客代码")
    private String turnOutTogetherCode;

    @Schema(description = "原转出房号-姓名;当该条账务为转入账时，需要记录转出的房号和姓名，用-隔开")
    private String turnOutRNo;

    @Schema(description = "接收账号-宾客代码;当该条为转出账时，需要记录转入的账号(no)")
    private String turnInNo;

    @Schema(description = "接收宾客代码;当该条为转出账时，需要记录转入的宾客代码")
    private String turnInTogetherCode;

    @Schema(description = "接收房号-姓名;当该条为转出账时，需要记录转入的房号和姓名，用-隔开")
    private String turnInRNo;

    @Schema(description = "是否为拆了的帐;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isSplit;

    @Schema(description = "拆账后原账号;当该条为拆账账务时，需要记录被拆的账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String originalSplitAccNo;

    @Schema(description = "结账号;标识同一次结账的账务")
    private String payNo;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "结账营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate payBizDate;

    @Schema(description = "结账时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime payTime;

    @Schema(description = "结账人")
    private String payer;

    @Schema(description = "计费周期开始")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime priceStartTime;

    @Schema(description = "计费周期结束")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime priceEndTime;

    @Schema(description = "间夜数;只有房账才有间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "付款码;付款时的码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String payCode;

    @Schema(description = "银行类型;建设银行、招商银行....", example = "2")
    private String bankType;

    @Schema(description = "银行卡号")
    private String bankCardNo;

    @Schema(description = "预授权有效日期;当支付方式为预授权时有效")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate validDate;

    @Schema(description = "挂账类型;0：不是挂账账务，agent:中介、protocol:单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String creditTargetType;

    @Schema(description = "针对哪条账务进行退款;针对哪条账务进行退款（微信支付宝退款时有效）")
    private String refundAccNo;

    @Schema(description = "外部订单号;第三方支付成功后返回的订单号，退款时需要传这个订单号")
    private String outOrderNo;

    @Schema(description = "是否为预订押金转订单产生的账;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isConvertDeposit;

    @Schema(description = "是否已核销;0:否 1：是；当银行卡、支票付款的账需要财务去查账并做核销操作", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isVerify;

    @Schema(description = "数据更新所属班次")
    private String handleShiftNo;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "是否集团账务；0:门店 1:集团")
    private String isGAcc;

    @Schema(description = "是否已退款账务；0:门店 1:集团")
    private String isRefunded;

    @Schema(description = "是否显示退款按钮；0:门店 1:集团")
    private Boolean isRefundButtonVisible;

}