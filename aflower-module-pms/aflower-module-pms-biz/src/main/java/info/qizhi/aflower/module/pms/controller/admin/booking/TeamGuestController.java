package info.qizhi.aflower.module.pms.controller.admin.booking;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamGuestPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamGuestRespVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.team.TeamGuestSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.booking.TeamGuestDO;
import info.qizhi.aflower.module.pms.service.booking.TeamGuestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 团队客人")
@RestController
@RequestMapping("/pms/team-guest")
@Validated
public class TeamGuestController {

    @Resource
    private TeamGuestService teamGuestService;

    @PostMapping("/create")
    @Operation(summary = "创建团队客人")
    @PreAuthorize("@ss.hasPermission('pms:team-guest:create')")
    public CommonResult<Long> createTeamGuest(@Valid @RequestBody TeamGuestSaveReqVO createReqVO) {
        return success(teamGuestService.createTeamGuest(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新团队客人")
    @PreAuthorize("@ss.hasPermission('pms:team-guest:update')")
    public CommonResult<Boolean> updateTeamGuest(@Valid @RequestBody TeamGuestSaveReqVO updateReqVO) {
        teamGuestService.updateTeamGuest(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除团队客人")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:team-guest:delete')")
    public CommonResult<Boolean> deleteTeamGuest(@RequestParam("id") Long id) {
        teamGuestService.deleteTeamGuest(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得团队客人")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:team-guest:query')")
    public CommonResult<TeamGuestRespVO> getTeamGuest(@RequestParam("id") Long id) {
        TeamGuestDO teamGuest = teamGuestService.getTeamGuest(id);
        return success(BeanUtils.toBean(teamGuest, TeamGuestRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得团队客人分页")
    @PreAuthorize("@ss.hasPermission('pms:team-guest:query')")
    public CommonResult<PageResult<TeamGuestRespVO>> getTeamGuestPage(@Valid TeamGuestPageReqVO pageReqVO) {
        PageResult<TeamGuestDO> pageResult = teamGuestService.getTeamGuestPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TeamGuestRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出团队客人 Excel")
    @PreAuthorize("@ss.hasPermission('pms:team-guest:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportTeamGuestExcel(@Valid TeamGuestPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TeamGuestDO> list = teamGuestService.getTeamGuestPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "团队客人.xls", "数据", TeamGuestRespVO.class,
                        BeanUtils.toBean(list, TeamGuestRespVO.class));
    }

}