package info.qizhi.aflower.module.pms.api.arset;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.arset.dto.ArSetDTO;
import info.qizhi.aflower.module.pms.api.arset.dto.CalculateArSetDTO;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.ArSetSaveReqVO;
import info.qizhi.aflower.module.pms.service.arset.ArSetService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.enums.ArSetHandleTypeEnum.CREDIT;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class ArSetApiImpl implements ArSetApi {

    @Resource
    @Lazy
    private ArSetService arSetService;


    @Override
    public CommonResult<Boolean> updateArSet(ArSetDTO updateReqVO) {
        arSetService.updateArSet(BeanUtils.toBean(updateReqVO, ArSetSaveReqVO.class));
        return CommonResult.success(true);
    }

    @Override
    public CommonResult<String> createArSet(ArSetDTO createReqVO) {
        return CommonResult.success(arSetService.createArSet(BeanUtils.toBean(createReqVO, ArSetSaveReqVO.class)));
    }

    @Override
    public CommonResult<ArSetDTO> getArSet(String arSetCode,String gcode) {
        return CommonResult.success(BeanUtils.toBean(arSetService.getArSet(arSetCode,gcode), ArSetDTO.class));
    }

    @Override
    public CommonResult<Boolean> calculateArSet(CalculateArSetDTO reqVO) {
        arSetService.calculateArSet(reqVO.getGcode(), reqVO.getArSetCode(), CREDIT, reqVO.getFee());
        return CommonResult.success(true);
    }

}
