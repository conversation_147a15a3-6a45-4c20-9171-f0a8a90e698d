package info.qizhi.aflower.module.pms.controller.admin.account;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.AccountStatusEnum;
import info.qizhi.aflower.framework.common.enums.DictTypeEnum;
import info.qizhi.aflower.framework.common.enums.TimeConstants;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.idempotent.core.annotation.Idempotent;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.AccountChooseReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.service.account.AccountService;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.ACCOUNT_STATUS_NOT_UNCLOSED;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 账务")
@RestController
@RequestMapping("/pms/account")
@Validated
public class AccountController {

    @Resource
    private AccountService accountService;
    @Resource
    private DictDataApi dictDataApi;

    @PostMapping("/pay")
    @Operation(summary = "付款、预授权操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<String> createPayAccount(@Valid @RequestBody PayAccountSaveReqVO createReqVO) {
        return success(accountService.pay(createReqVO));
    }

    @PostMapping("/batch/pay")
    @Operation(summary = "批量付款操作(暂时只适用优惠券类型)")
    @PreAuthorize("@ss.hasPermission('pms:account:create')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<String> batchCreatePayAccount(@Valid @RequestBody BatchPayAccountSaveReqVO createReqVO) {
        return success(accountService.batchPay(createReqVO));
    }

    @PostMapping("/consume")
    @Operation(summary = "消费操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:consume')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> createConsumeAccount(@Valid @RequestBody ConsumeAccountSaveReqVO createReqVO) {
        accountService.consume(createReqVO);
        return success(true);
    }

    @PostMapping("/refund-good")
    @Operation(summary = "退货操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:consume')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> refundGood(@Valid @RequestBody ConsumeRefundGoodReqVO createReqVO) {
        accountService.refundGood(createReqVO);
        return success(true);
    }

    @GetMapping("/get-refund-good")
    @Operation(summary = "获得可退货的商品数量")
    @PreAuthorize("@ss.hasPermission('pms:account:create:consume')")
    public CommonResult<List<ConsumeGoodRespVO>> getRefundGood(@Valid ConsumeGoodReqVO reqVO) {
        List<ConsumeGoodRespVO> refundGoods = accountService.getRefundGood(reqVO);
        return success(refundGoods);
    }

    @PutMapping("/split")
    @Operation(summary = "拆账操作")
    @PreAuthorize("@ss.hasPermission('pms:account:update:split')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> splitAccount(@Valid @RequestBody SplitAccountReqVO reqVO) {
        accountService.splitAccount(reqVO);
        return success(true);
    }

    @PostMapping("/transfer")
    @Operation(summary = "转账操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:transfer')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> transferAccount(@Valid @RequestBody TransferOutAccountReqVO createReqVO) {
        accountService.transferAccount(createReqVO);
        return success(true);
    }

    @PostMapping("/red")
    @Operation(summary = "冲账操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:red')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> redAccount(@Valid @RequestBody RedAccountReqVO r) {
        accountService.redAccount(r);
        return success(true);
    }

    @PostMapping("/refund")
    @Operation(summary = "退款操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:refund')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> refundAccount(@Valid @RequestBody RefundAccountReqVO r) {
        accountService.refundAccount(r);
        return success(true);
    }

    @GetMapping("/stat-account-refund")
    @Operation(summary = "统计退款账务金额")
    @PreAuthorize("@ss.hasPermission('pms:account:create:refund')")
    public CommonResult<AccountStatRefundRespVO> getAccountStatRefund(@Valid AccountChooseReqVO accountChooseReqVO) {
        AccountStatRefundRespVO accountStatRefund=accountService.getAccountStatRefund(accountChooseReqVO);
        return success(accountStatRefund);
    }



    @PostMapping("/validate-red")
    @Operation(summary = "批量冲调前的验证，是否存在多条扫码付的账务", description = "扫码付的账务只能单条冲调，因为需要调用退款接口")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<Boolean> validateMoreRedScanGanAccount(@Valid @RequestBody AccountSelectedReqVO r) {
        accountService.validateMoreRedScanGanAccount(r);
        return success(true);
    }

    @GetMapping("/get-remark")
    @Operation(summary = "获取账务备注信息")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", example = "1", required = true),
            @Parameter(name = "hcode", description = "酒店代码", example = "1", required = true),
            @Parameter(name = "accNo", description = "账务号", required = true)
    })
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<AccountRemarkRespVO> getAccountRemark(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("accNo") String accNo) {
        AccountDO account = accountService.getAccount(new AccountReqVO().setGcode(gcode).setHcode(hcode).setAccNo(accNo));
        return success(BeanUtils.toBean(account, AccountRemarkRespVO.class));
    }

    @PutMapping("/update-remark")
    @Operation(summary = "更新账务备注")
    @PreAuthorize("@ss.hasPermission('pms:account:update:update-remark')")
    public CommonResult<Boolean> updateRemark(@Valid @RequestBody AccountRemarkSaveReqVO reqVO) {
        accountService.updateRemark(reqVO);
        return success(true);
    }

    /**
     * 接待： 所有房间账务  no: 订单号列表, noType: orderList
     * 团队接待：所有账务，包括房间和团队的账务  no: teamCode noType: teamReception
     * 团队主单：团队主单账务 no: teamCode noType: teamMain
     * 房间：房间下账务 no: orderNo noType: room
     * 客人：客人账务 no: orderNo noType: order
     * @param noList
     * @return
     */
    @GetMapping("/stat-account-by-no")
    @Operation(summary = "统计单号的账务(订单详情账务统计数据)")
    @Parameters({
            @Parameter(name = "noList", description = "单号列表", required = true),
    })
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<OrderAccountStatRespVO> getOrderAccountStatByNo(@RequestParam("noList") List<String> noList) {
        return success(BeanUtils.toBean(accountService.getOrderAccountStatByNo(noList), OrderAccountStatRespVO.class));
    }

    @GetMapping("/stat-account-by-together-code")
    @Operation(summary = "统计宾客的账务(订单详情账务统计数据)")
    @Parameter(name = "no", description = "宾客代码", required = true)
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<OrderAccountStatRespVO> getOrderAccountStatByTogetherCode(@RequestParam("no") String no) {
        return success(BeanUtils.toBean(accountService.getOrderAccountStatByTogetherCode(no), OrderAccountStatRespVO.class));
    }

    @GetMapping("/stat-account-by-notype")
    @Operation(summary = "统计账务(订单详情账务统计数据,根据单号类型统计)")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", example = "1", required = true),
            @Parameter(name = "hcode", description = "酒店代码", example = "1", required = true),
            @Parameter(name = "noList", description = "单号列表", required = true),
            @Parameter(name = "noType", description = "单号类型", required = true)
    })
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<OrderAccountStatRespVO> getOrderAccountStatByNoType(@RequestParam("gcode") String gcode,
                                                                            @RequestParam("hcode") String hcode,
                                                                            @RequestParam("noList") List<String> noList,
                                                                            @RequestParam("noType") String noType) {
        return success(BeanUtils.toBean(accountService.getOrderAccountStatByNo(gcode, hcode, noList, noType), OrderAccountStatRespVO.class));
    }


    @GetMapping("/get-list-by-accnos")
    @Operation(summary = "根据账单号获取账务列表(结账、部分结账页面调用)")
    @PreAuthorize("@ss.hasPermission('pms:account:create:part-close-account')")
    public CommonResult<PartCloseAccountRespVO> getPartCloseAccount(@Valid AccountSelectPartCloseReqVO reqVO) {
        PartCloseAccountRespVO partCloseAccount = accountService.getPartCloseAccount(reqVO);
        return success(partCloseAccount);
    }

    @PostMapping("/stat-close-account")
    @Operation(summary = "统计付款金额(完成部分结账界面需要)")
    @PreAuthorize("@ss.hasPermission('pms:account:create:part-close-account')")
    public CommonResult<StatCheckOutAccountRespVO> statCloseAccount(@Valid @RequestBody FinishCloseAccountReqVO reqVO) {
        StatCheckOutAccountRespVO statCheckOutAccountRespVO = accountService.statCloseAccount(reqVO);
        return success(statCheckOutAccountRespVO);
    }

    @PostMapping("/part-close-account")
    @Operation(summary = "结账、部分结账操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:part-close-account')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复结账", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> partCloseAccount(@Valid @RequestBody PartCloseAccountReqVO createReqVO) {
        accountService.partCloseAccount(createReqVO);
        return success(true);
    }

    /** 前端传过来的值
     * 账务
     * 接待： 所有房间账务  no: bindCode noType: orderList togetherCode: 第一个订单的第一个客单代码 state: ''
     * 团队接待：所有账务，包括房间和团队的账务  no: teamCode, noType: teamReception, togetherCode: teamCode, state: ''
     * 团队主单：团队主单账务 no: teamCode, noType: teamMain, togetherCode: teamCode, state: ''
     * 房间：房间下账务 no: orderNo noType: room
     * 客人：客人账务 no: orderNo, noType: order, togetherCode: togetherCode, state: 客单状态
     * 预订单: 预订单单账务 no: bookNo, noType: book or team, togetherCode: bookNo, state: 预订单状态
     */
    @GetMapping("/get-record-account")
    @Operation(summary = "获取入账账号")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "酒店代码", required = true),
            @Parameter(name = "noType", description = "单号类型", required = true),
            @Parameter(name = "no", description = "单号列表", required = true)
    })
    public CommonResult<List<TogetherAccRespVO>> getRecordAccountList(@RequestParam("gcode") String gcode,
                                                                   @RequestParam("hcode") String hcode,
                                                                   @RequestParam("noType") String noType,
                                                                   @RequestParam("no") List<String> no) {
        List<TogetherAccRespVO> account = accountService.getRecordAccountList(gcode, hcode, noType, no);
        return success(account);
    }

    // ------>>> 结账退房、挂账退房
    @GetMapping("/get-checkout-order-list")
    @Operation(summary = "结账退房-结账(挂账)账号列表")
    @Parameters({
            @Parameter(name = "no", description = "单号，根据订单类型 可能是 客单号、预订单号、团队代码", required = true),
            @Parameter(name = "noType", description = "订单类型", required = false),
            @Parameter(name = "all", description = "是否获取所有,1是，0或者空 否", required = false)
    })
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out, pms:account:create:credit-check-out')")
    public CommonResult<OrderCheckOutGuestRespVO> getCheckOutGuestList(@RequestParam("no") String no,
                                                                       @RequestParam(value = "noType", required = false) String noType,
                                                                       @RequestParam(value = "all", required = false) String all) {
        OrderCheckOutGuestRespVO orderCheckOutGuestRespVO = accountService.getCheckOutGuestList(no, noType, all);
        return success(orderCheckOutGuestRespVO);
    }

    @PostMapping("/get-confirm-room-fee")
    @Operation(summary = "结账退房-计算房费")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out, pms:account:create:credit-check-out')")
    public CommonResult<List<ConfirmCheckOutAccountRespVO>> getConfirmRoomFeeList(@Valid @RequestBody ConfirmCheckOutAccountReqVO reqVO) {
        List<ConfirmCheckOutAccountRespVO> confirms = accountService.getConfirmRoomFeeList(reqVO);
        return success(confirms);
    }

    @PostMapping("/confirm-room-fee")
    @Operation(summary = "结账退房-确认生成房费")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out, pms:account:create:credit-check-out')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> confirmRoomFee(@Valid @RequestBody ConfirmRoomFeeReqVO reqVO) {
        accountService.confirmRoomFee(reqVO);
        return success(true);
    }

    @PostMapping("/get-confirm-preauth")
    @Operation(summary = "结账退房-需要确认的预授权列表")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out')")
    public CommonResult<List<ConfirmCheckOutPreAuthRespVO>> getConfirmPreAuthList(@Valid @RequestBody ConfirmPreAuthListReqVO reqVO) {
        List<ConfirmCheckOutPreAuthRespVO> accounts = accountService.getConfirmPreAuthList(reqVO);
        return success(accounts);
    }

    @PostMapping("/confirm-preauth")
    @Operation(summary = "结账退房-确认预授权（收款金额）")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out')")
    public CommonResult<Boolean> confirmPreAuth(@Valid @RequestBody ConfirmPreAuthReqVO r) {
        accountService.confirmPreAuth(r);
        return success(true);
    }

    @PutMapping("/cancel-preauth")
    @Operation(summary = "取消预授权")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out, pms:account:create:credit-check-out')")
    public CommonResult<Boolean> cancelPreAuth(@Valid @RequestBody CancelPreAuthReqVO r) {
        accountService.cancelPreAuth(r.getAccNo());
        return success(true);
    }

    @PostMapping("/stat-account")
    @Operation(summary = "统计付款金额(完成结账界面需要)", description = "第二步确认的房费和第一步选中的账号的账务合计统计付款")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out, pms:account:create:credit-check-out')")
    public CommonResult<StatCheckOutAccountRespVO> statCheckOutAccount(@Valid @RequestBody FinishCheckOutAccountReqVO reqVO) {
        StatCheckOutAccountRespVO statCheckOutAccountRespVO = accountService.statCheckOutAccount(reqVO);
        return success(statCheckOutAccountRespVO);
    }

    @PostMapping("/judge-ar-account")
    @Operation(summary = "判断是否有AR账务可挂")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out')")
    public CommonResult<Boolean> judgeARAccount(@Valid @RequestBody FinishCheckOutAccountReqVO reqVO) {
        accountService.judgeARAccount(reqVO);
        return success(true);
    }

    @PostMapping("/pay-check-out")
    @Operation(summary = "结账退房")
    @PreAuthorize("@ss.hasPermission('pms:account:create:pay-check-out')")
    public CommonResult<Boolean> payCheckOut(@Valid @RequestBody PayCheckOutAccountReqVO reqVO) {
        accountService.payCheckOut(reqVO);
        return success(true);
    }

    @PostMapping("/credit-check-out")
    @Operation(summary = "挂账退房")
    @PreAuthorize("@ss.hasPermission('pms:account:create:credit-check-out')")
    public CommonResult<Boolean> creditCheckOut(@Valid @RequestBody ConfirmRoomFeeReqVO reqVO) {
        accountService.creditCheckOut(reqVO);
        return success(true);
    }
    // <<<-------结账退房、挂账退房

    @PostMapping("/create-accounts")
    @Operation(summary = "批量创建账务")
    @PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> createAccounts(@Valid @RequestBody List<AccountSaveReqVO> createAccountList) {
        return success(accountService.createAccounts(createAccountList));
    }

    @GetMapping("/get-selected-list")
    @Operation(summary = "获得选中的账务列表(批量冲账、转账)")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<AccountRespVO>> getSelectAccountList(@Valid AccountSelectedReqVO reqVO) {
        List<AccountDO> accounts = accountService.getAccountList(new AccountListReqVO().setAccNos(reqVO.getAccNos())
                                                                                       .setState(AccountStatusEnum.UNCLOSED.getCode())
                                                                                       .setGcode(reqVO.getGcode())
                                                                                       .setHcode(reqVO.getHcode()));
        return success(buildAccountList(accounts));
    }

    private List<AccountRespVO> buildAccountList(List<AccountDO> accounts) {
        if (CollUtil.isEmpty(accounts)) {
            return CollUtil.newArrayList();
        }
        Map<String, String> dictMap = dictDataApi.getDictDataLabelMap(List.of(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode()));
        List<AccountRespVO> accountRespVOS = BeanUtils.toBean(accounts, AccountRespVO.class);
        accountRespVOS.forEach(o -> o.setSubName(dictMap.getOrDefault(o.getSubCode(), "")));
        return accountRespVOS;
    }


    @GetMapping("/get-list")
    @Operation(summary = "获得账务列表")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<AccountRespVO>> getAccountList(@Valid AccountListReqVO reqVO) {
        List<AccountDO> account = accountService.getAccountList(reqVO);
        return success(BeanUtils.toBean(account, AccountRespVO.class));
    }

    @GetMapping("/get-close-account")
    @Operation(summary = "获得撤销的结账列表")
    @PreAuthorize("@ss.hasPermission('pms:account:create:undo-pay')")
    public CommonResult<List<CloseAccountRespVO>> getCloseAccountList(@Valid CloseAccountRepVO r) {
        List<CloseAccountRespVO> account = accountService.getCloseAccountList(r);
        return success(account);
    }

    @GetMapping("/get-account-by-payno")
    @Operation(summary = "获得结账号的结账列表")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "酒店编码", required = true, example = "1024"),
            @Parameter(name = "payNo", description = "结账号", required = true, example = "1024"),
    })
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<CloseAccountPayNoRespVO>> getAccountByPayNo(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("payNo") String payNo) {
        List<CloseAccountPayNoRespVO> list = accountService.getAccountByPayNo(gcode, hcode, payNo);
        return success(list);
    }

    @PostMapping("/undo-pay")
    @Operation(summary = "撤销结账操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create:undo-pay')")
    public CommonResult<Boolean> undoPay(@Valid @RequestBody CloseAccountPayNoRepVO r) {
        accountService.undoPay(r);
        return success(true);
    }

    @GetMapping("/get-single-account-fee")
    @Operation(summary = "获得单条账务的金额")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "酒店编码", required = true, example = "1024"),
            @Parameter(name = "accNo", description = "账务号", required = true, example = "1024"),
    })
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<AccountAccNoFeeRespVO> getAccountAccNoFee(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("accNo") String accNo) {
        AccountDO account = accountService.getAccount(new AccountReqVO().setGcode(gcode).setHcode(hcode).setAccNo(accNo));
        validateAccount(account);
        return success(BeanUtils.toBean(account, AccountAccNoFeeRespVO.class));
    }

    private void validateAccount(AccountDO account) {
        if (!Objects.equals(account.getState(), AccountStatusEnum.UNCLOSED.getCode())) {
            throw exception(ACCOUNT_STATUS_NOT_UNCLOSED);
        }
    }

    @GetMapping("/get-list-for-sub")
    @Operation(summary = "获得账务列表中科目列表")
    public CommonResult<List<AccountSubRespVO>> getAccountSubList(@Valid AccountSubReqVO reqVO) {
        List<AccountSubRespVO> subList = accountService.getAccountSubList(reqVO);
        return success(subList);
    }

    @GetMapping("/page")
    @Operation(summary = "获得账务分页(基于宾客代码查询)")
    @PreAuthorize("@ss.hasPermission('pms:account:query:page')")
    public CommonResult<PageResult<AccountRespVO>> getAccountPageByTogetherCodes(@Valid AccountPageReq2VO pageReqVO) {
        PageResult<AccountRespVO> pageResult = accountService.getAccountPageByTogetherCodes(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page-by-nos")
    @Operation(summary = "获得账务分页(基于账单号查询)")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<PageResult<AccountRespVO>> getAccountPageByNos(@Valid AccountPageReq2VO pageReqVO) {
        PageResult<AccountRespVO> pageResult = accountService.getAccountPageByNos(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/transaction-page")
    @Operation(summary = "获得交易分页(基于账单号查询)")
    public CommonResult<PageResult<AccountRespVO>> getTransactionPage(TransactionPageReqVO reqVO) {
        PageResult<AccountRespVO> result = accountService.getTransactionPage(reqVO);
        return success(result);
    }

    @GetMapping("/deposit")
    @Operation(summary = "获得人民币押金")
    public CommonResult<DepositAccountRespVO> getDeposit(AccountReqVO reqVO) {
        DepositAccountRespVO result = accountService.getDeposit(reqVO);
        return success(result);
    }

    @PostMapping("/recording")
    @Operation(summary = "补录操作")
    @PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<Boolean> recordingAccount(@Valid @RequestBody RecordingAccountReqVO reqVO) {
        accountService.recordingAccount(reqVO);
        return success(true);
    }

    @GetMapping("/get-recording")
    @Operation(summary = "补录查询")
    @PreAuthorize("@ss.hasPermission('pms:account:create')")
    public CommonResult<RecordingAccountRespVO> getRecordingAccount(@Valid RecordingAccountQueryReqVO reqVO) {
        return success(accountService.getRecordingAccount(reqVO));
    }

    @GetMapping("/room-fee-list")
    @Operation(summary = "获得每日房费列表")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<AccountRoomFeeRespVO>> getAccountRoomFeeList(@Valid AccountListReqVO reqVO) {
        List<AccountRoomFeeRespVO> account = accountService.getAccountRoomFeeList(reqVO);
        return success(account);
    }

    @GetMapping("/get-reminder-list")
    @Operation(summary = "获得催账列表")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<List<AccountReminderRespVO>> getReminderList(@Valid ReminderReqVO pageReqVO) {
        List<AccountReminderRespVO> pageResult = accountService.getReminderList(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/get-reminder-count")
    @Operation(summary = "统计预计欠费的订单数")
    @PreAuthorize("@ss.hasPermission('pms:account:query')")
    public CommonResult<ReminderCountRespVO> getReminderCount(@Valid ReminderReqVO pageReqVO) {
        ReminderCountRespVO countResult = accountService.getReminderCount(pageReqVO);
        return success(countResult);
    }

    @GetMapping("/handover-report")
    @Operation(summary = "交班报表")
    @PreAuthorize("@ss.hasPermission('pms:account:query:handover-report')")
    public CommonResult<HandoverReportRespVO> handoverReport(@Valid HandoverReportReqVO reqVO) {
        return success(accountService.handoverReport(reqVO));
    }

    @GetMapping("/handover-report-cash-realization")
    @Operation(summary = "交班报表(收付实现制)")
    @PreAuthorize("@ss.hasPermission('pms:account:query:handover-report')")
    public CommonResult<HandoverReportCashRealizationRespVO> handoverReportCashRealization(@Valid HandoverReportCashRealizationReqVO reqVO) {
        return success(accountService.handoverReportCashRealization(reqVO));
    }

}

