package info.qizhi.aflower.module.pms.controller.admin.serviceintegration;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.ServiceTypeEnum;
import info.qizhi.aflower.framework.common.enums.SolutionTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.serviceintegration.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.serviceintegration.ServiceIntegrationDO;
import info.qizhi.aflower.module.pms.service.serviceintegration.ServiceIntegrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 对接服务管理")
@RestController
@RequestMapping("/pms/service-integration")
@Validated
public class ServiceIntegrationController {
    @Resource
    private ServiceIntegrationService serviceIntegrationService;

    @PostMapping("/create")
    @Operation(summary = "创建对接服务")
    public CommonResult<Long> createServiceIntegration(@Valid @RequestBody ServiceIntegrationSaveVO createReqVO) {
        return success(serviceIntegrationService.createServiceIntegration(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新对接服务")
    public CommonResult<Boolean> updateServiceIntegration(@Valid @RequestBody ServiceIntegrationSaveVO updateReqVO) {
        serviceIntegrationService.updateServiceIntegration(updateReqVO);
        return success(true);
    }


    @GetMapping("/ota-list")
    @Operation(summary = "获得OTA对接服务列表")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码"),
            @Parameter(name = "hcode", description = "酒店编码"),
    })
    public CommonResult<List<ServiceIntegrationOtaRespVO>> getOtaServiceIntegration(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        List<ServiceIntegrationDO> serviceIntegration = serviceIntegrationService.getServiceIntegrationList(new ServiceIntegrationReqVO()
                .setGcode(gcode)
                .setHcode(hcode)
                .setType(ServiceTypeEnum.OTA.getCode()));
        return success(BeanUtils.toBean(serviceIntegration, ServiceIntegrationOtaRespVO.class));
    }


    @GetMapping("/pay-list")
    @Operation(summary = "获得支付对接服务列表")
    public CommonResult<List<ServiceIntegrationPaymentRespVO>> getPayServiceIntegration(@Valid ServiceIntegrationReqVO reqVO) {
        reqVO.setType(ServiceTypeEnum.PAYMENT.getCode());
        List<ServiceIntegrationDO> serviceIntegrationList = serviceIntegrationService.getServiceIntegrationList(reqVO);
        return success(BeanUtils.toBean(serviceIntegrationList, ServiceIntegrationPaymentRespVO.class));
    }

    @GetMapping("/invoice")
    @Operation(summary = "获得数电发票服务")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码"),
            @Parameter(name = "hcode", description = "酒店编码"),
    })
    public CommonResult<ServiceIntegrationRespVO> getInvoiceServiceIntegration(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(gcode, hcode, ServiceTypeEnum.INVOICE.getCode());
        return success(BeanUtils.toBean(serviceIntegration, ServiceIntegrationRespVO.class));
    }

    @GetMapping("/psb")
    @Operation(summary = "获得PSB服务")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码"),
            @Parameter(name = "hcode", description = "酒店编码"),
    })
    public CommonResult<ServiceIntegrationRespVO> getPsbServiceIntegration(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(gcode, hcode, ServiceTypeEnum.PSB.getCode());
        return success(BeanUtils.toBean(serviceIntegration, ServiceIntegrationRespVO.class));
    }


    @GetMapping("/list")
    @Operation(summary = "获得门店服务列表")
    public CommonResult<List<ServiceIntegrationRespVO>> getServiceIntegrationList(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        List<ServiceIntegrationDO> serviceIntegrationList = serviceIntegrationService.getServiceIntegrationList(new ServiceIntegrationReqVO()
                .setGcode(gcode)
                .setHcode(hcode));
        return success(BeanUtils.toBean(serviceIntegrationList, ServiceIntegrationRespVO.class));
    }
}
