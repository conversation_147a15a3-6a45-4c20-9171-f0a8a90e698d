package info.qizhi.aflower.module.pms.controller.admin.customer.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 客历分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CustomerPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "姓名", example = "张三")
    private String name;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "黑名单;0正常 1拉黑")
    private String isBlack;

    @Schema(description = "入住次数", example = "3")
    private Integer checkinNum;

    @Schema(description = "是否会员 0: 否 1: 是")
    private String isMember;

    @Schema(description = "大于,小于 0: 大于 1: 小于")
    @InStringEnum(value = BooleanEnum.class,message = "{gtOrlt.enum}")
    private String gtOrlt;




}