package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.userparam.UserParamConfigRoomPanelRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.userparam.UserParamConfigSaveReqVO;
import info.qizhi.aflower.module.pms.service.config.UserParamConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 用户参数设置")
@RestController
@RequestMapping("/pms/user-param-config")
@Validated
public class UserParamConfigController {

    @Resource
    private UserParamConfigService userParamConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建用户参数设置")
    @PreAuthorize("@ss.hasPermission('pms:user-param-config:create')")
    public CommonResult<Long> createUserParamConfig(@Valid @RequestBody UserParamConfigSaveReqVO createReqVO) {
        return success(userParamConfigService.createUserParamConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户参数设置")
    @PreAuthorize("@ss.hasPermission('pms:user-param-config:update')")
    public CommonResult<Boolean> updateUserParamConfig(@Valid @RequestBody UserParamConfigSaveReqVO updateReqVO) {
        userParamConfigService.updateUserParamConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get/panel")
    @Operation(summary = "获得用户参数设置-房态看板")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    @Parameter(name = "username", description = "账号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:user-param-config:query')")
    public CommonResult<UserParamConfigRoomPanelRespVO> getUserParamConfigRoomPanel(@RequestParam("gcode") String gcode, @RequestParam("username") String username) {
        UserParamConfigRoomPanelRespVO userParamConfig = userParamConfigService.getUserParamConfigRoomPanel(gcode, username);
        return success(userParamConfig);
    }


}