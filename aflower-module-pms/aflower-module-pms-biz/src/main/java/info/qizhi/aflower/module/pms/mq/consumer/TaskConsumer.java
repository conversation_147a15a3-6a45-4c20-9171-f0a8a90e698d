package info.qizhi.aflower.module.pms.mq.consumer;

import info.qizhi.aflower.framework.common.enums.RoomCleanTaskEnum;
import info.qizhi.aflower.framework.common.enums.RoomCleanTypeEnum;
import info.qizhi.aflower.framework.common.enums.TaskTypeEnum;
import info.qizhi.aflower.module.pms.controller.admin.task.vo.TaskSaveReqVO;
import info.qizhi.aflower.module.pms.mq.message.TaskMessage;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import info.qizhi.aflower.module.pms.service.task.TaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 任务消费者
 * @Version: 1.0
 */
@Component
@RabbitListener(queues = TaskMessage.QUEUE) // 重点：添加 @RabbitListener 注解，声明消费的 queue
@Slf4j
public class TaskConsumer {
    @Resource
    private TaskService taskService;
    @Resource
    private GeneralConfigService generalService;

    /**
     * 消费消息（执行夜审）
     *
     * @param message 消息
     */
    @RabbitHandler // 重点：添加 @RabbitHandler 注解，实现消息的消费
    public void onMessage(TaskMessage message) {
       /* List<GeneralConfigDO> generalConfigList = generalService.getGeneralConfigList(new GeneralConfigReqVO()
                .setGcode(message.getGcode()).setHcode(message.getHcode())
                .setType(GeneralConfigTypeEnum.ROOM_CLEAN_TASK.getCode()));
        Map<String, GeneralConfigDO> generalConfigDOMap = CollectionUtils.convertMap(generalConfigList, GeneralConfigDO::getCode);*/

        RoomCleanTaskEnum roomCleanTaskEnum = RoomCleanTaskEnum.fromCode(message.getType());
        switch (roomCleanTaskEnum) {
            case RoomCleanTaskEnum.CHECK_OUT:
                //GeneralConfigDO checckOutGeneralConfigDO = generalConfigDOMap.get(RoomCleanTaskEnum.CHECK_OUT.getCode());
                taskService.autoCreateTask(buildTaskSaveReqVO(message, RoomCleanTypeEnum.RETURN_PLUNDER.getCode()));
                break;
            case RoomCleanTaskEnum.CONTINUE:
                taskService.autoCreateTask(buildTaskSaveReqVO(message, RoomCleanTypeEnum.CONTINUE_PLUNDER.getCode()));
                break;
            case RoomCleanTaskEnum.CHANGE_ROOM:
                taskService.autoCreateTask(buildTaskSaveReqVO(message, RoomCleanTypeEnum.RETURN_PLUNDER.getCode()));
                break;
            case RoomCleanTaskEnum.FINISH_REPAIR:
                taskService.autoCreateTask(buildTaskSaveReqVO(message, RoomCleanTypeEnum.SPECIAL_PLUNDER.getCode()));
                break;
        }

    }

    private TaskSaveReqVO buildTaskSaveReqVO(TaskMessage message, String type) {
        return new TaskSaveReqVO().setGcode(message.getGcode())
                .setHcode(message.getHcode())
                .setTaskType(TaskTypeEnum.ROOM_CLEAN_TYPE.getCode())
                .setType(type)
                .setRCodes(message.getRCodes());
    }


}
