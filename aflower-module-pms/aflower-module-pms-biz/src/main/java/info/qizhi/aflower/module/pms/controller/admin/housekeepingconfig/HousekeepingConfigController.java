package info.qizhi.aflower.module.pms.controller.admin.housekeepingconfig;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.housekeepingconfig.vo.HousekeepingConfigRespVO;
import info.qizhi.aflower.module.pms.controller.admin.housekeepingconfig.vo.HousekeepingConfigSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.housekeepingconfig.HousekeepingConfigDO;
import info.qizhi.aflower.module.pms.service.housekeepingconfig.HousekeepingConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 酒店房务配置")
@RestController
@RequestMapping("/pms/housekeeping-config")
@Validated
public class HousekeepingConfigController {

    @Resource
    private HousekeepingConfigService housekeepingConfigService;


    @PutMapping("/update")
    @Operation(summary = "更新酒店房务配置")
    @PreAuthorize("@ss.hasPermission('pms:housekeeping-config:update')")
    public CommonResult<Boolean> updateHousekeepingConfig(@Valid @RequestBody HousekeepingConfigSaveReqVO updateReqVO) {
        housekeepingConfigService.updateHousekeepingConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得酒店房务配置")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "酒店代码", required = true, example = "1024")
    })
    //@PreAuthorize("@ss.hasPermission('pms:housekeeping-config:query')")
    public CommonResult<HousekeepingConfigRespVO> getHousekeepingConfig(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        HousekeepingConfigDO housekeepingConfig = housekeepingConfigService.getHousekeepingConfig(gcode, hcode);
        return success(BeanUtils.toBean(housekeepingConfig, HousekeepingConfigRespVO.class));
    }

}