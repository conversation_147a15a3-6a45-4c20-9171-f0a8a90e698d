package info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 现付账冲调 Request VO")
@Data
public class CashBillRevocationReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "现付账订单号")
    @NotEmpty(message = "{cashBillOrderNumber.notempty}")
    private String cashBillOrderNo;

    @Schema(description = "冲调原因")
    @NotEmpty(message = "冲调原因不能为空")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}
