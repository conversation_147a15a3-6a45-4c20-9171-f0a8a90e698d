package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.strategy;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 佣金策略新增/修改 Request VO")
@Data
public class BrokerageStrategySaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10503")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "是否集团佣金策略;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isG.notempty}")
    private String isG;

    @Schema(description = "策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String strategyCode;

    @Schema(description = "策略名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "{strategyName.notempty}")
    @Size(max = 32, message = "{strategyName.size}")
    private String strategyName;

    @Schema(description = "公司类型;0:中介 1:协议单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{companyType.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{companyType.invalid}")
    private String companyType;

    @Schema(description = "是否集团房型;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isGrt.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isGrt.invalid}")
    private String isGrt;

    @Schema(description = "房型代码;json，房型代码 存储字符串数组")
    @NotNull(message = "{roomTypes.notempty}")
    private List<String> rts;

    @Schema(description = "返佣名称;0:间夜定额返佣 1:百分比返佣", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{brokerageType.notempty}")
    private String brokerageType;

    @Schema(description = "目标值;返佣的值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{brokerageValue.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long brokerageValue;

    @Schema(description = "渠道代码;json,存储多个渠道 存储字符串数组")
    @NotNull(message = "{channelCode.notempty}")
    private List<String> channels;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isEnable.notempty}")
    private String isEnable;

    @Schema(description = "佣金等级代码")
    private String brokerageLevelCode;

    @Schema(description = "佣金策略关联酒店列表")
    private List<String> hotels;

}