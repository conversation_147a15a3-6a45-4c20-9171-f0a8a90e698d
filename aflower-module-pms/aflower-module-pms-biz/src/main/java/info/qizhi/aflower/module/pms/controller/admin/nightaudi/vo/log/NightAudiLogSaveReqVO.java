package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;

import jakarta.validation.constraints.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 夜审记录新增/修改 Request VO")
@Data
public class NightAudiLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3963")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "营业日yyyy-MM-dd", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "营业日yyyy-MM-dd不能为空")
    private LocalDate bizDate;

    @Schema(description = "夜审开始时间（也是断账时间）精确到时分秒", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "夜审开始时间（也是断账时间）精确到时分秒不能为空")
    private LocalDateTime startTime;

    @Schema(description = "夜审结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "夜审结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "自然日（yyyy-MM-dd）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "自然日（yyyy-MM-dd）不能为空")
    private LocalDate naturalDate;

    @Schema(description = "夜审是否成功;0失败 1成功", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "夜审是否成功;0失败 1成功不能为空")
    private String isSuccess;

    @Schema(description = "备注", example = "随便")
    private String remark;

}