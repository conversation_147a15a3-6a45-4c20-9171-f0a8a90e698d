package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.framework.common.enums.ParamConfigTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam.*;
import info.qizhi.aflower.module.pms.dal.dataobject.config.HotelParamConfigDO;
import info.qizhi.aflower.module.pms.service.config.HotelParamConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 门店参数设置")
@RestController
@RequestMapping("/pms/hotel-param-config")
@Validated
public class HotelParamConfigController {

    @Resource
    private HotelParamConfigService hotelParamConfigService;

    @PostMapping("/create")
    @Operation(summary = "创建门店参数设置")
    @PreAuthorize("@ss.hasPermission('pms:hotel-param-config:create')")
    public CommonResult<Long> createHotelParamConfig(@Valid @RequestBody HotelParamConfigSaveReqVO createReqVO) {
        Map<String, Object> mp = new HashMap<>();
        mp.put(createReqVO.getParamType(), createReqVO.getValue());
        createReqVO.setValue(mp);
        return success(hotelParamConfigService.createHotelParamConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店参数设置")
    @PreAuthorize("@ss.hasPermission('pms:hotel-param-config:update, pms:hotel-param-config:update:shiftmode," +
            "pms:hotel-param-config:update:deposit, pms:hotel-param-config:update:breakfast-ticket')")
    public CommonResult<Boolean> updateHotelParamConfig(@Valid @RequestBody HotelParamConfigSaveReqVO updateReqVO) {
        Map<String, Object> mp = new HashMap<>();
        mp.put(updateReqVO.getParamType(), updateReqVO.getValue());
        updateReqVO.setValue(mp);
        hotelParamConfigService.updateHotelParamConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get/front")
    @Operation(summary = "获得门店参数设置-前台")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true)
    })
    //@PreAuthorize("@ss.hasPermission('pms:hotel-param-config:query:get:front')")
    public CommonResult<HotelParamConfigFrontRespVO> getHotelParamConfigFront(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        HotelParamConfigDO hotelParamConfig = getHotelParamConfig(gcode, hcode, ParamConfigTypeEnum.PARAM_TYPE_FRONT.getParamType());
        return success(BeanUtils.toBean(hotelParamConfig, HotelParamConfigFrontRespVO.class));
    }

    @GetMapping("/get/deposit")
    @Operation(summary = "获得门店参数设置-收押配置")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true)
    })
    //@PreAuthorize("@ss.hasPermission('pms:hotel-param-config:query:get:deposit')")
    public CommonResult<HotelParamConfigDepositRespVO> getHotelParamConfigDeposit(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        HotelParamConfigDO hotelParamConfig = getHotelParamConfig(gcode, hcode, ParamConfigTypeEnum.PARAM_TYPE_DEPOSIT.getParamType());
        HotelParamConfigDepositRespVO bean = BeanUtils.toBean(hotelParamConfig, HotelParamConfigDepositRespVO.class);
        if(bean.getValue().getOpenPay() == null){
            bean.getValue().setOpenPay("0");
            updateHotelParamConfig(BeanUtils.toBean(bean, HotelParamConfigSaveReqVO.class));
        }
        return success(bean);
    }

    @GetMapping("/get/shiftmode")
    @Operation(summary = "获得门店参数设置-班次设置.交班模式")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true)
    })
    //@PreAuthorize("@ss.hasPermission('pms:hotel-param-config:query:get:shiftmode, pms:shift-time:query')")
    public CommonResult<HotelParamConfigShiftModeRespVO> getHotelParamConfigShiftmode(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        HotelParamConfigDO hotelParamConfig = getHotelParamConfig(gcode, hcode, ParamConfigTypeEnum.PARAM_TYPE_SHIFT_MODE.getParamType());
        return success(BeanUtils.toBean(hotelParamConfig, HotelParamConfigShiftModeRespVO.class));
    }

    @GetMapping("/get/breakfast-ticket")
    @Operation(summary = "获得门店参数设置-早餐券设置")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true),
            @Parameter(name = "hcode", description = "门店代码", required = true)
    })
    //@PreAuthorize("@ss.hasPermission('pms:hotel-param-config:query:get:breakfast-ticket')")
    public CommonResult<HotelParamConfigBreakfastTicketRespVO> getHotelParamConfigBreakfastTicket(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        HotelParamConfigDO hotelParamConfig = getHotelParamConfig(gcode, hcode, ParamConfigTypeEnum.PARAM_TYPE_BREAKFAST_TICKET.getParamType());
        return success(BeanUtils.toBean(hotelParamConfig, HotelParamConfigBreakfastTicketRespVO.class));
    }

    private HotelParamConfigDO getHotelParamConfig(String gcode, String hcode, String paramType) {
        return hotelParamConfigService.getHotelParamConfig(new HotelParamConfigReqVO().setParamType(paramType)
                                                                                      .setGcode(gcode).setHcode(hcode));
    }

}