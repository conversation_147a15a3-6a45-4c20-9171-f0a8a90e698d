package info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule;

import com.baomidou.mybatisplus.annotation.TableField;
import info.qizhi.aflower.module.pms.dal.dataobject.config.MerchantTypeRuleDO;
import info.qizhi.aflower.module.pms.service.config.bo.merchanttyperule.RegisterBrokerage;
import info.qizhi.aflower.module.pms.service.config.bo.merchanttyperule.StoreBrokerage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 门店类型规则 Response VO")
@Data
public class MerchantTypeRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18853")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String merchantType;

    @Schema(description = "门店类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String merchantTypeName;

    @Schema(description = "会员信息共享;0：不共享 1：共享给集团下所有门店", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memberShare;

    @Schema(description = "余额和积分共享;0：不共享 1：共享， 直营类型时，0表示仅所有直营门店使用，1表示所有门店使用", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memberPointShare;

    @Schema(description = "会员储值金额入账到;0: 入账到集团 1:入账到门店", requiredMode = Schema.RequiredMode.REQUIRED)
    private String storeFeeRecord;

    @Schema(description = "会员储值提成和报表")
    @TableField(typeHandler = MerchantTypeRuleDO.StoreBrokerageTypeHandler.class)
    private StoreBrokerage storeBrokerage;

    @Schema(description = "会员办卡提成规则")
    @TableField(typeHandler = MerchantTypeRuleDO.RegisterBrokerageTypeHandler.class)
    private RegisterBrokerage registerBrokerage;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}