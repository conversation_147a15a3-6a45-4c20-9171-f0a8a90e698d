package info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 房扫数量汇总 Response VO")
@Data
public class RoomCleanSummaryData {
    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码")
    @JsonProperty("rtCode")
    private String rtCode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码")
    @JsonProperty("rtName")
    private String rtName;

    @Schema(description = "打扫前房态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cleanState;

    @Schema(description = "空脏房扫数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("VDSum")
    private Long VDSum;

    @Schema(description = "住脏房扫数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("ODSum")
    private Long ODSum;

    @Schema(description = "房扫数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long sum;
}
