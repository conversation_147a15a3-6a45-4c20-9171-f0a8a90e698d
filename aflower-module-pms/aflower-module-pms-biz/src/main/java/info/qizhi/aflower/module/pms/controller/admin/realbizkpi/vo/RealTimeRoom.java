package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "小程序 - 实时房态 Response VO")
@Data
public class RealTimeRoom {
    @Schema(description = "可售房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer availableRoomNum;

    @Schema(description = "今日预离数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer checkoutRoomNum;

    @Schema(description = "没有自用房的今日预离数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer noSelfcheckoutRoomNum;

    @Schema(description = "今日预抵数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer todayBookingRoomNum;

    @Schema(description = "预订未入住数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer bookingNoCheckinRoomNum;

    @Schema(description = "在住客房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer occupiedRoomNum;

    @Schema(description = "维修房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer OORoomNum;

    @Schema(description = "自用房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer selfUseRoomNum;

    @Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer totalRoomNum;

    @Schema(description = "今日入住总价格")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long inHouseTotal;

    @Schema(description = "今日离店总价格")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayCheckoutTotal;

    @Schema(description = "今日预抵总价格")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayBookingTotal;

}
