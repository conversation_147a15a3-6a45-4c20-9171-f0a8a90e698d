package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 开票信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InvoicePageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "代码")
    private String invoiceCode;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", example = "1")
    private String invoiceTitleType;

    @Schema(description = "发票抬头")
    private String invoiceTitle;

    @Schema(description = "纳税人识别号", example = "3344")
    private String taxpayerId;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", example = "1")
    private String invoiceType;

    @Schema(description = "开户银行")
    private String bank;

    @Schema(description = "开户账号")
    private String bankNo;

    @Schema(description = "企业注册地址")
    private String regAddress;

    @Schema(description = "企业注册电话")
    private String regPhone;

    @Schema(description = "email")
    private String email;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}