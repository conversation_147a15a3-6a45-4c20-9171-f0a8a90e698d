package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import info.qizhi.aflower.framework.common.enums.CheckInTypeEnum;
import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 在住订单(今日预离)分页 Request VO")
@Data
@ToString(callSuper = true)
public class OrderTodayCheckoutAndCheckinReqVO {

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "客源类型", example = "2")
    @InStringEnum(value = GuestSrcTypeEnum.class, message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    @InStringEnum(value = CheckInTypeEnum.class, message = "{checkinType.instringenum}")
    private String checkinType;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "客源名称，中介单位代码")
    private String guestCode;

    @Schema(description = "关键字查询条件；包含字段订单号，外部订单号，姓名，手机号")
    private String keyWords;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "关联订单号")
    private List<String> orderNo;
}
