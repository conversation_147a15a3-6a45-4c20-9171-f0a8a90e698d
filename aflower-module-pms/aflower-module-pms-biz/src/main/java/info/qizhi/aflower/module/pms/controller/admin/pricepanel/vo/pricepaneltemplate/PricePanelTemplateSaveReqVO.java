package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 房价牌模板新增/修改 Request VO")
@Data
public class PricePanelTemplateSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3340")
    private Long id;

    @Schema(description = "模板代码;系统生成 (唯一标识)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String templateCode;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{templateName.notempty}")
    private String templateName;

    @Schema(description = "模板地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{layoutType.notempty}")
    private String templateWebsite;

    @Schema(description = "版面类型;0:横版 1：竖版", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{layoutType.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{layoutType.invalid}")
    private String layoutType;

}