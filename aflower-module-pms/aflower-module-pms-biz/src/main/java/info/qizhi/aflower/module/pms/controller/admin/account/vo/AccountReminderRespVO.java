package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

@Schema(description = "管理后台 - 催账明细 Request VO")
@Data
public class AccountReminderRespVO {
    @Schema(description = "类型，1：单订单 2：单房间同住订单，3：联房，4：团队，", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String type;

    @Schema(description = "类型名称，单订单 ；单房间同住订单；联房；团队，", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String typeName;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "客单代码")
    private String togetherCode;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    // @NameDesensitize
    private String name;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime checkinTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String checkinType;

    @Schema(description = "入住类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String checkinTypeName;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String guestSrcType;

    @Schema(description = "客源类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String guestSrcTypeName;

    @Schema(description = "会员级别或公司", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String levelOrCompanyName;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "消费合计", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeFee;

    @Schema(description = "付款合计", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "预授权费用", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long preFee;

    @Schema(description = "需加收房费", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long addRoomFee;

    @Schema(description = "预计欠费", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long arrearsFee;

    @Schema(description = "主单", example = "你说的对")
    private String isMain;


   /* @Schema(description = "团队名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String teamName;

     @Schema(description = "团队类型")
    private String teamType;
*/
    @Schema(description = "团队代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String teamCode;

    @Schema(description = "绑定代码;当为直接入住时，值为联房号。当订单来自预订时，值为预订单号.")
    private String bindCode;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime createTime;
}
