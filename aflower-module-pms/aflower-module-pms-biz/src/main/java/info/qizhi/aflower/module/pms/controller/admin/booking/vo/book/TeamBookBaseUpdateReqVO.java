package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.common.validation.Telephone;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单(团队预订)基本信息修改 Request VO")
@Data
public class TeamBookBaseUpdateReqVO {

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{guestSrcType.notempty}")
    @InStringEnum(value= GuestSrcTypeEnum.class ,message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    private String checkinType;

    @Schema(description = "客人代码;会员代码、协议单位、中介代码")
    private String guestCode;

    @Schema(description = "客人名称;会员姓名、协议单位、中介名称", example = "张三")
    @Size(max = 32, message = "{guestName.size}")
    private String guestName;

    @Schema(description = "团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{teamCode.notempty}")
    private String teamCode;

    @Schema(description = "团队名称;团队预订时输入", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @Size(max = 32, message = "{teamName.size}")
    private String teamName;

    @Schema(description = "合同号;团队预订时输入")
    @Size(max = 32, message = "{contractNo.size}")
    private String contractNo;

    @Schema(description = "预订人(联系人);如果是团队预订，这里保存团队的联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{contact.notempty}")
    @Size(max = 32, message = "{contact.size}")
    private String contact;

    @Schema(description = "预订人电话;联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @Telephone(message = "{phone.invalidFormat}")
    private String phone;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "外部订单号")
    @Size(max = 32, message = "{outOrderNo.size}")
    private String outOrderNo;

    @Schema(description = "订单备注", example = "随便")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}