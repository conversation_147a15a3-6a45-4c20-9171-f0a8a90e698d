package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Data
public class Order {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称简称")
    private String channelShortName;

    @Schema(description = "统计渠道代码")
    private String statChannelCode;

    @Schema(description = "订单来源")
    private String orderSource;

    @Schema(description = "入住类型")
    private String checkinType;

    @Schema(description = "入住时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime checkinTime;

    @Schema(description = "预离时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "x天离")
    @JsonProperty("xDay")
    private Integer xDay;

    @Schema(description = "客单代码")
    private String togetherCode;

    @Schema(description = "客人姓名")
    // @NameDesensitize
    private String name;

    @Schema(description = "房间下所有住客姓名，多个用'&'号隔开")
    private String names;

    @Schema(description = "入住人数")
    private Integer guestCount;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "拼音，多个用'&'号隔开")
    private String pinyins;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "客源类型代码")
    private String guestSrcType;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "预订单号")
    private String bookNo;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "绑定单号")
    private String bindCode;

    @Schema(description = "是否主订单")
    private String isMain;

    @Schema(description = "订单状态")
    private String state;

    @Schema(description = "客源代码(会员：会员代码 单位：单位代码 中介:中介代码)")
    private String guestCode;

    @Schema(description = "时租房代码;当入住类型为时租房时保存时租房代码")
    private String hourCode;

//    @Schema(description = "欠费状态")
//    private String arrearState;
//
//    @Schema(description = "押金状态")
//    private String deposit;

    @Schema(description = "房间备注")
    private String remark;

    @Schema(description = "续住操作的营业日")
    private LocalDate continueHandleBizDate;

    @Schema(description = "是否做了续住操作 1:是 0:否")
    private String isContinued;

    @Schema(description = "标签")
    private Tag tags;

    @Data
    public static class Tag {

        @Schema(description = "保密服务设置,0:否 1:是")
        private String secrecy;

        @Schema(description = "请勿打扰服务设置 0:否 1:是")
        private String notDisturbing;

        @Schema(description = "叫早时间")
        private LocalDateTime awaken;

        @Schema(description = "是否今天生日,0:否 1:是")
        private String birthday;
    }
}
