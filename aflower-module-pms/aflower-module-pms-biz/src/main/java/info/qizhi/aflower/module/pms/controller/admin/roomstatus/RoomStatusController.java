package info.qizhi.aflower.module.pms.controller.admin.roomstatus;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.future.FutureRoomStatusReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.future.FutureRoomStatusRespVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.predict.RoomClassPredictionReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.predict.RoomPredictionRespVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime.RealTimeOccRespVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime.RealTimeRoomStatusReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime.RealTimeRoomStatusRespVO;
import info.qizhi.aflower.module.pms.service.roomstatus.RealTimeRoomStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "biz - 房态")
@RestController
@RequestMapping("/pms/room-status")
@Validated
public class RoomStatusController {

    @Resource
    //@Lazy
    private RealTimeRoomStatusService realTimeRoomStatusService;

    @GetMapping("/list")
    @Operation(summary = "实时房态")
    @PreAuthorize("@ss.hasPermission('pms:room-status:query:list')")
    public CommonResult<List<RealTimeRoomStatusRespVO>> getRealTimeRoomTypeList(@Valid RealTimeRoomStatusReqVO reqVO) {
        List<RealTimeRoomStatusRespVO> list = realTimeRoomStatusService.getRealTimeRoomStatusList(reqVO);
        return success(list);
    }

    @GetMapping("/get-occ")
    @Operation(summary = "获取实时出租率")
    @PreAuthorize("@ss.hasPermission('pms:room-status:query:list')")
    public CommonResult<RealTimeOccRespVO> getRealTimeOcc(@Valid RealTimeRoomStatusReqVO reqVO) {
        RealTimeOccRespVO respVO = realTimeRoomStatusService.getRealTimeOcc(reqVO);
        return success(respVO);
    }


    @GetMapping("/future-room-status")
    @Operation(summary = "远期房态")
    @PreAuthorize("@ss.hasPermission('pms:room-status:query:future-room-status')")
    public CommonResult<FutureRoomStatusRespVO> getFutureRoomStatus(@Valid FutureRoomStatusReqVO reqVO) {
        FutureRoomStatusRespVO futureRoomStatus = realTimeRoomStatusService.getFutureRoomStatus(reqVO);
        return success(futureRoomStatus);
    }

    @GetMapping("/predict-room")
    @Operation(summary = "房类预测")
    @PreAuthorize("@ss.hasPermission('pms:room-status:query:room-prediction')")
    public CommonResult<RoomPredictionRespVO> predictRoomClass(@Valid RoomClassPredictionReqVO reqVO) {
        RoomPredictionRespVO prediction = realTimeRoomStatusService.predictRoomClass(reqVO);
        return success(prediction);
    }


}
