package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.time.LocalDate;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 预订单 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BookPageReq2VO extends PageParam {

    @Schema(description = "集团代码")
    @NotBlank(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "门店代码")
    @NotBlank(message = "门店代码不能为空")
    private String hcode;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "关键词 姓名 订单号 手机号")
    private String keyWord;

    @Schema(description = "订单状态")
    private String state;

    @Schema(description = "计划入住日期")
    private LocalDate planCheckinTime;

}