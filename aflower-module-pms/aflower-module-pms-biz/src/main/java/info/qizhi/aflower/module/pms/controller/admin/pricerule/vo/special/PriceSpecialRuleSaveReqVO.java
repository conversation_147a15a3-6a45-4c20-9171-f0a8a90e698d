package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule.RtFeeRule;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;

@Schema(description = "管理后台 - 特殊房(白天房午夜房)计费规则新增/修改 Request VO")
@Data
public class PriceSpecialRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17799")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "开始时间(时分);如：16：00")
    private LocalTime startTime;

    @Schema(description = "结束时间(时分);如：16：00")
    private LocalTime endTime;

    @Schema(description = "固定退房时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkOutTime.notnull}")
    private LocalTime checkOutTime;

    @Schema(description = "入住N分钟后收起步费")
    @Min(value = 0, message = "{startPriceNMin.min}")
    private Integer startPriceNMin;

    @Schema(description = "入住N分钟后收全价")
    @Min(value = 0, message = "{allPriceNMin.min}")
    private Integer allPriceNMin;

    @Schema(description = "预离超时N分钟后收费")
    @Min(value = 0, message = "{overNMinCollect.min}")
    private Integer overNMinCollect;

    @Schema(description = "超时收费规则;0：按时租房半价加收 1：按每小时加收", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{overCollectStyle.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{overCollectStyle.invalid}")
    private String overCollectStyle;

    @Schema(description = "预离超过N分钟后转全日租")
    @Min(value = 0, message = "{overNMinAllDay.min}")
    private Integer overNMinAllDay;

    @Schema(description = "房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系")
    private List<RtFeeRule> rtFeeRule;

    @Schema(description = "类型;daytime：白天房 midnight：午夜房", requiredMode = Schema.RequiredMode.REQUIRED, example = "daytime")
    @NotEmpty(message = "{ruleType.notempty}")
    private String ruleType;

    @Schema(description = "说明", example = "随便")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}