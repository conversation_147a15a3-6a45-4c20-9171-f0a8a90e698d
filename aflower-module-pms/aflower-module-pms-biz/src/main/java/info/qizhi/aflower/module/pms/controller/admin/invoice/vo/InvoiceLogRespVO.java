package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 开票记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28542")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleType;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceType;

    @Schema(description = "发票代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票代码")
    private String invoiceCode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "开票时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开票时间")
    private LocalDateTime makeTime;

    @Schema(description = "发票号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票号码")
    private String invoiceNo;

    @Schema(description = "开票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开票金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long invoiceMoney;

    @Schema(description = "开票人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开票人")
    private String invoicePerson;

    @Schema(description = "发票抬头", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发票抬头")
    private String invoiceTitle;

    @Schema(description = "税金")
    @ExcelProperty("税金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long tax;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    @ExcelProperty("纳税人识别号")
    private String taxpayerId;

    @Schema(description = "开户银行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bank;

    @Schema(description = "开户账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bankNo;

    @Schema(description = "企业注册地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    private String regAddress;

    @Schema(description = "企业注册电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    private String regPhone;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态", example = "随便")
    @ExcelProperty("状态")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}