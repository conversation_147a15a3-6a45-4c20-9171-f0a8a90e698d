package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 交班报表(收付实现制) Response VO")
@Data
public class HandoverReportCashRealizationRespVO {

    @Schema(description = "酒店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hname;

    @Schema(description = "酒店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "班次号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate bizDate;

    @Schema(description = "最后查询时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

    @Schema(description = "是否交班", requiredMode = Schema.RequiredMode.REQUIRED)
    private String handoverStatus;

    // ========== 本班交班明细 ==========
    @Schema(description = "本班交班明细")
    private HandoverDetail handoverDetail;

    // ========== 消费科目 ==========
    @Schema(description = "消费科目明细")
    private List<SubjectDetail> consumeSubjects;

    // ========== 付款科目 ==========
    @Schema(description = "付款科目明细")
    private List<SubjectDetail> paymentSubjects;

    @Data
    @Schema(description = "交班明细")
    public static class HandoverDetail {
        @Schema(description = "上班留存")
        private HandoverAmountDetail previousShiftAmount;

        @Schema(description = "本班收款")
        private HandoverAmountDetail currentShiftAmount;

        @Schema(description = "本班合计存手头金额")
        private HandoverAmountDetail totalAmount;

        @Schema(description = "交班 - 上缴金额")
        private HandoverAmountDetail handoverAmount;

        @Schema(description = "交班 - 本班留存")
        private HandoverAmountDetail remainingAmount;
    }

    @Data
    @Schema(description = "交班金额明细")
    public static class HandoverAmountDetail {
        @Schema(description = "现金")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long cashAmount = 0L;

        @Schema(description = "银行卡")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long bankCardAmount = 0L;

        @Schema(description = "微信")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long wechatAmount = 0L;

        @Schema(description = "支付宝")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long alipayAmount = 0L;

        @Schema(description = "合计")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalAmount = 0L;

        /**
         * 计算合计金额
         */
        public void calculateTotal() {
            this.totalAmount = this.cashAmount + this.bankCardAmount + this.wechatAmount + this.alipayAmount;
        }
    }

    @Data
    @Schema(description = "科目明细")
    public static class SubjectDetail {
        @Schema(description = "科目代码")
        private String subjectCode;

        @Schema(description = "科目名称")
        private String subjectName;

        @Schema(description = "今日发生金额")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long amount;

        @Schema(description = "今日(结账)收回")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long todaySettle;
    }

}
