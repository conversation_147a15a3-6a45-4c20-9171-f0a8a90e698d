package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.allday;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule.RtFeeRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 全天房计费规则新增/修改 Request VO")
@Data
public class PriceAllDayRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18010")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "最早入住时间")
    private LocalTime earliestCheckinTime;

    @Schema(description = "入住N分钟后收起步费")
    @Min(value = 0, message = "{startPriceNMin.min}")
    private Integer startPriceNMin;

    @Schema(description = "入住N分钟后收全日租")
    @Min(value = 0, message = "{allPriceNMin.min}")
    private Integer allPriceNMin;

    @Schema(description = "预离超过N分钟后收费")
    @Min(value = 0, message = "{overNMinCollect.min}")
    private Integer overNMinCollect;

    @Schema(description = "预离超时N分钟后收全日租")
    @Min(value = 0, message = "{overNMinAllDay.min}")
    private Integer overNMinAllDay;

    @Schema(description = "超时收费方式;0：按半日租收费  1：按每小时加收", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{overCollectStyle.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{overCollectStyle.invalid}")
    private String overCollectStyle;

    @Schema(description = "房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RtFeeRule> rtFeeRule;

    @Schema(description = "说明", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}