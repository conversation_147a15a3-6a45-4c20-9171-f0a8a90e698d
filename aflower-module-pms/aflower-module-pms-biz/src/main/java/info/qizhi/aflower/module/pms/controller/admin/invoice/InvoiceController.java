package info.qizhi.aflower.module.pms.controller.admin.invoice;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.invoice.InvoiceDO;
import info.qizhi.aflower.module.pms.service.invoice.InvoiceService;


@Tag(name = "管理后台 - 开票信息")
@RestController
@RequestMapping("/pms/invoice")
@Validated
public class InvoiceController {

    @Resource
    private InvoiceService invoiceService;

    @PostMapping("/create")
    @Operation(summary = "创建开票信息")
    @PreAuthorize("@ss.hasPermission('pms:invoice:create')")
    public CommonResult<String> createInvoice(@Valid @RequestBody InvoiceSaveReqVO createReqVO) {
        return success(invoiceService.createInvoice(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新开票信息")
    @PreAuthorize("@ss.hasPermission('pms:invoice:update')")
    public CommonResult<Boolean> updateInvoice(@Valid @RequestBody InvoiceSaveReqVO updateReqVO) {
        invoiceService.updateInvoice(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除开票信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:invoice:delete')")
    public CommonResult<Boolean> deleteInvoice(@RequestParam("id") Long id) {
        invoiceService.deleteInvoice(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得开票信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:invoice:query')")
    public CommonResult<InvoiceRespVO> getInvoice(@RequestParam("id") Long id) {
        InvoiceDO invoice = invoiceService.getInvoice(id);
        return success(BeanUtils.toBean(invoice, InvoiceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得开票信息分页")
    @PreAuthorize("@ss.hasPermission('pms:invoice:query')")
    public CommonResult<PageResult<InvoiceRespVO>> getInvoicePage(@Valid InvoicePageReqVO pageReqVO) {
        PageResult<InvoiceDO> pageResult = invoiceService.getInvoicePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InvoiceRespVO.class));
    }


}