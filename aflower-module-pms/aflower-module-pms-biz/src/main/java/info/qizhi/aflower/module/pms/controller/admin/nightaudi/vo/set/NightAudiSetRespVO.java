package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.LocalTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 夜审设置 Response VO")
@Data
public class NightAudiSetRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "254")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "是否时租房转全天房;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String toAllDay;

    @Schema(description = "是否把在住过夜房置为脏房;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String toDirty;

    @Schema(description = "夜审时中介订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String agentCredit;

    @Schema(description = "夜审时协议单位订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String protocolCredit;

    @Schema(description = "夜审时OTA订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String otaCredit;

    @Schema(description = "自动审核;0：手动夜审 1：自动夜审", requiredMode = Schema.RequiredMode.REQUIRED)
    private String auto;

    @Schema(description = "手动夜审时间范围开始;手动夜审有效，如：04:00")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime handStartTime;

    @Schema(description = "手动夜审时间范围结束;手动夜审有效，如：06:00")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime handEndTime;

    @Schema(description = "自动夜审时间;自动夜审有效，如：06:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime time;

    @Schema(description = "每日自动夜审提前多少分钟提醒", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer notice;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}