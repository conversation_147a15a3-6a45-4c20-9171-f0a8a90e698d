package info.qizhi.aflower.module.pms.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomTypeMessage;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.pms.controller.admin.overbook.vo.OverBookReqVO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.room.RoomReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.overbook.OverBookDO;
import info.qizhi.aflower.module.pms.dal.dataobject.room.RoomDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeBedDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.overbook.OverBookService;
import info.qizhi.aflower.module.pms.service.room.RoomService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import info.qizhi.aflower.module.pms.service.sender.RoomTypeChangeSenderService;
import info.qizhi.aflower.module.pms.service.sender.bo.RoomTypeChangeBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 房型变更消费者
 * <AUTHOR>
 */
@Component
@RabbitListener(queues = QueueConstants.ROOM_TYPE_MESSAGE_QUEUE) // 重点：添加 @RabbitListener 注解，声明消费的 queue
@Slf4j
public class RoomTypeConsumer {

    @Resource
    @Lazy
    private RoomTypeService roomTypeService;

    @Resource
    private RoomTypeChangeSenderService roomTypeChangeSenderService;

    @Resource
    private RoomService roomService;

    @Resource
    private OverBookService overBookService;


    /**
     * 处理消息
     *
     * @param message 消息
     */
    @RabbitHandler
    public void onMessage(RoomTypeMessage message) {
        //休眠300毫秒
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(message.getGcode()).setHcode(message.getHcode()).setRtCode(message.getRtCode()), false);
        List<RoomTypeChangeBO> list = CollectionUtils.convertList(roomTypeList, roomTypeRespVO -> {
            RoomTypeChangeBO roomTypeChangeBO = new RoomTypeChangeBO();
            roomTypeChangeBO.setArea(roomTypeRespVO.getArea());
            roomTypeChangeBO.setPhysicalRoomtypeCode(roomTypeRespVO.getRtCode());
            roomTypeChangeBO.setPhysicalRoomtypeName(roomTypeRespVO.getRtName());
            roomTypeChangeBO.setStatus(Integer.valueOf(roomTypeRespVO.getIsEnable()));
            return roomTypeChangeBO;
        });
        if (CollUtil.isNotEmpty(list)) {
            // 配置房型详细信息
            configRoomTypeInfo(message.getGcode(), message.getHcode(), list);
            // 发送消息
            roomTypeChangeSenderService.sendRoomTypeChangeInfo(message.getHcode(), message.getChangeType(), JsonUtils.toJsonString(list));
        }
    }

    private void configRoomTypeInfo(String gcode, String hcode, List<RoomTypeChangeBO> list) {
        // 获取门店下所有房间列表
        List<RoomDO> roomDOList = roomService.getRoomList(RoomReqVO.builder().gcode(gcode).hcode(hcode).build());
        // 将roomDOList转换为以房型代码为key, value为房间数的map
        Map<String, Long> roomNumMap = roomDOList.stream()
                .collect(Collectors.groupingBy(RoomDO::getRtCode, Collectors.counting()));
        // 获取房型代码列表
        List<String> rtCodes = CollectionUtils.convertList(list, RoomTypeChangeBO::getPhysicalRoomtypeCode);
        // 获取床型列表
        List<RoomTypeBedDO> roomTypeBedListByRtCodes = roomTypeService.getRoomTypeBedListByRtCodes(rtCodes);
        Map<String, List<RoomTypeBedDO>> roomTypeBedDOMap = CollectionUtils.convertMultiMap(roomTypeBedListByRtCodes, RoomTypeBedDO::getRtCode);
        // 获取超预订配置列表
        List<OverBookDO> overBookList = overBookService.getOverBookList(new OverBookReqVO().setGcode(gcode).setHcode(hcode));
        Map<String, List<OverBookDO>> overBookMap = CollectionUtils.convertMultiMap(overBookList, OverBookDO::getRtCode);

        list.forEach(roomTypeRespVO -> {
            Long roomNum = roomNumMap.get(roomTypeRespVO.getPhysicalRoomtypeCode());
            roomTypeRespVO.setRoomNum(Math.toIntExact(roomNum == null ? 0L : roomNum));
            roomTypeRespVO.setBedType(roomTypeBedDOMap.getOrDefault(roomTypeRespVO.getPhysicalRoomtypeCode(), new ArrayList<>()));
            roomTypeRespVO.setOverBookNum(overBookMap.getOrDefault(roomTypeRespVO.getPhysicalRoomtypeCode(), new ArrayList<>()));
        });
    }

}
