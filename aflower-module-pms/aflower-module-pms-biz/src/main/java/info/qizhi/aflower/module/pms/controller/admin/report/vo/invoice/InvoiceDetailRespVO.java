package info.qizhi.aflower.module.pms.controller.admin.report.vo.invoice;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 发票明细 Request VO")
@Data
public class InvoiceDetailRespVO {

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rNo;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime checkinTime;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime checkoutTime;

    @Schema(description = "发票代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceCode;

    @Schema(description = "发票号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceNo;

    @Schema(description = "开票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long invoiceMoney;

    @Schema(description = "税后金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long afterTaxFee;

    @Schema(description = "税金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long tax;

    @Schema(description = "房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "其他消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherFee;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleType;

    @Schema(description = "发票类别名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleTypeName;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceType;

    @Schema(description = "发票类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTypeName;

    @Schema(description = "发票抬头", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitle;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开票人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoicePerson;

    @Schema(description = "开票人昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoicePersonName;

    @Schema(description = "开票时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime makeTime;

    @Schema(description = "作废人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cancelPerson;

    @Schema(description = "作废人昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cancelPersonName;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "状态", example = "随便")
    private String state;

    @Schema(description = "最后修改时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime updateTime;
}
