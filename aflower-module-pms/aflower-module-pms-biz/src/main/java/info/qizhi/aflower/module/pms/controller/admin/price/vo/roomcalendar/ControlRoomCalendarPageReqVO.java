package info.qizhi.aflower.module.pms.controller.admin.price.vo.roomcalendar;

import lombok.*;

import java.time.LocalDate;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 控房日历分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ControlRoomCalendarPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "渠道")
    private String channel;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "日期")
    private LocalDate calendar;

    @Schema(description = "房态;0:开房 1:关房")
    private String rtState;

    @Schema(description = "房量数")
    private Integer roomNum;

    @Schema(description = "限量房量")
    private Integer limitRoomNum;

    @Schema(description = "预留房量")
    private Integer reservedRoomNum;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}