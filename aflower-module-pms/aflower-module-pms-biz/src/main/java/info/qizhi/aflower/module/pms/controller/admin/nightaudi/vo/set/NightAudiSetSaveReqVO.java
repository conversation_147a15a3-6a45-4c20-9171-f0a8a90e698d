package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.set;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalTime;

@Schema(description = "管理后台 - 夜审设置新增/修改 Request VO")
@Data
public class NightAudiSetSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "254")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "是否时租房转全天房;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{toAllDay.notempty}空")
    private String toAllDay;

    @Schema(description = "是否把在住过夜房置为脏房;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{toDirty.notempty}")
    private String toDirty;

    @Schema(description = "夜审时中介订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{agentCredit.notempty}")
    private String agentCredit;

    @Schema(description = "夜审时协议单位订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{protocolCredit.notempty}")
    private String protocolCredit;

    @Schema(description = "夜审时OTA订单自动挂账;0：否  1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{otaCredit.notempty}")
    private String otaCredit;

    @Schema(description = "自动审核;0：手动夜审 1：自动夜审", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{auto.notempty}")
    private String auto;

    @Schema(description = "手动夜审时间范围开始;手动夜审有效，如：04:00")
    private LocalTime handStartTime;

    @Schema(description = "手动夜审时间范围结束;手动夜审有效，如：06:00")
    private LocalTime handEndTime;

    @Schema(description = "自动夜审时间;自动夜审有效，如：06:00", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{time.notnull}")
    private LocalTime time;

    @Schema(description = "每日自动夜审提前多少分钟提醒", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{notice.notnull}")
    private Integer notice;

}