package info.qizhi.aflower.module.pms.controller.admin.config.vo.groupglobal;

import com.baomidou.mybatisplus.annotation.TableField;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GroupGlobalConfigDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 集团全局配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class GroupGlobalConfigRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20344")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "集团入账商户号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团入账商户号")
    private String accountNo;

    @Schema(description = "ota预付订单结算方式-直营;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ota预付订单结算方式-直营;1：入账到集团账户 0：门店账户")
    private String otaDirectMgmt;

    @Schema(description = "ota预付订单结算方式-加盟;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ota预付订单结算方式-加盟;1：入账到集团账户 0：门店账户")
    private String otaJoin;

    @Schema(description = "ota预付订单结算方式-托管;1：入账到集团账户 0：门店账户", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("ota预付订单结算方式-托管;1：入账到集团账户 0：门店账户")
    private String otaAuth;

    @Schema(description = "会员购卡成本;对应每个会员类型，物理卡的成本价格")
    @ExcelProperty("会员购卡成本")
    @TableField(typeHandler = GroupGlobalConfigDO.CardCostTypeHandler.class)
    private List<GroupGlobalConfigDO.CardCost> cardCosts;

    @Schema(description = "是否收取中央预订佣金;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否收取中央预订佣金;0:否 1：是")
    private String crsBrokerage;

    @Schema(description = "收取佣金方式;0: 按单笔订单金额 1: 按营收收取", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收取佣金方式")
    private String brokerageMode;

    @Schema(description = "收取佣金比例;比如：0.12表示12%的佣金收取", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收取佣金比例")
    private BigDecimal brokerageRatio;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}