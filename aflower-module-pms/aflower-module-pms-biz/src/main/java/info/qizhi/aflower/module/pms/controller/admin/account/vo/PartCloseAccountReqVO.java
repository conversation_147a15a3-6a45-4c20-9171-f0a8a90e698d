package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 部分结账 Request VO")
@Data
public class PartCloseAccountReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "入账账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{no.notblank}")
    private String no;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{togetherCode.notempty}")
    private String togetherCode;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= AccountTypeEnum.class, message = "{accType.instringenum}")
    @NotEmpty(message = "{accType.notblank}")
    private String accType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{subCode.notblank}")
    private String subCode;

    @Schema(description = "付款码;付款方式：支付宝码、微信码、预授权码、储值卡号、账套代码(AR账)")
    @Size(max = 32, message = "{payCode.size}")
    private String payCode;

    @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
    private String bankType;

    @Schema(description = "银行卡号")
    @Size(max = 20, message = "{bankCardNo.size}")
    private String bankCardNo;

    //----会员卡支付--->>>//
    @Schema(description = "会员代码，如果是储值卡支付，需要传会员代码")
    private String mcode;

    @Schema(description = "手机号，如果是储值卡支付,需要传会员手机号")
    private String phone;

    @Schema(description = "储值卡号")
    private String storeCardNo;

    @Schema(description = "会员卡密码,当付款方式为储值卡时需要")
    private String pwd;
    //<<<----会员卡支付---//

    @Schema(description = "业务详情")
    private String accDetail;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "账单号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accNoList.notempty}")
    private List<String> accNoList;

}