package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;


import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.IntToDecDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import java.util.List;

@Schema(description = "管理后台 - 房价牌新增/修改 Request VO")
@Data
public class PricePanelSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3763")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "房型代码;存多个房型级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "标题字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{titleFontSize.notnull}")
    @Range(min = 180, max = 360, message = "{titleFontSize.range}")
    @JsonDeserialize(using = IntToDecDeserializer.class)
    private Integer titleFontSize;

    @Schema(description = "内容字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{contentFontSize.notnull}")
    @Range(min = 180, max = 360, message = "{contentFontSize.range}")
    @JsonDeserialize(using = IntToDecDeserializer.class)
    private Integer contentFontSize;

    @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "{mtCode.notempty}")
    private String mtCode;

    @Schema(description = "展示行数;每屏房型展示行数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{showRows.notnull}")
    @Range(min = 1, max = 30, message = "{showRows.range}")
    private Integer showRows;

    @Schema(description = "切换时间;翻页切换时间,不切换为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{switchingTime.notnull}")
    @Range(min = 0, max = 60, message = "{switchingTime.range}")
    private Integer switchingTime;

    @Schema(description = "房价牌(自定义房型房价)列表")
    private List<PricePanelCustomReqVO> pricePanelCustoms;


}