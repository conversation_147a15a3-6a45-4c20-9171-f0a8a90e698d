package info.qizhi.aflower.module.pms.mq.message;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 订单消息
 */

@Data
public class BookingOrderMessage implements Serializable {

    public static final String QUEUE = QueueConstants.BOOKING_ORDER_MESSAGE_QUEUE; // 重点：需要增加消息对应的 Queue

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "动作类型;")
    private String actionType;

    @Schema(description = "订单消息")
    private String msg;

}
