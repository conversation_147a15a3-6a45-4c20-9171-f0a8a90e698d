package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.TuyaMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HiiiTuyaSenderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送消息
     *
     */
    public void sendOrderMessage(String type, String msg) {
        TuyaMessage tuyaMessage=new TuyaMessage();
        tuyaMessage.setActionType(type);
        tuyaMessage.setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HIII_TUYA_MESSAGE_QUEUE, tuyaMessage);
    }

}
