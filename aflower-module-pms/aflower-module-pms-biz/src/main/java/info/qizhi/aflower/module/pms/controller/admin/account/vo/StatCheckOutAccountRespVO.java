package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 统计账务 Response VO")
@Data
public class StatCheckOutAccountRespVO {

    @Schema(description = "付款科目代码")
    private String subCode;

    @Schema(description = "付款方式,1:收款 -1:退款")
    private String payMode;

    @Schema(description = "付款金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "是否结清, 0:否 1:是")
    private String isCloseAccount;

    @Schema(description = "是否是AR账，false:是AR账务 true:不是")
    private Boolean isAR;
}