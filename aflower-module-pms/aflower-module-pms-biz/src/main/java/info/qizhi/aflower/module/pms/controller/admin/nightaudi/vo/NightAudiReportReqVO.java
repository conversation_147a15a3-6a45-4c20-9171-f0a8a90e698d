package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 夜审报表 Request VO")
@Data
public class NightAudiReportReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "上个营业日期的夜审时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{startTime.notnull}")
    private LocalDateTime lastBizDateTime;

}