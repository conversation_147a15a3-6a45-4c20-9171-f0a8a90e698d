package info.qizhi.aflower.module.pms.controller.admin.cashbillorder;

import info.qizhi.aflower.framework.common.enums.TimeConstants;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.idempotent.core.annotation.Idempotent;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo.CashBillOrderPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo.CashBillOrderRespVO;
import info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo.CashBillOrderSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo.CashBillRevocationReqVO;
import info.qizhi.aflower.module.pms.service.cashbillorder.CashBillOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 现付账订单")
@RestController
@RequestMapping("/pms/cash-bill-order")
@Validated
public class CashBillOrderController {

    @Resource
    private CashBillOrderService cashBillOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建现付账订单")
    @PreAuthorize("@ss.hasPermission('finance:cash-bill-order:create')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "短时间内请勿重复消费", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Long> createCashBillOrder(@Valid @RequestBody CashBillOrderSaveReqVO createReqVO) {
        return success(cashBillOrderService.createCashBillOrder(createReqVO).getId());
    }

    @PostMapping("/revocation")
    @Operation(summary = "现付账冲调")
    @PreAuthorize("@ss.hasPermission('finance:cash-bill-order:update:revocation')")
    @Idempotent(timeout = TimeConstants.TIMEOUT, message = "请勿重复提交", keyResolver = UserIdempotentKeyResolver.class)
    public CommonResult<Boolean> cashBillRevocation(@Valid @RequestBody CashBillRevocationReqVO reqVO) {
        cashBillOrderService.cashBillRevocation(reqVO);
        return success(true);
    }

    @GetMapping("/page")
    @Operation(summary = "获得现付账订单(未冲调)分页")
    @PreAuthorize("@ss.hasPermission('finance:cash-bill-order:query:page')")
    public CommonResult<PageResult<CashBillOrderRespVO>> getCashBillOrderPage(@Valid CashBillOrderPageReqVO pageReqVO) {
        PageResult<CashBillOrderRespVO> pageResult = cashBillOrderService.getCashBillOrderPage(pageReqVO);
        return success(pageResult);
    }

}