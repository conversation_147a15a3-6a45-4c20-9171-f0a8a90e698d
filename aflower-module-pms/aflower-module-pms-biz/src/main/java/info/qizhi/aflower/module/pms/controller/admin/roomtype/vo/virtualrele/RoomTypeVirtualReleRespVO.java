package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 物理房型与虚拟房型关联 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RoomTypeVirtualReleRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "关联代码;系统自动生成", requiredMode = Schema.RequiredMode.REQUIRED)
    private String releCode;

    @Schema(description = "物理房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "物理房型名称")
    private String rtName;

    @Schema(description = "虚拟房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("vRtCode")
    private String vRtCode;

    @Schema(description = "虚拟房型名称")
    @JsonProperty("vRtName")
    private String vRtName;

    @Schema(description = "是否有效", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isEnable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}