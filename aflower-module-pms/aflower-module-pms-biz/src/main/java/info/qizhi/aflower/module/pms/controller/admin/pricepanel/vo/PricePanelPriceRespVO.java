package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import info.qizhi.aflower.module.member.api.memberType.dto.MemberTypeRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.bo.PricePanelContentBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 房价牌 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PricePanelPriceRespVO {
    @Schema(description = "内容字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("内容字号")
    private Integer contentFontSize;

    @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码;存多个会员级别代码")
    private String mtCode;

    @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员级别代码;存多个会员级别代码")
    private List<MemberTypeRespDTO> memberType;

    @Schema(description = "展示行数;每屏房型展示行数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("展示行数;每屏房型展示行数")
    private Integer showRows;

    @Schema(description = "切换时间;翻页切换时间,不切换为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("切换时间;翻页切换时间,不切换为0")
    private Integer switchingTime;

    @Schema(description = "业务开通状态;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务开通状态;0:关闭 1：开启")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "房型详情")
    private List<PricePanelContentBO> pricePanelContent;

    @Schema(description = "集团logo地址")
    private String logo;

}