package info.qizhi.aflower.module.pms.controller.admin.report.vo.bk;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.framework.desensitize.core.slider.annotation.NameDesensitize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 余额明细 Request VO")
@Data
public class BKRespVO {

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    // @NameDesensitize
    private String name;

    @Schema(description = "同住人数")
    private Integer togetherNum;

    @Schema(description = "赠早数")
    private Integer bkNum;

    @Schema(description = "房包早餐数")
    private Integer roomBkNum;

    @Schema(description = "购买早餐数")
    private Integer buyBkNum;

    @Schema(description = "总早餐数")
    private Integer effectiveBkNum;
}
