package info.qizhi.aflower.module.pms.controller.admin.report.vo.guest;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_MONTH_DAY_TWO;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 余额明细 Request VO")
@Data
public class HotelGuestRespVO {
    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "客单代码")
    private String togetherCode;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
//    @NameDesensitize
    private String name;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
//    @MobileDesensitize
    private String phone;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkinTime;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkoutTime;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idType;

    @Schema(description = "证件类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idTypeName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
//    @IdCardDesensitize
    private String idNo;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String address;

    @Schema(description = "民族", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String  nation;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String sex;

    @Schema(description = "生日")
    @JsonFormat(pattern = FORMAT_MONTH_DAY_TWO)
    private LocalDate birthday;

    @Schema(description = "入住状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String state;

    @Schema(description = "创建者", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String creator;

    @Schema(description = "创建者昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String creatorName;

}
