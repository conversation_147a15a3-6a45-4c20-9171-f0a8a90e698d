package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;


/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房 Request VO")
@Data
public class BookRoomTypeArrangeReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型代码列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> rtCodes;

    @Schema(description = "房态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "预低时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckinTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{planCheckoutTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "预订单占用房间,0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String preOccupied;

    @Schema(description = "是否会议室;0:客房 1:会议室", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isMeetingRoom.notempty}")
    private String isMeetingRoom;

}