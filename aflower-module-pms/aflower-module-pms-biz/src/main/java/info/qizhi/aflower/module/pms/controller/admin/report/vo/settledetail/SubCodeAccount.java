package info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 宾客明细报表[固化] Response VO")
@Data
public class SubCodeAccount {
    @Schema(description = "科目代码")
    @ExcelProperty("科目代码")
    private String subCode;

    @Schema(description = "科目名称")
    @ExcelProperty("科目名称")
    private String subName;

    @Schema(description = "费用")
    @ExcelProperty("费用")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;
}
