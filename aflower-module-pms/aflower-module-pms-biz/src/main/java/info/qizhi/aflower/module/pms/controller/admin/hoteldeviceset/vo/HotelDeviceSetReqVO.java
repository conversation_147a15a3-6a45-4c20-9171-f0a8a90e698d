package info.qizhi.aflower.module.pms.controller.admin.hoteldeviceset.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.module.pms.dal.dataobject.hoteldeviceset.HotelDeviceSetDO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 门店设备信息 Request VO")
@Data
public class HotelDeviceSetReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{deviceCode.notempty}")
    private String deviceCode;

    @Schema(description = "设备类型")
    @NotEmpty(message = "设备类型不能为空")
    private String deviceType;

    @Schema(description = "数据库路径")
    @JsonProperty(value = "url")
    private String url;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "端口号")
    private String port;

    @Schema(description = "发卡器类型")
    private String cardDispenserType;

    @Schema(description = "是否允许开反锁")
    private String allowLockOut;

    @Schema(description = "新卡替换旧卡")
    private String replaceCard;

    @Schema(description = "检查入住时间")
    private String checkTime;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "配置信息")
    private List<HotelDeviceSetDO.ConfParameter> conf;

}