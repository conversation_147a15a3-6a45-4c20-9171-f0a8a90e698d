package info.qizhi.aflower.module.pms.controller.admin.report.vo.settledetail;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 前台结账报表 Request VO")
@Data
public class SettleDetailReportReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "结账操作员")
    @ExcelProperty("结账操作员")
    private String payOperator;

    @Schema(description = "入住类型")
    @ExcelProperty("入住类型")
    private String inType;

    @Schema(description = "结账类型")
    @ExcelProperty("结账类型")
    private String payType;

    @Schema(description = "日期类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    //@NotEmpty(message = "日期类型不能为空")
    private String timeType;

    @Schema(description = "开始日期", example = "2024-07-04")
    //@JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private String startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    //@JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private String endDate;

    @Schema(description = "结账营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    //@NotNull(message = "{payBizDate.notnull}")
    private LocalDate payBizDate;

}
