package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/24 13:33
 */
@Data
public class InvoiceOrderRedReqVO {

    @Schema(description ="集团代码")
    @NotBlank(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "酒店代码")
    @NotBlank(message = "酒店代码不能为空")
    private String hcode;

    @Schema(description = "红冲原因代码 01-开票有误；02-销货退回；03-服务中止；04-销售折让")
    @NotBlank(message = "红冲原因不能为空")
    private String reasonType;

    @Schema(description = "发票开票请求流水号")
    @NotBlank(message = "发票开票请求流水号不能为空")
    private String invoiceSerialNo;
}
