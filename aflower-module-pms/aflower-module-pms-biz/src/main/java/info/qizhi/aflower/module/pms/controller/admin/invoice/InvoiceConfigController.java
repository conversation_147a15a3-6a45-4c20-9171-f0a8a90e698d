package info.qizhi.aflower.module.pms.controller.admin.invoice;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.InvoiceConfigSaveReqVO;
import info.qizhi.aflower.module.pms.service.invoice.InvoiceConfigService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发票配置 Controller
 * 
 * <AUTHOR>
 * @date 2025/01/22 15:39
 */
@RestController()
@RequestMapping("/pms/invoice-config")
public class InvoiceConfigController {
    @Resource
    InvoiceConfigService invoiceConfigService;

    @PostMapping("/create")
    public CommonResult<Long> createConfig(@Validated @RequestBody InvoiceConfigSaveReqVO reqVO) {
        return CommonResult.success(invoiceConfigService.createInvoiceConfig(reqVO));
    }

    @PostMapping("/update")
    public CommonResult<Long> updateConfig(@Validated @RequestBody InvoiceConfigSaveReqVO reqVO) {
        if (reqVO.getId() == null) {
            return CommonResult.error(-1, "id不能为空");
        }
        invoiceConfigService.updateInvoiceConfig(reqVO);
        return CommonResult.success(reqVO.getId());
    }
}
