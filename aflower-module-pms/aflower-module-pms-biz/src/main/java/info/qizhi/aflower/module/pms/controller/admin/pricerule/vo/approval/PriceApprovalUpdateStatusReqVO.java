package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 集团价格审批新增/修改 Request VO")
@Data
public class PriceApprovalUpdateStatusReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25451")
    @NotNull(message = "{id.notnull}")
    private Long id;

    @Schema(description = "是否有效 0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{isEnable.notempty}")
    @InStringEnum( value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

}