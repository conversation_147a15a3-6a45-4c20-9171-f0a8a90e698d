package info.qizhi.aflower.module.pms.controller.admin.cashbillorder.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 现付账订单 Request VO")
@Data
@ToString(callSuper = true)
public class CashBillOrderReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "付款方式")
    private String payMethod;

    @Schema(description = "科目")
    private String subCode;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "订单号列表")
    private List<String> cashBillOrderNos;

    @Schema(description = "营业日期")
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "现付账套")
    private String accCode;

    @Schema(description = "AR账户")
    private String arSetCode;

}