package info.qizhi.aflower.module.pms.controller.admin.channel;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.channel.vo.MerchantChannelRelePageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.channel.vo.MerchantChannelReleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.channel.vo.MerchantChannelReleSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.channel.MerchantChannelReleDO;
import info.qizhi.aflower.module.pms.service.channel.MerchantChannelReleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 门店OTA关联")
@RestController
@RequestMapping("/pms/merchant-ota-rele")
@Validated
public class MerchantChannelReleController {

    @Resource
    private MerchantChannelReleService merchantChannelReleService;

    @PostMapping("/create")
    @Operation(summary = "创建门店OTA关联")
    @PreAuthorize("@ss.hasPermission('pms:merchant-ota-rele:create')")
    public CommonResult<Long> createMerchantOtaRele(@Valid @RequestBody MerchantChannelReleSaveReqVO createReqVO) {
        return success(merchantChannelReleService.createMerchantOtaRele(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新门店OTA关联")
    @PreAuthorize("@ss.hasPermission('pms:merchant-ota-rele:update')")
    public CommonResult<Boolean> updateMerchantOtaRele(@Valid @RequestBody MerchantChannelReleSaveReqVO updateReqVO) {
        merchantChannelReleService.updateMerchantOtaRele(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除门店OTA关联")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:merchant-ota-rele:delete')")
    public CommonResult<Boolean> deleteMerchantOtaRele(@RequestParam("id") Long id) {
        merchantChannelReleService.deleteMerchantOtaRele(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店OTA关联")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:merchant-ota-rele:query')")
    public CommonResult<MerchantChannelReleRespVO> getMerchantOtaRele(@RequestParam("id") Long id) {
        MerchantChannelReleDO merchantOtaRele = merchantChannelReleService.getMerchantOtaRele(id);
        return success(BeanUtils.toBean(merchantOtaRele, MerchantChannelReleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得门店OTA关联分页")
    @PreAuthorize("@ss.hasPermission('pms:merchant-ota-rele:query')")
    public CommonResult<PageResult<MerchantChannelReleRespVO>> getMerchantOtaRelePage(@Valid MerchantChannelRelePageReqVO pageReqVO) {
        PageResult<MerchantChannelReleDO> pageResult = merchantChannelReleService.getMerchantOtaRelePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MerchantChannelReleRespVO.class));
    }

}