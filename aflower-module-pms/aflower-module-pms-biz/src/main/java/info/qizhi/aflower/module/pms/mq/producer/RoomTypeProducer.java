package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomTypeMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 *  房型变更生产者
 */
@Slf4j
@Component
public class RoomTypeProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送房型变更消息
     *
     */
    public void sendRealRoomChangeMessage(String gcode, String hcode, String rtCode,String changeType) {
        RoomTypeMessage message = new RoomTypeMessage();
        message.setGcode(gcode);
        message.setHcode(hcode);
        message.setRtCode(rtCode);
        message.setChangeType(changeType);
        rabbitTemplate.convertAndSend(QueueConstants.ROOM_TYPE_MESSAGE_QUEUE, message);
    }
}
