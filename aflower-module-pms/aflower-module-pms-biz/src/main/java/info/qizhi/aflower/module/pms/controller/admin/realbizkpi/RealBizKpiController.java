package info.qizhi.aflower.module.pms.controller.admin.realbizkpi;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo.BusinessData;
import info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo.RealBizKpiVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime.RealTimeRoomStatusReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime.RealTimeRoomStatusTwoReqVO;
import info.qizhi.aflower.module.pms.service.realbizkpi.RealBizKpiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 首页实时数据")
@RestController("AppRealBizKpiController")
@RequestMapping("/pms/real-biz-kpi")
@Validated
public class RealBizKpiController {

    @Resource
    RealBizKpiService realBizKpiService;

    @GetMapping("/get")
    @Operation(summary = "实时经营数据")
    //@PreAuthorize("@ss.hasPermission('pms:real-biz-kpi:query')")
    public CommonResult<RealBizKpiVO> getRealBizKpi(@Valid RealTimeRoomStatusReqVO reqVO) {
        RealBizKpiVO realBizKpiVO=realBizKpiService.getRealBizKpi(reqVO);
        return success(realBizKpiVO);
    }

    @GetMapping("/get-classification")
    @Operation(summary = "实时经营数据分类")
    //@PreAuthorize("@ss.hasPermission('pms:real-biz-kpi:query')")
    public CommonResult<List<BusinessData>> getRealBizKpiClassification(@Valid RealTimeRoomStatusTwoReqVO reqVO) {
        return success(realBizKpiService.getRealBizKpiClassification(reqVO));
    }
}
