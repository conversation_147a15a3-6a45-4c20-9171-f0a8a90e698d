package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 夜审房间数设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RoomNightsNumSetRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19936")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("入住类型")
    private String checkinType;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间夜数")
    private BigDecimal nightNum;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}