package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/23 15:30
 */
@Data
public class InvoiceOrderRespVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "酒店代码")
    private String hcode;

    @Schema(description = "开票请求流水号")
    private String invoiceSerialNo;

    @Schema(description = "订单类型 blue-蓝字发票；red-红字发票")
    private String orderType;

    @Schema(description = "发票号码")
    private String invoiceNo;

    @Schema(description = "发票代码")
    private String invoiceCode;

    @Schema(description = "数电发票类型代码，81-数电专票；82-数电普票")
    private String digitalInvoiceType;

    @Schema(description = "发票类型 0:公司专票 1:公司普票 2:个人普票")
    private String invoiceType;

    @Schema(description = "当前票关联的订单号,单个订单开票请传订单号，联房订单开票请传联房号")
    private String orderNo;

    @Schema(description = "宾客代码,开票时针对的宾客单号")
    private String togetherCode;

    @Schema(description = "发票格式 0:纸质发票 1:电子发票 2:数电发票")
    private String invoiceFormat;
    /**
     * 开票状态
     * {@link info.qizhi.aflower.framework.invoice.core.enums.InvoiceStatusEnum}
     */
    @Schema(description = "发票状态 0-待开票 1-开票中 2-开票成功 3-开票失败 4-已冲红")
    private Integer invoiceStatus;

    @Schema(description = "金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long amount;

    @Schema(description = "价税合计 = 金额 + 税费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalAmount;

    @Schema(description = "税费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long taxAmount;

    @Schema(description = "发票抬头")
    private String buyerName;

    @Schema(description = "纳税人识别号")
    private String buyerTaxpayerId;

    @Schema(description = "发票地址")
    private String buyerAddress;

    @Schema(description = "发票电话")
    private String buyerPhone;

    @Schema(description = "开户行")
    private String buyerBankName;

    @Schema(description = "开户行账号")
    private String buyerBankNo;

    @Schema(description = "PDF下载地址")
    private String pdfUrl;

    @Schema(description = "是否已冲红标识, 0:否 1:是")
    private Integer redFlag;

    @Schema(description = "被冲红原发票号码")
    private String blueInvoiceNo;

    @Schema(description = "自助开票二维码")
    private String selfQrCode;

    @Schema(description = "自助开票截止日期")
    private LocalDate selfExpiresDate;

    @Schema(description = "开票成功时间")
    private LocalDateTime successTime;

    @Schema(description = "失败原因")
    private String failReason;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "创建者")
    private String creator;
}
