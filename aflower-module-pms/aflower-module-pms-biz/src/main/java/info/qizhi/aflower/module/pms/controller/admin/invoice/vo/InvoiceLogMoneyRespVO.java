package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 开票记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceLogMoneyRespVO {
    @Schema(description = "总开票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalInvoiceMoney;

    @Schema(description = "发票记录", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<InvoiceLogRespVO> list;
}
