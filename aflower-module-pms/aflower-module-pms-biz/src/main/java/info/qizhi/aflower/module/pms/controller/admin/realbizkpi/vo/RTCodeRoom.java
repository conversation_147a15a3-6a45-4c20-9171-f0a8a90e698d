package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "小程序 - 实时房态 Response VO")
@Data
public class RTCodeRoom {
    @Schema(description = "可售房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer availableRoomNum;

    @Schema(description = "在住房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer occupiedRoomNum;

    @Schema(description = "今日预抵房间数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer todayBookingRoomNum;

    @Schema(description = "钟点房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer hourRoomNum;
}
