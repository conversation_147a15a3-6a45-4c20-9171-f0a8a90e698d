package info.qizhi.aflower.module.pms.controller.admin.serviceitems;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;

import info.qizhi.aflower.module.pms.controller.admin.serviceitems.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.serviceitems.ServiceItemsDO;
import info.qizhi.aflower.module.pms.service.serviceitems.ServiceItemsService;

@Tag(name = "管理后台 - 服务项目")
@RestController
@RequestMapping("/pms/service-items")
@Validated
public class ServiceItemsController {

    @Resource
    private ServiceItemsService serviceItemsService;

    @PostMapping("/create")
    @Operation(summary = "创建服务项目")
    @PreAuthorize("@ss.hasPermission('pms:service-items:create')")
    public CommonResult<Long> createServiceItems(@Valid @RequestBody ServiceItemsSaveReqVO createReqVO) {
        return success(serviceItemsService.createServiceItems(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务项目")
    @PreAuthorize("@ss.hasPermission('pms:service-items:update')")
    public CommonResult<Boolean> updateServiceItems(@Valid @RequestBody ServiceItemsSaveReqVO updateReqVO) {
        serviceItemsService.updateServiceItems(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务项目")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:service-items:query')")
    public CommonResult<ServiceItemsRespVO> getServiceItems(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        ServiceItemsDO serviceItems = serviceItemsService.getServiceItems(gcode, hcode);
        return success(BeanUtils.toBean(serviceItems, ServiceItemsRespVO.class));
    }

}