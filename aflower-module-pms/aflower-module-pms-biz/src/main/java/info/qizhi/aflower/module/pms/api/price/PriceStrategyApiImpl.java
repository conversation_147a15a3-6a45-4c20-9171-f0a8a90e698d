package info.qizhi.aflower.module.pms.api.price;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.price.dto.PriceStrategyBoardReqDTO;
import info.qizhi.aflower.module.pms.api.price.dto.PriceStrategyBoardRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricestrategy.PriceStrategyBoardReqVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricestrategy.PriceStrategyBoardRespVO;
import info.qizhi.aflower.module.pms.service.price.PriceStrategyService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class PriceStrategyApiImpl implements PriceStrategyApi{

    @Resource
    PriceStrategyService priceStrategyService;

    @Override
    public CommonResult<List<PriceStrategyBoardRespDTO>> getPriceStrategyBoardList(PriceStrategyBoardReqDTO reqVO) {
        PriceStrategyBoardReqVO bean = BeanUtils.toBean(reqVO, PriceStrategyBoardReqVO.class);
        List<PriceStrategyBoardRespVO> list = priceStrategyService.getPriceStrategyBoardList(bean);
        return success(BeanUtils.toBean(list, PriceStrategyBoardRespDTO.class));
    }

    @Override
    public CommonResult<List<PriceStrategyBoardRespDTO>> getMultiMerchatPriceStrategyBoardList(PriceStrategyBoardReqDTO reqVO) {
        PriceStrategyBoardReqVO bean = BeanUtils.toBean(reqVO, PriceStrategyBoardReqVO.class);
        bean.setHcode(null);
        List<PriceStrategyBoardRespVO> list = priceStrategyService.getMultiMerchatPriceStrategyBoardList(bean);
        return success(BeanUtils.toBean(list, PriceStrategyBoardRespDTO.class));
    }
}
