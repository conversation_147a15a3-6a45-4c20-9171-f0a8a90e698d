package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.framework.common.enums.StatTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 实时房态 Request VO")
@Data
public class RealTimeRoomStatusTwoReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "统计类型")
    @NotEmpty(message = "统计类型不能为空")
    @InStringEnum(value = StatTypeEnum.class, message = "统计类型传值不正确")
    private String statType;

    @Schema(description = "房间代码列表,如果不传，则所有的房间")
    @JsonProperty(value = "rCodes")
    private List<String> rCodes;
}
