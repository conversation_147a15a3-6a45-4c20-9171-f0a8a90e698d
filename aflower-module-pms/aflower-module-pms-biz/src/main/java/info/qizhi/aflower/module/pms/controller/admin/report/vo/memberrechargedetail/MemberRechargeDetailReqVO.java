package info.qizhi.aflower.module.pms.controller.admin.report.vo.memberrechargedetail;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员充值&支付明细报表 Request VO")
@Data
public class MemberRechargeDetailReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "报表操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "会员级别")
    private String mtCode;

    @Schema(description = "姓名,手机号")
    private String keyWords;

    @Schema(description = "充值渠道")
    private String rechargeChannel;

    @Schema(description = "操作类型; 0: 会员充值，1: 会员支付")
    @InStringEnum(value = BooleanEnum.class,message = "操作类型; 0: 会员充值，1: 会员支付")
    @NotEmpty(message = "{optName.notempty}")
    private String operateType;

    @Schema(description = "营业日")
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

}
