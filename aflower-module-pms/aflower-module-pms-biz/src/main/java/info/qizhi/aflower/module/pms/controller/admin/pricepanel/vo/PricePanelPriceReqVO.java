package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 房价牌新增/修改 Request VO")
@Data
public class PricePanelPriceReqVO {
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "房型代码;存多个房型级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "会员级别代码;存多个会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;
}