package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.enums.AccountStatusEnum;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AccountPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号或订单号,预订时产生的账务存储预订单号，入住后产生的账务存储订单号	")
    private String no;

    @Schema(description = "消费科目代码;消费科目代码")
    private String subCode;

    @Schema(description = "科目类型;0: 消费科目 1：付款科目", example = "2")
    private String subType;

    @Schema(description = "状态;未结 已结 已冲 转出")
    @InStringEnum(value = AccountStatusEnum.class, message = "状态;未结 已结 已冲 转出")
    private String state;

    @Schema(description = "是否已核销;0:否 1：是；当银行卡、支票付款的账需要财务去查账并做核销操作")
    private String isVerify;

    @Schema(description = "关键字,挂账人姓名，房号，订单号，外部订单号")
    private String keyWords;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "挂账日期或营业日期;0:挂账日期，1:营业日期")
    @InStringEnum(value = BooleanEnum.class, message = "{accountingOrBusinessDate.instringenum}")
    private String dateRange;

    @Schema(description = "账套代码;如果是挂账，需要关联应收账(账套)的代码")
    private String arSetCode;

    @Schema(description = "价税展示；0：价税分离展示,1：价税合并展示")
    @InStringEnum(value = BooleanEnum.class, message = "价税展示；0：价税分离展示,1：价税合并展示")
    @NotEmpty(message = "价税展示不能为空")
    private String isPriceShow;


}