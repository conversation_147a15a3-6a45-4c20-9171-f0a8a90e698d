package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Schema(description = "biz - 删除预订房间 Request VO")
@Data
@ToString(callSuper = true)
public class BookRoomDeleteReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单号,删除单个排房时需要传入")
    @NotEmpty(message = "{orderNumber.notempty}")
    private String orderNo;

}
