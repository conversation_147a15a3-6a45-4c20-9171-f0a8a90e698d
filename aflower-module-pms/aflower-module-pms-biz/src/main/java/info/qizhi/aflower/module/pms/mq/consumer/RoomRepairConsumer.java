package info.qizhi.aflower.module.pms.mq.consumer;

import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.RoomRepairMessage;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.pms.dal.dataobject.room.RoomDO;
import info.qizhi.aflower.module.pms.service.room.RoomService;
import info.qizhi.aflower.module.pms.service.sender.RoomRepairChangeSenderService;
import info.qizhi.aflower.module.pms.service.sender.bo.RoomRepairInfoBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/**
 * 房间维修消息消费者
 * <AUTHOR>
 */
@Component
@RabbitListener(queues = QueueConstants.ROOM_REPAIR_MESSAGE_QUEUE)
@Slf4j
public class RoomRepairConsumer {

    @Resource
    @Lazy
    private RoomService roomService;

    @Resource
    private RoomRepairChangeSenderService roomRepairChangeSenderService;


    /**
     * 推送维修消息
     *
     * @param message 消息
     */
    @RabbitHandler
    public void onMessage(RoomRepairMessage message) {
        //休眠300毫秒
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        RoomDO roomByRCode = roomService.getRoomByRCode(message.getGcode(), message.getHcode(), message.getRcode());
        if (ObjectUtil.isNotEmpty(roomByRCode)){
            RoomRepairInfoBO roomRepairInfoBO = new RoomRepairInfoBO();
            roomRepairInfoBO.setRoomCode(message.getRcode());
            roomRepairInfoBO.setPhysicalRoomtypeCode(roomByRCode.getRtCode());
            roomRepairInfoBO.setRepairStartTime(roomByRCode.getRepairStartTime());
            roomRepairInfoBO.setRepairEndTime(roomByRCode.getRepairEndTime());
            String msg = JsonUtils.toJsonString(roomRepairInfoBO);
            roomRepairChangeSenderService.sendRoomRepairChangeInfo(message.getHcode(), message.getRepairType(), msg);
        }
    }
}
