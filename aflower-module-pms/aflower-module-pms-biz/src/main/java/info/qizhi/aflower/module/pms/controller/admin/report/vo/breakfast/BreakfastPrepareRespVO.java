package info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 早餐备餐报表明细 Response VO")
@Data
public class BreakfastPrepareRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "同住人数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer togetherNum;

    @Schema(description = "房包早", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer roomBkNum;

    @Schema(description = "赠送早餐数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer bkNum;

    @Schema(description = "可使用购买早餐数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer useAbleBuyBkNum;

    @Schema(description = "有效早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer useAbleBkNum;

}
