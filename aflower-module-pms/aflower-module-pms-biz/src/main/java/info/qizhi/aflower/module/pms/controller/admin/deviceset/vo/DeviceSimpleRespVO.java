package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import info.qizhi.aflower.module.pms.dal.dataobject.deviceset.DeviceSetDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 设备信息 Request VO")
@Data
public class DeviceSimpleRespVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "设备代码")
    private String deviceCode;

    @Schema(description = "设备品牌代码")
    private String brandCode;

    @Schema(description = "设备品牌")
    private String brandName;

    @Schema(description = "设备型号名称")
    private String deviceVerName;

    @Schema(description = "版本号")
    private String version;

    @Schema(description = "配置信息")
    @TableField(typeHandler = DeviceSetDO.ConfParameterTypeHandler.class)
    private List<DeviceSetDO.ConfParameter> conf;

}