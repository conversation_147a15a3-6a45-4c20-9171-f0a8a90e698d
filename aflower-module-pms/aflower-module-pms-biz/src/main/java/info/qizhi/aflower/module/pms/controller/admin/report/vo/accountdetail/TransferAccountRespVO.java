package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 转账报表 Response VO")
@Data
public class TransferAccountRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", example = "门店代码示例")
    private String no;

    @Schema(description = "超链接", example = "门店代码示例")
    private String url;

    @Schema(description = "转出房号", example = "门店代码示例")
    //@JsonProperty(value = "rNo")
    private String turnOutRNo;

    @Schema(description = "转入房号", example = "门店代码示例")
    private String turnInRNo;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "转出账务姓名", example = "门店代码示例")
    private String turnOutGuestName;

    @Schema(description = "转出账务姓名", example = "门店代码示例")
    private String turnInGuestName;

    @Schema(description = "金额", example = "门店代码示例")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "营业日", example = "门店代码示例")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "离店时间", example = "2024-07-04")
    private LocalDateTime checkoutTime;

    @Schema(description = "入住时间", example = "2024-07-04")
    private LocalDateTime checkinTime;

    @Schema(description = "操作时间", example = "2024-07-04")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "操作员", example = "门店代码示例")
    private String  recorder;

    @Schema(description = "操作员昵称", example = "门店代码示例")
    private String  recorderName;

    @Schema(description = "备注", example = "报表操作员")
    private String remark;

}
