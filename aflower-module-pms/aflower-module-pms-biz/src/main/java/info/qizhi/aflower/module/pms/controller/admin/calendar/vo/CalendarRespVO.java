package info.qizhi.aflower.module.pms.controller.admin.calendar.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 日历 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CalendarRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4664")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "日历代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日历代码")
    private String calendarCode;

    @Schema(description = "日历名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("日历名称")
    private String calendarName;

    @Schema(description = "共享范围;0：单店使用 1：集团共享", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("共享范围;0：单店使用 1：集团共享")
    private String scope;

    @Schema(description = "门店代码;scope=0单店使用时需要填写酒店代码，否则为null")
    @ExcelProperty("门店代码;scope=0单店使用时需要填写酒店代码，否则为null")
    private String hcode;

    @Schema(description = "门店名称;scope=0单店使用时需要填写酒店名称，否则为null")
    @ExcelProperty("门店名称;scope=0单店使用时需要填写酒店名称，否则为null")
    private String hname;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("结束日期")
    private LocalDate endDate;

    @Schema(description = "选择方式;0：按周 1：按日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("选择方式;0：按周 1：按日")
    private String calendarMode;

    @Schema(description = "选择条件;用|号分隔存储,如：0|1|2|3|4表示周一 周二 周三 周四11|12|14 表示11号 12号 14号")
    @ExcelProperty("选择条件;用|号分隔存储,如：0|1|2|3|4表示周一 周二 周三 周四11|12|14 表示11号 12号 14号")
    private String calendarCondition;

    @Schema(description = "日期;json对象，存放日历设置的每天的日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("日期;json对象，存放日历设置的每天的日期")
    private String calendarDate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1：有效")
    private String isEnable;

    @Schema(description = "是否使用中;0:未使用  1：使用中	当其他业务引用该日历，状态改为1，使用中的修改只能修改结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否使用中;0:未使用  1：使用中	当其他业务引用该日历，状态改为1，使用中的修改只能修改结束日期")
    private String isUse;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}