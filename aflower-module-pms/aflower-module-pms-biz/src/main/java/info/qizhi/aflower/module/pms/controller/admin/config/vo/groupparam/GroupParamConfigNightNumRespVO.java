package info.qizhi.aflower.module.pms.controller.admin.config.vo.groupparam;

import info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.NightNum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 集团参数设置 夜审间夜数 Response VO")
@Data
public class GroupParamConfigNightNumRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "5103")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String paramType;

    @Schema(description = "集团参数设置 间夜数设置", requiredMode = Schema.RequiredMode.REQUIRED)
    private NightNum value;

}