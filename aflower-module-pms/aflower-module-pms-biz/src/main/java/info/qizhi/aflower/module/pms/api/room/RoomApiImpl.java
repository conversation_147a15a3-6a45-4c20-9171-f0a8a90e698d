package info.qizhi.aflower.module.pms.api.room;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.room.dto.RoomRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.room.RoomReqVO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.room.RoomRespVO;
import info.qizhi.aflower.module.pms.dal.dataobject.room.RoomDO;
import info.qizhi.aflower.module.pms.service.room.RoomService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Lazy
public class RoomApiImpl implements RoomApi {

    @Resource
    private RoomService roomService;

    @Override
    public CommonResult<RoomRespDTO> getRoomByRNo(String hcode, String rNo) {
        RoomRespVO roomByRNo = roomService.getRoomByRNo(hcode, rNo);
        return success(BeanUtils.toBean(roomByRNo, RoomRespDTO.class));
    }

    @Override
    public CommonResult<List<RoomRespDTO>> getRoomList(String gcode, String hcode) {
        List<RoomDO> roomDOList = roomService.getRoomList(RoomReqVO.builder()
                                                                   .gcode(gcode)
                                                                   .hcode(hcode)
                                                                   .build());
        List<RoomRespDTO> roomRespVOList = BeanUtils.toBean(roomDOList, RoomRespDTO.class);
        return success(roomRespVOList);
    }

    @Override
    public CommonResult<List<RoomRespDTO>> getRoomList2(String gcode, String hcode) {
        List<RoomRespVO> roomList = roomService.getRoomList2(RoomReqVO.builder()
                                                                   .gcode(gcode)
                                                                   .hcode(hcode)
                                                                   .build());
        List<RoomRespDTO> roomRespVOList = BeanUtils.toBean(roomList, RoomRespDTO.class);
        return success(roomRespVOList);
    }
}
