package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class InvoiceOrderReqListVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "酒店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "酒店代码不能为空")
    private String hcode;

    @Schema(description = "订单号，在订单里查询发票需要传")
    private String orderNo;

    @Schema(description = "联房号")
    @NotBlank(message = "联房单号不能为空")
    private String bindCode;

}
