package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.*;

@Schema(description = "管理后台 - 冲账调账明细报表 Response VO")
@Data
public class RedAndAdjustReportRespVO {
    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "开始日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Resource(description = "冲账调账明细报表")
    private List<RedAndAdjustRespVO> list;



}
