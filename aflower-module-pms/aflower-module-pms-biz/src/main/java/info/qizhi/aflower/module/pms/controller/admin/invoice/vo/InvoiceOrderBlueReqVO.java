package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/24 11:20
 */
@Data
public class InvoiceOrderBlueReqVO extends InvoiceOrderReqVO {

    @Schema(description = "发票号码")
    @Size(max = 30, message = "发票号码长度不能超过 30 个字符")
    private String invoiceNo;

    @Schema(description = "发票代码")
    @Size(max = 30, message = "发票代码长度不能超过 30 个字符")
    private String invoiceCode;

    @Schema(description = "发票类型 0:公司专票 1:公司普票 2:个人普票")
    private String invoiceType;

    @Schema(description = "发票格式 0:纸质发票 1:电子发票 2:数电发票")
    private String invoiceFormat;

    @Schema(description = "购方名称")
    private String buyerName;

    @Schema(description = "购方纳税人识别号")
    private String buyerTaxpayerId;

    @Schema(description = "购方地址")
    private String buyerAddress;

    @Schema(description = "购方电话")
    private String buyerPhone;

    @Schema(description = "购方开户行")
    private String buyerBankName;

    @Schema(description = "购方账户")
    private String buyerBankNo;

}
