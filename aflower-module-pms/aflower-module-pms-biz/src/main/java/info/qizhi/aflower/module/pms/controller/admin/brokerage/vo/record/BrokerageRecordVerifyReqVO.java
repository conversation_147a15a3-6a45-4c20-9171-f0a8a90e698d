package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 返佣记录核销(撤销) Request VO")
@Data
public class BrokerageRecordVerifyReqVO {

    @Schema(description = "核销（撤销）操作; 核销：1 ，撤销：0")
    @NotEmpty(message = "{verificationStatus.notempty}")
    @InStringEnum(value = BooleanEnum.class)
    private String state;

    @Schema(description = "返佣id列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{commissionIdList.notempty}")
    private List<Long> idList;

}
