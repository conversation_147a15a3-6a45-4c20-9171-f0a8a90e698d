package info.qizhi.aflower.module.pms.controller.admin.nightaudi;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.NightAudiReportReqVO;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.NightAudiReqVO;
import info.qizhi.aflower.module.pms.mq.producer.NightAudiProducer;
import info.qizhi.aflower.module.pms.mq.producer.NightAudiReportProducer;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 夜审")
@RestController
@RequestMapping("/pms/night-audi")
@Validated
public class NightAudiController {
    @Resource
    private NightAudiProducer nightAudiProducer;
    @Resource
    private NightAudiReportProducer nightAudiReportProducer;
    @Resource
    private NightAudiService nightAudiService;

    @PostMapping("/begin")
    @Operation(summary = "夜审")
    @PreAuthorize("@ss.hasPermission('pms:night-audi:create:begin')")
    public CommonResult<Boolean> createNightAudi(@Valid @RequestBody NightAudiReqVO r) {
        // 将夜审任务发送到队列
        nightAudiProducer.sendNightAudiMessage(r.getGcode(), r.getHcode());
        return success(true);
    }

    @GetMapping("/is-night-audi")
    @Operation(summary = "是否已夜审")
    @Parameter(name = "hcode", description = "门店代码")
    //@PreAuthorize("@ss.hasPermission('pms:night-audi:query, pms:night-audi:create:begin')")
    public CommonResult<Boolean> isNightAudi(@RequestParam("hcode") String hcode) {
        return success(nightAudiService.isNightAudi(hcode));
    }

    @PostMapping("/report")
    @Operation(summary = "生成夜审报表(当夜审报表失败时，可手动执行该接口)")
    @PreAuthorize("@ss.hasPermission('pms:night-audi:create')")
    public CommonResult<Boolean> createNightAudiReport(@Valid @RequestBody NightAudiReportReqVO reqVO) {
        // 将夜审报表任务发送到队列
        nightAudiReportProducer.sendNightAudiReportMessage(reqVO.getGcode(), reqVO.getHcode(), reqVO.getBizDate(), reqVO.getLastBizDateTime());
        return success(true);
    }

}