package info.qizhi.aflower.module.pms.controller.admin.customer.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/*
    <AUTHOR>
    @description 
    @create 2024 12 2024/12/23 10:36
*/
@Schema(description = "管理后台 - 客历 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustomerNoDesRespVO {
	@Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22512")
	@ExcelProperty("id")
	private Long id;

	@Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("集团代码")
	private String gcode;

	@Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("门店代码")
	private String hcode;

	@Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
	@ExcelProperty("姓名")
	private String name;

	@Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("性别")
	private String sex;

	@Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
	@ExcelProperty("证件类型")
	private String idType;

	@Schema(description = "证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("证件号码")
	private String idNo;

	@Schema(description = "出生日期")
	@ExcelProperty("出生日期")
	@JsonSerialize(using = LocalDateSerializer.class)
	@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
	private LocalDate birthday;

	@Schema(description = "电话")
	@ExcelProperty("电话")
	private String phone;

	@Schema(description = "民族")
	@ExcelProperty("民族")
	private String nation;

	@Schema(description = "地址")
	@ExcelProperty("地址")
	private String address;

	@Schema(description = "黑名单;0正常 1拉黑", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("黑名单;0正常 1拉黑")
	private String isBlack;

	@Schema(description = "拉黑原因", example = "不香")
	@ExcelProperty("拉黑原因")
	private String blackReason;

	@Schema(description = "是否接收短信;0：不接收 1：接收", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("是否接收短信;0：不接收 1：接收")
	private String isSms;

	@Schema(description = "备注", example = "你说的对")
	@ExcelProperty("备注")
	private String remark;

	@Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
	@ExcelProperty("创建时间")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
	private LocalDateTime createTime;

	@Schema(description = "入住次数", example = "3")
	@ExcelProperty("入住次数")
	private Integer checkinNum;

	@Schema(description = "是否会员 0: 否 1: 是")
	@ExcelProperty("是否会员")
	private String isMember;

	@Schema(description = "最后一次入住门店名称", example = "张三")
	@ExcelProperty("最后一次入住门店名称")
	private String lastMerchant;

	@Schema(description = "最后一次入住时间", example = "2022-02-02 00:00:00")
	@ExcelProperty("最后一次入住时间")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
	private LocalDateTime lastCheckinTime;

	@Schema(description = "首次入住门店名称", example = "张三")
	@ExcelProperty("首次入住门店名称")
	private String firstMerchant;

	@Schema(description = "会员级别名称")
	@ExcelProperty("会员级别名称")
	private String mtName;

	@Schema(description = "会员有效期")
	@ExcelProperty("会员有效期")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
	private LocalDateTime period;

	@Schema(description = "会员卡号")
	@ExcelProperty("会员卡号")
	private String mCardNum;

	@Schema(description = "邮箱地址")
	@ExcelProperty("邮箱地址")
	private String email;
}
