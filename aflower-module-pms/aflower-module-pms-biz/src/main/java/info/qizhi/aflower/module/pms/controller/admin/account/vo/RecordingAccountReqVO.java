package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import info.qizhi.aflower.framework.common.enums.AccountTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Schema(description = "biz - 账务补录 Request VO")
@Data
public class RecordingAccountReqVO {
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "外部订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "外部订单号不能为空")
    private String outOrderNo;

  /*  @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{subCode.notblank}")
    private String subCode;*/

   /* @Schema(description = "支付类型值 1为收款，-1为退款", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{payValue.notblank}")
    private String payValue;*/

    @Schema(description = "预订单号或订单号,预订时产生的账务存储预订单号，入住后产生的账务存储订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{no.notblank}")
    private String no;

    @Schema(description = "宾客代码，订单入账时需要")
    private String togetherCode;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= AccountTypeEnum.class, message = "{accType.instringenum}")
    private String accType;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;
}
