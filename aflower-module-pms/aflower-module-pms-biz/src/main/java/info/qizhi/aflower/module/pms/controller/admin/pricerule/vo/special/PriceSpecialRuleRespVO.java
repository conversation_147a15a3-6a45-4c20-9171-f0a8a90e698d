package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.special;

import com.fasterxml.jackson.annotation.JsonFormat;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.chargerule.RtFeeRule;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalTime;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.*;
import org.springframework.format.annotation.DateTimeFormat;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 特殊房(白天房午夜房)计费规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PriceSpecialRuleRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17799")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "开始时间(时分);如：16：00")
    @ExcelProperty("开始时间(时分);如：16：00")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime startTime;

    @Schema(description = "结束时间(时分);如：16：00")
    @ExcelProperty("结束时间(时分);如：16：00")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime endTime;

    @Schema(description = "固定退房时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("固定退房时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime checkOutTime;

    @Schema(description = "入住N分钟后收起步费")
    @ExcelProperty("入住N分钟后收起步费")
    private Integer startPriceNMin;

    @Schema(description = "入住N分钟后收全价")
    @ExcelProperty("入住N分钟后收全价")
    private Integer allPriceNMin;

    @Schema(description = "预离超时N分钟后收费")
    @ExcelProperty("预离超时N分钟后收费")
    private Integer overNMinCollect;

    @Schema(description = "超时收费规则;0：按时租房半价加收 1：按每小时加收", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("超时收费规则;0：按时租房半价加收 1：按每小时加收")
    private String overCollectStyle;

    @Schema(description = "预离超过N分钟后转全日租")
    @ExcelProperty("预离超过N分钟后转全日租")
    private Integer overNMinAllDay;

    @Schema(description = "房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系")
    @ExcelProperty("房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系")
    private List<RtFeeRule> rtFeeRule;

    @Schema(description = "类型;0：白天房 1：午夜房", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型;0：白天房 1：午夜房")
    private String ruleType;

    @Schema(description = "说明", example = "随便")
    @ExcelProperty("说明")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}