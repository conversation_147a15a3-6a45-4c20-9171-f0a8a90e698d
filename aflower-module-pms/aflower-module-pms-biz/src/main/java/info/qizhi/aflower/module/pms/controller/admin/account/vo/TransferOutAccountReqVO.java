package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务 - 转账 Request VO")
@Data
public class TransferOutAccountReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "转入账号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{no.notblank}")
    private String no;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{togetherCode.notempty}")
    private String togetherCode;

    @Schema(description = "转入账号的房号,如果是团队，这里传'团队主单'", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人名称，如果是团队，显示团队名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{guestName.notblank}")
    private String guestName;

    @Schema(description = "转入的账号是否为团队主单,0:宾客账号 1:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{isTeam.notblank}")
    private String isTeam;

    @Schema(description = "备注")
    @Size(max = 200, message = "{remark.size200}")
    private String remark;

    @Schema(description = "账单号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accNoList.notempty}")
    private List<String> accNoList;

    @Schema(description = "是否从预订账号转出，0：否 1：是; 1：表示是从预订单转账到第一个客单下")
    @InStringEnum(value = BooleanEnum.class)
    private String isBookAccount;
}