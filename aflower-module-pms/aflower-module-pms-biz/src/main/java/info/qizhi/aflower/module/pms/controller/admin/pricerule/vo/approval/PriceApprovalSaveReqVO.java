package info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 集团价格审批新增/修改 Request VO")
@Data
public class PriceApprovalSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25451")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "审批代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String approvalCode;

    @Schema(description = "自定义价格名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{approvalName.notempty}")
    @Size(max = 30, message = "{approvalName.size}")
    private String approvalName;

    @Schema(description = "房价折扣", example = "0.85")
    @DecimalMax(value = "1", message = "{discount.max}")
    @DecimalMin(value = "0", message = "{discount.min}")
    private BigDecimal discount;

    @Schema(description = "涨价", example = "189.4")
    @Max(value = 99999999, message = "{markUp.max}")
    @Min(value = 0, message = "{markUp.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long markUp;

    @Schema(description = "降价", example = "189.4")
    @Max(value = 99999999, message = "{markUp.max}")
    @Min(value = 0, message = "{markUp.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long markDown;

    @Schema(description = "是否有效 0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum( value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

}