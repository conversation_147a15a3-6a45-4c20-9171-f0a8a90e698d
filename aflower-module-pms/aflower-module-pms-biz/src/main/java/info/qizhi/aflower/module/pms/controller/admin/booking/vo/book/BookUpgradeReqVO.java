package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 散客预订 Request VO")
@Data
@ToString(callSuper = true)
public class BookUpgradeReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "升级方式;0:免费升级，1:补差价")
    private String upgradeMethod;

    @Schema(description = "预订单号(当修改占房、修改预订操作时需要携带预订单号)")
    @NotEmpty(message = "预订单号不能为空")
    private String bookNo;

    @Schema(description = "房型代码")
    @NotEmpty(message = "房型代码不能为空")
    private String rtCode;

    @Schema(description = "升级房型代码")
    @NotEmpty(message = "升级房型代码不能为空")
    private String upgradeRtCode;

    @Schema(description = "订单号,给指定房间升级，为空则升级整个预订单下的所有房间")
    private String orderNo;

}
