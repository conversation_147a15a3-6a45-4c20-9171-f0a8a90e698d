package info.qizhi.aflower.module.pms.controller.admin.deviceset;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.deviceset.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.deviceset.DeviceSetDO;
import info.qizhi.aflower.module.pms.service.deviceset.DeviceSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 设备信息")
@RestController
@RequestMapping("/pms/device-set")
@Validated
public class DeviceSetController {

    @Resource
    private DeviceSetService deviceSetService;

    @PostMapping("/create")
    @Operation(summary = "创建设备信息")
    public CommonResult<Long> createDeviceSet(@Valid @RequestBody DeviceSetSaveReqVO createReqVO) {
        return success(deviceSetService.createDeviceSet(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新设备信息")
    public CommonResult<Boolean> updateDeviceSet(@Valid @RequestBody DeviceSetSaveReqVO updateReqVO) {
        deviceSetService.updateDeviceSet(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得设备信息")
    public CommonResult<DeviceSetRespVO> getDeviceSet(@Valid DeviceSetReqVO reqVO) {
        DeviceSetDO deviceSet = deviceSetService.getDeviceSet(reqVO);
        return success(BeanUtils.toBean(deviceSet, DeviceSetRespVO.class));
    }

    @GetMapping("/get-door-lock")
    @Operation(summary = "获得门锁信息")
    public CommonResult<DeviceLockRespVO> getDeviceDoorLock(@Valid DeviceDoorLockReqVO reqVO) {
        DeviceLockRespVO deviceSet = deviceSetService.getDeviceDoorLock(reqVO);
        return success(deviceSet);
    }

    @GetMapping("/list-brand")
    @Operation(summary = "获得设备品牌")
    public CommonResult<List<DeviceSimpleRespVO>> getDeviceBrandList(@Valid DeviceSimpleReqVO reqVO) {
        List<DeviceSetDO> brandList = deviceSetService.getDeviceBrandList(reqVO);
        return success(BeanUtils.toBean(brandList, DeviceSimpleRespVO.class));
    }

    @GetMapping("/card-dispenser-type")
    @Operation(summary = "获得发卡器类型")
    public CommonResult<List<String>> getDeviceDispenserList(@Valid DeviceSimpleReqVO reqVO) {
        List<String> dispenserList = deviceSetService.getDeviceDispenserList(reqVO);
        return success(dispenserList);
    }

    @GetMapping("/get-ver-name")
    @Operation(summary = "获得门锁型号")
    public CommonResult<List<LockModelRespVO>> getDeviceVerName(@Valid DeviceSimpleReqVO reqVO) {
        List<DeviceSetDO> brandList = deviceSetService.getDeviceBrandList(reqVO);
        return success(BeanUtils.toBean(brandList, LockModelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得设备信息分页")
    @PreAuthorize("@ss.hasPermission('pms:device-set:page')")
    public CommonResult<PageResult<DeviceSetRespVO>> getDeviceSetPage(@Valid DeviceSetPageReqVO pageReqVO) {
        PageResult<DeviceSetDO> pageResult = deviceSetService.getDeviceSetPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeviceSetRespVO.class));
    }

}