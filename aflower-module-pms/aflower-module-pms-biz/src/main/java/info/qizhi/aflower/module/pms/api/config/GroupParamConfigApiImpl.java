package info.qizhi.aflower.module.pms.api.config;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.config.dto.group.GroupParamConfigMemberRespDTO;
import info.qizhi.aflower.module.pms.service.config.GroupParamConfigService;
import info.qizhi.aflower.module.pms.service.config.bo.memberconfig.MemberConfigBO;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class GroupParamConfigApiImpl implements GroupParamConfigApi {
    @Resource
    private GroupParamConfigService groupParamConfigService;
    @Override
    public CommonResult<GroupParamConfigMemberRespDTO> getGroupParamConfigMember(String gcode) {
        MemberConfigBO memberConfigBO = groupParamConfigService.getGroupParamConfigMember(gcode);
        return success(BeanUtils.toBean(memberConfigBO, GroupParamConfigMemberRespDTO.class));
    }
}
