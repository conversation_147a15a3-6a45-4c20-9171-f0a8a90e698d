package info.qizhi.aflower.module.pms.controller.admin.report.vo.breakfast;


import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 购早/退早明细 Response VO")
@Data
public class BuyBkOrReturnBkDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rNo;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "客源")
    @ExcelProperty("客源")
    @JsonProperty("gSrc")
    private String gSrc;

    @Schema(description = "客源名称")
    @JsonProperty("gSrcName")
    @ExcelProperty("客源名称")
    private String gSrcName;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "购/退早份数", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Integer num;

    @Schema(description = "购/退早类型; 0:购早, 1:退早", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String bkType;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;

    @Schema(description = "入账操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operater;

}
