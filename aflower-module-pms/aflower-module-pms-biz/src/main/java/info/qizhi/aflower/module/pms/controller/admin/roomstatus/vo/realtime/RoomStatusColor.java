package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 实时房态 - 房态颜色 Response VO")
@Data
public class RoomStatusColor {

    @Schema(description = "房态代码", example = "'VC','VD','OC','OD','OO'")
    private String code;

    @Schema(description = "房态名称", example = "'空净','空脏','住净','住脏','维修'")
    private String name;

    @Schema(description = "房态颜色", example = "#1B4683','#54B69C','#0A5455','#CA5C5D','#649DEE'")
    private String color;
}
