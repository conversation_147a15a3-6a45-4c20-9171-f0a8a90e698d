package info.qizhi.aflower.module.pms.controller.admin.protocolagent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.common.validation.Mobile;
import info.qizhi.aflower.framework.common.validation.Telephone;
import info.qizhi.aflower.module.pms.controller.admin.arset.vo.ArSetSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.InvoiceSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 协议单位、中介新增/修改 Request VO")
@Data
public class ProtocolAgentSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4871")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "所属酒店代码")
    private String belongHcode;

    @Schema(description = "中介/协议单位代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String paCode;

    @Schema(description = "中介/协议单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{paName.notempty}")
    @Size(max = 30, message = "{paName.size}")
    private String paName;

    @Schema(description = "简称", example = "王五")
    @Size(max = 30, message = "{shortName.size}")
    private String shortName;

    @Schema(description = "法人;公司法人")
    private String legalPerson;

    @Schema(description = "电话;公司电话")
    @Telephone
    private String telephone;

    @Schema(description = "地址;公司地址")
    @Size(max = 255, message = "{address.size}")
    private String address;

    @Schema(description = "类型;0：协议单位 1：中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{paType.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{paType.invalid}")
    private String paType;

    @Schema(description = "渠道;中介才有,存储渠道代码")
    private String channel;

    @Schema(description = "是否共享;0:单店使用 1：集团共享；0时挂账的酒店范围就是单店", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isShare.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isShare.invalid}")
    private String isShare;

    @Schema(description = "联系人姓名")
    @Size(max = 30, message = "{contact.size30}")
    private String contact;

    @Schema(description = "联系人电话")
    @Mobile
    private String phone;

    @Schema(description = "联系人email")
    @Email(message = "{email.invalid}")
    private String email;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{startDate.notnull}")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{endDate.notnull}")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isEnable.notempty}")
    private String isEnable;

    @Schema(description = "登记单隐藏房价;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED, example = "17953")
    @NotEmpty(message = "{isHidePrice.notempty}")
    private String isHidePrice;

    @Schema(description = "是否允许挂账;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isCredit.notempty}")
    private String isCredit;

    @Schema(description = "销售等级")
    private String sellLevel;

    @Schema(description = "佣金等级")
    private String commissionLevel;

    @Schema(description = "应收账套")
    private ArSetSaveReqVO arSet;

    @Schema(description = "开票信息")
    private InvoiceSaveReqVO invoice;

    @Schema(description = "备注", example = "你说的对")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "是否系统初始;0:否 1:是")
    private String isSys;

}