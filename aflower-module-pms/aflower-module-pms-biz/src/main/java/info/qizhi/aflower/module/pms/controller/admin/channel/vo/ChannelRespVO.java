package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 渠道 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChannelRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19070")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;当is_g=0时，当前字段有值")
    @ExcelProperty("门店代码;当is_g=0时，当前字段有值")
    private String hcode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("渠道代码")
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("渠道名称")
    private String channelName;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "是否集团渠道;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否集团渠道;0:否 1:是")
    private String isG;

    @Schema(description = "类型;0:直营 1：分销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型;0:直营 1：分销")
    private String channelType;

    @Schema(description = "是否系统初始;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否系统初始;0:否 1:是")
    private String isSys;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}