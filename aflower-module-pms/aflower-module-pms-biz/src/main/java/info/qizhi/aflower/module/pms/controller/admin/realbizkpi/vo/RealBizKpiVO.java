package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "小程序首页 - 实时经营数据 Response VO")
@Data
public class RealBizKpiVO {

    @Schema(description = "门店收入", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "时租房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long hourRooms;

    @Schema(description = "全日房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long allRooms;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal occ;

    @Schema(description = "实时房态", requiredMode = Schema.RequiredMode.REQUIRED)
    private RealTimeRoom realTimeRoom;

    @Schema(description = "房型出租率")
    List<RealOcc> realOccList;
}
