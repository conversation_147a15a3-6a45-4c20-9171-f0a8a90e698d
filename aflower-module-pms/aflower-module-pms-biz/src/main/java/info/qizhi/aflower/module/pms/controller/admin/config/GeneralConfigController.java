package info.qizhi.aflower.module.pms.controller.admin.config;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.DictTypeEnum;
import info.qizhi.aflower.framework.common.enums.GeneralConfigTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.locale.MessageSourceUtil;
import info.qizhi.aflower.framework.common.util.date.DateUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.general.*;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GeneralConfigDO;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 通用配置")
@RestController
@RequestMapping("/pms/general-config")
@Validated
public class GeneralConfigController {

    @Resource
    private GeneralConfigService generalConfigService;

    @PostMapping("/create-reason")
    @Operation(summary = "创建通用配置-订单原因")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Long> createGeneralConfigReason(@Valid @RequestBody GeneralConfigReasonSaveReqVO createReqVO) {
        return success(generalConfigService.createGeneralConfig(BeanUtils.toBean(createReqVO, GeneralConfigSaveReqVO.class)));
    }

    @PutMapping("/update-reason")
    @Operation(summary = "更新通用配置-订单原因")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigReason(@Valid @RequestBody GeneralConfigReasonSaveReqVO updateReqVO) {
        generalConfigService.updateGeneralConfig(BeanUtils.toBean(updateReqVO, GeneralConfigSaveReqVO.class));
        return success(true);
    }

    @PutMapping("/update-reason-status")
    @Operation(summary = "更新通用配置-订单原因状态")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigReasonStatus(@Valid @RequestBody GeneralConfigUpdateStatusReqVO updateReqVO) {
        generalConfigService.updateGeneralConfigStatus(updateReqVO.getId(), updateReqVO.getIsEnable());
        return success(true);
    }

    @PostMapping("/create-room-feature")
    @Operation(summary = "创建通用配置-房间特征")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Long> createGeneralConfigRoomFeature(@Valid @RequestBody GeneralConfigFeatureSaveReqVO createReqVO) {
        createReqVO.setIsG(BooleanEnum.FALSE.getValue())
                   .setIsEnable(BooleanEnum.TRUE.getValue())
                   .setCode(IdUtil.getSnowflakeNextIdStr())
                   .setValue(createReqVO.getName())
                   .setType(DictTypeEnum.DICT_TYPE_ROOM_FEATURE.getCode());
        return success(generalConfigService.createGeneralConfig(BeanUtils.toBean(createReqVO, GeneralConfigSaveReqVO.class)));
    }

    @PutMapping("/update-room-feature")
    @Operation(summary = "更新通用配置-房间特征")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigRoomFeature(@Valid @RequestBody GeneralConfigFeatureSaveReqVO updateReqVO) {
        updateReqVO.setType(DictTypeEnum.DICT_TYPE_ROOM_FEATURE.getCode())
                   .setValue(updateReqVO.getName())
                   .setIsG(BooleanEnum.FALSE.getValue());
        generalConfigService.updateGeneralConfig(BeanUtils.toBean(updateReqVO, GeneralConfigSaveReqVO.class));
        return success(true);
    }

    @PutMapping("/update-room-feature-status")
    @Operation(summary = "更新通用配置-房间特征状态")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigRoomFeatureStatus(@Valid @RequestBody GeneralConfigUpdateStatusReqVO updateReqVO) {
        generalConfigService.updateGeneralConfigStatus(updateReqVO.getId(), updateReqVO.getIsEnable());
        return success(true);
    }

    @PutMapping("/update-pay-account-status")
    @Operation(summary = "更新通用配置-付款科目状态")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigPayAccountStatus(@Valid @RequestBody GeneralConfigUpdateStatusReqVO updateReqVO) {
        generalConfigService.updateGeneralConfigStatus(updateReqVO.getId(), updateReqVO.getIsEnable());
        return success(true);
    }

    @PutMapping("/update-consume-account-status")
    @Operation(summary = "更新通用配置-消费科目状态")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGeneralConfigConsumeAccountStatus(@Valid @RequestBody GeneralConfigUpdateStatusReqVO updateReqVO) {
        generalConfigService.updateGeneralConfigStatus(updateReqVO.getId(), updateReqVO.getIsEnable());
        return success(true);
    }

    @PutMapping("/update-default-plan-checkin-time")
    @Operation(summary = "更新默认预抵时间")
    @PreAuthorize("@ss.hasPermission('pms:general-config:update:default-plan-checkin-time')")
    public CommonResult<Boolean> updateDefaultPlanTime(@Valid @RequestBody GeneralConfigUpdateTimeReqVO updateReqVO) {
        generalConfigService.updateValue(updateReqVO.getId(), updateReqVO.getValue());
        return success(true);
    }


    @PostMapping("/create")
    @Operation(summary = "创建通用配置")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Long> createGeneralConfig(@Valid @RequestBody GeneralConfigSaveReqVO createReqVO) {
        return success(generalConfigService.createGeneralConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新通用配置")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create, pms:general-config:update:night-audi-set')")
    public CommonResult<Boolean> updateGeneralConfig(@Valid @RequestBody GeneralConfigSaveReqVO updateReqVO) {
        generalConfigService.updateGeneralConfig(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-batch")
    @Operation(summary = "批量更新通用配置")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create, pms:general-config:update:night-audi-set')")
    public CommonResult<Boolean> updateBatchGeneralConfig(@Valid @RequestBody List<GeneralConfigUpdateValueReqVO> updateReqVOs) {
        generalConfigService.updateBatchGeneralConfig(updateReqVOs);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得通用配置")
//    @PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GeneralConfigRespVO> getGeneralConfig(@Valid GeneralConfigReq2VO req) {
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(req);
        return success(convertToResponse(generalConfig));
    }

    @GetMapping("/get-how-minute")
    @Operation(summary = "获得超过多少分钟算1小时配置")
    @Parameters({
            @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024"),
            @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024"),
    })
//    @PreAuthorize("@ss.hasPermission('pms:price-rule:query')")
    public CommonResult<GeneralConfigRespVO> getHowMinuteConfig(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(new GeneralConfigReq2VO().setGcode(gcode)
                                                                                                       .setHcode(hcode)
                                                                                                       .setCode(GeneralConfigTypeEnum.HOW_MINUTE.getCode())
                                                                                                       .setType(GeneralConfigTypeEnum.HOW_MINUTE.getCode()));
        return success(convertToResponse(generalConfig));
    }

    @GetMapping("/get-check-out-time")
    @Operation(summary = "获得集团的退房时间")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GeneralConfigRespVO> getCheckOutTimeConfig(@RequestParam("gcode") String gcode) {
        GeneralConfigDO generalConfig = generalConfigService.getGeneralConfig(gcode, GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode());
        return success(convertToResponse(generalConfig));
    }

    @GetMapping("/get-biz-date")
    @Operation(summary = "获得门店的营业日")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<String> getHowMinuteConfig(@RequestParam("hcode") String hcode) {
        LocalDate date = generalConfigService.getBizDate(hcode);
        return success(DateUtil.format(date.atStartOfDay(), DateUtils.FORMAT_YEAR_MONTH_DAY));
    }

    @GetMapping("/get-warehouse")
    @Operation(summary = "获得门店仓库配置")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<String> getWarehouseConfig(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        return success(generalConfigService.getWarehouseConfig(gcode, hcode));
    }

    @PutMapping("/update-warehouse")
    @Operation(summary = "更新仓库配置")
    @PreAuthorize("@ss.hasPermission('pms:general-config:update:warehouse')")
    public CommonResult<Boolean> updateWarehouse(@Valid @RequestBody GeneralConfigUpdateReqVO updateReqVO) {
        generalConfigService.updateWarehouse(updateReqVO);
        return success(true);
    }

    @GetMapping("/get-pending-payment-time")
    @Operation(summary = "获得待支付时长")
    @Parameter(name = "hcode", description = "门店代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<LocalTime> getPendingPaymentTime(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode) {
        return success(generalConfigService.getPendingPaymentTime(gcode, hcode));
    }

    @GetMapping("/list")
    @Operation(summary = "获得通用配置列表(客户等级、订单原因、服务标识、房间特征)")
//    @PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<List<GeneralConfigRespVO>> getGeneralConfigList(@Valid GeneralConfigReqVO reqVO) {
        List<GeneralConfigDO> result = generalConfigService.getGeneralConfigList(reqVO);
        return success(BeanUtils.toBean(result, GeneralConfigRespVO.class));
    }

    @GetMapping("/list-pay-account")
    @Operation(summary = "获得付款科目列表")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "xxxx")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<List<GeneralConfigRespVO>> getPayAccountList(@RequestParam("gcode") String gcode) {
        GeneralConfigReqVO reqVO = new GeneralConfigReqVO().setParentCode(GeneralConfigTypeEnum.ALL_CREDIT_ACCOUNT.getCode())
                                                           .setGcode(gcode)
                                                           .setType(GeneralConfigTypeEnum.PAY_ACCOUNT.getCode())
                                                           .setIsEnable(BooleanEnum.TRUE.getValue());
        List<GeneralConfigDO> result = generalConfigService.getGeneralConfigList(reqVO);
        return success(convertToResponseList(result));
    }

    @GetMapping("/list-consume-account")
    @Operation(summary = "获得消费科目列表")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "xxxx")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<List<GeneralConfigRespVO>> getConsumeAccountList(@RequestParam("gcode") String gcode) {
        GeneralConfigReqVO reqVO = new GeneralConfigReqVO().setParentCode(GeneralConfigTypeEnum.ALL_DEBIT_ACCOUNT.getCode())
                                                           .setGcode(gcode)
                                                           .setType(GeneralConfigTypeEnum.CONSUME_ACCOUNT.getCode())
                                                           .setIsEnable(BooleanEnum.TRUE.getValue());
        List<GeneralConfigDO> result = generalConfigService.getGeneralConfigList(reqVO);
        return success(convertToResponseList(result));
    }


    private static List<GeneralConfigRespVO> convertToResponseList(List<GeneralConfigDO> results) {
        return BeanUtils.toBean(results, GeneralConfigRespVO.class).stream()
                        .peek(respVO -> {
                            // 动态获取 label 标签
                            String messageCode = respVO.getType() + "_" + respVO.getCode(); // 使用 code 生成唯一 message key
                            String message = MessageSourceUtil.getMessage(messageCode);
                            respVO.setName(messageCode.equals(message) ? respVO.getName() : message);
                        })
                        .collect(Collectors.toList());
    }

    private static GeneralConfigRespVO convertToResponse(GeneralConfigDO result) {
        GeneralConfigRespVO respVO = BeanUtils.toBean(result, GeneralConfigRespVO.class);
        String messageCode = respVO.getType() + "_" + respVO.getCode(); // 使用 code 生成唯一 message key
        // 如果 messageSource 返回的内容与 messageCode 相同，表示未找到对应的国际化内容
        String message = MessageSourceUtil.getMessage(messageCode);
        respVO.setName(messageCode.equals(message) ? respVO.getName() : message);
        return respVO;
    }

}