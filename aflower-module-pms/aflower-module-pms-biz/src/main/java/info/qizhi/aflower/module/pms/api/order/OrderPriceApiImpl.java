package info.qizhi.aflower.module.pms.api.order;

import groovy.lang.Lazy;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.order.dto.BreakFastReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderPriceReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderPriceRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.price.BreakFastReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.price.OrderPriceReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderPriceDO;
import info.qizhi.aflower.module.pms.service.order.OrderPriceService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class OrderPriceApiImpl implements OrderPriceApi {

    @Resource
    @Lazy
    private OrderPriceService orderPriceService;

    @Override
    public CommonResult<OrderPriceRespDTO> getBreakfastCoupon(BreakFastReqDTO reqVO) {
        OrderPriceDO breakfastCoupon = orderPriceService.getBreakfastCoupon(BeanUtils.toBean(reqVO, BreakFastReqVO.class));
        return success(BeanUtils.toBean(breakfastCoupon, OrderPriceRespDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateBreakfastCoupon(BreakFastReqDTO reqVO) {
        orderPriceService.updateBreakfastCoupon(BeanUtils.toBean(reqVO, BreakFastReqVO.class));
        return success(true);
    }

    @Override
    public CommonResult<List<OrderPriceRespDTO>> getOrderPriceList(OrderPriceReqDTO reqDTO) {
        List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(BeanUtils.toBean(reqDTO, OrderPriceReqVO.class));
        return success(BeanUtils.toBean(orderPriceList, OrderPriceRespDTO.class));
    }
}
