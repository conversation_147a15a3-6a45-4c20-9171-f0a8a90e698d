package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务科目 Request VO")
@Data
@ToString(callSuper = true)
public class AccountSubReqVO  {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "宾客代码列表")
    private List<String> togetherCodes;

    @Schema(description = "账号(入账账号)列表")
    private List<String> noList;

}