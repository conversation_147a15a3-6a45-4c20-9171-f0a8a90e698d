package info.qizhi.aflower.module.pms.controller.admin.report.vo.arentrydetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AR账入账明细 Response VO")
@Data
public class ArEntryDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "AR账套代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String arSetCode;

    @Schema(description = "AR账套名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String arSetName;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "收款金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "收款日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate receiveDate;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

    @Schema(description = "操作员昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operatorName;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED, example = "2023-07-12")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime operateTime;

    @Schema(description = "备注")
    private String remark;

}
