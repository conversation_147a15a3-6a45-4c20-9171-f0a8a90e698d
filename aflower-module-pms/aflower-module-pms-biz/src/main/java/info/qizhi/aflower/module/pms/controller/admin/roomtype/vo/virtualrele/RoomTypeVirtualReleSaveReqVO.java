package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.virtualrele;

import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 物理房型与虚拟房型关联新增/修改 Request VO")
@Data
public class RoomTypeVirtualReleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "892")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "关联代码;系统自动生成")
    private String releCode;

    @Schema(description = "物理房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{physicalRoomTypeCode.notempty}")
    private String rtCode;

    @Schema(description = "虚拟房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{virtualRoomTypeCode.notempty}")
    @JsonProperty("vRtCode")
    private String vRtCode;

    @Schema(description = "是否有效 0:无效 1:有效")
    @InStringEnum(value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

}