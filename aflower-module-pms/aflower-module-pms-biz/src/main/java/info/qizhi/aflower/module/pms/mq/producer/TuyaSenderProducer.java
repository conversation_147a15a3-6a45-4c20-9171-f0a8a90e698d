package info.qizhi.aflower.module.pms.mq.producer;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.module.pms.mq.message.tuya.OrderMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class TuyaSenderProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送消息
     *
     */
    public void sendOrderMessage(OrderMessage orderMessage) {
        rabbitTemplate.convertAndSend(QueueConstants.TUYA_MESSAGE_QUEUE, orderMessage);
    }

}
