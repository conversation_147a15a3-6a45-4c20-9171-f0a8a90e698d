package info.qizhi.aflower.module.pms.api.room;

import cn.hutool.core.bean.BeanUtil;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.room.dto.RoomLogSaveReqDTO;
import info.qizhi.aflower.module.pms.controller.admin.room.vo.log.RoomLogSaveReqVO;
import info.qizhi.aflower.module.pms.service.room.RoomLogService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
@Lazy
public class RoomLogApiImpl implements RoomLogApi{
    @Resource
    private RoomLogService roomLogService;

    @Override
    public CommonResult<Long> createRoomLog(RoomLogSaveReqDTO createReqVO) {
        return success(roomLogService.createRoomLog(BeanUtil.toBean(createReqVO, RoomLogSaveReqVO.class)));
    }

    @Override
    public CommonResult<Boolean> createRoomLogBatch(List<RoomLogSaveReqDTO> createReqVO) {
        List<RoomLogSaveReqVO> bean = BeanUtils.toBean(createReqVO, RoomLogSaveReqVO.class);
        roomLogService.createRoomLogBatch(bean);
        return success(true);
    }
}
