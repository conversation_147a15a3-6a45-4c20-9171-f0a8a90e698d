package info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 房扫明细报表 Response VO")
@Data
public class RoomCleanDetailReqVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "清洁工账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("清洁工账号")
    private String cleaner;

    @Schema(description = "打扫前房态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cleanState;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> rtCodes;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;
}
