package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 修改房价报表 Response VO")
@Data
public class ChangePriceRespVO {
    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "订单号", example = "门店代码示例")
    private String no;

    @Schema(description = "超链接", example = "门店代码示例")
    private String url;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value="rNo")
    private String rNo ;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestName ;

    @Schema(description = "统计渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statChannelCode;

    @Schema(description = "统计渠道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statChannelCodeName;

    @Schema(description = "客源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcType ;

    @Schema(description = "客源名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcTypeName;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinType ;

    @Schema(description = "入住类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinTypeName;

    @Schema(description = "客源关联账号")
    private String guestCode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "会员级别或公司", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String levelOrCompanyName;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkinTime ;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkoutTime ;

    @Schema(description = "原房价", example = "门店代码示例")
    private String originalPrice;

    @Schema(description = "现房价", example = "门店代码示例")
    private String newPrice;

    @Schema(description = "价格类型;0 放盘价 1 手工价", example = "门店代码示例")
    private String originalPriceType;

    @Schema(description = "价格类型名称", example = "门店代码示例")
    private String originalPriceTypeName;

    @Schema(description = "价格类型;0 放盘价 1 手工价", example = "门店代码示例")
    private String newPriceType;

    @Schema(description = "价格类型名称", example = "门店代码示例")
    private String newPriceTypeName;

    @Schema(description = "操作者", requiredMode = Schema.RequiredMode.REQUIRED)
    private String creator;

    @Schema(description = "操作者昵称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String creatorName;

    @Schema(description = "操作时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;
}
