package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单 Request VO")
@Data
public class Occupation {
    @Schema(description = "最大占用数")
    private int maxOccupied;
    @Schema(description = "入住状态的冲突房间数")
    private int checkinOccupied;
    @Schema(description = "预订状态的冲突房间数")
    private int noCheckInOccupied;
    @Schema(description = "维修状态的冲突房间数")
    private int OOOccupied;
    @Schema(description = "产生冲突的订单号")
    private List<String> conflictOrderNos;
    @Schema(description = "产生冲突的维修房的房间号")
    private List<String> conflictRNos;
    @Schema(description = "产生冲突的预订单号")
    private List<String> conflictBookNos;
}