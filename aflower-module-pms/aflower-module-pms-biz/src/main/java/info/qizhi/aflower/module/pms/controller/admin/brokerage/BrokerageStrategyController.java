package info.qizhi.aflower.module.pms.controller.admin.brokerage;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.strategy.*;
import info.qizhi.aflower.module.pms.service.brokerage.BrokerageStrategyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 佣金策略")
@RestController
@RequestMapping("/pms/brokerage-strategy")
@Validated
public class BrokerageStrategyController {

    @Resource
    private BrokerageStrategyService brokerageStrategyService;

    @PostMapping("/create")
    @Operation(summary = "创建佣金策略")
    @PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:create')")
    public CommonResult<Long> createBrokerageStrategy(@Valid @RequestBody BrokerageStrategySaveReqVO createReqVO) {
        return success(brokerageStrategyService.createBrokerageStrategy(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新佣金策略")
    @PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:create')")
    public CommonResult<Boolean> updateBrokerageStrategy(@Valid @RequestBody BrokerageStrategySaveReqVO updateReqVO) {
        brokerageStrategyService.updateBrokerageStrategy(updateReqVO);
        return success(true);
    }

    @PutMapping("/update-status")
    @Operation(summary = "更新佣金策略状态")
    @PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:create')")
    public CommonResult<Boolean> updateBrokerageStrategy(@Valid @RequestBody BrokerageStrategyUpdateStatusReqVO reqVO) {
        brokerageStrategyService.updateBrokerageStrategyStatus(reqVO.getId(), reqVO.getIsEnable());
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得佣金策略")
    @Parameter(name = "strategyCode", description = "策略代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:query')")
    public CommonResult<BrokerageStrategyRespVO> getBrokerageStrategy(@RequestParam("strategyCode") String strategyCode) {
        BrokerageStrategyRespVO brokerageStrategy = brokerageStrategyService.getBrokerageStrategyByStrategyCode(strategyCode);
        return success(brokerageStrategy);
    }

    @GetMapping("/page")
    @Operation(summary = "获得佣金策略分页(集团策略)")
    @PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:query:page')")
    public CommonResult<PageResult<BrokerageStrategyRespVO>> getBrokerageStrategyPage(@Valid BrokerageStrategyPageReqVO pageReqVO) {
        PageResult<BrokerageStrategyRespVO> pageResult = brokerageStrategyService.getBrokerageStrategyPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/list")
    @Operation(summary = "获得佣金策略(门店策略)")
    //@PreAuthorize("@ss.hasPermission('pms:brokerage-strategy:query:list')")
    public CommonResult<List<BrokerageStrategyRespVO>> getBrokerageStrategyList(@Valid BrokerageStrategyReqVO reqVO) {
        List<BrokerageStrategyRespVO> list = brokerageStrategyService.getBrokerageStrategyList(reqVO);
        return success(list);
    }

}