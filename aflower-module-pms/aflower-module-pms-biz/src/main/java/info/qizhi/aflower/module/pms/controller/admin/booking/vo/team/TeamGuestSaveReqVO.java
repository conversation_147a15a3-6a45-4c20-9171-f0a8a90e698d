package info.qizhi.aflower.module.pms.controller.admin.booking.vo.team;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 团队客人新增/修改 Request VO")
@Data
public class TeamGuestSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22623")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "关联book_room_type表book_rt_no;关联book_room_type表book_rt_no")
    private String bookRtNo;

    @Schema(description = "房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名", example = "张三")
    private String guestName;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "性别;0女 1男 2保密")
    private String sex;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "证件类型", example = "1")
    private String idType;

    @Schema(description = "证件号码")
    private String idNo;

    @Schema(description = "地址")
    private String addr;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}