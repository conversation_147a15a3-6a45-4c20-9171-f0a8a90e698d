package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 权益 Response VO")
@Data
public class RightsRespVO {

    @Schema(description = "价格策略代码")
    private String priceStrategyCode;

    @Schema(description = "延迟分钟数")
    private Long delayMinute;

    @Schema(description = "退房时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime checkOutTime;

}
