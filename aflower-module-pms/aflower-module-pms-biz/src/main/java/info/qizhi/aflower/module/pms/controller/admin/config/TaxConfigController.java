package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.tax.TaxConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.tax.TaxConfigRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.tax.TaxConfigSaveBatchReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.tax.TaxConfigSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.TaxConfigDO;
import info.qizhi.aflower.module.pms.service.config.TaxConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 集团门店税率配置")
@RestController
@RequestMapping("/pms/tax-config")
@Validated
public class TaxConfigController {

    @Resource
    private TaxConfigService taxConfigService;

    @PostMapping("/create")
    @Operation(summary = "批量创建集团门店税率配置")
    @PreAuthorize("@ss.hasPermission('pms:tax-config:create')")
    public CommonResult<Boolean> createTaxConfig(@Valid @RequestBody TaxConfigSaveBatchReqVO createReqVO) {
        return success(taxConfigService.createTaxConfig(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新集团门店税率配置")
    @PreAuthorize("@ss.hasPermission('pms:tax-config:create')")
    public CommonResult<Boolean> updateTaxConfig(@Valid @RequestBody TaxConfigSaveReqVO updateReqVO) {
        taxConfigService.updateTaxConfig(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除集团门店税率配置")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:tax-config:create')")
    public CommonResult<Boolean> deleteTaxConfig(@RequestParam("id") Long id) {
        taxConfigService.deleteTaxConfig(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得集团门店税率配置")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:tax-config:query')")
    public CommonResult<TaxConfigRespVO> getTaxConfig(@RequestParam("id") Long id) {
        TaxConfigDO taxConfig = taxConfigService.getTaxConfig(id);
        return success(BeanUtils.toBean(taxConfig, TaxConfigRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得集团或门店税率配置列表")
    //@PreAuthorize("@ss.hasPermission('pms:tax-config:query')")
    public CommonResult<List<TaxConfigRespVO>> getTaxConfigList(@Valid TaxConfigReqVO reqVO) {
        List<TaxConfigDO> pageResult = taxConfigService.getTaxConfigs(reqVO);
        return success(BeanUtils.toBean(pageResult, TaxConfigRespVO.class));
    }

}