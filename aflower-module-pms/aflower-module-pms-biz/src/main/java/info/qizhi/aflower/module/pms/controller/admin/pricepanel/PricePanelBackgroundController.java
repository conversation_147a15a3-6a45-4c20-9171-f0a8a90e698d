package info.qizhi.aflower.module.pms.controller.admin.pricepanel;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepanelbackground.PricePanelBackgroundRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepanelbackground.PricePanelBackgroundSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelBackgroundDO;
import info.qizhi.aflower.module.pms.service.pricepanel.PricePanelBackgroundService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 房价牌背景图片")
@RestController
@RequestMapping("/pms/price-panel-background")
@Validated
public class PricePanelBackgroundController {

    @Resource
    private PricePanelBackgroundService pricePanelBackgroundService;

    @PostMapping("/create")
    @Operation(summary = "创建房价牌背景图片")
    @PreAuthorize("@ss.hasPermission('biz:price-panel-background:create')")
    public CommonResult<Long> createPricePanelBackground(@Valid @RequestBody PricePanelBackgroundSaveReqVO createReqVO) {
        return success(pricePanelBackgroundService.createPricePanelBackground(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得房价牌背景图片")
    @Parameter(name = "backgroundCode", description = "背景图片代码", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('biz:price-panel-background:query')")
    public CommonResult<PricePanelBackgroundRespVO> getPricePanelBackground(@RequestParam("backgroundCode") String backgroundCode) {
        PricePanelBackgroundDO pricePanelBackground = pricePanelBackgroundService.getPricePanelBackground(backgroundCode);
        return success(BeanUtils.toBean(pricePanelBackground, PricePanelBackgroundRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得房价牌背景图片列表")
    @PreAuthorize("@ss.hasPermission('biz:price-panel-background:query')")
    public CommonResult<List<PricePanelBackgroundRespVO>> getPricePanelBackgroundList(@RequestParam("gcode") String gcode) {
        List<PricePanelBackgroundDO> pricePanelBackground = pricePanelBackgroundService.getPricePanelBackgroundList(gcode);
        return success(BeanUtils.toBean(pricePanelBackground, PricePanelBackgroundRespVO.class));
    }


}