package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "小程序 - 经营数据分类统计 Response VO")
@Data
public class BusinessData {

    @Schema(description = "分类类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cateGory;

    @Schema(description = "子类类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String classificationStatistics;

    @Schema(description = "入住类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinTypeName;

    @Schema(description = "房型统计", requiredMode = Schema.RequiredMode.REQUIRED)
    private RTCodeRoom rtCodeRoom;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal occ;

    @Schema(description = "总费用", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

    @Schema(description = "统计类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statType;

}
