package info.qizhi.aflower.module.pms.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.message.BookingOrderMessage;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.AccountListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.booking.vo.book.BookListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.price.OrderPriceReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.account.AccountDO;
import info.qizhi.aflower.module.pms.dal.dataobject.booking.BookDO;
import info.qizhi.aflower.module.pms.dal.dataobject.channel.MerchantChannelReleDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderPriceDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.account.AccountService;
import info.qizhi.aflower.module.pms.service.booking.BookService;
import info.qizhi.aflower.module.pms.service.channel.MerchantChannelReleService;
import info.qizhi.aflower.module.pms.service.order.OrderPriceService;
import info.qizhi.aflower.module.pms.service.order.OrderService;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import info.qizhi.aflower.module.pms.service.sender.BookingOrderChangeSendService;
import info.qizhi.aflower.module.pms.service.sender.bo.OrderSenderBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@RabbitListener(queues = QueueConstants.BOOKING_ORDER_MESSAGE_QUEUE) // 重点：添加 @RabbitListener 注解，声明消费的 queue
@Slf4j
public class BookingOrderConsumer {

    @Resource
    @Lazy
    private OrderService orderService;

    @Resource
    @Lazy
    private BookService bookService;

    @Resource
    @Lazy
    private OrderPriceService orderPriceService;

    @Resource
    @Lazy
    private BookingOrderChangeSendService bookingOrderChangeSendService;

    @Resource
    @Lazy
    private OrderTogetherService orderTogetherService;

    @Resource
    @Lazy
    private RoomTypeService roomTypeService;

    @Resource
    @Lazy
    private AccountService accountService;

    @Resource
    @Lazy
    private MerchantChannelReleService merchantChannelReleService;



    @RabbitHandler // 重点：添加 @RabbitHandler 注解，实现消息的消费
    public void onMessage(BookingOrderMessage message) {
        //休眠300毫秒
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        BookingOrderActionTypeEnum actionEnum = BookingOrderActionTypeEnum.fromCode(message.getActionType());
        switch (actionEnum){
            case BOOKING_CREATION, BOOKING_MODIFICATION:
                bookingCreationSend(message.getGcode(), message.getHcode(), message.getBookNo(), message.getActionType());
                break;
            case LOCAL_BOOKING_CANCELLATION:
                localBookingCancellationSend(message.getGcode(), message.getHcode(), message.getBookNo(), message.getActionType());
                break;
            case BOOKING_NOSHOW:
                break;
            case CHECK_IN:
                checkInSend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType());
                break;
            case ROOM_CHANGE:
                roomChangeSend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType());
                break;
            case EXTEND_STAY:
                extendStaySend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType());
                break;
            case CHECK_OUT:
                checkOutSend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType());
                break;
            case ORDER_INFO_MODIFICATION:
                orderInfoModificationSend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType());
                break;
            case GUEST_INFO_OPERATION:
                guestInfoOperationSend(message.getGcode(), message.getHcode(), message.getOrderNo(), message.getActionType(),message.getToogetherType());
                break;
            default:
                break;
        }
    }

    private void checkOutSend(String gcode, String hcode, List<String> orderNoList, String actionType) {
        // 定义房费类型的 subCode 集合
        List<String> roomFeeTypes = Arrays.asList(
                ConsumeAccountEnum.ROOM_FEE.getCode(),
                ConsumeAccountEnum.ADJUST_ROOM_FEE.getCode(),
                ConsumeAccountEnum.HAND_INPUT_ROOM_FEE.getCode(),
                ConsumeAccountEnum.ROOM_FEE_ADD.getCode(),
                ConsumeAccountEnum.ADD_HALF_DAY.getCode(),
                ConsumeAccountEnum.ADD_ALL_DAY.getCode()
        );
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNoList));
       if(CollUtil.isNotEmpty(orderList)){
           List<OrderSenderBO> list = CollectionUtils.convertList(orderList, order -> {
               // 订单信息
               OrderSenderBO orderSenderBO = new OrderSenderBO();
               orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(OrderStateEnum.CHECK_OUT.getCode()));

               //订单号
               OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
               orderNos.setInOrderNo(order.getOrderNo());
               orderSenderBO.setOrderNos(orderNos);


               List<AccountDO> accountList = accountService.getAccountList(new AccountListReqVO().setGcode(gcode).setHcode(hcode).setNo(order.getOrderNo()));
               List<AccountDO> payAccountList = accountList.stream().filter(account -> account.getSubType().equals(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode())).toList();
               List<AccountDO> collect = accountList.stream()
                       .filter(account -> roomFeeTypes.contains(account.getSubCode()))
                       .toList();

               long days = ChronoUnit.DAYS.between(order.getCheckinTime().toLocalDate(), order.getCheckoutTime().toLocalDate());
               OrderSenderBO.CheckoutInfo checkoutInfo = new OrderSenderBO.CheckoutInfo();
               checkoutInfo.setTotalAmount(payAccountList.stream().mapToLong(AccountDO::getFee).sum());
               checkoutInfo.setRoomAmount(collect.stream().mapToLong(AccountDO::getFee).sum());
               checkoutInfo.setCheckoutTime(accountList.getFirst().getPayTime());
               if(days > 0){
                   checkoutInfo.setFlag(NumberEnum.ZERO.getNumber());
               }else {
                   checkoutInfo.setFlag(NumberEnum.ONE.getNumber());
               }
               orderSenderBO.setCheckoutInfo(checkoutInfo);
               return orderSenderBO;

           });

           MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
           if(ota!=null){
               String msg = JsonUtils.toJsonString(list);
               bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
           }
       }

    }


    private void guestInfoOperationSend(String gcode, String hcode, List<String> orderNo, String actionType, String togetherType) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNo));
        if (CollUtil.isNotEmpty(orderList)){
            // 订单号与所有宾客关系映射
            List<String> orders = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orders));
            Map<String, List<OrderTogetherDO>> togetherMap = CollectionUtils.convertMultiMap(orderTogetherList, OrderTogetherDO::getOrderNo);
            // 订单号与订单关系映射
            Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
            List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {
                // 订单信息
                OrderSenderBO orderSenderBO = new OrderSenderBO();
                orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(orderMap.getOrDefault(orderDO.getOrderNo(), new OrderDO()).getState()));
                OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                orderNos.setPmsResNo(orderDO.getBookNo())
                        .setInOrderNo(orderDO.getOrderNo());
                orderSenderBO.setOrderNos(orderNos);
                // 客人信息
                List<OrderTogetherDO> togetherDOList = togetherMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                if (CollUtil.isNotEmpty(togetherDOList)){
                    List<OrderSenderBO.Guest> guests = CollectionUtils.convertList(togetherDOList, orderTogetherDO -> {
                        OrderSenderBO.Guest guest = new OrderSenderBO.Guest();
                        guest.setType(orderTogetherDO.getIsMain())
                                .setName(orderTogetherDO.getName())
                                .setSex(orderTogetherDO.getSex())
                                .setIdType(orderTogetherDO.getIdType())
                                .setIdNo(orderTogetherDO.getIdNo())
                                .setNational(orderTogetherDO.getNation())
                                .setPhone(orderTogetherDO.getPhone())
                                .setAddress(orderTogetherDO.getAddress());
                        return guest;
                    });
                    OrderSenderBO.GuestInfo guestInfo = new OrderSenderBO.GuestInfo();
                    // TODO 判断信息类型
                    if(NumberEnum.ZERO.getNumber().equals(togetherType)){
                        guestInfo.setAddedItems(guests);
                    }
                    if(NumberEnum.ONE.getNumber().equals(togetherType)){
                        guestInfo.setModifiedItems(guests);
                    }
                    if(NumberEnum.TWO.getNumber().equals(togetherType)){
                        guestInfo.setRemovedItems(guests);
                    }
                    orderSenderBO.setGuestInfo(guestInfo);
                }
                return orderSenderBO;
            });
            MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
            if(ota!=null){
                String msg = JsonUtils.toJsonString(list);
                bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
            }
        }
    }

    private void orderInfoModificationSend(String gcode, String hcode, List<String> orderNo, String actionType) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNo));
        if (CollUtil.isNotEmpty(orderList)){
            // 订单号与所有宾客关系映射
            List<String> orders = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orders));
            // 主客订单关系映射
            List<OrderTogetherDO> togetherIsMainList = CollectionUtils.filterList(orderTogetherList, orderTogetherDO -> NumberEnum.ONE.getNumber().equals(orderTogetherDO.getIsMain()));
            Map<String, OrderTogetherDO> togetherIsMainMap =  CollectionUtils.convertMap(togetherIsMainList, OrderTogetherDO::getOrderNo);
            // 订单号与订单关系映射
            Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
            // 订单价格
            List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNoList));
            Map<String, List<OrderPriceDO>> orderPriceMap = CollectionUtils.convertMultiMap(orderPriceList, OrderPriceDO::getOrderNo);
            // 订单信息
            List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {
                // 订单价格
                List<OrderPriceDO> priceList = orderPriceMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                List<OrderSenderBO.CheckinPrice> checkinPrices = CollectionUtils.convertList(priceList, orderPriceDO -> {
                    OrderSenderBO.CheckinPrice checkinPrice = new OrderSenderBO.CheckinPrice();
                    checkinPrice.setDate(orderPriceDO.getPriceDate())
                            .setPrice(orderPriceDO.getVipPrice());
                    return checkinPrice;
                });
                // 订单信息
                OrderSenderBO orderSenderBO = new OrderSenderBO();
                orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(orderMap.getOrDefault(orderDO.getOrderNo(), new OrderDO()).getState()));
                OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                orderNos.setPmsResNo(orderDO.getBookNo())
                        .setInOrderNo(orderDO.getOrderNo());
                orderSenderBO.setOrderNos(orderNos);
                OrderSenderBO.CommonInfo commonInfo = new OrderSenderBO.CommonInfo();
                commonInfo.setGuestSrcType(orderDO.getGuestSrcType())
                        .setChannelCode(orderDO.getChannelCode())
                        .setCheckinType(orderDO.getCheckinType())
                        .setBkTicketNum(priceList.stream().mapToInt(OrderPriceDO::getBkNum).sum() + priceList.stream().mapToInt(OrderPriceDO::getRoomBkNum).sum() + orderDO.getBuyBkNum());
                orderSenderBO.setCommonInfo(commonInfo);
                // 主客信息
                OrderSenderBO.CheckinPersonInfo checkinPersonInfo = new OrderSenderBO.CheckinPersonInfo();
                checkinPersonInfo.setCheckinPhone(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getPhone())
                        .setCheckinPerson(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getName());
                // 入住信息
                OrderSenderBO.CheckinInfo checkinInfo = new OrderSenderBO.CheckinInfo();
                checkinInfo.setInTime(orderDO.getCheckinTime())
                        .setOutTime(orderDO.getPlanCheckoutTime())
                        .setCheckinDays(orderDO.getDays())
                        .setCheckinPriceList(checkinPrices)
                        .setCheckinPersonInfo(checkinPersonInfo)
                        .setCheckinRemark(orderDO.getRemark());
                orderSenderBO.setCheckinInfo(checkinInfo);

                return orderSenderBO;
            });
            MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
            if(ota!=null){
                String msg = JsonUtils.toJsonString(list);
                bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
            }
        }
    }

    private void extendStaySend(String gcode, String hcode, List<String> orderNo, String actionType) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNo));
        if (CollUtil.isNotEmpty(orderList)){
            // 主客订单关系映射
            List<String> orders = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orders));
            List<OrderTogetherDO> togetherIsMainList = CollectionUtils.filterList(orderTogetherList, orderTogetherDO -> NumberEnum.ONE.getNumber().equals(orderTogetherDO.getIsMain()));
            Map<String, OrderTogetherDO> togetherIsMainMap =  CollectionUtils.convertMap(togetherIsMainList, OrderTogetherDO::getOrderNo);
            // 订单号与订单关系映射
            Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
            // 订单价格
            List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNoList));
            Map<String, List<OrderPriceDO>> orderPriceMap = CollectionUtils.convertMultiMap(orderPriceList, OrderPriceDO::getOrderNo);

            List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {
                // 订单信息
                OrderSenderBO orderSenderBO = new OrderSenderBO();
                orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(orderMap.getOrDefault(orderDO.getOrderNo(), new OrderDO()).getState()));

                OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                orderNos.setInOrderNo(orderDO.getOrderNo());
                orderSenderBO.setOrderNos(orderNos);

                // 订单价格
                List<OrderPriceDO> priceList = orderPriceMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                List<OrderSenderBO.CheckinPrice> checkinPrices = CollectionUtils.convertList(priceList, orderPriceDO -> {
                    OrderSenderBO.CheckinPrice checkinPrice = new OrderSenderBO.CheckinPrice();
                    checkinPrice.setDate(orderPriceDO.getPriceDate())
                            .setPrice(orderPriceDO.getVipPrice());
                    return checkinPrice;
                });

                // 主客信息
                OrderSenderBO.CheckinPersonInfo checkinPersonInfo = new OrderSenderBO.CheckinPersonInfo();
                checkinPersonInfo.setCheckinPhone(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getPhone())
                        .setCheckinPerson(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getName());

                // 入住信息
                OrderSenderBO.CheckinInfo checkinInfo = new OrderSenderBO.CheckinInfo();
                checkinInfo.setInTime(orderDO.getCheckinTime())
                        .setOutTime(orderDO.getPlanCheckoutTime())
                        .setCheckinDays(orderDO.getDays())
                        .setCheckinPriceList(checkinPrices)
                        .setCheckinPersonInfo(checkinPersonInfo)
                        .setCheckinRemark(orderDO.getRemark());
                orderSenderBO.setCheckinInfo(checkinInfo);
                return orderSenderBO;
            });
            MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
            if(ota!=null){
                String msg = JsonUtils.toJsonString(list);
                bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
            }
        }
    }

    private void roomChangeSend(String gcode, String hcode, List<String> orderNo, String actionType) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNo));
        if (CollUtil.isNotEmpty(orderList)){
            // 订单号与订单关系映射
            Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
            // 房型信息
            List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(gcode).setHcode(hcode), false);
            Map<String, String> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode, RoomTypeDO::getRtName);

            List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {

                // 订单信息
                OrderSenderBO orderSenderBO = new OrderSenderBO();
                orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(orderMap.getOrDefault(orderDO.getOrderNo(), new OrderDO()).getState()));
                OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                orderNos.setInOrderNo(orderDO.getOrderNo());
                orderSenderBO.setOrderNos(orderNos);

                // 房型信息
                OrderSenderBO.RoomInfo roomInfo = new OrderSenderBO.RoomInfo();
                roomInfo.setRoomNo(orderDO.getRNo())
                        .setPhysicalRoomtypeCode(orderDO.getRtCode())
                        .setPhysicalRoomtypeName(roomTypeMap.getOrDefault(orderDO.getRtCode(), ""));
                orderSenderBO.setRoomInfo(roomInfo);

                return orderSenderBO;
            });
            MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
            if(ota!=null){
                String msg = JsonUtils.toJsonString(list);
                bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
            }
        }
    }

    private void checkInSend(String gcode, String hcode, List<String> orderNo, String actionType) {
        List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNo));
        if (CollUtil.isNotEmpty(orderList)){
            // 订单号与所有宾客关系映射
            List<String> orders = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orders));
            Map<String, List<OrderTogetherDO>> togetherMap = CollectionUtils.convertMultiMap(orderTogetherList, OrderTogetherDO::getOrderNo);
            // 主客订单关系映射
            List<OrderTogetherDO> togetherIsMainList = CollectionUtils.filterList(orderTogetherList, orderTogetherDO -> NumberEnum.ONE.getNumber().equals(orderTogetherDO.getIsMain()));
            Map<String, OrderTogetherDO> togetherIsMainMap =  CollectionUtils.convertMap(togetherIsMainList, OrderTogetherDO::getOrderNo);
            // 订单号与订单关系映射
            Map<String, OrderDO> orderMap = CollectionUtils.convertMap(orderList, OrderDO::getOrderNo);
            // 订单价格
            List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
            List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNoList));
            Map<String, List<OrderPriceDO>> orderPriceMap = CollectionUtils.convertMultiMap(orderPriceList, OrderPriceDO::getOrderNo);
            // 房型信息
            List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(gcode).setHcode(hcode), false);
            Map<String, String> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode, RoomTypeDO::getRtName);

            List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {
                // 订单价格
                List<OrderPriceDO> priceList = orderPriceMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                List<OrderSenderBO.CheckinPrice> checkinPrices = CollectionUtils.convertList(priceList, orderPriceDO -> {
                    OrderSenderBO.CheckinPrice checkinPrice = new OrderSenderBO.CheckinPrice();
                    checkinPrice.setDate(orderPriceDO.getPriceDate())
                            .setPrice(orderPriceDO.getVipPrice());
                    return checkinPrice;
                });
                // 订单信息
                OrderSenderBO orderSenderBO = new OrderSenderBO();
                orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(orderMap.getOrDefault(orderDO.getOrderNo(), new OrderDO()).getState()));

                //订单号
                OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                if(!orderDO.getBookNo().equals("0")){
                    orderNos.setPmsResNo(orderDO.getBookNo());
                }
                orderNos.setInOrderNo(orderDO.getOrderNo()).setChannelResNo(orderDO.getOutOrderNo());
                orderSenderBO.setOrderNos(orderNos);

                //预订入住公共信息
                OrderSenderBO.CommonInfo commonInfo = new OrderSenderBO.CommonInfo();
                commonInfo.setGuestSrcType(orderDO.getGuestSrcType())
                        .setChannelCode(orderDO.getChannelCode())
                        .setCheckinType(orderDO.getCheckinType())
                        .setBkTicketNum(priceList.stream().mapToInt(OrderPriceDO::getBkNum).sum() + priceList.stream().mapToInt(OrderPriceDO::getRoomBkNum).sum() + orderDO.getBuyBkNum());
                orderSenderBO.setCommonInfo(commonInfo);

                // 主客信息
                OrderSenderBO.CheckinPersonInfo checkinPersonInfo = new OrderSenderBO.CheckinPersonInfo();
                checkinPersonInfo.setCheckinPhone(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getPhone())
                        .setCheckinPerson(togetherIsMainMap.getOrDefault(orderDO.getOrderNo(), new OrderTogetherDO()).getName());

                // 入住信息
                OrderSenderBO.CheckinInfo checkinInfo = new OrderSenderBO.CheckinInfo();
                checkinInfo.setInTime(orderDO.getCheckinTime())
                        .setOutTime(orderDO.getPlanCheckoutTime())
                        .setCheckinPriceList(checkinPrices)
                        .setCheckinPersonInfo(checkinPersonInfo)
                        .setCheckinRemark(orderDO.getRemark());
                if(CheckInTypeEnum.HOUR_ROOM.getCode().equals(orderDO.getCheckinType())){
                    checkinInfo.setCheckinDuration(calculateStayDurationInHours(orderDO.getPlanCheckinTime(), orderDO.getPlanCheckoutTime()));
                }else {
                    checkinInfo.setCheckinDays(orderDO.getDays());
                }
                orderSenderBO.setCheckinInfo(checkinInfo);

                // 房型信息
                OrderSenderBO.RoomInfo roomInfo = new OrderSenderBO.RoomInfo();
                roomInfo.setRoomNo(orderDO.getRNo())
                        .setPhysicalRoomtypeCode(orderDO.getRtCode())
                        .setPhysicalRoomtypeName(roomTypeMap.getOrDefault(orderDO.getRtCode(), ""));
                orderSenderBO.setRoomInfo(roomInfo);

                // 客人信息
                List<OrderTogetherDO> togetherDOList = togetherMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                if (CollUtil.isNotEmpty(togetherDOList)){
                    List<OrderSenderBO.Guest> guests = CollectionUtils.convertList(togetherDOList, orderTogetherDO -> {
                        OrderSenderBO.Guest guest = new OrderSenderBO.Guest();
                        guest.setType(orderTogetherDO.getIsMain())
                                .setName(orderTogetherDO.getName())
                                .setSex(orderTogetherDO.getSex())
                                .setIdType(orderTogetherDO.getIdType())
                                .setIdNo(orderTogetherDO.getIdNo())
                                .setNational(orderTogetherDO.getNation())
                                .setPhone(orderTogetherDO.getPhone())
                                .setAddress(orderTogetherDO.getAddress());
                        return guest;
                    });
                    OrderSenderBO.GuestInfo guestInfo = new OrderSenderBO.GuestInfo();
                    guestInfo.setAddedItems(guests);
                    orderSenderBO.setGuestInfo(guestInfo);
                }
                return orderSenderBO;
            });

            MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
            if(ota!=null){
                String msg = JsonUtils.toJsonString(list);
                bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
            }
        }
    }

    private void localBookingCancellationSend(String gcode, String hcode, List<String> bookNo, String actionType) {
        List<BookDO> bookList = bookService.getBookList(new BookListReqVO().setGcode(gcode).setHcode(hcode).setBookNos(bookNo));
        if (CollUtil.isNotEmpty(bookList)){
            List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setBookNos(bookNo));
            if (CollUtil.isNotEmpty(orderList)){
                List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {
                    // 订单信息
                    OrderSenderBO orderSenderBO = new OrderSenderBO();
                    orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(OrderStateEnum.CANCEL.getCode()));
                    OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                    orderNos.setPmsResNo(orderDO.getBookNo())
                            .setInOrderNo(orderDO.getOrderNo());
                    orderSenderBO.setOrderNos(orderNos);
                    return orderSenderBO;
                });
                MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
                if(ota!=null){
                    String msg = JsonUtils.toJsonString(list);
                    bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
                }
            }
        }
    }

    private void bookingCreationSend(String gcode, String hcode, List<String> bookNo, String actionType) {
        List<BookDO> bookList = bookService.getBookList(new BookListReqVO().setGcode(gcode).setHcode(hcode).setBookNos(bookNo));
        Map<String, BookDO> bookMap = CollectionUtils.convertMap(bookList, BookDO::getBookNo);
        if (CollUtil.isNotEmpty(bookList)){
            List<OrderDO> orderList = orderService.getOrderList(new OrderReqVO().setGcode(gcode).setHcode(hcode).setBookNos(bookNo));
            if (CollUtil.isNotEmpty(orderList)){
                // 订单价格
                List<String> orderNoList = CollectionUtils.convertList(orderList, OrderDO::getOrderNo);
                List<OrderPriceDO> orderPriceList = orderPriceService.getOrderPriceList(new OrderPriceReqVO().setGcode(gcode).setHcode(hcode).setOrderNos(orderNoList));
                Map<String, List<OrderPriceDO>> orderPriceMap = CollectionUtils.convertMultiMap(orderPriceList, OrderPriceDO::getOrderNo);
                // 房型信息
                List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(gcode).setHcode(hcode), false);
                Map<String, String> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode, RoomTypeDO::getRtName);

                List<OrderSenderBO> list = CollectionUtils.convertList(orderList, orderDO -> {

                    // 订单价格
                    List<OrderPriceDO> priceList = orderPriceMap.getOrDefault(orderDO.getOrderNo(), new ArrayList<>());
                    List<OrderSenderBO.ResPrice> resPrices = CollectionUtils.convertList(priceList, orderPriceDO -> {
                        OrderSenderBO.ResPrice resPrice = new OrderSenderBO.ResPrice();
                        resPrice.setDate(orderPriceDO.getPriceDate())
                                .setPrice(orderPriceDO.getVipPrice());
                        return resPrice;
                    });

                    // 订单信息配置
                    OrderSenderBO orderSenderBO = new OrderSenderBO();
                    orderSenderBO.setOrderStatus(OrderStateEnum.getHiiiStatusByCode(bookMap.getOrDefault(orderDO.getBookNo(), new BookDO()).getState()));
                    OrderSenderBO.OrderNos orderNos = new OrderSenderBO.OrderNos();
                    orderNos.setPmsResNo(orderDO.getBookNo())
                            .setInOrderNo(orderDO.getOrderNo())
                            .setChannelResNo(orderDO.getOutOrderNo());
                    orderSenderBO.setOrderNos(orderNos);

                    OrderSenderBO.CommonInfo commonInfo = new OrderSenderBO.CommonInfo();
                    commonInfo.setGuestSrcType(orderDO.getGuestSrcType())
                            .setChannelCode(orderDO.getChannelCode())
                            .setCheckinType(orderDO.getCheckinType())
                            .setBkTicketNum(priceList.stream().mapToInt(OrderPriceDO::getBkNum).sum() + priceList.stream().mapToInt(OrderPriceDO::getRoomBkNum).sum() + orderDO.getBuyBkNum());
                    orderSenderBO.setCommonInfo(commonInfo);

                    OrderSenderBO.ResPersonInfo resPersonInfo = new OrderSenderBO.ResPersonInfo();
                    resPersonInfo.setResPerson(bookMap.getOrDefault(orderDO.getBookNo(), new BookDO()).getContact())
                            .setResPhone(bookMap.getOrDefault(orderDO.getBookNo(), new BookDO()).getPhone());

                    OrderSenderBO.ResInfo resInfo = new OrderSenderBO.ResInfo();
                    resInfo.setResInTime(orderDO.getPlanCheckinTime())
                            .setResOutTime(orderDO.getPlanCheckoutTime())
                            .setResPriceList(resPrices)
                            .setResPersonInfo(resPersonInfo)
                            .setResRemark(bookMap.getOrDefault(orderDO.getBookNo(), new BookDO()).getRemark());
                    if(CheckInTypeEnum.HOUR_ROOM.getCode().equals(orderDO.getCheckinType())){
                        String correctRegex = "^\\d+(?=[A-Za-z]|$)";
                        Pattern pattern = Pattern.compile(correctRegex);
                        Matcher matcher = pattern.matcher(orderDO.getHourCode());
                        String hourTime="0";
                        if (matcher.find()) {
                            hourTime = matcher.group();
                        }
                        resInfo.setResDuration(Integer.parseInt(hourTime));
                    }else {
                        resInfo.setResDays(orderDO.getDays());
                    }
                    orderSenderBO.setResInfo(resInfo);

                    OrderSenderBO.RoomInfo roomInfo = new OrderSenderBO.RoomInfo();
                    roomInfo.setRoomNo(orderDO.getRNo())
                            .setPhysicalRoomtypeCode(orderDO.getRtCode())
                            .setPhysicalRoomtypeName(roomTypeMap.getOrDefault(orderDO.getRtCode(), ""));
                    orderSenderBO.setRoomInfo(roomInfo);

                    return orderSenderBO;
                });
                MerchantChannelReleDO ota = merchantChannelReleService.getMerchantOtaReleByGcodeAndHcode(gcode, hcode);
                if(ota!=null){
                    String msg = JsonUtils.toJsonString(list);
                    bookingOrderChangeSendService.sendBookingOrderChangeInfo(hcode, actionType, msg);
                }
            }
        }
    }

    private Integer calculateStayDurationInHours(LocalDateTime planCheckinTime, LocalDateTime planCheckoutTime) {
        return  Math.toIntExact(ChronoUnit.HOURS.between(planCheckinTime, planCheckoutTime));
    }

}
