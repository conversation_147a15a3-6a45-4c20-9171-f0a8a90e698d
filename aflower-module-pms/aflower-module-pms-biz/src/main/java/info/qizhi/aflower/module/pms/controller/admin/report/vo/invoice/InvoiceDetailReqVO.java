package info.qizhi.aflower.module.pms.controller.admin.report.vo.invoice;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 发票报表 Response VO")
@Data
public class InvoiceDetailReqVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @NotNull(message = "{startDate.notnull}")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @NotNull(message = "{endDate.notnull}")
    private LocalDate endDate;

    @Schema(description = "发票状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleType;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceType;

    @Schema(description = "关键字查询条件；包含字段发票抬头/代码/号码")
    private String keyWords;

    @Schema(description = "开票人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoicePerson;
}
