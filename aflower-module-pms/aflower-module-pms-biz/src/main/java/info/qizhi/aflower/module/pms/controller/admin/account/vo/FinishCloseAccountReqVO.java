package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 部分结账统计 Request VO")
@Data
public class FinishCloseAccountReqVO {
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "账单号列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accNoList.notempty}")
    private List<String> accNoList;
}
