package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房 Response VO")
@Data
public class BookRoomAutoArrangeRespVO {

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rCode")
    private String rCode;

}