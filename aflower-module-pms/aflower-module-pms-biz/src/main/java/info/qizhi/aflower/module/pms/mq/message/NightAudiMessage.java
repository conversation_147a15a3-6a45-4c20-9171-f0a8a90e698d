package info.qizhi.aflower.module.pms.mq.message;

import info.qizhi.aflower.framework.common.enums.QueueConstants;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: 夜审消息
 * @Version: 1.0
 */
@Data
public class NightAudiMessage implements Serializable {

    public static final String QUEUE = QueueConstants.NIGHT_AUDI_MESSAGE_QUEUE;

    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @NotEmpty(message = "酒店代码不能为空")
    private String hcode;

}
