package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 团队预订批量删除预订 Request VO")
@Data
@ToString(callSuper = true)
public class BookRoomBatchDeleteReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号")
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "以抵离日期作为批次号:05-01/05-10, 按批次删除时需要传入")
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "团队代码")
    @NotEmpty(message = "{teamCode.notempty}")
    private String teamCode;

}
