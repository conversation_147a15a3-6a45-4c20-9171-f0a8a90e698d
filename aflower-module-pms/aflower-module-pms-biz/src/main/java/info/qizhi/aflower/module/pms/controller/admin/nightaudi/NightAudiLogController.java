package info.qizhi.aflower.module.pms.controller.admin.nightaudi;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.log.NightAudiLogRespVO;
import info.qizhi.aflower.module.pms.dal.dataobject.nightaudi.NightAudiLogDO;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 夜审记录")
@RestController
@RequestMapping("/pms/night-audi-log")
@Validated
public class NightAudiLogController {

    @Resource
    private NightAudiLogService nightAudiLogService;

}