package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务列表 Request VO")
@Data
@ToString(callSuper = true)
public class AccountListReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "账单号")
    private String no;

    @Schema(description = "宾客代码")
    private String togetherCode;

    @Schema(description = "账务状态")
    private String state;

    @Schema(description = "结账操作员")
    private String payOperator;

    @Schema(description = "AR账套代码")
    private String arSetCode;

    @Schema(description = "是否是冲调产生的账务")
    private String isRev;

    @Schema(description = "科目代码")
    private List<String> subCodes;

    @Schema(description = "科目类型;0: 消费科目 1：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String subType;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "房号")
    private String rNo;

    @Schema(description = "班次")
    private String shiftNo;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "是否为转出转入的账务;-1: 为转出的账务, 0:默认， 1:为转入账务")
    private String isTurnOutIn;

    @Schema(description = "开始日期", example = "2024-07-04")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    private LocalDate endDate;

    @Schema(description = "结账营业日")
    private LocalDate payBizDate;

    @Schema(description = "营业日期")
    private LocalDate bizDate;

    @Schema(description = "挂账(AR账)类型")
    private List<String> creditTargetType;

    @Schema(description = "单号列表,统计多个订单账务使用")
    private List<String> nos;

    @Schema(description = "宾客代码列表")
    private List<String> togetherCodes;

    @Schema(description = "账单号列表,统计多个订单账务使用")
    private List<String> accNos;

}