package info.qizhi.aflower.module.pms.controller.admin.deviceset.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import info.qizhi.aflower.module.pms.dal.dataobject.deviceset.DeviceSetDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 设备信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeviceSetRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "26662")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "设备代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("设备代码")
    private String deviceCode;

    @Schema(description = "设备类别代码", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("设备类别代码")
    private String deviceType;

    @Schema(description = "设备类别名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("设备类别名称")
    private String deviceName;

    @Schema(description = "设备品牌代码")
    @ExcelProperty("设备品牌代码")
    private String brandCode;

    @Schema(description = "设备品牌名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("设备品牌名称")
    private String brandName;

    @Schema(description = "设备型号代码")
    @ExcelProperty("设备型号代码")
    private String deviceVerCode;

    @Schema(description = "设备型号名称", example = "张三")
    @ExcelProperty("设备型号名称")
    private String deviceVerName;

    @Schema(description = "版本号")
    @ExcelProperty("版本号")
    private String version;

    @Schema(description = "端口号")
    @ExcelProperty("端口号")
    private String port;

    @Schema(description = "发卡器类型")
    @ExcelProperty("发卡器类型")
    private String cardDispenserType;

    @Schema(description = "配置信息")
    @ExcelProperty("配置信息")
    @TableField(typeHandler = DeviceSetDO.ConfParameterTypeHandler.class)
    private List<DeviceSetDO.ConfParameter> conf;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1：有效")
    private String state;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}