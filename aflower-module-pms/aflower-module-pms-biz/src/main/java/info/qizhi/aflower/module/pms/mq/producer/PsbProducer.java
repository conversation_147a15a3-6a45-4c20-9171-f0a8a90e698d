package info.qizhi.aflower.module.pms.mq.producer;


import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.PsbMessage;
import info.qizhi.aflower.framework.common.message.RoomTypeChangeMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 *  PSB消息生产者
 */
@Slf4j
@Component
public class PsbProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象

    /**
     * 发送PSB消息
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param type 消息类型
     * @param psbJson 服务对象的json字符串
     * @param msgJson 消息json  如：订单信息 退房信息 换房信息
     */
    public void sendPSBMessage(String gcode, String hcode, String type, String psbJson, String msgJson) {
        PsbMessage message = new PsbMessage();
        message.setGcode(gcode);
        message.setHcode(hcode);
        message.setType(type);
        message.setPsb(psbJson);
        message.setMsg(msgJson);
        rabbitTemplate.convertAndSend(QueueConstants.PSB_MESSAGE_QUEUE, message);
    }
}
