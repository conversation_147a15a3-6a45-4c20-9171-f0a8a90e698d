package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepanelbackground;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 房价牌背景图片新增/修改 Request VO")
@Data
public class PricePanelBackgroundSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31580")
    private Long id;

    @Schema(description = "背景图片代码;系统生成 (唯一标识)", requiredMode = Schema.RequiredMode.REQUIRED)
    private String backgroundCode;

    @Schema(description = "背景图片地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{backgroundWebsite.notempty}")
    private String backgroundWebsite;

}