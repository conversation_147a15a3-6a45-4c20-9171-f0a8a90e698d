package info.qizhi.aflower.module.pms.controller.admin.serviceintegration.vo;

import info.qizhi.aflower.module.pms.dal.dataobject.serviceintegration.Payment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 服务对接支付类型房型 Response VO")
@Data
public class ServiceIntegrationPaymentRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7143")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "方案服务商", requiredMode = Schema.RequiredMode.REQUIRED)
    private String solutionProvider;

    @Schema(description = "方案类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String solutionType;

    @Schema(description = "服务状态; 0: 禁用, 1: 启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "对接场景", requiredMode = Schema.RequiredMode.REQUIRED)
    private Payment scenario;

}