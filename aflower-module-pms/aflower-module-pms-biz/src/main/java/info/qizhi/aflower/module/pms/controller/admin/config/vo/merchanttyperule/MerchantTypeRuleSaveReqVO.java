package info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.module.pms.service.config.bo.merchanttyperule.RegisterBrokerage;
import info.qizhi.aflower.module.pms.service.config.bo.merchanttyperule.StoreBrokerage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 门店类型规则新增/修改 Request VO")
@Data
public class MerchantTypeRuleSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18853")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{merchantTypeEmpty}")
    private String merchantType;

    @Schema(description = "门店类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "{merchantTypeNameEmpty}")
    private String merchantTypeName;

    @Schema(description = "会员信息共享;0：不共享 1：共享给集团下所有门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{memberShareEmpty}")
    @InStringEnum(value= BooleanEnum.class, message = "{memberShareInvalid}")
    private String memberShare;

    @Schema(description = "余额和积分共享;0：不共享 1：共享， 直营类型时，0表示仅所有直营门店使用，1表示所有门店使用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{memberPointShareEmpty}")
    private String memberPointShare;

    @Schema(description = "会员储值金额入账到;0: 入账到集团 1:入账到门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{memberPointShareEmpty}")
    @InStringEnum(value= BooleanEnum.class, message = "{memberPointShareInvalid}")
    private String storeFeeRecord;

    @Schema(description = "会员储值提成和报表")
    private StoreBrokerage storeBrokerage;

    @Schema(description = "会员办卡提成规则")
    private RegisterBrokerage registerBrokerage;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}