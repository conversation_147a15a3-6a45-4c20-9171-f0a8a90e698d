package info.qizhi.aflower.module.pms.controller.admin.report.vo.book;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.jackson.core.databind.LocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 今日预抵客人报表请求 VO")
@Data
public class BookGuestReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订日期", example = "2025-05-21")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDate bookDate;
} 