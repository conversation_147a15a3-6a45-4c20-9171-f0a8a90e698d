package info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.strategy;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeSimpleRespVO;
import info.qizhi.aflower.module.pms.dal.dataobject.brokerage.BrokerageStrategyMerchantDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 佣金策略 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BrokerageStrategyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10503")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "是否集团佣金策略;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否集团佣金策略;0：否 1：是")
    private String isG;

    @Schema(description = "策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("策略代码")
    private String strategyCode;

    @Schema(description = "策略名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("策略名称")
    private String strategyName;

    @Schema(description = "公司类型;0:中介 1:协议单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("公司类型;0:中介 1:协议单位")
    private String companyType;

    @Schema(description = "是否集团房型;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否集团房型;0:否 1:是")
    private String isGrt;

    @Schema(description = "房型代码;json，房型代码 存储字符串数组")
    @ExcelProperty("房型代码;json，房型代码 存储字符串数组")
    private List<String> rts;

    @Schema(description = "房型列表")
    private List<RoomTypeSimpleRespVO> roomTypes;

    @Schema(description = "返佣名称;0:间夜定额返佣 1:百分比返佣", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("返佣名称;0:间夜定额返佣 1:百分比返佣")
    private String brokerageType;

    @Schema(description = "目标值;返佣的值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("目标值;返佣的值")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long brokerageValue;

    @Schema(description = "渠道代码;json,存储多个渠道 存储字符串数组")
    @ExcelProperty("渠道代码;json,存储多个渠道 存储字符串数组")
    private List<String> channels;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "佣金等级代码")
    @ExcelProperty("佣金等级代码")
    private String brokerageLevelCode;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

//    @Schema(description = "适用酒店名称列表")
//    private List<BrokerageStrategyMerchantDO> hotels;

    @Schema(description = "适用酒店名称列表")
    private List<String> hotels;

}