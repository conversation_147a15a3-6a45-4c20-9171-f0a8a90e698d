package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: TY
 * @CreateTime: 2024-06-01
 * @Description: 单个订单账务统计业务对象
 * @Version: 1.0
 */
@Schema(description = "biz - 账务统计 业务对象")
@Data
public class OrderAccountStatRespVO {

    @Schema(description = "单号")
    private String no;

    @Schema(description = "余额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long balanceAmount;

    @Schema(description = "消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeAmount;

    @Schema(description = "付款")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payAmount;

    @Schema(description = "预授权")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long preAuthAmount;

    @Schema(description = "优惠券")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long couponAmount;

}
