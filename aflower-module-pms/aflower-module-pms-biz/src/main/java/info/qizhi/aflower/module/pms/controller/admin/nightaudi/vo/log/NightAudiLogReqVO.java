package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 夜审记录 Request VO")
@Data
@ToString(callSuper = true)
public class NightAudiLogReqVO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "营业日yyyy-MM-dd")
    private LocalDate bizDate;

    @Schema(description = "自然日（yyyy-MM-dd）")
    private LocalDate naturalDate;

    @Schema(description = "夜审是否成功;0失败 1成功")
    private String isSuccess;


}