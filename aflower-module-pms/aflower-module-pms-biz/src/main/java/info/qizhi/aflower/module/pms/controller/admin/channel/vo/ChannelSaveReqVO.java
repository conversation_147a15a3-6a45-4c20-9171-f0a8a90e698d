package info.qizhi.aflower.module.pms.controller.admin.channel.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 渠道新增/修改 Request VO")
@Data
public class ChannelSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19070")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码;当is_g=0时，当前字段有值")
    private String hcode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "{channelName.notempty}")
    @Size(max = 30, message = "{channelName.size}")
    private String channelName;

    @Schema(description = "渠道简称", requiredMode = Schema.RequiredMode.REQUIRED, example = "携")
    private String shortName;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class, message = "{enableStatus.instringenum}")
    private String isEnable;

    @Schema(description = "是否集团渠道;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value = BooleanEnum.class, message = "{isG.invalid}")
    private String isG;

    @Schema(description = "类型;0:直营 1：分销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{channelType.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{channelType.invalid}")
    private String channelType;

    @Schema(description = "是否系统初始;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isSys.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isSys.invalid}")
    private String isSys;

}