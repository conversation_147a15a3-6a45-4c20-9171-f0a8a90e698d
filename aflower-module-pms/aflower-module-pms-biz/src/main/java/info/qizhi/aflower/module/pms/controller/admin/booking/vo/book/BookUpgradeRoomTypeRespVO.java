package info.qizhi.aflower.module.pms.controller.admin.booking.vo.book;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 可预订房型房价 Response VO")
@Data
public class BookUpgradeRoomTypeRespVO {

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称")
    private String rtName;

    @Schema(description = "可售数")
    private Integer canSellNum;

    @Schema(description = "可超数")
    private Integer canOverNum;

  /*  @Schema(description = "预订数")
    private Integer roomNum;*/
}
