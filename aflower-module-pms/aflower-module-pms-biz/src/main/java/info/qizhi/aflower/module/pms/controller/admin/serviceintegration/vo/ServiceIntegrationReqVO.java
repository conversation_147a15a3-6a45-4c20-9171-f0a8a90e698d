package info.qizhi.aflower.module.pms.controller.admin.serviceintegration.vo;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.ServiceTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 服务对接 Request VO")
@Data
public class ServiceIntegrationReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "门店代码列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> hcodes;

    @Schema(description = "方案服务商", requiredMode = Schema.RequiredMode.REQUIRED)
    private String solutionProvider;

    @Schema(description = "方案类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String solutionType;

    @Schema(description = "方案类型列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private List<String> solutionTypes;

    @Schema(description = "服务对接类型; ota: OTA对接服务, payment: 支付对接服务, invoice: 数电发票", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= ServiceTypeEnum.class)
    private String type;

    @Schema(description = "服务状态; 0: 禁用, 1: 启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "有效期内,0:否，1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= BooleanEnum.class)
    private String InValidityPeriod;

}