package info.qizhi.aflower.module.pms.controller.admin.booking.vo.team;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.CheckInTypeEnum;
import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.common.validation.Telephone;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 团队新增/修改 Request VO")
@Data
public class TeamSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10137")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String teamCode;

    @Schema(description = "团队名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{teamName.notempty}")
    @Size(max = 32, message = "{teamName.size}")
    private String teamName;

    @Schema(description = "团队类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{teamType.notempty}")
    @InStringEnum(value= CheckInTypeEnum.class, message = "{teamType.invalid}")
    private String teamType;

    @Schema(description = "联系人")
    @Size(max = 32, message = "{contact.size}")
    private String contact;

    @Schema(description = "手机号码")
    @Telephone(message = "{phone.invalidFormat}")
    private String phone;

    @Schema(description = "客源类型;请看数据字典", example = "2")
    @InStringEnum(value= GuestSrcTypeEnum.class, message = "{guestSrcType.invalid}")
    private String guestSrcType;

    @Schema(description = "入住时间")
    private LocalDateTime checkinTime;

    @Schema(description = "预离时间")
    private LocalDateTime planCheckoutTime;

    @Schema(description = "状态;请看数据字典", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= OrderStateEnum.class, message = "{orderState.invalid}")
    private String state;

    @Schema(description = "账务状态 open:未结 closed:已结. 团队主单退房结账完成后，账务状态变为closed")
    private String accState;

    @Schema(description = "结账时间")
    private LocalDateTime payTime;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "结账操作人")
    private String payOperator;

    @Schema(description = "客人账号;如果客源类别为会员 账号为会员的账号 为单位则为单位账号 为中介则为中介账号", example = "5438")
    private String guestCode;

    @Schema(description = "总消费")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long totalFee;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "备注", example = "你猜")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

}