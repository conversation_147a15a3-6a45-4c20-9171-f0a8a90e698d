package info.qizhi.aflower.module.pms.controller.admin.calendar;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.calendar.vo.CalendarPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.calendar.vo.CalendarRespVO;
import info.qizhi.aflower.module.pms.controller.admin.calendar.vo.CalendarSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.calendar.CalendarDO;
import info.qizhi.aflower.module.pms.service.calendar.CalendarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 日历")
@RestController
@RequestMapping("/pms/calendar")
@Validated
public class CalendarController {

    @Resource
    private CalendarService calendarService;

    @PostMapping("/create")
    @Operation(summary = "创建日历")
    @PreAuthorize("@ss.hasPermission('pms:calendar:create')")
    public CommonResult<Long> createCalendar(@Valid @RequestBody CalendarSaveReqVO createReqVO) {
        return success(calendarService.createCalendar(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新日历")
    @PreAuthorize("@ss.hasPermission('pms:calendar:update')")
    public CommonResult<Boolean> updateCalendar(@Valid @RequestBody CalendarSaveReqVO updateReqVO) {
        calendarService.updateCalendar(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除日历")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('pms:calendar:delete')")
    public CommonResult<Boolean> deleteCalendar(@RequestParam("id") Long id) {
        calendarService.deleteCalendar(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得日历")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('pms:calendar:query')")
    public CommonResult<CalendarRespVO> getCalendar(@RequestParam("id") Long id) {
        CalendarDO calendar = calendarService.getCalendar(id);
        return success(BeanUtils.toBean(calendar, CalendarRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得日历分页")
    @PreAuthorize("@ss.hasPermission('pms:calendar:query')")
    public CommonResult<PageResult<CalendarRespVO>> getCalendarPage(@Valid CalendarPageReqVO pageReqVO) {
        PageResult<CalendarRespVO> pageResult = calendarService.getCalendarPage(pageReqVO);
        return success(pageResult);
    }

}