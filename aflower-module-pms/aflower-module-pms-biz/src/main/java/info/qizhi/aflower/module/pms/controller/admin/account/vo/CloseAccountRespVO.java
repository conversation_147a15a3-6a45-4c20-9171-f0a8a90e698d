package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 已结账务 Response VO")
@Data
public class CloseAccountRespVO {

    @Schema(description = "结账号")
    private String payNo;

    @Schema(description = "金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "宾客代码")
    private String togetherCode;

    @Schema(description = "班次")
    private String payShiftNo;

    @Schema(description = "班次名称")
    private String payShiftName;

    @Schema(description = "操作人")
    private String payer;

    @Schema(description = "营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "结账时间")
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime payTime;
}