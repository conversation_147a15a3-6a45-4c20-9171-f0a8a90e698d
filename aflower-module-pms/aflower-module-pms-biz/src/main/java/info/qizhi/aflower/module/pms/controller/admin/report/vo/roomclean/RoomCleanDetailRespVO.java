package info.qizhi.aflower.module.pms.controller.admin.report.vo.roomclean;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 房扫明细 Response VO")
@Data
public class RoomCleanDetailRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10099")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rCode")
    private String rCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rtCode")
    private String rtCode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rtName")
    private String rtName;

    @Schema(description = "打扫前房态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cleanState;

    @Schema(description = "清洁工账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String cleaner;

    @Schema(description = "清洁工账号昵称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String cleanerName;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "班次号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "操作员昵称")
    private String operatorName;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "打扫开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime cleanStartTime;

    @Schema(description = "打扫结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime cleanEndTime;

    @Schema(description = "审核人账号")
    private String auditor;

    @Schema(description = "审核人账号昵称")
    private String auditorName;

    @Schema(description = "审核时间")
    private LocalDateTime auditTime;

    @Schema(description = "房扫类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String roomCleanType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}
