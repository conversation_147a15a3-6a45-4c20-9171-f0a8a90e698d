package info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.BreakfastTicket;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 门店参数设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class HotelParamConfigBreakfastTicketRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19859")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("参数类型")
    private String paramType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "早餐卷设置")
    private BreakfastTicket value;

}