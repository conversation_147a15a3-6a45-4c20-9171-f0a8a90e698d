package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalTime;
import java.util.List;
import java.util.Map;

@Schema(description = "小程序 - 经营数据分类统计 Response VO")
@Data
public class TotalData {
    @Schema(description = "经营数据")
    private List<BusinessData> businessDatas;

    @Schema(description = "小时段数据")
    private Map<LocalTime, RTCodeRoom> rtCodeRoomMap;
}
