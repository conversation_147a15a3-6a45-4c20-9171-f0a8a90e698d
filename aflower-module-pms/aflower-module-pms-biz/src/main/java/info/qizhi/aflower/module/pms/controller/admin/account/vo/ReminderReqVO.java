package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "biz - 催账 Request VO")
@Data
@ToString(callSuper = true)
public class ReminderReqVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "是否显示同住人")
    private String isShowTogether;

    @Schema(description = "房号")
    private String rNo;

    @Schema(description = "类型集合，1：单订单 2：单房间同住订单，3：联房，4：团队，", example = "1")
    private List<String> types;

}
