package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.hourroomtype;

import info.qizhi.aflower.framework.common.enums.HourRoomEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 时租房房型新增/修改 Request VO")
@Data
public class HourRoomTypeSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7143")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "时租房代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hourCode.notempty}")
    @InStringEnum(value= HourRoomEnum.class, message = "{hourCode.invalid}")
    private String hourCode;

    @Schema(description = "时租房名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{hourName.notempty}")
    @Size(max = 20, message = "{hourName.size}")
    private String hourName;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomTypes.notempty}")
    private List<String> rtCodes;

}