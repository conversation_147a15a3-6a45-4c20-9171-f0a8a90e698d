package info.qizhi.aflower.module.pms.controller.admin.initbizdata.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: TY
 * @CreateTime: 2024-08-25
 * @Description: 初始化数据Req VO
 * @Version: 1.0
 */
@Schema(description = "管理后台 - 初始化酒店数据 Request VO")
@Data
@ToString(callSuper = true)
public class InitBizDataReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "酒店代码")
    @NotEmpty(message = "酒店代码不能为空")
    private String hcode;

    @Schema(description = "酒店名称")
    @NotEmpty(message = "酒店名称不能为空")
    private String hname;
}
