package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate;


import com.fasterxml.jackson.annotation.JsonProperty;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.ImageVO;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 房价牌新增/修改 Request VO")
@Data
public class PricePanelSaveBaseReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3763")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "酒店名称;与门店代码无关联", example = "王五")
    @NotEmpty(message = "{hname.notempty}")
    private String hname;

    @Schema(description = "酒店名称字号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{hnameFontSize.notnull}")
    //@Range(min = 36, max = 72, message = "{hnameFontSize.range}")
    private Integer hnameFontSize;

    @Schema(description = "是否展示集团logo;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isShow.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isShow.invalid}")
    private String isShow;

    @Schema(description = "酒店提示")
    private String reminder;

    @Schema(description = "是否滚动播放;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isRolling.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isRolling.invalid}")
    private String isRolling;

    @Schema(description = "酒店提示字号")
    //@Range(min = 18, max = 36, message = "{reminderFontSize.range}")
    private Integer reminderFontSize;

    @Schema(description = "背景图片代码")
    @NotEmpty(message = "{backgroundCode.notempty}")
    private String backgroundCode;

    @Schema(description = "图片")
    private List<ImageVO> houseImage;

    @Schema(description = "门店Logo")
    private List<ImageVO> logo;

    @Schema(description = "二维码图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "QRImage")
    private List<ImageVO> QRImage;

    @Schema(description = "二维码文案", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tips;

    @Schema(description = "滚动时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer rollTime;

    @Schema(description = "业务开通状态;0:关闭 1：开启", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "版面类型;0:横版 1：竖版", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{layoutType.notempty}")
    private String layoutType;

}