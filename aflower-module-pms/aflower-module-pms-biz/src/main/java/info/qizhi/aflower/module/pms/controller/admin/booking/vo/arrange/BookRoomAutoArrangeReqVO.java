package info.qizhi.aflower.module.pms.controller.admin.booking.vo.arrange;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房-自动排房 Request VO")
@Data
public class BookRoomAutoArrangeReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号")
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "订单单号")
    private String orderNo;

    @Schema(description = "批次号")
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

}