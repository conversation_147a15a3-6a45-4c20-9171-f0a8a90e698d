package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.ParamConfigTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.groupparam.*;
import info.qizhi.aflower.module.pms.dal.dataobject.config.GroupParamConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.HourRoomRuleConfig;
import info.qizhi.aflower.module.pms.service.config.GroupParamConfigService;
import info.qizhi.aflower.module.pms.service.config.bo.memberconfig.MemberConfigBO;
import info.qizhi.aflower.module.pms.service.nightaudi.NightAudiSetService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 集团参数设置(业务逻辑、会员配置、房态盘颜色配置)")
@RestController
@RequestMapping("/pms/group-param-config")
@Validated
public class GroupParamConfigController {

    @Resource
    private GroupParamConfigService groupParamConfigService;
    @Resource
    private NightAudiSetService nightAudiSetService;

    @PutMapping("/update")
    @Operation(summary = "更新集团参数设置")
    @PreAuthorize("@ss.hasPermission('pms:general-config:create')")
    public CommonResult<Boolean> updateGroupParamConfig(@Valid @RequestBody GroupParamConfigSaveReqVO updateReqVO) {
        groupParamConfigService.updateGroupParamConfig(updateReqVO);
        return success(true);
    }

    @GetMapping("/get/order")
    @Operation(summary = "获得集团参数设置-订单配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigOrderRespVO> getGroupParamConfigOrder(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_ORDER.getParamType());
        return success(BeanUtils.toBean(groupParamConfig, GroupParamConfigOrderRespVO.class));
    }

    @GetMapping("/get/night-num")
    @Operation(summary = "获得集团参数设置-间夜数设置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigNightNumRespVO> getGroupParamConfigNightNum(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_NIGHT_NUM.getParamType());
        return success(BeanUtils.toBean(groupParamConfig, GroupParamConfigNightNumRespVO.class));
    }

    @GetMapping("/get/finance")
    @Operation(summary = "获得集团参数设置-财务设置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigFinanceRespVO> getGroupParamConfigFinance(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_FINANCE.getParamType());
        GroupParamConfigFinanceRespVO x = BeanUtils.toBean(groupParamConfig, GroupParamConfigFinanceRespVO.class);
        return success(x);
    }

    @GetMapping("/get/member")
    @Operation(summary = "获得集团参数设置-会员配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigMemberRespVO> getGroupParamConfigMember(@RequestParam("gcode") String gcode) {
        MemberConfigBO memberConfigBO = groupParamConfigService.getGroupParamConfigMember(gcode);
        return success(BeanUtils.toBean(memberConfigBO, GroupParamConfigMemberRespVO.class));
    }

    @GetMapping("/get/member-rule")
    @Operation(summary = "获得集团参数设置-会员积分规则配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigMemberRuleRespVO> getGroupParamConfigMemberRule(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_MEMBERRULE.getParamType());
        return success(BeanUtils.toBean(groupParamConfig, GroupParamConfigMemberRuleRespVO.class));
    }

    @GetMapping("/get/color")
    @Operation(summary = "获得集团参数设置-房态盘颜色配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigRoomColorRespVO> getGroupParamConfigRoomColor(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_ROOMCOLOR.getParamType());
        return success(BeanUtils.toBean(groupParamConfig, GroupParamConfigRoomColorRespVO.class));
    }

    @GetMapping("/get/hour")
    @Operation(summary = "获得集团参数设置-钟点房占房配置")
    @Parameter(name = "gcode", description = "集团编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:general-config:query')")
    public CommonResult<GroupParamConfigHourRoomRespVO> getGroupParamConfigHourRoom(@RequestParam("gcode") String gcode) {
        GroupParamConfigDO groupParamConfig = getGroupParamConfig(gcode, ParamConfigTypeEnum.PARAM_TYPE_HOURROOM.getParamType());
        if (groupParamConfig == null) {
            return success(BeanUtils.toBean( createHourRoomGroupParamConfig(gcode), GroupParamConfigHourRoomRespVO.class)
                    .setValue(new HourRoomRuleConfig().setNum(NumberEnum.ONE.getNumber())));
        }
        return success(BeanUtils.toBean(groupParamConfig, GroupParamConfigHourRoomRespVO.class));
    }

    private GroupParamConfigSaveReqVO createHourRoomGroupParamConfig(String gcode) {
        Map<String, Object> value = new HashMap<>();

        String json = """
                {
                    "@class": "info.qizhi.aflower.module.pms.dal.dataobject.config.groupparamconfig.HourRoomRuleConfig",
                    "num": "1"
                }
                """;
        HourRoomRuleConfig hourRoomRuleConfig = JsonUtils.parseObject(json, HourRoomRuleConfig.class);
        value.put(ParamConfigTypeEnum.PARAM_TYPE_HOURROOM.getParamType(), hourRoomRuleConfig);

        GroupParamConfigSaveReqVO groupParamConfigSaveReqVO = new GroupParamConfigSaveReqVO()
                .setGcode(gcode).setParamType(ParamConfigTypeEnum.PARAM_TYPE_HOURROOM.getParamType())
                .setValue(value);
        groupParamConfigService.createGroupParamConfig(groupParamConfigSaveReqVO);

        return groupParamConfigSaveReqVO;
    }


    private GroupParamConfigDO getGroupParamConfig(String gcode, String paramType) {
        GroupParamConfigDO groupParamConfig = groupParamConfigService.getGroupParamConfig(gcode, paramType);
        return groupParamConfig;
    }

}