package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 账务 返回 账务号 金额 Response VO")
@Data
public class AccountAccNoFeeRespVO {

    @Schema(description = "账号(入账账号);预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "账务号;系统生成唯一标识，每笔账都生成一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "宾客代码;关联宾客表代码,用于区分账务属于那个宾客。账务类型为订单时需要保存宾客代码,为团队主单时保存团队代码，否则默认为0", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "金额;账务的金额,如果是冲调就为负数", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "货币单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencyUnit;

}