package info.qizhi.aflower.module.pms.service.shift;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.ParamConfigTypeEnum;
import info.qizhi.aflower.framework.common.enums.ServiceTypeEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.security.core.util.SecurityFrameworkUtils;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.HandoverReportCashRealizationReqVO;
import info.qizhi.aflower.module.pms.controller.admin.account.vo.HandoverReportCashRealizationRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.hotelparam.HotelParamConfigReqVO;
import info.qizhi.aflower.module.pms.controller.admin.shift.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.config.HotelParamConfigDO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.hotelparamconfig.ShiftMode;
import info.qizhi.aflower.module.pms.dal.dataobject.serviceintegration.ServiceIntegrationDO;
import info.qizhi.aflower.module.pms.dal.dataobject.shift.ShiftTimeDO;
import info.qizhi.aflower.module.pms.dal.mysql.shift.ShiftTimeMapper;
import info.qizhi.aflower.module.pms.dal.redis.shift.UserShiftTimeRedisDAO;
import info.qizhi.aflower.module.pms.service.account.AccountService;
import info.qizhi.aflower.module.pms.service.config.GeneralConfigService;
import info.qizhi.aflower.module.pms.service.config.HotelParamConfigService;
import info.qizhi.aflower.module.pms.service.serviceintegration.ServiceIntegrationService;
import info.qizhi.aflower.module.report.api.handoverreport.HandoverReportApi;
import info.qizhi.aflower.module.report.api.handoverreport.dto.HandoverReportRespDTO;
import info.qizhi.aflower.module.report.api.handoverreport.dto.HandoverReportSaveReqDTO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantShiftReqDTO;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantSimpleRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.*;

/**
 * 班次设置 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class ShiftTimeServiceImpl implements ShiftTimeService {

    @Resource
    private ShiftTimeMapper shiftTimeMapper;

    @Resource
    private MerchantApi merchantApi;

    @Resource
    private UserShiftTimeRedisDAO userShiftTimeRedisDAO;

    @Resource
    private GeneralConfigService generalConfigService;

    @Resource
    private ServiceIntegrationService serviceIntegrationService;

    @Resource
    @Lazy
    private AccountService accountService;

    @Resource
    private HotelParamConfigService hotelParamConfigService;

    @Resource
    @Lazy
    private HandoverReportApi handoverReportApi;

    private static final String DEFAULT_TIME_ZONE = "Asia/Shanghai";

    @Override
    public Long createShiftTime(ShiftTimeSaveReqVO createReqVO) {
        // 插入
        ShiftTimeDO shiftTime = BeanUtils.toBean(createReqVO, ShiftTimeDO.class);
        shiftTimeMapper.insert(shiftTime);
        // 返回
        return shiftTime.getId();
    }

    @Override
    public Boolean createShiftTimes(List<ShiftTimeSaveReqVO> shiftTimeSaveReqList) {
        List<ShiftTimeDO> shiftTimes = BeanUtils.toBean(shiftTimeSaveReqList, ShiftTimeDO.class);
        return shiftTimeMapper.insertBatch(shiftTimes);
    }

    @Override
    public void updateShiftTime(List<ShiftTimeSaveReqVO> updateReqVOS) {
        List<ShiftTimeSaveReqVO> activeShifts = CollectionUtils.filterList(updateReqVOS, st -> BooleanEnum.TRUE.getValue().equals(st.getState()));
        if (activeShifts.isEmpty()) {
            throw exception(SHIFT_TIME_CLOSE_ERROR);
        }

        // 校验启用的班次是否有开始时间或结束时间为 null
        for (ShiftTimeSaveReqVO shift : activeShifts) {
            if (shift.getStartTime() == null || shift.getEndTime() == null) {
                throw exception(SHIFT_TIME_NOT_NULL);
            }
        }

        // 校验时间总和为 24 小时
        long totalMinutes = 0;
        if(activeShifts.size()==1){
            ShiftTimeSaveReqVO shift = activeShifts.getFirst();
            if(!shift.getStartTime().equals(shift.getEndTime())){
                throw exception(SHIFT_TIME_NOT_CONTINUOUS);
            }
        }else {
            for (ShiftTimeSaveReqVO shift : activeShifts) {
                LocalTime startTime = shift.getStartTime();
                LocalTime endTime = shift.getEndTime();

                // 处理跨天时间段
                if (endTime.isBefore(startTime)) {
                    // 跨天的班次，计算跨天的时间
                    totalMinutes += Duration.between(startTime.atDate(LocalDate.now()), endTime.atDate(LocalDate.now().plusDays(1))).toMinutes();
                } else {
                    // 正常的时间段
                    totalMinutes += Duration.between(startTime.atDate(LocalDate.now()), endTime.atDate(LocalDate.now())).toMinutes();
                }
            }

            // 如果总时间不等于 24 小时，抛出异常
            if (totalMinutes != 1440) {
                throw exception(SHIFT_TIME_NOT_CONTINUOUS);
            }

            // 校验班次时间不交叉，且时间连续
            // 排序班次时间，以便校验连续性和交叉问题
            activeShifts.sort(Comparator.comparing(ShiftTimeSaveReqVO::getStartTime));

            // 校验班次时间不交叉，且时间连续
            // 排序班次时间，以便校验连续性和交叉问题
            activeShifts.sort(Comparator.comparing(ShiftTimeSaveReqVO::getStartTime));

            for (int i = 1; i < activeShifts.size(); i++) {
                ShiftTimeSaveReqVO previousShift = activeShifts.get(i - 1);
                ShiftTimeSaveReqVO currentShift = activeShifts.get(i);

                // 如果当前班次的开始时间在上一个班次结束之前，则存在交叉
                if (currentShift.getStartTime().isBefore(previousShift.getEndTime())) {
                    throw exception(SHIFT_TIME_OVERLAP); // 存在交叉的班次
                }
            }
        }

        // 更新
        List<ShiftTimeDO> updateObjS = BeanUtils.toBean(updateReqVOS, ShiftTimeDO.class);
        shiftTimeMapper.updateBatch(updateObjS);
    }

    @Override
    public MerchantShiftSimpleRespVO switchVisitMerchantAndShiftTimeAndBizDate(MerchantShiftReqVO reqVO) {
        // 校验服务是否过期
        validateServiceTime(reqVO.getGcode(), reqVO.getHcode());
        // 1. 验证当前用户是否可以访问当前门店
        List<MerchantSimpleRespDTO> visitMerchantList = merchantApi.getVisitMerchantList(reqVO.getUserId()).getData();
        if (CollUtil.isEmpty(visitMerchantList)) {
            throw exception(MERCHANT_NO_VISIT_ERROR);
        }
        MerchantSimpleRespDTO merchant = visitMerchantList.stream().filter(m -> m.getHcode().equals(reqVO.getHcode())).findFirst().orElse(null);
        if (merchant == null) {
            throw exception(MERCHANT_NO_VISIT_ERROR);
        }
        //1.2 设置时区国家
        merchantApi.update(BeanUtils.toBean(reqVO, MerchantShiftReqDTO.class));
        // 2. 验证选中的班次是否存在且启用
        ShiftTimeDO shiftTime = getShiftTime(reqVO.getGcode(), reqVO.getShiftCode(), reqVO.getHcode());
        if (shiftTime == null || BooleanEnum.FALSE.getValue().equals(shiftTime.getState())) {
            throw exception(SHIFT_TIME_NOT_EXISTS);
        }
        // 3. 获取选中门店的营业日
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());

        /*// 4. 固化原班次的账务数据（在更新缓存之前获取原本的班次）
        // 首先查询表里是否有该班次数据，有则更新，无则新增
        MerchantShiftSimpleRespVO originalShift = userShiftTimeRedisDAO.get(reqVO.getGcode(), SecurityFrameworkUtils.getLoginUserToken());
        if (originalShift != null) {
            // 固化原班次的数据
            solidifyHandoverReportData(reqVO.getGcode(), originalShift.getHcode(), originalShift.getShiftCode(), bizDate);
        }*/

        // 4. 将当前用户账号和选中的门店、班次保存到redis缓存中
        MerchantShiftSimpleRespVO merchantShiftSimpleRespVO = MerchantShiftSimpleRespVO.builder()
                                                                                       .hcode(shiftTime.getHcode())
                                                                                       .hname(merchant.getHname())
                                                                                       .shiftCode(shiftTime.getShiftCode())
                                                                                       .shiftName(shiftTime.getShiftName())
                                                                                       .bizDate(bizDate)
                                                                                       .build();
        userShiftTimeRedisDAO.set(reqVO.getHcode(), SecurityFrameworkUtils.getLoginUserToken(), merchantShiftSimpleRespVO);

        return merchantShiftSimpleRespVO;
    }

    @Override
    public MerchantShiftSimpleRespVO handover(HandoverReqVO reqVO) {
        // 1. 从缓存获取当前用户的门店和班次信息
        MerchantShiftSimpleRespVO currentShift = userShiftTimeRedisDAO.get(reqVO.getHcode(), SecurityFrameworkUtils.getLoginUserToken());
        if (currentShift == null) {
            throw exception(SHIFT_TIME_NOT_EXISTS);
        }

        String hcode = reqVO.getHcode();
        String gcode = reqVO.getGcode();

        // 2. 验证新班次是否存在且启用（只能在同一门店内交班）
        ShiftTimeDO newShiftTime = getShiftTime(gcode, reqVO.getShiftCode(), hcode);
        if (newShiftTime == null || BooleanEnum.FALSE.getValue().equals(newShiftTime.getState())) {
            throw exception(SHIFT_TIME_NOT_EXISTS);
        }

        // 3. 获取营业日
        LocalDate bizDate = generalConfigService.getBizDate(hcode);

        // 4. 固化当前班次的账务数据
        solidifyHandoverReportData(gcode, currentShift.getHcode(), currentShift.getShiftCode(), bizDate);

        // 5. 更新缓存中的班次信息（保持门店不变，只更新班次）
        MerchantShiftSimpleRespVO newShiftInfo = MerchantShiftSimpleRespVO.builder()
                .hcode(currentShift.getHcode())
                .hname(currentShift.getHname())
                .shiftCode(newShiftTime.getShiftCode())
                .shiftName(newShiftTime.getShiftName())
                .bizDate(bizDate)
                .build();
        userShiftTimeRedisDAO.allSet(hcode, newShiftInfo);

        return newShiftInfo;
    }

    @Override
    public MerchantShiftSimpleRespVO nightHandover(HandoverReqVO reqVO) {
        HotelParamConfigDO hotelParamConfig = hotelParamConfigService.getHotelParamConfig(new HotelParamConfigReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setParamType(ParamConfigTypeEnum.PARAM_TYPE_SHIFT_MODE.getParamType()));
        if(hotelParamConfig == null){
            return null;
        }
        ShiftMode shiftMode = BeanUtils.toBean(hotelParamConfig.getValue(), ShiftMode.class);
        if(!ParamConfigTypeEnum.PARAM_TYPE_PAID_IN.getParamType().equals(shiftMode.getShiftMode())){
            return null;
        }
        // 1. 从缓存获取当前用户的门店和班次信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        // 2. 获取当前营业日和上个营业日
        LocalDate currentBizDate = reqVO.getBizDate();
        LocalDate previousBizDate = currentBizDate.minusDays(1);

        // 3. 查询该门店所有启用的班次列表，按开始时间排序
        ShiftTimeReqVO shiftTimeReqVO = new ShiftTimeReqVO();
        shiftTimeReqVO.setGcode(reqVO.getGcode());
        shiftTimeReqVO.setHcode(reqVO.getHcode());
        shiftTimeReqVO.setState(BooleanEnum.TRUE.getValue());
        List<ShiftTimeDO> shiftTimeList = getShiftTimeList(shiftTimeReqVO);

        if (CollUtil.isEmpty(shiftTimeList)) {
            throw exception(SHIFT_TIME_NOT_EXISTS);
        }

        // 按开始时间排序，获取第一个班次
        ShiftTimeDO firstShiftTime = shiftTimeList.stream().min(Comparator.comparing(ShiftTimeDO::getStartTime))
                .orElseThrow(() -> exception(SHIFT_TIME_NOT_EXISTS));

        // 4. 获取上个营业日的交班报表数据
        List<HandoverReportRespDTO> previousDayReports = handoverReportApi.getHandoverReportListByBizDate(
                reqVO.getGcode(), reqVO.getHcode(), previousBizDate).getData();

        // 5. 从交班报表中提取已固化的班次代码
        List<String> solidifiedShiftCodes = CollUtil.isEmpty(previousDayReports) ?
                Collections.emptyList() :
                previousDayReports.stream()
                        .map(HandoverReportRespDTO::getShiftNo)
                        .toList();

        // 6. 找出上个营业日中还没有固化的班次数据
        List<ShiftTimeDO> unsolidifiedShifts = shiftTimeList.stream()
                .filter(shift -> !solidifiedShiftCodes.contains(shift.getShiftCode()))
                .toList();

        // 7. 固化未固化的班次数据
        for (ShiftTimeDO shift : unsolidifiedShifts) {
            try {
                solidifyHandoverReportData(reqVO.getGcode(), reqVO.getHcode(), shift.getShiftCode(), previousBizDate);
            } catch (Exception e) {
                // 固化失败不影响主流程，记录日志
                log.error("夜审交班固化班次数据失败: hcode={}, shiftCode={}, bizDate={}, error={}",
                        reqVO.getHcode(), shift.getShiftCode(), previousBizDate, e.getMessage(), e);
            }
        }

        // 8. 更新缓存中的班次信息为第一个班次
        MerchantShiftSimpleRespVO newShiftInfo = MerchantShiftSimpleRespVO.builder()
                .hcode(reqVO.getHcode())
                .hname(merchant.getHname())
                .shiftCode(firstShiftTime.getShiftCode())
                .shiftName(firstShiftTime.getShiftName())
                .bizDate(currentBizDate)
                .build();
        userShiftTimeRedisDAO.allSet(reqVO.getHcode(), newShiftInfo);

        return newShiftInfo;
    }

    private void validateServiceTime(String gcode, String hcode) {
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(gcode, hcode, ServiceTypeEnum.PMS.getCode());
        if (serviceIntegration != null && LocalDate.now().isAfter(serviceIntegration.getEndTime())) {
            throw exception(PMS_SERVICE_EXPIRED, serviceIntegration.getEndTime());
        }
    }

    @Override
    public void deleteShiftRedisCache() {
        userShiftTimeRedisDAO.delete(SecurityFrameworkUtils.getLoginUserToken());
    }


    @Override
    public String getShiftTimeByUserIdFromCache(String hcode) {
        MerchantShiftSimpleRespVO respVO = userShiftTimeRedisDAO.get(hcode, SecurityFrameworkUtils.getLoginUserToken());
        if(ObjectUtil.isEmpty(respVO)){
            throw exception(SHIFT_TIME_LOST);
        }
        return respVO.getShiftCode();
    }

    @Override
    public String getHnameByUserIdFromCache(String hcode) {
        MerchantShiftSimpleRespVO respVO = userShiftTimeRedisDAO.get(hcode, SecurityFrameworkUtils.getLoginUserToken());
        return respVO.getHname();
    }

    @Override
    public ShiftTimeDO getShiftTime(String gcode, String shiftCode, String hcode) {
        return shiftTimeMapper.selectOne(ShiftTimeDO::getGcode, gcode, ShiftTimeDO::getShiftCode, shiftCode, ShiftTimeDO::getHcode, hcode);
    }

    @Override
    public List<ShiftTimeDO> getShiftTimeList(ShiftTimeReqVO reqVO) {
        return shiftTimeMapper.selectList(reqVO);
    }

    @Override
    public List<ShiftTimeDO> getGcodeShiftTimeList(String gcode, String state) {
        return shiftTimeMapper.selectGcodeShiftTimeList(gcode, state);
    }

    @Override
    public ChangeShiftTimeRespVO getShift(HandoverReqVO reqVO) {
        MerchantShiftSimpleRespVO respVO = userShiftTimeRedisDAO.get(reqVO.getHcode(), SecurityFrameworkUtils.getLoginUserToken());
        if(ObjectUtil.isEmpty(respVO)){
            throw exception(SHIFT_TIME_LOST);
        }

        // 获取营业日期
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());

        // 获得班次列表
        List<ShiftTimeDO> shiftTimeList = getShiftTimeList(new ShiftTimeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(BooleanEnum.TRUE.getValue()));

        // 获取当前营业日的交班报表数据
        List<HandoverReportRespDTO> currentDayReports = handoverReportApi.getHandoverReportListByBizDate(
                reqVO.getGcode(), reqVO.getHcode(), bizDate).getData();

        // 从交班报表中提取已使用的班次代码
        List<String> usedShiftCodes = CollUtil.isEmpty(currentDayReports) ?
                Collections.emptyList() :
                currentDayReports.stream()
                        .map(HandoverReportRespDTO::getShiftNo)
                        .toList();

        // 过滤掉当前班次和已在交班报表中的班次，只保留可交换的班次
        List<ShiftTimeRespVO> availableShifts = shiftTimeList.stream()
                .filter(shift -> !shift.getShiftCode().equals(respVO.getShiftCode())) // 过滤当前班次
                .filter(shift -> !usedShiftCodes.contains(shift.getShiftCode())) // 过滤已交班的班次
                .map(shift -> {
                    ShiftTimeRespVO shiftTimeRespVO = BeanUtils.toBean(shift, ShiftTimeRespVO.class);
                    return shiftTimeRespVO;
                })
                .collect(Collectors.toList());

        return ChangeShiftTimeRespVO.builder()
                .hcode(respVO.getHcode())
                .gcode(reqVO.getGcode())
                .currentShiftCode(respVO.getShiftCode())
                .currentShiftName(respVO.getShiftName())
                .shiftTimeList(availableShifts)
                .build();
    }

    @Override
    public List<ShiftTimeDO> getChangeShiftByShiftSet(HandoverReqVO reqVO) {
        HotelParamConfigDO hotelParamConfig = hotelParamConfigService.getHotelParamConfig(new HotelParamConfigReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setParamType(ParamConfigTypeEnum.PARAM_TYPE_SHIFT_MODE.getParamType()));
        if(hotelParamConfig == null){
            return new ArrayList<>();
        }
        ShiftMode shiftMode = BeanUtils.toBean(hotelParamConfig.getValue(), ShiftMode.class);
        if(ParamConfigTypeEnum.PARAM_TYPE_CASH_FLOW.getParamType().equals(shiftMode.getShiftMode())){
            return getShiftTimeList(new ShiftTimeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(BooleanEnum.TRUE.getValue()));
        }
        if(ParamConfigTypeEnum.PARAM_TYPE_PAID_IN.getParamType().equals(shiftMode.getShiftMode())){
            return getPaidInShift(reqVO);
        }

        return new ArrayList<>();
    }

    private List<ShiftTimeDO> getPaidInShift(HandoverReqVO reqVO) {
        // 获取营业日期
        LocalDate bizDate = generalConfigService.getBizDate(reqVO.getHcode());

        // 获得班次列表，按开始时间排序
        List<ShiftTimeDO> shiftTimeList = getShiftTimeList(new ShiftTimeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setState(BooleanEnum.TRUE.getValue()));

        if (CollUtil.isEmpty(shiftTimeList)) {
            return new ArrayList<>();
        }

        // 获取当前营业日的交班报表数据
        List<HandoverReportRespDTO> currentDayReports = handoverReportApi.getHandoverReportListByBizDate(
                reqVO.getGcode(), reqVO.getHcode(), bizDate).getData();

        // 从交班报表中提取已固化的班次代码（shiftNo就是shiftCode）
        List<String> solidifiedShiftCodes = CollUtil.isEmpty(currentDayReports) ?
                Collections.emptyList() :
                currentDayReports.stream()
                        .map(HandoverReportRespDTO::getShiftNo)
                        .toList();

        // 找出不在固化表中存在的第一个班次（按开始时间排序）
        // 过滤已固化的班次
        // 按开始时间排序
        Optional<ShiftTimeDO> firstUnsolidifiedShift = shiftTimeList.stream()
                .filter(shift -> !solidifiedShiftCodes.contains(shift.getShiftCode())).min(Comparator.comparing(ShiftTimeDO::getStartTime)); // 获取第一个

        if (firstUnsolidifiedShift.isEmpty()) {
            return new ArrayList<>();
        }

        // 转换为响应VO
        ShiftTimeDO firstShift = firstUnsolidifiedShift.get();
        return List.of(firstShift);
    }

    /**
     * 固化交班报表数据（本班留存的数据）
     * 参考accountController的handoverReportCashRealization方法
     * 固化的是缓存中原本的班次数据，而不是新切换的班次
     */
    private void solidifyHandoverReportData(String gcode, String hcode, String shiftNo, LocalDate bizDate) {
        try {

            // 构建查询条件
            HandoverReportCashRealizationReqVO reqVO = new HandoverReportCashRealizationReqVO();
            reqVO.setGcode(gcode);
            reqVO.setHcode(hcode);
            reqVO.setShiftNo(shiftNo);
            reqVO.setBizDate(bizDate);
            reqVO.setOperator(SecurityFrameworkUtils.getLoginUserNickname());

            // 调用AccountService获取交班报表数据
            HandoverReportCashRealizationRespVO handoverData = accountService.handoverReportCashRealization(reqVO);

            // 获取本班留存数据
            HandoverReportCashRealizationRespVO.HandoverAmountDetail remainingAmount =
                    handoverData.getHandoverDetail().getRemainingAmount();

            // 构建固化数据
            HandoverReportSaveReqDTO saveReqDTO = new HandoverReportSaveReqDTO();
            saveReqDTO.setGcode(gcode);
            saveReqDTO.setHcode(hcode);
            saveReqDTO.setShiftNo(shiftNo);
            saveReqDTO.setBizDate(bizDate);
            //saveReqDTO.setOperator(reqVO.getOperator());

            // 设置本班留存的各支付方式金额
            saveReqDTO.setCashAmount(remainingAmount.getCashAmount());
            saveReqDTO.setBankCardAmount(remainingAmount.getBankCardAmount());
            saveReqDTO.setWechatAmount(remainingAmount.getWechatAmount());
            saveReqDTO.setAlipayAmount(remainingAmount.getAlipayAmount());
            //saveReqDTO.setTotalAmount(remainingAmount.getTotalAmount());

            // 首先查询表里是否有该班次数据
            List<HandoverReportRespDTO> existingReports = handoverReportApi.getHandoverReportListByShiftNo(
                    gcode, hcode, shiftNo, bizDate).getData();

            if (CollUtil.isNotEmpty(existingReports)) {
                // 有则更新
                HandoverReportRespDTO existingReport = existingReports.getFirst();
                saveReqDTO.setId(existingReport.getId());
                handoverReportApi.updateHandoverReport(saveReqDTO);
            } else {
                // 无则新增
                handoverReportApi.createHandoverReport(saveReqDTO);
            }

        } catch (Exception e) {
            // 固化失败不影响主流程，记录日志即可
            log.error("固化交班报表数据失败: hcode={}, shiftNo={}, bizDate={}, error={}",
                     hcode, shiftNo, bizDate, e.getMessage(), e);
        }
    }

}