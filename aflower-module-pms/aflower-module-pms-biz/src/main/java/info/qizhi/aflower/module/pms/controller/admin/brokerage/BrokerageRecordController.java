package info.qizhi.aflower.module.pms.controller.admin.brokerage;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.pms.controller.admin.brokerage.vo.record.*;
import info.qizhi.aflower.module.pms.service.brokeragerecord.BrokerageRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 返佣记录")
@RestController
@RequestMapping("/pms/brokerage-record")
@Validated
public class BrokerageRecordController {

    @Resource
    private BrokerageRecordService brokerageRecordService;

    @PostMapping("/create")
    @Operation(summary = "创建返佣记录")
    @PreAuthorize("@ss.hasPermission('finance:brokerage-record:create')")
    public CommonResult<Long> createBrokerageRecord(@Valid @RequestBody BrokerageRecordSaveReqVO createReqVO) {
        return success(brokerageRecordService.createBrokerageRecord(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得返佣记录分页")
    @PreAuthorize("@ss.hasPermission('finance:brokerage-record:query:page')")
    public CommonResult<BrokerageRespVO> getBrokerageRecordPage(@Valid BrokerageRecordPageReqVO pageReqVO) {
        BrokerageRespVO brokerageRecordPage = brokerageRecordService.getBrokerageRecordPage(pageReqVO);
        return success(brokerageRecordPage);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出返佣记录 Excel")
    @PreAuthorize("@ss.hasPermission('finance:brokerage-record:export')")
    @ApiAccessLog(operateType = OperateTypeEnum.EXPORT)
    public void exportBrokerageRecordExcel(@Valid BrokerageRecordPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<BrokerageRecordRespVO> list = brokerageRecordService.getBrokerageRecordPage(pageReqVO).getPageResult().getList();
        // 导出 Excel
        ExcelUtils.write(response, "返佣记录.xls", "数据", BrokerageRecordRespVO.class,
                        BeanUtils.toBean(list, BrokerageRecordRespVO.class));
    }

    @PutMapping("/state")
    @Operation(summary = "批量核销（撤销）返佣记录")
    @PreAuthorize("@ss.hasPermission('finance:brokerage-record:update')")
    public CommonResult<Boolean> updateBrokerageRecord(@Valid @RequestBody BrokerageRecordVerifyReqVO reqVO) {
        brokerageRecordService.updateBrokerageRecordState(reqVO);
        return success(true);
    }

    @GetMapping("/choose-list")
    @Operation(summary = "选中返佣记录列表")
    //@PreAuthorize("@ss.hasPermission('finance:brokerage-record:query')")
    public CommonResult<List<BrokerageRecordRespVO>> getChooseBrokerageRecordList(@Valid BrokerageRecordChooseReqVO reqVO) {
        List<BrokerageRecordRespVO> list = brokerageRecordService.getChooseBrokerageRecordList(reqVO);
        return success(list);
    }
}