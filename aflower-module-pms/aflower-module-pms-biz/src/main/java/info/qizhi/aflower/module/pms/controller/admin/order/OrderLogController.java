package info.qizhi.aflower.module.pms.controller.admin.order;

import info.qizhi.aflower.framework.common.enums.DictDataEnum;
import info.qizhi.aflower.framework.common.enums.ShiftTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.log.OrderLogPageReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.log.OrderLogRespVO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderLogDO;
import info.qizhi.aflower.module.pms.service.order.OrderLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 订单日志")
@RestController
@RequestMapping("/pms/order-log")
@Validated
public class OrderLogController {

    @Resource
    private OrderLogService orderLogService;

    @GetMapping("/get")
    @Operation(summary = "获得订单日志")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<OrderLogRespVO> getOrderLog(@RequestParam("id") Long id) {
        OrderLogDO orderLog = orderLogService.getOrderLog(id);
        return success(BeanUtils.toBean(orderLog, OrderLogRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得订单日志分页")
    public CommonResult<PageResult<OrderLogRespVO>> getOrderLogPage(@Valid OrderLogPageReqVO pageReqVO) {
        PageResult<OrderLogDO> pageResult = orderLogService.getOrderLogPage(pageReqVO);
        return success(buildOrderLogRespVOPage(pageResult));
    }

    private PageResult<OrderLogRespVO> buildOrderLogRespVOPage(PageResult<OrderLogDO> pageResult) {
        PageResult<OrderLogRespVO> pages=new PageResult<>();
        List<OrderLogRespVO> list=new ArrayList<>();
        for (OrderLogDO orderLogDO : pageResult.getList()) {
            OrderLogRespVO bean = BeanUtils.toBean(orderLogDO, OrderLogRespVO.class);
            bean.setShiftNoName(ShiftTypeEnum.getNameByCode(bean.getShiftNo()));
            bean.setHandle(DictDataEnum.getLabelByCode(orderLogDO.getHandle()));
            list.add(bean);
        }
        pages.setList(list);
        pages.setTotal(pageResult.getTotal());
        return pages;
    }

}