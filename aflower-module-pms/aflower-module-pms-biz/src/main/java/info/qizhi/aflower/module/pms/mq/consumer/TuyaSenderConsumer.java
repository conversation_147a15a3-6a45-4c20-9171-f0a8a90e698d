package info.qizhi.aflower.module.pms.mq.consumer;

import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.HiiiActionTypeEnum;
import info.qizhi.aflower.framework.common.enums.IdTypeEnum;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.enums.ServiceTypeEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.hiii.api.order.TuyaOrderApi;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.order.OrderContinueInReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.together.OrderTogetherSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderDO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderTogetherDO;
import info.qizhi.aflower.module.pms.dal.dataobject.room.RoomDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.dal.dataobject.serviceintegration.ServiceIntegrationDO;
import info.qizhi.aflower.module.pms.mq.message.tuya.OrderMessage;
import info.qizhi.aflower.module.pms.mq.producer.HiiiTuyaSenderProducer;
import info.qizhi.aflower.module.pms.service.order.OrderTogetherService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import info.qizhi.aflower.module.pms.service.sender.bo.*;
import info.qizhi.aflower.module.pms.service.serviceintegration.ServiceIntegrationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.enums.HiiiActionTypeEnum.*;

@Component
@RabbitListener(queues = QueueConstants.TUYA_MESSAGE_QUEUE) // 重点：添加 @RabbitListener 注解，声明消费的 queue
@Slf4j
public class TuyaSenderConsumer {

    @Resource
    private TuyaOrderApi tuyaOrderApi;
    @Resource
    private OrderTogetherService orderTogetherService;
    @Resource
    private RoomTypeService roomTypeService;
    @Resource
    private HiiiTuyaSenderProducer hiiiTuyaSenderProducer;
    @Resource
    private ServiceIntegrationService serviceIntegrationService;

    /**
     * 处理消息
     *
     * @param message 消息
     */
    @RabbitHandler
    public void onMessage(OrderMessage message) {
        //休眠300毫秒
        try {
            Thread.sleep(300);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        HiiiActionTypeEnum actionEnum = HiiiActionTypeEnum.fromCode(message.getActionType());
        // 获得对接服务平台
        ServiceIntegrationDO serviceIntegration = serviceIntegrationService.getServiceIntegration(message.getGcode(), message.getHcode(), ServiceTypeEnum.RCS.getCode());
        if(serviceIntegration == null){
            return;
        }
        switch (actionEnum) {
            case CHECK_IN:
                checkInSend(message.getMsg());
                break;
            case CHECK_OUT:
                checkOutSend(message.getMsg());
                break;
            case ROOM_CHANGE:
                roomChangeSend(message.getMsg());
                break;
            case EXTEND_STAY:
                ExtendStaySend(message.getMsg());
                break;
            case ADD_TOGETHER:
                addTogetherSend(message.getMsg());
                break;
            case UPDATE_TOGETHER:
                updateTogetherSend(message.getMsg(), message.getExt());
                break;
            case DELETE_TOGETHER:
                deleteTogetherSend(message.getMsg());
                break;
            case CREATE_ROOM:
                createRoomSend(message.getMsg());
                break;
            case UPDATE_ROOM:
                updateRoomSend(message.getMsg(), message.getExt());
                break;
            default:
                break;
        }
    }

    private void deleteTogetherSend(String msg) {
        List<OrderTogetherDO> orderTogetherList = JsonUtils.parseArray(msg, OrderTogetherDO.class);
        // 创建 CheckOutInfoBO 实例
        DeleteTogetherInfoBO deleteTogetherInfoBO = new DeleteTogetherInfoBO();
        deleteTogetherInfoBO.setHotel_code(orderTogetherList.getFirst().getHcode());
        List<DeleteTogetherInfoBO.GuestDetail> details = new ArrayList<>();
        for (OrderTogetherDO orderTogether : orderTogetherList) {
            DeleteTogetherInfoBO.GuestDetail deleteTogetherDetail = new DeleteTogetherInfoBO.GuestDetail();
            deleteTogetherDetail.setCheckin_id(orderTogether.getTogetherCode());
            deleteTogetherDetail.setId_card_no(orderTogether.getIdNo());
            deleteTogetherDetail.setId_card_type(convertIdType(orderTogether.getIdType()));
            deleteTogetherDetail.setPhone_no(addPhonePrefix(orderTogether.getPhone()));
            details.add(deleteTogetherDetail);
        }
        deleteTogetherInfoBO.setData(details);
        //tuyaOrderApi.deleteTogether(JsonUtils.toJsonString(deleteTogetherInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(DELETE_TOGETHER.getCode(),JsonUtils.toJsonString(deleteTogetherInfoBO));
        //log.info("删除同住人信息发送成功，响应: {}", JsonUtils.toJsonString(deleteTogetherInfoBO));
    }

    private void checkInSend(String msg) {
        List<OrderTogetherDO> orderTogetherList = JsonUtils.parseArray(msg, OrderTogetherDO.class);
        // 创建 CheckinInfoBO 实例
        CheckinInfoBO checkinInfoBO = new CheckinInfoBO();
        // 将 RoomChangeDetail 添加到 RoomChangeInfoBO 的 data 列表中
        List<CheckinInfoBO.CheckinDetail> details = new ArrayList<>();
        // 遍历每个房间
        for (OrderTogetherDO orderTogether : orderTogetherList) {

            checkinInfoBO.setHotel_code(orderTogether.getHcode()); // 设置门店代码

            details.add(bulidCheckinInfo(orderTogether));

        }
        // 将 CheckinDetail 添加到 CheckinInfoBO 的 data 列表中
        checkinInfoBO.setData(details);

        // 将 checkinInfoBO 转换为 JSON 并调用 tuyaOrderApi.createOrder
//        JsonUtils.toJsonString(checkinInfoBO)
        hiiiTuyaSenderProducer.sendOrderMessage(CHECK_IN.getCode(), JsonUtils.toJsonString(checkinInfoBO));
        //log.info("入住信息发送成功，响应: {}", JsonUtils.toJsonString(checkinInfoBO));
        //tuyaOrderApi.createOrder(BeanUtils.toBean(checkinInfoBO, CheckinInfoDTO.class));
        //tuyaOrderApi.ceShi("ces");
    }

    private void updateRoomSend(String msg, String oldRNo) {
        // 将 JSON 字符串解析为 RoomDO 对象
        RoomDO room = JsonUtils.parseObject(msg, RoomDO.class);

        // 获取房型列表并转换为 Map
        List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(room.getGcode()).setHcode(room.getHcode()), false);
        Map<String, RoomTypeDO> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode);

        // 创建 RoomSyncInfoBO 对象
        RoomSyncInfoBO roomSyncInfoBO = new RoomSyncInfoBO();
        roomSyncInfoBO.setHotel_code(room.getHcode()); // 设置门店代码

        // 创建 RoomDetail 对象
        RoomSyncInfoBO.RoomDetail roomDetail = new RoomSyncInfoBO.RoomDetail();
        roomDetail.setRoom_no(room.getRNo());
        roomDetail.setOld_room_no(oldRNo); // 旧房间号
        roomDetail.setRoom_name(roomTypeMap.get(room.getRtCode()).getRtName()); // 房间名称
        roomDetail.setRoom_type(room.getRtCode()); // 房间房型
        roomDetail.setTel(room.getExtNum()); // 房间座机
        roomDetail.setFloor(room.getFloorCode()); // 楼层
        roomDetail.setDescription(room.getRemark()); // 房间描述

        // 将 RoomDetail 添加到 RoomSyncInfoBO 的 rooms 列表中
        List<RoomSyncInfoBO.RoomDetail> rooms = new ArrayList<>();
        rooms.add(roomDetail);
        roomSyncInfoBO.setRooms(rooms);

        // 将 RoomSyncInfoBO 对象转换为 JSON 字符串
        String roomSyncInfoJson = JsonUtils.toJsonString(roomSyncInfoBO);

        // 调用 API 发送房间信息
        //tuyaOrderApi.updateRoom(roomSyncInfoJson);
        hiiiTuyaSenderProducer.sendOrderMessage(UPDATE_ROOM.getCode(), roomSyncInfoJson);
        //log.info("更新房间信息发送成功，响应: {}", roomSyncInfoJson);
    }

    private void createRoomSend(String msg) {
        RoomDO room = JsonUtils.parseObject(msg, RoomDO.class);
        List<RoomTypeDO> roomTypeList = roomTypeService.getRoomTypeList(new RoomTypeReqVO().setGcode(room.getGcode()).setHcode(room.getHcode()), false);
        Map<String, RoomTypeDO> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode);


        // 创建 RoomInfoBO 对象
        RoomInfoBO roomInfoBO = new RoomInfoBO();
        roomInfoBO.setHotel_code(room.getHcode()); // 设置门店代码

        // 创建 RoomDetail 对象
        RoomInfoBO.RoomDetail roomDetail = new RoomInfoBO.RoomDetail();
        roomDetail.setRoom_no(room.getRNo());
        roomDetail.setFloor(room.getFloorCode());
        roomDetail.setBuilding(room.getBuildingCode());
        roomDetail.setRoom_name(roomTypeMap.get(room.getRtCode()).getRtName());
        roomDetail.setRoom_type(room.getRtCode());
        roomDetail.setTel(room.getExtNum());
        roomDetail.setDescription(room.getRemark()); // 使用备注作为房间描述

        // 将 RoomDetail 添加到 RoomInfoBO 的 rooms 列表中
        List<RoomInfoBO.RoomDetail> rooms = new ArrayList<>();
        rooms.add(roomDetail);
        roomInfoBO.setRooms(rooms);

        // 调用 API 发送房间信息
        //tuyaOrderApi.createRoom(JsonUtils.toJsonString(roomInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(CREATE_ROOM.getCode(), JsonUtils.toJsonString(roomInfoBO));
        //log.info("创建房间信息发送成功，响应: {}", JsonUtils.toJsonString(roomInfoBO));
    }


    private void updateTogetherSend(String msg, String oldPhone) {
        OrderTogetherSaveReqVO orderTogether = JsonUtils.parseObject(msg, OrderTogetherSaveReqVO.class);
        UpdateGuestInfoBO updateGuestInfoBO = new UpdateGuestInfoBO();
        updateGuestInfoBO.setHotel_code(orderTogether.getHcode());

        UpdateGuestInfoBO.GuestDetail guestDetail = new UpdateGuestInfoBO.GuestDetail();
        guestDetail.setCheckin_id(orderTogether.getTogetherCode());
        guestDetail.setCust_name(orderTogether.getName());
        guestDetail.setId_card_no(orderTogether.getIdNo());
        guestDetail.setId_card_type(convertIdType(orderTogether.getIdType()));
        guestDetail.setGender("1".equals(orderTogether.getSex()) ? 1 : 0);
        guestDetail.setNew_phone_no(addPhonePrefix(orderTogether.getPhone()));
        guestDetail.setOld_phone_no(addPhonePrefix(oldPhone));

        updateGuestInfoBO.setData(List.of(guestDetail));
        //tuyaOrderApi.updateGuestInfo(JsonUtils.toJsonString(updateGuestInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(UPDATE_TOGETHER.getCode(),JsonUtils.toJsonString(updateGuestInfoBO));
        //log.info("更新客人信息发送成功，响应: {}", JsonUtils.toJsonString(updateGuestInfoBO));
    }

    private void addTogetherSend(String msg) {
        OrderTogetherDO orderTogether = JsonUtils.parseObject(msg, OrderTogetherDO.class);
        CheckinInfoBO checkinInfoBO = new CheckinInfoBO();
        checkinInfoBO.setHotel_code(orderTogether.getHcode());
        checkinInfoBO.setData(List.of(bulidCheckinInfo(orderTogether)));
        //tuyaOrderApi.createOrderTogether(JsonUtils.toJsonString(checkinInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(ADD_TOGETHER.getCode(),JsonUtils.toJsonString(checkinInfoBO));
        //log.info("添加同住人信息发送成功，响应: {}", JsonUtils.toJsonString(checkinInfoBO));
    }

    private void ExtendStaySend(String msg) {
        OrderContinueInReqVO reqVO = JsonUtils.parseObject(msg, OrderContinueInReqVO.class);
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setOrderNo(reqVO.getOrderNo())
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()));

        //获得换房的客人列表
        List<OrderTogetherDO> continueOrderTogetherList = CollectionUtils.filterList(orderTogetherList, orderTogetherDO -> reqVO.getTogetherCodes().contains(orderTogetherDO.getTogetherCode()));
        // 创建 ContinueInInfoBO 实例
        ContinueInInfoBO continueInInfoBO = new ContinueInInfoBO();
        List<ContinueInInfoBO.ExtendStayDetail> details = new ArrayList<>();
        // 遍历每个客人
        for (OrderTogetherDO orderTogether : orderTogetherList) {

            continueInInfoBO.setHotel_code(orderTogether.getHcode()); // 设置门店代码

            // 创建 RoomChangeDetail 实例
            ContinueInInfoBO.ExtendStayDetail extendStayDetail = new ContinueInInfoBO.ExtendStayDetail();
            extendStayDetail.setCheckin_id(orderTogether.getTogetherCode()); // 客户入住唯一标识（新）
            extendStayDetail.setCheckout_time(orderTogether.getPlanCheckoutTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 预离店时间
            extendStayDetail.setId_card_no(orderTogether.getIdNo()); // 入住人身份证件号
            extendStayDetail.setId_card_type(convertIdType(orderTogether.getIdType())); // 证件类型


            details.add(extendStayDetail);
            continueInInfoBO.setData(details);
        }
        // 将 RoomChangeDetail 添加到 RoomChangeInfoBO 的 data 列表中
        continueInInfoBO.setData(details);

        //tuyaOrderApi.continueIn(JsonUtils.toJsonString(continueInInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(EXTEND_STAY.getCode(),JsonUtils.toJsonString(continueInInfoBO));
        //log.info("续住信息发送成功，响应: {}", JsonUtils.toJsonString(continueInInfoBO));
    }

    private void roomChangeSend(String msg) {
        OrderDO order = JsonUtils.parseObject(msg, OrderDO.class);
        List<OrderTogetherDO> orderTogetherList = orderTogetherService.getOrderTogetherList(new OrderTogetherReqVO().setHcode(order.getHcode()).setGcode(order.getGcode()).setOrderNo(order.getOrderNo()));
        // 创建 RoomChangeInfoBO 实例
        RoomChangeInfoBO roomChangeInfoBO = new RoomChangeInfoBO();
        // 将 RoomChangeDetail 添加到 RoomChangeInfoBO 的 data 列表中
        List<RoomChangeInfoBO.RoomChangeDetail> details = new ArrayList<>();
        // 遍历每个客人
        for (OrderTogetherDO orderTogether : orderTogetherList) {

            roomChangeInfoBO.setHotel_code(orderTogether.getHcode()); // 设置门店代码

            // 创建 RoomChangeDetail 实例
            RoomChangeInfoBO.RoomChangeDetail roomChangeDetail = new RoomChangeInfoBO.RoomChangeDetail();
            roomChangeDetail.setNew_checkin_id(orderTogether.getTogetherCode()); // 客户入住唯一标识（新）
            roomChangeDetail.setOld_checkin_id(orderTogether.getTogetherCode()); // 客户入住的旧标识
            roomChangeDetail.setRoom_no(orderTogether.getRNo()); // 房间号
           /* roomChangeDetail.setBuilding(""); // 楼栋
            roomChangeDetail.setFloor(""); // 楼层*/
            roomChangeDetail.setCheckin_time(orderTogether.getCheckinTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 入住时间
            roomChangeDetail.setCheckout_time(orderTogether.getPlanCheckoutTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 预离店时间
            roomChangeDetail.setCust_name(orderTogether.getName()); // 入住人姓名
            roomChangeDetail.setGender("1".equals(orderTogether.getSex()) ? 1 : 0); // 入住人性别
            roomChangeDetail.setPhone_no(addPhonePrefix(orderTogether.getPhone())); // 用户手机号码
            roomChangeDetail.setId_card_no(orderTogether.getIdNo()); // 入住人身份证件号
            roomChangeDetail.setId_card_type(convertIdType(orderTogether.getIdType())); // 证件类型


            details.add(roomChangeDetail);
            roomChangeInfoBO.setData(details);
        }
        // 将 RoomChangeDetail 添加到 RoomChangeInfoBO 的 data 列表中
        roomChangeInfoBO.setData(details);

        //tuyaOrderApi.changeRoom(JsonUtils.toJsonString(roomChangeInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(ROOM_CHANGE.getCode(),JsonUtils.toJsonString(roomChangeInfoBO));
        //log.info("换房信息发送成功，响应: {}", JsonUtils.toJsonString(roomChangeInfoBO));
    }

    private void checkOutSend(String msg) {
        // 将 msg 转换为 CheckInReqVO 对象
        List<OrderTogetherDO> orderTogetherList = JsonUtils.parseArray(msg, OrderTogetherDO.class);
        // 创建 CheckOutInfoBO 实例
        CheckOutInfoBO checkOutInfoBO = new CheckOutInfoBO();
        // 将 CheckOutDetail 添加到 CheckOutInfoBO 的 data 列表中
        List<CheckOutInfoBO.CheckOutDetail> details = new ArrayList<>();
        // 遍历每个房间
        for (OrderTogetherDO orderTogether : orderTogetherList) {
            checkOutInfoBO.setHotel_code(orderTogether.getHcode()); // 设置门店代码
            // 创建 CheckOutDetail 实例
            CheckOutInfoBO.CheckOutDetail checkOutDetail = new CheckOutInfoBO.CheckOutDetail();
            checkOutDetail.setCheckin_id(orderTogether.getTogetherCode()); //
            checkOutDetail.setDeparture_time(orderTogether.getCheckoutTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 退房时间，转换为13位时间戳
            checkOutDetail.setId_card_no(orderTogether.getIdNo()); // 证件号码
            checkOutDetail.setId_card_type(convertIdType(orderTogether.getIdType())); // 证件类型
            if(StrUtil.isNotEmpty(orderTogether.getPhone())){
                checkOutDetail.setPhone_no(addPhonePrefix(orderTogether.getPhone()));
            }
            details.add(checkOutDetail);
        }
        // 将 CheckOutDetail 添加到 CheckOutInfoBO 的 data 列表中
        checkOutInfoBO.setData(details);

        // 将 checkOutInfoBO 转换为 JSON 并调用 tuyaOrderApi.payCheckOut
        //tuyaOrderApi.payCheckOut(JsonUtils.toJsonString(checkOutInfoBO));
        hiiiTuyaSenderProducer.sendOrderMessage(CHECK_OUT.getCode(),JsonUtils.toJsonString(checkOutInfoBO));
        //log.info("退房信息发送成功，响应: {}", JsonUtils.toJsonString(checkOutInfoBO));

    }


    private static CheckinInfoBO.CheckinDetail bulidCheckinInfo(OrderTogetherDO orderTogether) {
        CheckinInfoBO.CheckinDetail checkinDetail = new CheckinInfoBO.CheckinDetail();
        checkinDetail.setCheckin_id(orderTogether.getTogetherCode()); //
        checkinDetail.setRoom_no(orderTogether.getRNo()); // 房间号
        checkinDetail.setCheckin_time(orderTogether.getCheckinTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 入住时间，转换为13位时间戳
        checkinDetail.setCheckout_time(orderTogether.getPlanCheckoutTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()); // 计划退房时间，转换为13位时间戳
        checkinDetail.setCust_name(orderTogether.getName()); // 客人姓名
        checkinDetail.setGender("1".equals(orderTogether.getSex()) ? 1 : 0); // 客人性别
        checkinDetail.setPhone_no(addPhonePrefixStatic(orderTogether.getPhone())); // 客人电话
        checkinDetail.setId_card_no(orderTogether.getIdNo()); // 证件号码
        checkinDetail.setId_card_type(convertIdTypeStatic(orderTogether.getIdType())); // 证件类型
        return checkinDetail;
    }

    /**
     * 为手机号添加86-前缀（实例方法）
     *
     * @param phone 原始手机号
     * @return 添加86-前缀后的手机号
     */
    private String addPhonePrefix(String phone) {
        return addPhonePrefixStatic(phone);
    }

    /**
     * 为手机号添加86-前缀（静态方法）
     *
     * @param phone 原始手机号
     * @return 添加86-前缀后的手机号
     */
    private static String addPhonePrefixStatic(String phone) {
        if (StrUtil.isBlank(phone)) {
            return phone;
        }

        // 如果已经有86-前缀，直接返回
        if (phone.startsWith("86-")) {
            return phone;
        }

        // 如果只有86前缀但没有-，添加-
        if (phone.startsWith("86") && phone.length() > 2) {
            return "86-" + phone.substring(2);
        }

        // 添加86-前缀
        return "86-" + phone;
    }

    /**
     * 转换证件类型（实例方法）
     *
     * @param idType 原始证件类型
     * @return 转换后的证件类型
     */
    private String convertIdType(String idType) {
        return convertIdTypeStatic(idType);
    }

    /**
     * 转换证件类型（静态方法）
     * 将系统内部的证件类型转换为涂鸦智能平台要求的类型
     *
     * @param idType 原始证件类型
     * @return 转换后的证件类型
     */
    private static String convertIdTypeStatic(String idType) {
        if (StrUtil.isBlank(idType)) {
            return "SFZ"; // 默认返回身份证
        }

        // 使用枚举进行匹配
        if (IdTypeEnum.IDCERT.getCode().equals(idType)) {
            return "SFZ"; // 居民身份证
        } else if (IdTypeEnum.HONGKONG.getCode().equals(idType)) {
            return "HKMOJUMIN"; // 港澳通行证
        } else if (IdTypeEnum.PASSPORT.getCode().equals(idType)) {
            return "WAIGUOREN"; // 护照
        } else if (IdTypeEnum.OFFICERCERT.getCode().equals(idType) ||
                   IdTypeEnum.SOLDIERCERT.getCode().equals(idType) ||
                   IdTypeEnum.POLICECERT.getCode().equals(idType) ||
                   IdTypeEnum.HOUSEHOLDREGISTER.getCode().equals(idType) ||
                   IdTypeEnum.DRIVINGLICENCE.getCode().equals(idType) ||
                   IdTypeEnum.OTHER.getCode().equals(idType)) {
            return "SFZ"; // 其他类型默认映射为身份证
        } else {
            return "SFZ"; // 未知类型默认映射为身份证
        }
    }

}
