package info.qizhi.aflower.module.pms.controller.admin.pricepanel;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate.PricePanelTemplateListReqVO;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate.PricePanelTemplateRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate.PricePanelTemplateSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelTemplateDO;
import info.qizhi.aflower.module.pms.service.pricepanel.PricePanelTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 房价牌模板")
@RestController
@RequestMapping("/pms/price-panel-template")
@Validated
public class PricePanelTemplateController {

    @Resource
    private PricePanelTemplateService pricePanelTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建房价牌模板")
    @PreAuthorize("@ss.hasPermission('biz:price-panel-template:create')")
    public CommonResult<Long> createPricePanelTemplate(@Valid @RequestBody PricePanelTemplateSaveReqVO createReqVO) {
        return success(pricePanelTemplateService.createPricePanelTemplate(createReqVO));
    }

    @GetMapping("/list")
    @Operation(summary = "获得房价牌模板列表")
    @PreAuthorize("@ss.hasPermission('biz:price-panel-template:query')")
    public CommonResult<List<PricePanelTemplateRespVO>> getPricePanelTemplate(@Valid PricePanelTemplateListReqVO reqVO) {
        List<PricePanelTemplateDO> pricePanelTemplate = pricePanelTemplateService.getPricePanelTemplateList(reqVO);
        return success(BeanUtils.toBean(pricePanelTemplate, PricePanelTemplateRespVO.class));
    }


}