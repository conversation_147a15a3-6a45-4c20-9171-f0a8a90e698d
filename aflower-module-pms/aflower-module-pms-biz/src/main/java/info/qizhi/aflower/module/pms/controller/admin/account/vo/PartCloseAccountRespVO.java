package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 账务- 结账、部分结账 Response VO")
@Data
public class PartCloseAccountRespVO {

    @Schema(description = "付款方式")
    private String subCode;

    @Schema(description = "消费金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeFee;

    @Schema(description = "付款金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "余额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long balance;

    @Schema(description = "宾客代码列表")
    private List<String> togetherCodes;

    @Schema(description = "入账账号列表")
    private List<RecordAccountNo> recordAccountNos;

    @Schema(description = "账务列表")
    private List<Account> accounts;

    @Data
    public static class RecordAccountNo {

        @Schema(description = "入账账号", example = "111")
        private String no;

        @Schema(description = "房号", example = "111")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "客人姓名", example = "李四")
        private String name;

        @Schema(description = "宾客代码")
        private String togetherCode;

        @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", example = "2")
        private String accType;
    }

    @Data
    public static class Account {

        @Schema(description = "账务号;系统生成唯一标识，每笔账都生成一个")
        private String accNo;

        @Schema(description = "房号", example = "111")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "客人姓名", example = "李四")
        private String name;

        @Schema(description = "科目名称;消费科目名称、付款方式名称")
        private String subName;

        @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", example = "2")
        private String subType;

        @Schema(description = "金额")
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long fee;

    }
}