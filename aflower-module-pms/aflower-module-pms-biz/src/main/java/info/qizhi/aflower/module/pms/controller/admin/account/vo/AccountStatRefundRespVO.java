package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "biz - 统计账务 Response VO")
@Data
public class AccountStatRefundRespVO {
    @Schema(description = "消费科目、付款方式代码;消费科目、付款方式代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称;消费科目名称、付款方式名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subName;

    @Schema(description = "付款金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "已退金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long refundedAmount;

    @Schema(description = "可选金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long availableAmount;
}
