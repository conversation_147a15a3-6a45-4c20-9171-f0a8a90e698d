package info.qizhi.aflower.module.pms.controller.admin.realbizkpi.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

@Schema(description = "小程序 - 订单查询(订单相关)分页 Response VO")
@Data
public class RealOrderVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29575")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "外部订单号;OTA的订单号")
    private String outOrderNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "预抵时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String checkinType;


    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String state;


    @Schema(description = "入住类型名称", example = "2")
    private String checkinTypeName;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcType;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestCode;

    @Schema(description = "订单类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderType;

    @Schema(description = "订单来源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderSource;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "统计渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statChannelCode;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime createTime;

}
