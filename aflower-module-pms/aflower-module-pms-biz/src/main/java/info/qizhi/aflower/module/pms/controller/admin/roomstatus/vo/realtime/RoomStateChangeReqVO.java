package info.qizhi.aflower.module.pms.controller.admin.roomstatus.vo.realtime;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 房态改变 Request VO")
@Data
public class RoomStateChangeReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "房间代码")
    @NotEmpty(message = "{rCode.notempty}")
    private String rCode;

    @Schema(description = "原状态")
    @NotEmpty(message = "{ sourceStatus.notempty }")
    private String sourceState;

    @Schema(description = "目标状态")
    @NotEmpty(message = "{ targetStatus.notempty }")
    private String targetState;
}
