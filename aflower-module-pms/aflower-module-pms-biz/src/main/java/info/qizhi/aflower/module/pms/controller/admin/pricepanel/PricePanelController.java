package info.qizhi.aflower.module.pms.controller.admin.pricepanel;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.*;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.bo.PricePanelContentBO;
import info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo.pricepaneltemplate.PricePanelSaveBaseReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelCustomDO;
import info.qizhi.aflower.module.pms.service.pricepanel.PricePanelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 房价牌")
@RestController
@RequestMapping("/pms/price-panel")
@Validated
public class PricePanelController {

    @Resource
    private PricePanelService pricePanelService;

    // 创建一个Sinks.Many实例，用于发布房价牌信息
//    private final Sinks.Many<PricePanelRespVO> pricePanelSink = Sinks.many().multicast().onBackpressureBuffer();

    @PostMapping("/create-or-update-base")
    @Operation(summary = "创建(更新)房价牌基础信息")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:create')")
    public CommonResult<Boolean> createPricePanelBase(@Valid @RequestBody PricePanelSaveBaseReqVO createReqVO) {
        boolean result = pricePanelService.createPricePanelBase(createReqVO);
//        if (result) {
//            PricePanelRespVO pricePanel = pricePanelService.getPricePanel(createReqVO.getHcode(), createReqVO.getGcode());
//            pricePanelSink.tryEmitNext(pricePanel); // 推送更新后的房价牌信息
//        }
        return success(result);
    }


    @PostMapping("/update")
    @Operation(summary = "(更新)房价牌房价设置")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:create')")
    public CommonResult<Boolean> createPricePanel(@Valid @RequestBody PricePanelSaveReqVO createReqVO) {
        boolean result = pricePanelService.createPricePanel(createReqVO);
//        if (result) {
//            PricePanelRespVO pricePanel = pricePanelService.getPricePanel(createReqVO.getHcode(), createReqVO.getGcode());
//            pricePanelSink.tryEmitNext(pricePanel); // 推送更新后的房价牌信息
//        }
        return success(result);
    }


    @GetMapping("/get")
    @Operation(summary = "获得房价牌")
    @Parameter(name = "hcode", description = "门店代码", required = true)
    @Parameter(name = "gcode", description = "集团代码", required = true)
    @PreAuthorize("@ss.hasPermission('biz:price-panel:query')")
    public CommonResult<PricePanelRespVO> getPricePanel(@RequestParam("hcode") String hcode, @RequestParam("gcode") String gcode) {
        PricePanelRespVO pricePanel = pricePanelService.getPricePanel(hcode, gcode);
        return success(pricePanel);
    }

    @GetMapping("/get-price")
    @Operation(summary = "获得房价牌房价")
    @Parameter(name = "hcode", description = "门店代码", required = true)
    @Parameter(name = "gcode", description = "集团代码", required = true)
    @PreAuthorize("@ss.hasPermission('biz:price-panel:query')")
    public CommonResult<List<PricePanelContentBO>> getPricePanelPrice(@Valid PricePanelPriceReqVO reqVO) {
        List<PricePanelContentBO> pricePanels = pricePanelService.getPricePanelPrice(reqVO);
        return success(pricePanels);
    }

    @GetMapping("/get-price-show")
    @Operation(summary = "获得房价牌房价用于酒店展示")
    @Parameter(name = "hcode", description = "门店代码", required = true)
    @Parameter(name = "gcode", description = "集团代码", required = true)
    public CommonResult<List<List<PricePanelContentBO>>> getPricePanelPriceShow(@Valid PricePanelPriceReqVO reqVO) {
        List<List<PricePanelContentBO>> pricePanels = pricePanelService.getPricePanelPriceShow(reqVO);
        return success(pricePanels);
    }

 /*   @PutMapping("/update-state")
    @Operation(summary = "更新房价牌状态")
    @Parameter(name = "hcode", description = "门店代码", required = true)
    @Parameter(name = "gcode", description = "集团代码", required = true)
    @Parameter(name = "state", description = "业务开通状态", required = true)
    @PreAuthorize("@ss.hasPermission('biz:price-panel:update')")
    public CommonResult<Boolean> updatePricePanelState(@RequestParam("hcode") String hcode,@RequestParam("gcode") String gcode,@RequestParam("state") String state) {
        pricePanelService.updatePricePanelState(hcode,gcode,state);
        return success(true);
    }*/

    // ==================== 子表（房价牌(自定义房型房价)） ====================

  /*  @GetMapping("/price-panel-custom/list-by-hcode")
    @Operation(summary = "获得房价牌(自定义房型房价)列表")
    @Parameter(name = "hcode", description = "门店代码")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:query')")
    public CommonResult<List<PricePanelCustomDO>> getPricePanelCustomListByHcode(@RequestParam("hcode") String hcode) {
        return success(pricePanelService.getPricePanelCustomListByHcode(hcode));
    }*/

    @GetMapping("/get-price-panel-custom")
    @Operation(summary = "获得房价牌(自定义房型房价)默认房价")
    @Parameter(name = "hcode", description = "门店代码")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:query')")
    public CommonResult<PricePanelCustomDO> getPricePanelCustom(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam(required = false ,value = "mtCode") String mtCode) {
        return success(pricePanelService.getPricePanelCustom(gcode, hcode, mtCode));
    }

    @PostMapping("/create-price-panel-custom")
    @Operation(summary = "创建房价牌(自定义房型房价)")
    @Parameter(name = "hcode", description = "门店代码")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:create')")
    public CommonResult<Boolean> createPricePanelCustom(@Valid @RequestBody PricePanelCustomReqVO reqVO) {
        pricePanelService.createPricePanelCustom(reqVO);
        return success(true);
    }

    @PostMapping("/update-price-panel-custom")
    @Operation(summary = "修改房价牌(自定义房型房价)")
    @Parameter(name = "hcode", description = "门店代码")
    @PreAuthorize("@ss.hasPermission('biz:price-panel:create')")
    public CommonResult<Boolean> updatePricePanelCustom(@Valid @RequestBody PricePanelCustomUpdateReqVO reqVO) {
        pricePanelService.updatePricePanelCustom(reqVO);
        return success(true);
    }

    @DeleteMapping("/delete-price-panel-custom")
    @Operation(summary = "删除房价牌(自定义房型房价)")
    @Parameter(name = "customCode", description = "自定义房型房价代码", required = true)
    @PreAuthorize("@ss.hasPermission('biz:price-panel:delete')")
    public CommonResult<Boolean> deletePricePanel(@RequestParam("customCode") String customCode) {
        pricePanelService.deletePricePanelCustomByCustomCode(customCode);
        return success(true);
    }
    // SSE端点，用于前端订阅房价牌信息
//    @GetMapping(value = "/sse", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
//    public Flux<String> getSseStream() {
//        return Flux.interval(Duration.ofSeconds(5000))
//                .map(sequence -> "heartbeat: " + sequence)
//                .mergeWith(pricePanelSink.asFlux().map(pricePanel -> "data: " + pricePanel));
//    }

}