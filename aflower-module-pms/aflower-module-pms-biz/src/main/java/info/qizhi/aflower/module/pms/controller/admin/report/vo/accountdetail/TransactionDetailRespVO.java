package info.qizhi.aflower.module.pms.controller.admin.report.vo.accountdetail;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 交易记录明细 Response VO")
@Data
public class TransactionDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1037")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hname;

    @Schema(description = "姓名", example = "门店代码示例")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "外部订单号（订单编号）", example = "门店代码示例")
    private String outOrderNo;

    @Schema(description = "订单号", example = "门店代码示例")
    private String no;

    @Schema(description = "姓名", example = "门店代码示例")
    private String guestName;

    @Schema(description = "金额", example = "门店代码示例")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "科目代码")
    private String subCode;

    @Schema(description = "科目名称", example = "门店代码示例")
    private String subCodeName;

    @Schema(description = "状态")
    private String state;

    @Schema(description = "交易时间", example = "2024-07-04")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

    @Schema(description = "操作员", example = "门店代码示例")
    private String  recorder;

    @Schema(description = "操作员昵称", example = "门店代码示例")
    private String  recorderName;

}
