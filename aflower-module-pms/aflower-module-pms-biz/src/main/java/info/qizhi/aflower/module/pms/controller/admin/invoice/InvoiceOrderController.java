package info.qizhi.aflower.module.pms.controller.admin.invoice;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.invoice.core.client.dto.BlueInvoiceRespDTO;
import info.qizhi.aflower.framework.invoice.core.client.dto.EnterpriseRespDTO;
import info.qizhi.aflower.framework.invoice.core.client.dto.RedInvoiceRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.invoice.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.invoice.InvoiceDO;
import info.qizhi.aflower.module.pms.service.invoice.InvoiceOrderService;
import info.qizhi.aflower.module.pms.service.invoice.InvoiceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 开票")
@RestController
@RequestMapping("/pms/invoice-order")
@Validated
public class InvoiceOrderController {

    @Resource
    private InvoiceOrderService invoiceOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建开票信息")
    @PreAuthorize("@ss.hasPermission('pms:invoice:create')")
    public CommonResult<BlueInvoiceRespDTO> createInvoice(@Valid @RequestBody InvoiceOrderBlueReqVO createReqVO) {
        return success(invoiceOrderService.blueInvoice(createReqVO));
    }

    @PutMapping("/red")
    @Operation(summary = "发票冲红")
    @PreAuthorize("@ss.hasPermission('pms:invoice:create')")
    public CommonResult<RedInvoiceRespDTO> redInvoice(@Valid @RequestBody InvoiceOrderRedReqVO updateReqVO) {
        return success(invoiceOrderService.redInvoice(updateReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "查询企业抬头信息")
    @Parameter(name = "name", description = "企业名称", required = true, example = "奇智")
    @PreAuthorize("@ss.hasPermission('pms:invoice:query')")
    public CommonResult<List<EnterpriseRespDTO>> queryEnterpriseInfo(@RequestParam("name") String name) {
        List<EnterpriseRespDTO> enterpriseList = invoiceOrderService.queryEnterpriseInfo(name);
        return success(enterpriseList);
    }


    @GetMapping("/list")
    @Operation(summary = "查询发票列表")
    public CommonResult<List<InvoiceOrderRespVO>> getInvoiceOrderList(@Valid InvoiceOrderReqListVO reqListVO) {
        List<InvoiceOrderRespVO> invoiceOrderList = invoiceOrderService.getInvoiceOrderList(reqListVO);
        return success(invoiceOrderList);
    }


}