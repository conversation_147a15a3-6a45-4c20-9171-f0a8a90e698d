package info.qizhi.aflower.module.pms.controller.admin.pricepanel.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.module.pms.dal.dataobject.pricepanel.PricePanelCustomDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
@Schema(description = "管理后台 - 房价牌(自定义房型房价) Response VO")
@Data
public class PricePanelCustomRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3763")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "自定义房型房价代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "门市价", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long marketPrice;

    @Schema(description = "会员价", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField(typeHandler = PricePanelCustomDO.MemberPriceParameterTypeHandler.class)
    private List<PricePanelCustomDO.MemberPriceParameter> memberPrice;
}
