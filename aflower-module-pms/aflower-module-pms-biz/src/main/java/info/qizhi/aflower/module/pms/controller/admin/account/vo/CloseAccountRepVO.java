package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 已结账务 Request VO")
@Data
public class CloseAccountRepVO {
    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{togetherCode.notempty}")
    private String togetherCode;

}