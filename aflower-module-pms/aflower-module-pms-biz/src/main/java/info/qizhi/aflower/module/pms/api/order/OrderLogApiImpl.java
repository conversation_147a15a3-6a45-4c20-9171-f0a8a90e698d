package info.qizhi.aflower.module.pms.api.order;

import cn.hutool.core.bean.BeanUtil;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.order.dto.OrderLogReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderLogRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderLogSaveReqDTO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.log.OrderLogReqVO;
import info.qizhi.aflower.module.pms.controller.admin.order.vo.log.OrderLogSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.order.OrderLogDO;
import info.qizhi.aflower.module.pms.service.order.OrderLogService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class OrderLogApiImpl implements OrderLogApi{

    @Resource
    private OrderLogService orderLogService;

    @Override
    public CommonResult<Long> createOrderLog(OrderLogSaveReqDTO createReqVO) {
        return success(orderLogService.createOrderLog(BeanUtil.toBean(createReqVO, OrderLogSaveReqVO.class)));
    }

    @Override
    public CommonResult<List<OrderLogRespDTO>> getOrderLogList(OrderLogReqDTO createReqVO) {
        List<OrderLogDO> orderLogList = orderLogService.getOrderLogList(BeanUtils.toBean(createReqVO, OrderLogReqVO.class));
        return success(BeanUtils.toBean(orderLogList, OrderLogRespDTO.class));
    }
}
