package info.qizhi.aflower.module.pms.controller.admin.channel;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.locale.MessageSourceUtil;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.channel.vo.*;
import info.qizhi.aflower.module.pms.dal.dataobject.channel.ChannelDO;
import info.qizhi.aflower.module.pms.service.channel.ChannelService;
import info.qizhi.aflower.module.pms.service.selfhelp.SelfHelpYKService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 渠道")
@RestController
@RequestMapping("/pms/channel")
@Validated
public class ChannelController {

    @Resource
    private ChannelService channelService;

    @Resource
    SelfHelpYKService selfHelpYKService;

    @PostMapping("/create")
    @Operation(summary = "创建渠道")
    @PreAuthorize("@ss.hasPermission('pms:channel:create')")
    public CommonResult<Long> createChannel(@Valid @RequestBody ChannelSaveReqVO createReqVO) {
        return success(channelService.createChannel(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新渠道")
    @PreAuthorize("@ss.hasPermission('pms:channel:create')")
    public CommonResult<Boolean> updateChannel(@Valid @RequestBody ChannelSaveReqVO updateReqVO) {
        channelService.updateChannel(updateReqVO);
        return success(true);
    }


    @PutMapping("/update-status")
    @Operation(summary = "更新渠道状态")
    @PreAuthorize("@ss.hasPermission('pms:channel:create')")
    public CommonResult<Boolean> updateChannelStatus(@Valid @RequestBody ChannelUpdateStatusReqVO reqVO) {
        channelService.updateChannelStatus(reqVO.getId(), reqVO.getIsEnable());
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得渠道")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:channel:query')")
    public CommonResult<ChannelRespVO> getChannel(@RequestParam("id") Long id) {
        ChannelDO channel = channelService.getChannel(id);
        return success(BeanUtils.toBean(channel, ChannelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得渠道分页")
    @PreAuthorize("@ss.hasPermission('pms:channel:query:page')")
    public CommonResult<PageResult<ChannelRespVO>> getChannelPage(@Valid ChannelPageReqVO pageReqVO) {
        PageResult<ChannelDO> channelList = channelService.getChannelPage(pageReqVO);
        return success(BeanUtils.toBean(channelList, ChannelRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得渠道列表", description = "门店代码如果不为空，查询集团和门店创建的渠道，否则只查询集团创建的渠道")
    //@PreAuthorize("@ss.hasPermission('pms:channel:query')")
    public CommonResult<List<ChannelRespVO>> getChannelList(@Valid ChannelReqVO reqVO) {
        List<ChannelDO> channelList = channelService.getChannelList(reqVO);
        return success(BeanUtils.toBean(channelList, ChannelRespVO.class));
    }

    @GetMapping("/simple-list")
    @Operation(summary = "获得渠道列表（只包含渠道代码和名称）", description = "只包含渠道代码和名称，主要用于前端的下拉选项")
    //@PreAuthorize("@ss.hasPermission('pms:channel:query')")
    public CommonResult<List<ChannelSimpleRespVO>> getChannelSimpleList(@Valid ChannelReqVO reqVO) {
        List<ChannelDO> channelList = channelService.getChannelList(reqVO);
        return success(convertToResponseList(channelList));
    }


    /**
     * 不要调用，仅供测试使用
     * @return
     */
    @PostMapping("/test-push")
    public CommonResult<?> testOtaPush() {
        selfHelpYKService.testPush();
        return success(true);
    }

    private static List<ChannelSimpleRespVO> convertToResponseList(List<ChannelDO> channelList) {
        return channelList.stream()
                .map(channelDO -> {
                    ChannelSimpleRespVO respVO = new ChannelSimpleRespVO();
                    respVO.setChannelCode(channelDO.getChannelCode());

                    // 动态获取 label 标签
                    String messageCode = "channel_" + channelDO.getChannelCode(); // 使用 code 生成唯一 message key
                    String channelName  = MessageSourceUtil.getMessage(messageCode);

                    // 如果 messageSource 返回的内容与 messageCode 相同，表示未找到对应的国际化内容
                    respVO.setChannelName(messageCode.equals(channelName) ? channelDO.getChannelName() : channelName);

                    return respVO;
                })
                .collect(Collectors.toList());
    }


}