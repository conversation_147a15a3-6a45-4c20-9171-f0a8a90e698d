package info.qizhi.aflower.module.pms.controller.admin.nightaudi.vo.roomnum;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 夜审房间数设置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RoomNightsNumSetPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "入住类型", example = "1")
    private String checkinType;

    @Schema(description = "间夜数")
    private BigDecimal nightNum;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}