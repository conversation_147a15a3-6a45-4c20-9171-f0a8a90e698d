package info.qizhi.aflower.module.pms.controller.admin.account.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "biz - 账务补录 Request VO")
@Data
public class RecordingAccountQueryReqVO {
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "外部订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "外部订单号不能为空")
    private String outOrderNo;

  /*  @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{subCode.notblank}")
    private String subCode;*/

   /* @Schema(description = "支付类型值 1为收款，-1为退款", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{payValue.notblank}")
    private String payValue;*/
}
