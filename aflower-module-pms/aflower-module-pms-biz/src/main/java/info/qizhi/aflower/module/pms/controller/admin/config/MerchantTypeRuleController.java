package info.qizhi.aflower.module.pms.controller.admin.config;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule.MerchantTypeRuleReqVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule.MerchantTypeRuleRespVO;
import info.qizhi.aflower.module.pms.controller.admin.config.vo.merchanttyperule.MerchantTypeRuleSaveReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.config.MerchantTypeRuleDO;
import info.qizhi.aflower.module.pms.service.config.MerchantTypeRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 门店类型规则")
@RestController
@RequestMapping("/pms/merchant-type-rule")
@Validated
public class MerchantTypeRuleController {

    @Resource
    private MerchantTypeRuleService merchantTypeRuleService;

    @PutMapping("/update")
    @Operation(summary = "更新门店类型规则")
    @PreAuthorize("@ss.hasPermission('pms:merchant-type-rule:update')")
    public CommonResult<Boolean> updateMerchantTypeRule(@Valid @RequestBody MerchantTypeRuleSaveReqVO updateReqVO) {
        merchantTypeRuleService.updateMerchantTypeRule(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得门店类型规则")
    @Parameter(name = "gcode", description = "集团代码", required = true, example = "1024")
    @Parameter(name = "merchantType", description = "类型代码", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('pms:merchant-type-rule:query')")
    public CommonResult<MerchantTypeRuleRespVO> getMerchantTypeRule(@RequestParam("gcode") String gcode, @RequestParam("merchantType") String merchantType) {
        MerchantTypeRuleRespVO merchantTypeRule = merchantTypeRuleService.getMerchantTypeRule(gcode, merchantType);
        return success(merchantTypeRule);
    }

    @GetMapping("/list")
    @Operation(summary = "获得门店类型规则列表")
    @PreAuthorize("@ss.hasPermission('pms:merchant-type-rule:query:list')")
    public CommonResult<List<MerchantTypeRuleRespVO>> getMerchantTypeRuleList(@Valid MerchantTypeRuleReqVO reqVO) {
        List<MerchantTypeRuleDO> list = merchantTypeRuleService.getMerchantTypeRuleList(reqVO);
        return success(BeanUtils.toBean(list, MerchantTypeRuleRespVO.class));
    }


}