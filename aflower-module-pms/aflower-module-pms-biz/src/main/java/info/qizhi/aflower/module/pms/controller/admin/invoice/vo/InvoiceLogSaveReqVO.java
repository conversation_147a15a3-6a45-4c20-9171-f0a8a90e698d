package info.qizhi.aflower.module.pms.controller.admin.invoice.vo;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 开票记录新增/修改 Request VO")
@Data
public class InvoiceLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "28542")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "抬头类型;0:个人  1:企业 2:组织", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceTitleType;

    @Schema(description = "发票类型;0:增值税普通发票 1：增值税专用发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private String invoiceType;

    @Schema(description = "发票代码", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "{invoiceCode.notempty}")
    private String invoiceCode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderNumber.notempty}")
    private String orderNo;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "开户银行", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bank;

    @Schema(description = "开户账号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bankNo;

    @Schema(description = "开票时间", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotNull(message = "{makeTime.notnull}")
    private LocalDateTime makeTime;

    @Schema(description = "发票号码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{invoiceNo.notempty}")
    private String invoiceNo;

    @Schema(description = "开票金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{invoiceAmount.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long invoiceMoney;

    @Schema(description = "开票人", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{invoicePerson.notempty}")
    private String invoicePerson;

    @Schema(description = "发票抬头", requiredMode = Schema.RequiredMode.REQUIRED)
    //@NotEmpty(message = "{invoiceTitle.notempty}")
    private String invoiceTitle;

    @Schema(description = "税金")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long tax;

    @Schema(description = "纳税人识别号", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    //@NotEmpty(message = "{taxpayerId.notempty}")
    private String taxpayerId;

    @Schema(description = "企业注册地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    private String regAddress;

    @Schema(description = "企业注册电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "15374")
    private String regPhone;

    @Schema(description = "备注", example = "随便")
    private String remark;

}