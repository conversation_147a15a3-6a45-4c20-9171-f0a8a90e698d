package info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 房型简单模型 Response VO")
@Data
@Builder
public class RoomTypeSimpleRespVO {

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String rtName;

}