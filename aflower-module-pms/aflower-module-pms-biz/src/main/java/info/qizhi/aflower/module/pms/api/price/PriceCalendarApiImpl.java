package info.qizhi.aflower.module.pms.api.price;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.price.dto.*;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentRespDTO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricecalendar.PriceCalendarAloneSaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricecalendar.PriceCalendarReqVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricecalendar.PriceCalendarRespVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.pricecalendar.PriceCalendarUnifySaveReqVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.roomtypebaseprice.RoomTypeBasePriceReqVO;
import info.qizhi.aflower.module.pms.controller.admin.price.vo.roomtypebaseprice.RoomTypeBasePriceRespVO;
import info.qizhi.aflower.module.pms.controller.admin.pricerule.vo.approval.PriceApprovalReqVO;
import info.qizhi.aflower.module.pms.controller.admin.roomtype.vo.roomtype.RoomTypeReqVO;
import info.qizhi.aflower.module.pms.dal.dataobject.price.RoomTypeBasePriceDO;
import info.qizhi.aflower.module.pms.dal.dataobject.roomtype.RoomTypeDO;
import info.qizhi.aflower.module.pms.service.price.PriceCalendarService;
import info.qizhi.aflower.module.pms.service.price.RoomTypeBasePriceService;
import info.qizhi.aflower.module.pms.service.roomtype.RoomTypeService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class PriceCalendarApiImpl implements PriceCalendarApi{
    @Resource
    private PriceCalendarService priceCalendarService;

    @Resource
    private RoomTypeBasePriceService roomTypeBasePriceService;

    @Resource
    private RoomTypeService roomTypeService;

    @Override
    public CommonResult<List<PriceCalendarRespDTO>> getPriceCalendarList(PriceCalendarReqDTO priceCalendarReqVO) {
        PriceCalendarReqVO bean = BeanUtils.toBean(priceCalendarReqVO, PriceCalendarReqVO.class);
        List<PriceCalendarRespVO> list = priceCalendarService.getPriceCalendarList(bean);
        return success(BeanUtils.toBean(list, PriceCalendarRespDTO.class));
    }

    @Override
    public CommonResult<Boolean> updatePriceCalendarAlone(PriceCalendarAloneSaveReqDTO updateReqVO) {
        PriceCalendarAloneSaveReqVO bean = BeanUtils.toBean(updateReqVO, PriceCalendarAloneSaveReqVO.class);
        priceCalendarService.updatePriceCalendarAlone(bean);
        return success(true);
    }

    @Override
    public CommonResult<Boolean> updatePriceCalendarUnify(PriceCalendarUnifySaveReqDTO updateReqVO) {
        PriceCalendarUnifySaveReqVO bean = BeanUtils.toBean(updateReqVO, PriceCalendarUnifySaveReqVO.class);
        priceCalendarService.updatePriceCalendarUnify(bean);
        return success(true);
    }

    @Override
    public CommonResult<List<RoomTypeBasePriceRespDTO>> getRoomTypeBasePrices(RoomTypeBasePriceReqDTO reqVO) {
        RoomTypeBasePriceReqVO bean = BeanUtils.toBean(reqVO, RoomTypeBasePriceReqVO.class);
        List<RoomTypeBasePriceDO> roomTypeBasePrices = roomTypeBasePriceService.getRoomTypeBasePriceList(bean);
        return success(BeanUtils.toBean(buildRoomTypeBasePriceRespVO(roomTypeBasePrices, bean), RoomTypeBasePriceRespDTO.class));
    }

    private List<RoomTypeBasePriceRespVO> buildRoomTypeBasePriceRespVO(List<RoomTypeBasePriceDO> roomTypeBasePrices, RoomTypeBasePriceReqVO reqVO) {
        if (CollUtil.isEmpty(roomTypeBasePrices)) {
            return CollUtil.newArrayList();
        }
        List<RoomTypeBasePriceRespVO> roomTypeBasePriceRespList = BeanUtils.toBean(roomTypeBasePrices, RoomTypeBasePriceRespVO.class);
        // 获取酒店的物理房型
        List<RoomTypeDO> roomTypeList = roomTypeService.getPhysicsRoomTypeList(new RoomTypeReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setIsEnable(BooleanEnum.TRUE.getValue()));
        Map<String, RoomTypeDO> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeDO::getRtCode);
        // 将roomTypeDOS中的房型名称赋值给roomTypeBasePrices
        List<RoomTypeBasePriceRespVO> toRemove = CollUtil.newArrayList();
        roomTypeBasePriceRespList.forEach(roomTypeBasePriceRespVO -> {
            RoomTypeDO roomTypeDO = roomTypeMap.get(roomTypeBasePriceRespVO.getRtCode());
            if (roomTypeDO != null) {
                roomTypeBasePriceRespVO.setRtName(roomTypeDO.getRtName());
            } else {
                // 移除roomTypeBasePriceRespVO
                toRemove.add(roomTypeBasePriceRespVO);
            }
        });
        roomTypeBasePriceRespList.removeAll(toRemove);
        return roomTypeBasePriceRespList;
    }
}
