package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 房型简单模型 Response VO")
@Data
@Builder
public class RoomTypeSimpleRespDTO {

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String rtName;

    // 添加默认构造函数
    public RoomTypeSimpleRespDTO() {
    }

    // 全参构造函数（可选）
    public RoomTypeSimpleRespDTO(String rtCode, String rtName) {
        this.rtCode = rtCode;
        this.rtName = rtName;
    }
}