package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 房型 Request VO")
@Data
@ToString(callSuper = true)
public class RoomTypeReqDTO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果当前是门店房型，该字段为门店代码，否则为0")
    private String hcode;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称", example = "张三")
    private String rtName;

    @Schema(description = "是否虚拟房;1是 0否")
    private String isVirtual;

    @Schema(description = "是否集团房型;1是 0否")
    private String isGRt;

    @Schema(description = "是否有效;1是 0否")
    private String isEnable;

}