package info.qizhi.aflower.module.pms.api.account.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 交班报表 Response DTO")
@Data
public class HandoverReportRespDTO {

    @Schema(description = "酒店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hname;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

    @Schema(description = "所有账务付款科目集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HandoverReportAccountRespVO> paymentTypeAccounts;

    @Schema(description = "所有账务付款科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentTypeTotalFee;

    @Schema(description = "所有账务现金收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long rmbPayTotalFee;

    @Schema(description = "所有账务消费科目集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HandoverReportAccountRespVO> consumptionTypeAccounts;

    @Schema(description = "所有账务消费科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionTypeTotalFee;

    @Schema(description = "付款科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> paymentDetails;

    @Schema(description = "消费科目明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<Detail> consumptionDetails;

    @Schema(description = "入账人收款明细集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RecorderDetail> recorderPaymentDetails;

    @Schema(description = "会员充值集合", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<HandoverReportAccountRespVO> memberRechargeAccounts;

    @Schema(description = "会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeTotalFee;

    @Schema(description = "门店会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long mMemberRechargeTotalFee;

    @Schema(description = "集团会员充值收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long gMemberRechargeTotalFee;

    @Schema(description = "会员充值账务现金收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeRmbPayTotalFee;

    @Data
    public static class Detail{

        @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subCode;

        @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subName;

        @Schema(description = "科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
        //@JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "账务集合", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<HandoverReportAccountRespVO> accounts;

    }

    @Data
    public static class RecorderDetail{

        @Schema(description = "入账人", requiredMode = Schema.RequiredMode.REQUIRED)
        private String recorder;

        @Schema(description = "入账人收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;

        @Schema(description = "入账人现金收款合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long memberRechargeRmbPayTotalFee;

        @Schema(description = "账务集合", requiredMode = Schema.RequiredMode.REQUIRED)
        private List<HandoverReportAccountRespVO> accounts;

    }
}
