package info.qizhi.aflower.module.pms.api.realbizkpi;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.realbizkpi.dto.*;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 17:07
*/
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 首页实时数据")
public interface RealBizKpiApi {

	String PREFIX = ApiConstants.PREFIX + "/pms/real-biz-kpi";

	@PostMapping(PREFIX + "/get")
	@Operation(summary = "获得协议单位、中介")
	CommonResult<RealBizKpiRespDTO> getRealBizKpi(@Valid @RequestBody RealTimeRoomStatusReqDTO reqVO);


	@PostMapping(PREFIX + "/get-classification")
	@Operation(summary = "实时经营数据分类")
	CommonResult<List<BusinessDataDTO>> getRealBizKpiClassification(@Valid @RequestBody RealTimeRoomStatusTwoReqDTO reqVO);


	@PostMapping(PREFIX + "/get-multiple-classification")
	@Operation(summary = "多个实时经营数据分类")
	CommonResult<List<BusinessDataDTO>> getMultipleRealBizKpiClassification(@Valid @RequestBody RealTimeRoomStatusTwoReq2DTO reqVO);
}
