package info.qizhi.aflower.module.pms.api.invoice.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/23 15:58
 */
@Data
public class InvoiceOrderSelfRespDTO {
    /**
     * 订单流水号，每张发票唯一
     */
    private String invoiceSerialNo;
    /**
     * 自助开票二维码地址
     */
    private String urlQrCode;
    /**
     * 二维码有效期时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate expiresDate;
}
