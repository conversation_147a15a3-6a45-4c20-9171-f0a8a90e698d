package info.qizhi.aflower.module.pms.api.booking.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 团队查询 Request VO")
@Data
@ToString(callSuper = true)
public class TeamReqDTO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "团队类型;请看数据字典", example = "2")
    private String teamType;

    @Schema(description = "状态;请看数据字典")
    private String state;

    @Schema(description = "状态列表;请看数据字典")
    private List<String> states;

    @Schema(description = "团队名称")
    private String teamName;

    @Schema(description = "团队代码列表")
    private List<String> teamCodes;

}