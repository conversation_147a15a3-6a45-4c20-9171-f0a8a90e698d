package info.qizhi.aflower.module.pms.api.customer.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 客历 Request DTO")
@Data
public class CustomerDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22512")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String name;

    @Schema(description = "性别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sex;

    @Schema(description = "证件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String idType;

    @Schema(description = "证件号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String idNo;

    @Schema(description = "出生日期")
    private LocalDate birthday;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "民族")
    private String nation;

    @Schema(description = "地址")
    private String address;

    @Schema(description = "黑名单;0正常 1拉黑", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isBlack;

    @Schema(description = "拉黑原因", example = "不香")
    private String blackReason;

    @Schema(description = "是否接收短信;0：不接收 1：接收", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isSms;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "入住次数", example = "3")
    private Integer checkinNum;

    @Schema(description = "是否会员;0: 否 1: 是")
    private String isMember;

    @Schema(description = "最后一次入住门店名称", example = "张三")
    private String lastMerchant;

}
