package info.qizhi.aflower.module.pms.api.protocolagent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 协议单位、中介 简单模型 Response VO")
@Data
public class ProtocolAgentSimpleRespDTO {

    @Schema(description = "中介/协议单位代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String paCode;

    @Schema(description = "中介/协议单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String paName;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channel;

    @Schema(description = "账套代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String arSetCode;

    @Schema(description = "是否系统初始;0:否 1:是")
    private String isSys;
}