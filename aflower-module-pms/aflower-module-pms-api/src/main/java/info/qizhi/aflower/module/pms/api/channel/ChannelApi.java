package info.qizhi.aflower.module.pms.api.channel;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelReqDTO;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelRespDTO;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelSaveReqDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 渠道")
public interface ChannelApi {

    String PREFIX = ApiConstants.PREFIX + "/channel";

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "批量创建渠道")
    CommonResult<Boolean> createChannels(@Valid @RequestBody List<ChannelSaveReqDTO> createReqDTOS);

    @GetMapping(PREFIX + "/channel-List")
    @Operation(summary = "获得渠道列表")
    CommonResult<List<ChannelRespDTO>> getChannelList(@Valid @SpringQueryMap  ChannelReqDTO reqDTO);
}
