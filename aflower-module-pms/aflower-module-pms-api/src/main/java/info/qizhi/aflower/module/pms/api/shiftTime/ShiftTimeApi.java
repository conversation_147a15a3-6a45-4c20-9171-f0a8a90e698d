package info.qizhi.aflower.module.pms.api.shiftTime;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.MerchantShiftReqDTO;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.MerchantShiftSimpleRespDTO;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.ShiftTimeReqDTO;
import info.qizhi.aflower.module.pms.api.shiftTime.dto.ShiftTimeRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 班次设置")
public interface ShiftTimeApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/shift-time";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得班次设置列表")
    CommonResult<List<ShiftTimeRespDTO>> getShiftTimeList(@Valid @SpringQueryMap ShiftTimeReqDTO reqVO);

    @GetMapping(PREFIX + "/list/multi-merchant")
    @Operation(summary = "获得多个门店班次设置列表(实收)")
    CommonResult<List<ShiftTimeRespDTO>> getMultiMerchantShiftTimeList(@RequestParam("gcode") String gcode, @RequestParam("state") String state);

    @PostMapping(PREFIX + "/delete-shift-cache")
    @Operation(summary = "删除班次缓存信息")
    CommonResult<Boolean> deleteShiftFromRedisCache();

    @GetMapping(PREFIX + "/shift-no")
    @Operation(summary = "获得当前操作员的班次号")
    CommonResult<String> getShiftNo(@RequestParam("hcode") String hcode, @RequestParam("userId") Long userId);

    @GetMapping(PREFIX + "/shift-time")
    @Operation(summary = "从缓存获取班次号")
    CommonResult<String> getShiftTimeByUserIdFromCache(@RequestParam("hcode") String hcode, @RequestParam("token") String token);

    @GetMapping(PREFIX + "/shift-time/cache")
    @Operation(summary = "从缓存获取班次号")
    CommonResult<String> getShiftTimeByFromCache(@RequestParam("hcode") String hcode);

    @PostMapping(PREFIX+ "/switch-merchant")
    @Operation(summary = "(切换门店选择班次，同时改变营业日)")
    CommonResult<MerchantShiftSimpleRespDTO> switchVisitMerchantAndShiftTime(@Valid @RequestBody MerchantShiftReqDTO reqVO);
}
