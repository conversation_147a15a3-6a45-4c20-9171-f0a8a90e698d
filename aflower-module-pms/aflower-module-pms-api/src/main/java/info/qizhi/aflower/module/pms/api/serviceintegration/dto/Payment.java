package info.qizhi.aflower.module.pms.api.serviceintegration.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 支付类型参数配置")
@Data
public class Payment implements ScenarioParamConfig{

    private List<ScenarioParameter> parameters;

    @Data
    public static class ScenarioParameter {

        @Schema(description = "参数名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String code;

        @Schema(description = "参数内容", requiredMode = Schema.RequiredMode.REQUIRED)
        private String label;
    }

}

