package info.qizhi.aflower.module.pms.api.serviceintegration.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 对接服务 Response VO")
@Data
public class ServiceIntegrationRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7143")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "方案服务商", requiredMode = Schema.RequiredMode.REQUIRED)
    private String solutionProvider;

    @Schema(description = "方案类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String solutionType;

    @Schema(description = "服务状态; 0: 禁用, 1: 启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer state;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startTime;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endTime;

    @Schema(description = "对接服务", requiredMode = Schema.RequiredMode.REQUIRED)
    private Payment scenario;

}