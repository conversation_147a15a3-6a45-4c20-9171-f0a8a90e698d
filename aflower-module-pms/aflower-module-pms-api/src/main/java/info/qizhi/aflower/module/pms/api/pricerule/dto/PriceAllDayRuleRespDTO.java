package info.qizhi.aflower.module.pms.api.pricerule.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 全天房计费规则 Response VO")
@Data
public class PriceAllDayRuleRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18010")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "最早入住时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE)
    private LocalTime earliestCheckinTime;

    @Schema(description = "入住N分钟后收起步费")
    private Integer startPriceNMin;

    @Schema(description = "入住N分钟后收全日租")
    private Integer allPriceNMin;

    @Schema(description = "预离超过N分钟后收费")
    private Integer overNMinCollect;

    @Schema(description = "预离超时N分钟后收全日租")
    private Integer overNMinAllDay;

    @Schema(description = "超时收费方式;0：按半日租收费  1：按每小时加收", requiredMode = Schema.RequiredMode.REQUIRED)
    private String overCollectStyle;

    @Schema(description = "超过多少分钟算1小时", requiredMode = Schema.RequiredMode.REQUIRED)
    private String howMinute;

    @Schema(description = "房型计费规则;json对象，属性：房型代码、起步价、每小时加收金额（散客、会员、中介、协议公司）、备注规则与房型是1：N关系", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RtFeeRule> rtFeeRule;

    @Schema(description = "说明", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}