package info.qizhi.aflower.module.pms.api.invoice.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/23 11:41
 */
@Data
public class InvoiceOrderReqDTO {
    /**
     * 集团代码
     */
    @NotBlank(message = "集团代码不能为空")
    private String gcode;
    /**
     * 酒店代码
     */
    @NotBlank(message = "酒店代码不能为空")
    private String hcode;
    /**
     * 发票类型代码
     * 81-数电专票
     * 82-数电普票
     */
    @NotBlank(message = "数电发票类型代码不能为空")
    private String digitalInvoiceType;

    /**
     * 开票详情集合
     */
    @Size(min = 1, message = "开票详情集合不能为空")
    private List<DetailInfo> detailInfoList;

    @Schema(description = "当前票关联的订单号集合")
    @NotEmpty(message = "当前票关联的订单号集合不能为空")
    private List<String> orderNos;

    /**
     * 回调地址
     */
    private String notifyUrl;

    @Data
    public static class DetailInfo {
        /**
         * 发票行性质
         * 0-正常行；1-折扣行；2-被折扣行
         * 开具折扣行发票时，第一行应为被折扣行商品，第二行为折扣行商品
         */
        @NotBlank(message = "发票行性质不能为空")
        private String lineNature;
        /**
         * 税收分类编码
         */
        @NotBlank(message = "税收分类编码不能为空")
        private String taxClassifyCode;
        /**
         * 金额（含税）
         */
        @NotBlank(message = "金额（含税）不能为空")
        private Long amount;
    }
}
