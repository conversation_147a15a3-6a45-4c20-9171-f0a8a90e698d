package info.qizhi.aflower.module.pms.api.report.vo.nightpriceprequalify;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 夜审房价预审报表明细 Response VO")
@Data
public class NightPricePrequalifyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "统计渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statChannel;

    @Schema(description = "统计渠道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statChannelName;

    @Schema(description = "客源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gSrc;

    @Schema(description = "客源名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gSrcName;

    @Schema(description = "入住类型", example = "2")
    private String inType;

    @Schema(description = "入住类型名称", example = "2")
    private String inTypeName;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime checkInTime;

    @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime planCheckOutTime;

    @Schema(description = "房价类型")
    private String priceType;

    @Schema(description = "房价类型名称")
    private String priceTypeName;

    @Schema(description = "门市价", requiredMode = Schema.RequiredMode.REQUIRED, example = "17263")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "本日房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayPrice;

    @Schema(description = "房价差异")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long priceDifference;

    @Schema(description = "操作员")
    private String operator;

}
