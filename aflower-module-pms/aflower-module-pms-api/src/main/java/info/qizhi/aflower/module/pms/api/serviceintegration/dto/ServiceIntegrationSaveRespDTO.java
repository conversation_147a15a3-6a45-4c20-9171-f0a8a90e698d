package info.qizhi.aflower.module.pms.api.serviceintegration.dto;

import info.qizhi.aflower.framework.common.enums.ServiceTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 对接服务 Response VO")
@Data
public class ServiceIntegrationSaveRespDTO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "方案服务商", requiredMode = Schema.RequiredMode.REQUIRED)
    private String solutionProvider;

    @Schema(description = "方案类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String solutionType;

    @Schema(description = "服务对接类型; ota: OTA对接服务, payment: 支付对接服务, invoice: 数电发票", requiredMode = Schema.RequiredMode.REQUIRED)
    @InStringEnum(value= ServiceTypeEnum.class)
    private String type;

    @Schema(description = "服务状态; 0: 禁用, 1: 启用", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "服务开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startTime;

    @Schema(description = "服务结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate  endTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "对接场景 配置是动态参数，所以使用 Map 接收", requiredMode = Schema.RequiredMode.REQUIRED)
    private Map<String, Object> scenario;

}