package info.qizhi.aflower.module.pms.api.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第三步 确认预授权列表 Request VO")
@Data
public class ConfirmPreAuthListReqDTO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "客单代码列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> togetherCodeList;


}