package info.qizhi.aflower.module.pms.api.booking.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import info.qizhi.aflower.framework.jackson.core.databind.CustomLocalDateTimeDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - ota新预定 Request DTO")
@Data
public class OtaBookOpenReqDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8352")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "平台")
    @InStringEnum(value = PlatformEnum.class, message = "{platform.instringenum}")
    private String platform;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "订单来源", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{orderSource.notempty}")
    @InStringEnum(value = OrderSrcEnum.class, message = "{orderSource.instringenum}")
    private String orderSource;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{guestSrcType.notempty}")
    @InStringEnum(value = GuestSrcTypeEnum.class, message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "预订类型;general:(个人)普通订单  group：团队订单", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{bookType.notempty}")
    @InStringEnum(value = OrderTypeEnum.class, message = "{bookType.instringenum}")
    private String bookType;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    private String checkinType;

    @Schema(description = "小时房代码;入住类型为时租房时，该字段有值")
    @InStringEnum(value = HourRoomEnum.class, message = "{hourCode.instringenum}")
    private String hourCode;

    @Schema(description = "团队代码")
    private String teamCode;

    @Schema(description = "客人代码;会员代码、协议单位、中介代码")
    private String guestCode;

    @Schema(description = "客人名称;会员姓名、协议单位、中介名称", example = "张三")
    @Size(max = 32, message = "{guestName.size}")
    private String guestName;

    @Schema(description = "团队名称;团队预订时输入", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @Size(max = 32, message = "{teamName.size}")
    private String teamName;

    @Schema(description = "合同号;团队预订时输入")
    @Size(max = 32, message = "{contractNo.size}")
    private String contractNo;

    @Schema(description = "预抵时间;普通预订存储，团队预订低离时间存储在预订房型中", requiredMode = Schema.RequiredMode.REQUIRED)
    //@JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckinTime;

    @Schema(description = "预离时间;普通预订存储，团队预订低离时间存储在预订房型中")
    //@JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "预订人(联系人);如果是团队预订，这里保存团队的联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{contact.notempty}")
    @Size(max = 32, message = "{contact.size}")
    private String contact;

    @Schema(description = "预订人电话;联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "入住人姓名")
    @Size(max = 32, message = "{checkinPerson.size}")
    private String checkinPerson;

    @Schema(description = "入住人电话")
    private String checkinPhone;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "保留时间")
    @JsonDeserialize(using = CustomLocalDateTimeDeserializer.class)
    private LocalDateTime retainTime;

    @Schema(description = "延迟退房分钟数")
    private Long delayMinute;

    @Schema(description = "是否发短信;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isSendSms.notempty}")
    @InStringEnum(value = BooleanEnum.class, message = "{isSendSms.instringenum}")
    private String isSendSms;

    @Schema(description = "市场活动代码")
    private String marketActivityCode;

    @Schema(description = "市场活动名称", example = "王五")
    private String marketActivityName;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "外部订单号")
    @Size(max = 32, message = "{outOrderNo.size}")
    private String outOrderNo;

    @Schema(description = "订单备注", example = "随便")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "外部订单备注", example = "随便")
    @Size(max = 2555, message = "{externalOrderRemark.size}")
    private String outOrderRemark;

    @Schema(description = "货币单位")
    private String currencyUnit;

    @Schema(description = "批次列表")
    @Valid
    @NotEmpty(message = "{batchList.notempty}")
    private List<Batch> batches;

    @Schema(description = "付款信息")
    private PayAccount payAccount;

    @Schema(description = "批次")
    @Data
    public static class Batch {

        @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{batchNo.notempty}")
        private String batchNo;

        @Schema(description = "天数", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{days.notnull}")
        private Integer days;

        @Schema(description = "预低时间", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{planCheckinTime.notnull}")
        //@JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
        @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
        private LocalDateTime planCheckinTime;

        @Schema(description = "预离时间", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{planCheckoutTime.notnull}")
        @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
        //@JsonDeserialize(using = LocalDateTimeMinuteDeserializer.class)
        private LocalDateTime planCheckoutTime;

        @Schema(description = "预订房型列表")
        @Valid
        @NotEmpty(message = "{bookRoomTypeList.notempty}")
        private List<BookRoomType> bookRoomTypes;

        @Schema(description = "预订房型")
        @Data
        public static class BookRoomType {
            @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotEmpty(message = "{roomTypeCode.notempty}")
            private String rtCode;

            @Schema(description = "赠早餐份数,前端提交赠早数", requiredMode = Schema.RequiredMode.REQUIRED)
            @NotNull(message = "{breakfastCount.notnull}")
            @Min(value = 0, message = "{breakfastCount.min}")
            private Integer bkNum;

            @Schema(description = "价格类型;0：放盘价 1：手工价", requiredMode = Schema.RequiredMode.REQUIRED)
            @InStringEnum(value = BooleanEnum.class, message = "{priceType.invalid}")
            private String priceType;

            @Schema(description = "预订房间数", requiredMode = Schema.RequiredMode.REQUIRED)
            @Min(value = 1, message = "{roomCount.min}")
            @NotNull(message = "{roomCount.notnull}")
            private Integer roomNum;

            @Schema(description = "是否会议室;0:客房 1:会议室", requiredMode = Schema.RequiredMode.REQUIRED)
            private String isMeetingRoom;

            @Schema(description = "排房列表")
            @Valid
            private List<BookRoom> bookRooms;

            @Schema(description = "每日价格")
            @Valid
            @NotEmpty(message = "{dailyPriceList.notempty}")
            private List<DayPrice> dayPrices;

            @Schema(description = "预订房间")
            @Data
            public static class BookRoom {

                @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{rCode.notempty}")
                @JsonProperty(value = "rCode")
                private String rCode;

                @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{rNo.notempty}")
                @JsonProperty(value = "rNo")
                private String rNo;

                @Schema(description = "选中的房间是不是已有预订单占用;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotEmpty(message = "{isPreOccupied.notempty}")
                private String preOccupied;

                @Schema(description = "状态,no_check_in、check_in、check_out", requiredMode = Schema.RequiredMode.REQUIRED)
                //@NotEmpty(message = "{status.notempty}")
                @InStringEnum(value = OrderStateEnum.class, message = "{state.invalid}")
                private String state;

            }

            @Data
            public static class DayPrice {

                @Schema(description = "日期", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotNull(message = "{date.notempty}")
                @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
                private LocalDate priceDate;

                @Schema(description = "周几", requiredMode = Schema.RequiredMode.REQUIRED)
                private Integer week;

                @Schema(description = "赠早餐份数", requiredMode = Schema.RequiredMode.REQUIRED)
                @Min(value = 0, message = "{breakfastCount.min}")
                private Integer bkNum;

                @Schema(description = "房包早餐数")
                private Integer roomBkNum;

                @Schema(description = "预订价格,单位元", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotNull(message = "{bookingPrice.notnull}")
                private Long price;

                @Schema(description = "优惠价,单位元", requiredMode = Schema.RequiredMode.REQUIRED)
                @NotNull(message = "{discountPrice.notnull}")
                private Long vipPrice;
// 暂时注释，因为自助机来的预订单不需要传这个
//                @Schema(description = "价格策略代码", requiredMode = Schema.RequiredMode.REQUIRED)
//                private String priceStrategyCode;
            }

        }
    }

    @Schema(description = "付款信息")
    @Data
    public static class PayAccount {
        @Schema(description = "宾客代码，订单入账时需要")
        private String togetherCode;

        @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单 cash:现付账订单", requiredMode = Schema.RequiredMode.REQUIRED)
        @InStringEnum(value = AccountTypeEnum.class, message = "{accType.instringenum}")
        @NotBlank(message = "{accType.notblank}")
        private String accType;

        @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{fee.notnull}")
        @Min(value = 1, message = "{fee.min}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "付款科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{subCode.notblank}")
        private String subCode;

        @Schema(description = "付款码;付款方式：支付宝码、微信码、预授权码、储值卡号、账套代码(AR账)")
        @Size(max = 32, message = "{payCode.size}")
        private String payCode;

        //----银行卡支付--->>>//
        @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
        private String bankType;

        @Schema(description = "银行卡号")
        @Size(max = 20, message = "{bankCardNo.size}")
        private String bankCardNo;
        //<<<----银行卡支付---//

        //----会员卡支付--->>>//
        @Schema(description = "会员代码，如果是储值卡支付，需要传会员代码")
        private String mcode;

        @Schema(description = "手机号，如果是储值卡支付,需要传会员手机号")
        private String phone;

        @Schema(description = "储值卡号")
        private String storeCardNo;

        @Schema(description = "会员卡密码,当付款方式为储值卡时需要")
        private String pwd;
        //<<<----会员卡支付---//

        @Schema(description = "业务详情")
        @Size(max = 128, message = "{accDetail.size}")
        private String accDetail;

        @Schema(description = "状态，与消费账务同时产生时，该付款科目状态为已结，前端可不传")
        private String state;

        @Schema(description = "结账号,前端不需要传，后端调用pay接口时使用")
        private String payNo;

        @Schema(description = "备注")
        @Size(max = 255, message = "{remark.size}")
        private String remark;
    }


}
