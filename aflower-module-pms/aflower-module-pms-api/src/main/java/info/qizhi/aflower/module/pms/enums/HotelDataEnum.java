package info.qizhi.aflower.module.pms.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/*
    <AUTHOR>
    @description 
    @create 2025 04 2025/4/17 15:58
*/
@AllArgsConstructor
@Getter
public enum HotelDataEnum {

	PUBLISHED(1,"上架"),
	UNPUBLISHED(2,"下架"),
	CHANGE(3, "变更"),
	WATCH(4, "添加观察"),
	NEGLECT(5, "取消观察");

	private final Integer changeType;

	private final String changeName;
}
