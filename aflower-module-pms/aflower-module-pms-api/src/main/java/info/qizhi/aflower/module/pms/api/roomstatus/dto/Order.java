package info.qizhi.aflower.module.pms.api.roomstatus.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import info.qizhi.aflower.framework.desensitize.core.slider.annotation.NameDesensitize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Data
public class Order {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "渠道代码")
    private String channelCode;

    @Schema(description = "订单来源")
    private String orderSource;

    @Schema(description = "入住类型")
    private String checkinType;

    @Schema(description = "入住时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime checkinTime;

    @Schema(description = "预离时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "客人姓名")
    // @NameDesensitize
    private String name;

    @Schema(description = "拼音")
    private String pinyin;

    @Schema(description = "电话")
    private String phone;

    @Schema(description = "性别")
    private String sex;

    @Schema(description = "客源类型代码")
    private String guestSrcType;

    @Schema(description = "担保方式")
    private String guarantyStyle;

    @Schema(description = "预订单号")
    private String bookNo;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "绑定单号")
    private String bindCode;

    @Schema(description = "是否主订单")
    private String isMain;

    @Schema(description = "订单状态")
    private String state;

//
//    @Schema(description = "欠费状态")
//    private String arrearState;
//
//    @Schema(description = "押金状态")
//    private String deposit;
//
//    @Schema(description = "唤醒服务时间")
//    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
//    private LocalDateTime awakenTime;
//
//    @Schema(description = "保密服务设置")
//    private String secrecy;
//
//    @Schema(description = "请勿打扰服务设置")
//    private String notDisturbing;

    @Schema(description = "房间备注")
    private String remark;
}
