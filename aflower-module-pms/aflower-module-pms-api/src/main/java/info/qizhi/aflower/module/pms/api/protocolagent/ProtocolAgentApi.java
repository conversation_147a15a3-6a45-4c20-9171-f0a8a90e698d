package info.qizhi.aflower.module.pms.api.protocolagent;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentRespDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 单位，中介信息")
public interface ProtocolAgentApi {
    String PREFIX = ApiConstants.PREFIX + "/pms/protocol-agent";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得协议单位、中介")
    @Parameter(name = "paCode", description = "编号", required = true, example = "1024")
    CommonResult<ProtocolAgentRespDTO>  getProtocolAgent(@RequestParam("paCode") String paCode);

    @PutMapping(PREFIX+"/update")
    @Operation(summary = "更新协议单位、中介")
    CommonResult<Boolean> updateProtocolAgent(@Valid @RequestBody ProtocolAgentRespDTO updateReqVO);

    @GetMapping(PREFIX+"/list")
    @Operation(summary = "获得协议单位、中介列表")
    CommonResult<List<ProtocolAgentRespDTO>> getProtocolAgentList(@Valid @SpringQueryMap ProtocolAgentReqDTO reqVO);

    @GetMapping(PREFIX+ "/get-simple")
    @Operation(summary = "获得协议单位、中介列表(只包括代码,名称和渠道代码)用于下拉列表及绑定渠道")
    CommonResult<ProtocolAgentSimpleRespDTO> getProtocolAgentSimple(@RequestParam("hcode") String hcode, @RequestParam("pdName") String pdName);
}
