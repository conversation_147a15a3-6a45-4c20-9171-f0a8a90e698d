package info.qizhi.aflower.module.pms.api.account.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.OrderTypeEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 退房结账 Request VO")
@Data
public class PayCheckOutAccountRepDTO {
    @Schema(description = "集团代码")
    @NotBlank(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotBlank(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单类型,general：普通订单 join：联房订单 group：团队订单")
    @InStringEnum(value = OrderTypeEnum.class)
    @NotBlank(message = "{orderType.notblank}")
    private String orderType;

    @Schema(description = "备注")
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "客人列表")
    @Valid
    @NotEmpty(message = "客人列表不能为空")
    private List<OrderTogether> orderTogethers;

    @Schema(description = "付款")
    @Valid
    @NotNull(message = "{payment.notnull}")
    private Pay pay;

    @Data
    public static class OrderTogether {

        @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{no.notempty}")
        private String no;

        @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotBlank(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "是否被选中为入账账号,0:否 1:是")
        @InStringEnum(value = BooleanEnum.class)
        private String isRecord;

        @Schema(description = "是否团队主账,0:否 1:是")
        @NotBlank(message = "{isTeam.notempty}")
        @InStringEnum(value = BooleanEnum.class)
        private String isTeam;

        @Schema(description = "是否主客单,0:否 1:是")
        @NotBlank(message = "{isMain.notempty}")
        @InStringEnum(value = BooleanEnum.class)
        private String isMain;
    }

    @Data
    public static class Pay {
        @Schema(description = "付款科目代码")
        @NotBlank(message = "{subCode.notblank}")
        private String subCode;

        @Schema(description = "付款方式,1:收款 -1:退款")
        @NotBlank(message = "{paymentMethod.notblank}")
        private String payMode;

        @Schema(description = "付款金额")
        @NotNull(message = "{paymentAmount.notnull}")
        @Min(value = 0L, message = "{paymentAmount.min}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;

        @Schema(description = "付款时的码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)")
        private String payCode;

        // 银行卡------
        @Schema(description = "银行类型;建设银行、招商银行....")
        private String bankType;

        @Schema(description = "银行卡号")
        private String bankCardNo;
        // ------银行卡


        // 会员支付--------
        @Schema(description = "会员代码,储值卡支付时需要")
        private String mcode;

        @Schema(description = "储值卡号")
        private String storeCardNo;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "会员卡密码")
        private String pwd;

        // --------会员支付

    }


}