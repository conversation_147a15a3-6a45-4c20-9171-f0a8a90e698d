package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Schema(description = "管理后台 - 房型 Request VO")
@Data
@ToString(callSuper = true)
public class RoomTypeOpenReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "房型代码列表")
    private List<String> rtCodes;

}