package info.qizhi.aflower.module.pms.api.shiftTime.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 切换门店和班次 Request VO")
@Data
@ToString(callSuper = true)
public class MerchantShiftReqDTO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "用户id", example = "1024")
    @NotNull(message = "{userId.notnull}")
    private Long userId;

    @Schema(description = "班次代码", example = "111111")
    private String shiftCode;

    @Schema(description = "访问令牌", example = "111111")
    private String accessToken;


}