package info.qizhi.aflower.module.pms.api.pricerule.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "房型计费规则 小时房计费规则 房型、小时数、客源类型、价格")
@Data
public class HourRtFeeRule {

    @Schema(description = "小时房代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "小时房代码")
    private String hourCode;

    @Schema(description = "小时房名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hourName;

    @Schema(description = "规则")
    private List<RtFeeRule> hourRules;

}
