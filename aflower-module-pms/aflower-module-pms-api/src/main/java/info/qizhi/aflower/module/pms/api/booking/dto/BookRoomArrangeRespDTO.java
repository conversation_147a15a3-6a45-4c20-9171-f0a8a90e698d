package info.qizhi.aflower.module.pms.api.booking.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订排房 Response VO")
@Data
public class BookRoomArrangeRespDTO {

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房态", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "state")
    private String state;

    @Schema(description = "锁号")
    private String lockNo;

    @Schema(description = "门锁mac地址")
    private String mac;
}