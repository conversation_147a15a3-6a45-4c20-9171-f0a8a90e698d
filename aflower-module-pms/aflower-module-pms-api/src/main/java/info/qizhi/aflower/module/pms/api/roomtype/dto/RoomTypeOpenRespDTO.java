package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: TY
 * @CreateTime: 2024-07-28
 * @Description: 房型Response DTO
 * @Version: 1.0
 */
@Data
public class RoomTypeOpenRespDTO {
    @Schema(description = "物理房型代码")
    private String rtCode;

    @Schema(description = "物理房型名称")
    private String rtName;

    @Schema(description = "面积")
    private BigDecimal area;

    @Schema(description = "房间数量")
    private Long roomNum;

    @Schema(description = "超预订数量")
    private List<OverBook> overBooks;

    @Schema(description = "床型")
    private List<BedType> bedTypes;

    @Schema(description = "状态")
    private String status;

    @Data
    public static class BedType {
        @Schema(description = "床型代码")
        private String bedTypeCode;

        @Schema(description = "床型名称")
        private String bedTypeName;

        @Schema(description = "尺寸名称")
        private String sizeName;

        @Schema(description = "床数")
        private Integer num;
    }

    @Data
    public static class OverBook {
        @Schema(description = "超预订数量")
        private Integer overBookNum;

        @Schema(description = "渠道代码")
        private String channelCode;
    }

}
