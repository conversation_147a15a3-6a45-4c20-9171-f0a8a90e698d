package info.qizhi.aflower.module.pms.api.booking;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.booking.dto.TeamReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.TeamRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 团队")
public interface TeamApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/team";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得团队列表(根据状态可以获取不同状态团队)")
    CommonResult<List<TeamRespDTO>> getTeamList(@Valid TeamReqDTO reqVO);

}
