package info.qizhi.aflower.module.pms.api.pricerule;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.pricerule.dto.PriceAllDayRuleRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 全天房计费规则")
public interface PriceAllDayRuleApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/price-all-day-rule";


    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得全天房计费规则")
    CommonResult<PriceAllDayRuleRespDTO> getPriceAllDayRule(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode);


}