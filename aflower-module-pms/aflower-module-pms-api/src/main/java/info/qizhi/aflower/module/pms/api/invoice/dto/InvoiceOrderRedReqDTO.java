package info.qizhi.aflower.module.pms.api.invoice.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/24 13:33
 */
@Data
public class InvoiceOrderRedReqDTO {
    /**
     * 集团代码
     */
    @NotBlank(message = "集团代码不能为空")
    private String gcode;
    /**
     * 酒店代码
     */
    @NotBlank(message = "酒店代码不能为空")
    private String hcode;
    /**
     * 红冲原因代码
     * 01-开票有误；02-销货退回；03-服务中止；04-销售折让
     */
    @NotBlank(message = "红冲原因不能为空")
    private String reasonType;
    /**
     * 被冲蓝字发票号
     */
    @NotBlank(message = "被冲蓝字发票号不能为空")
    private String blueInvoiceNo;
}
