package info.qizhi.aflower.module.pms.api.booking.dto;

import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.HourRoomEnum;
import info.qizhi.aflower.framework.common.enums.OrderSrcEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 散客预订 Request VO")
@Data
@ToString(callSuper = true)
public class BookReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "预订单号(当修改占房、修改预订操作时需要携带预订单号)")
    private String bookNo;

    @Schema(description = "渠道代码", example ="lobby")
    @NotEmpty(message = "{channelCode.notempty}")
    private String channelCode;

    @Schema(description = "入住类型， 全天房：all_day， 时租房:hour_room, 长包：long_stay, 免费：free", example ="all_day")
    private String checkinType;

    @Schema(description = "时租房代码， 1小时：1hour", example ="1hour")
    @InStringEnum(value = HourRoomEnum.class, message = "{hourCode.instringenum}")
    private String hourCode;

    @Schema(description = "客源类型, 散客：walk_in, 会员：member， 中介：agent，协议单位： protocol", example ="walk_in")
    @NotEmpty(message = "{guestSrcType.notempty}")
    @InStringEnum(value = GuestSrcTypeEnum.class, message = "{guestSrcType.instringenum}")
    private String guestSrcType;

    @Schema(description = "客源代码, 客源为散客时为空，为会员时当前为会员代码，为单位时为单位代码，为中介时为中介代码")
    private String guestCode;

    @Schema(description = "延迟退房分钟数,从房价策略中获取", example ="0")
    @NotNull(message = "{delayMinute.notnull}")
    private Long delayMinute;

    @Schema(description = "会员类型代码,当客源为会员时，需要知道该会员的会员类型代码")
    private String mtCode;

    @Schema(description = "订单来源", example ="agent")
    @NotEmpty(message = "{orderSource.notempty}")
    @InStringEnum(value = OrderSrcEnum.class, message = "{orderSource.instringenum}")
    private String orderSource;

    @Schema(description = "预订数")
    private Integer roomNum;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "计划入住时间", example ="2001-12-12 12:00")
    @NotNull(message = "{planCheckinTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckinTime;

    @Schema(description = "计划离店时间", example ="2001-12-13 12:00")
    @NotNull(message = "{planCheckoutTime.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "外部订单号")
    private String outOrderNo;
}
