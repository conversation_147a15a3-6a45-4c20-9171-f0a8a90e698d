package info.qizhi.aflower.module.pms.api.invoice.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/24 11:20
 */
@Data
public class InvoiceOrderBlueReqDTO extends InvoiceOrderReqDTO {
    /**
     * 自然人标识
     * Y-是；N-否
     */
    @NotBlank(message = "自然人标识不能为空")
    private String naturalPersonFlag;
    /**
     * 购方名称
     */
    private String buyerName;
    /**
     * 购方税号
     */
    private String buyerTaxpayerId;
    /**
     * 购方地址
     */
    private String buyerAddress;
    /**
     * 购方电话
     */
    private String buyerPhone;
    /**
     * 购方开户行
     */
    private String buyerBankName;
    /**
     * 购方开户行账号
     */
    private String buyerBankNo;

}
