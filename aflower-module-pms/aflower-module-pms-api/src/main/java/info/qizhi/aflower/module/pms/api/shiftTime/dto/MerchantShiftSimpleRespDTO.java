package info.qizhi.aflower.module.pms.api.shiftTime.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 切换门店时选择班次和门店的营业日 Response VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MerchantShiftSimpleRespDTO {

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "门店名称", example = "一朵花酒店")
    private String hname;

    @Schema(description = "班次代码")
    private String shiftCode;

    @Schema(description = "班次名称", example = "早班")
    private String shiftName;
    
    @Schema(description = "营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

}