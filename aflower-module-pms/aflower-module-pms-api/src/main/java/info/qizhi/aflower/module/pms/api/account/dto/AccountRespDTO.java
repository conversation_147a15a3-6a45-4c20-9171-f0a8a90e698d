package info.qizhi.aflower.module.pms.api.account.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 账务新增/修改 Request VO")
@Data
public class AccountRespDTO {


    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "宾客代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String togetherCode;

    @Schema(description = "预订单号或订单号,预订时产生的账务存储预订单号，入住后产生的账务存储订单号	", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accType;

    @Schema(description = "账单号;系统生成唯一标识，每笔账都生成一个", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accNo;

    @Schema(description = "房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号;当该账务为团队主账时，该字段名为团队主账,否则为房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客人姓名;当该账务为团队主账时，该字段名为团队主账", example = "芋艿")
    private String guestName;

    @Schema(description = "客源类型;散客 会员 单位 中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String guestSrcType;

    @Schema(description = "金额;账务的金额,如果是冲调就为负数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fee;

    @Schema(description = "消费科目代码;消费科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目类型;0: 消费科目 1：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String subType;

    @Schema(description = "业务详情")
    private String accDetail;

    @Schema(description = "是否隐藏;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isHide;

    @Schema(description = "入账班次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String shiftNo;

    @Schema(description = "入账人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String recorder;

    @Schema(description = "入账营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate bizDate;

    @Schema(description = "状态;0未结 1已结 2 已冲 3转出", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "是否可以冲调;0否 1是 夜审后的账务不能冲 已结账务不能冲 转出账务不能冲", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isCanRev;

    @Schema(description = "是否为冲调产生的账务;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isRev;

    @Schema(description = "冲调账号;当该条账务被冲调时，需要把冲调账务的账号记录")
    private String revAccNo;

    @Schema(description = "是否为转出转入的账务;-1: 为转出的账务, 0:默认， 1:为转入账务", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isTurnOutIn;

    @Schema(description = "转出账号;当该条账务为转入账时，需要记录转出的账务号")
    private String turnOutAccNo;

    @Schema(description = "是否为拆了的帐;0否 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isSplit;

    @Schema(description = "拆账后原账号;当该条为拆账账务时，需要记录被拆的账务号")
    private String originalSplitAccNo;

    @Schema(description = "结账号;标识同一次结账的账务")
    private String payNo;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "结账营业日")
    private LocalDateTime payBizDate;

    @Schema(description = "结账时间")
    private LocalDateTime payTime;

    @Schema(description = "结账人")
    private String payer;

    @Schema(description = "计费周期开始")
    private LocalDateTime priceStartTime;

    @Schema(description = "计费周期结束")
    private LocalDateTime priceEndTime;

    @Schema(description = "间夜数;只有房账才有间夜数")
    private BigDecimal nightNum;

    @Schema(description = "有效日期;当支付方式为预授权时有效")
    private LocalDate validDate;

    @Schema(description = "码;付款时的码：支付宝码、微信码、预授权码、储值卡号、账套代码(挂账时)")
    private String payCode;

    @Schema(description = "银行类型;建设银行、招商银行....", example = "1")
    private String bankType;

    @Schema(description = "银行卡号")
    private String bankCardNo;

    @Schema(description = "挂账到哪里;agent:中介、protocol:单位 replace_pay:代付 group:团队/旅行社", example = "1")
    private String creditTargetType;

    @Schema(description = "针对哪条账务进行退款;针对哪条账务进行退款（微信支付宝退款时有效）或：是哪笔公付出账（团队公付时有效）")
    private String refundAccNo;

    @Schema(description = "外部订单号;第三方支付时有效")
    private String outOrderNo;

    @Schema(description = "是否为订金转押金产生的账;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isConvertDeposit;

    @Schema(description = "是否已核销;0:否 1：是；当银行卡、支票付款的账需要财务去查账并做核销操作")
    private String isVerify;

    @Schema(description = "数据更新所属班次", requiredMode = Schema.RequiredMode.REQUIRED)
    private String handleShiftNo;

    @Schema(description = "状态;0:已撤销  1:未撤销", requiredMode = Schema.RequiredMode.REQUIRED)
    private String notRevoked;

    @Schema(description = "税后金额")
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long afterTaxFee;

    @Schema(description = "货币单位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currencyUnit;

}
