package info.qizhi.aflower.module.pms.enums;

import info.qizhi.aflower.framework.common.exception.ErrorCode;

/**
 * <AUTHOR>
 */
public interface ErrorCodeConstants {
    // ========== 常量  ==========
    ErrorCode CONSTANTS_NOT_EXISTS = new ErrorCode(1009000000, "CONSTANTS_NOT_EXISTS");
    // ========== 楼栋楼层  ==========
    ErrorCode BUILD_FLOOR_NOT_EXISTS = new ErrorCode(1009001001, "BUILD_FLOOR_NOT_EXISTS");
    ErrorCode BUILD_FLOOR_EXITS_CHILDREN = new ErrorCode(1009001002, "BUILD_FLOOR_EXITS_CHILDREN");
    ErrorCode BUILD_FLOOR_HAS_ROOM = new ErrorCode(1009011001, "BUILD_FLOOR_HAS_ROOM");
    ErrorCode BUILD_FLOOR_PARENT_NOT_EXITS = new ErrorCode(1009001003, "BUILD_FLOOR_PARENT_NOT_EXITS");
    ErrorCode BUILD_FLOOR_PARENT_ERROR = new ErrorCode(1009001004, "BUILD_FLOOR_PARENT_ERROR");
    ErrorCode BUILD_FLOOR_NAME_DUPLICATE = new ErrorCode(1009001005, "BUILD_FLOOR_NAME_DUPLICATE");
    ErrorCode BUILD_FLOOR_PARENT_IS_CHILD = new ErrorCode(1009001006, "BUILD_FLOOR_PARENT_IS_CHILD");
    // ========== 房间  ==========
    ErrorCode ROOM_NOT_EXISTS = new ErrorCode(1009001007, "ROOM_NOT_EXISTS");
    ErrorCode ROOM_IS_OCCUPIED = new ErrorCode(1009011008, "ROOM_IS_OCCUPIED");
    ErrorCode ROOM_NO_REPEAT = new ErrorCode(1009001008, "ROOM_NO_REPEAT");
    ErrorCode ROOM_LOG_NOT_EXISTS = new ErrorCode(1009001009, "ROOM_LOG_NOT_EXISTS");
    ErrorCode ROOM_IS_BOOKED = new ErrorCode(1009001010, "ROOM_IS_BOOKED");
    ErrorCode ROOM_IS_INVALID = new ErrorCode(1009001011, "ROOM_IS_INVALID");
    ErrorCode ROOM_IS_INVALID2 = new ErrorCode(1009001029, "ROOM_IS_INVALID2");
    ErrorCode IDNO_USER_EXISTS = new ErrorCode(1009001012, "IDNO_USER_EXISTS");
    ErrorCode CUSTOMER_IS_BLACK = new ErrorCode(1009001013, "CUSTOMER_IS_BLACK");
    ErrorCode DUPLICATION_USER_EXISTS = new ErrorCode(1009001014, "DUPLICATION_USER_EXISTS");
    ErrorCode ROOM_STATE_CHANGE_ERROR = new ErrorCode(1009001015, "ROOM_STATE_CHANGE_ERROR");
    ErrorCode ROOM_STOP_ERROR = new ErrorCode(1009001016, "ROOM_STOP_ERROR");
    ErrorCode ROOM_REPAIR_TIME_ERROR = new ErrorCode(1009001017, "ROOM_REPAIR_TIME_ERROR");
    ErrorCode ROOM_REPAIR_REASON_ERROR = new ErrorCode(1009001018, "ROOM_REPAIR_REASON_ERROR");
    ErrorCode ROOM_REPAIR_TIME_ERROR2 = new ErrorCode(1009001019, "ROOM_REPAIR_TIME_ERROR2");
    ErrorCode ROOM_REPAIR_TIME_ERROR3 = new ErrorCode(1009001020, "ROOM_REPAIR_TIME_ERROR3");
    ErrorCode ROOM_STATE_ALREADY_DIRTY = new ErrorCode(1009001021, "ROOM_STATE_ALREADY_DIRTY");
    ErrorCode ROOM_LOCKED_ERROR = new ErrorCode(1009001022, "ROOM_LOCKED_ERROR");
    ErrorCode ROOM_STATE_ALREADY_CLEAN = new ErrorCode(1009001023, "ROOM_STATE_ALREADY_CLEAN");
    ErrorCode ROOM_LOCK_NO_EMPTY = new ErrorCode(1009001025, "ROOM_LOCK_NO_EMPTY");
    ErrorCode ID_NO_IS_EMPTY = new ErrorCode(1009001025, "ID_NO_IS_EMPTY");
    ErrorCode ROOM_TYPE_CHANGE_ERROR = new ErrorCode(1009001026, "ROOM_TYPE_CHANGE_ERROR");
    ErrorCode ROOM_TYPE_IS_OCCUPIED = new ErrorCode(1009001027, "ROOM_TYPE_IS_OCCUPIED");
    ErrorCode ROOM_CLEAN_LOG_ALREADY_CLEAN_ERROR = new ErrorCode(1009001028, "ROOM_CLEAN_LOG_ALREADY_CLEAN_ERROR");
    ErrorCode ROOM_TYPE_NOT_MATCH = new ErrorCode(1009001029, "ROOM_TYPE_NOT_MATCH");
    // 维修时间段与订单时间段存在冲突，不允许置维修
    ErrorCode ROOM_REPAIR_OVERLAP_ERROR = new ErrorCode(1009001024, "ROOM_REPAIR_OVERLAP_ERROR");
    ErrorCode ROOM_ALREADY_CLEAN_ERROR = new ErrorCode(1009001025, "ROOM_ALREADY_CLEAN_ERROR");
    // ========== 房扫记录 ==========
    ErrorCode ROOM_CLEAN_LOG_NOT_EXISTS = new ErrorCode(1009003001, "ROOM_CLEAN_LOG_NOT_EXISTS");
    // ========== 房型  ==========
    ErrorCode ROOM_TYPE_NOT_EXISTS = new ErrorCode(1009002001, "ROOM_TYPE_NOT_EXISTS");
    ErrorCode ROOM_TYPE_HAS_ROOM = new ErrorCode(1009002002, "ROOM_TYPE_HAS_ROOM");
    ErrorCode ROOM_TYPE_VIRTUAL_RELE_EXISTS = new ErrorCode(1009002003, "ROOM_TYPE_VIRTUAL_RELE_EXISTS");
    ErrorCode ROOM_TYPE_NAME_EXISTS = new ErrorCode(1009002004, "ROOM_TYPE_NAME_EXISTS");
    ErrorCode ROOM_TYPE_BASE_PRICE_NOT_EXISTS = new ErrorCode(1009002005, "ROOM_TYPE_BASE_PRICE_NOT_EXISTS");
    ErrorCode ROOM_TYPE_CODE_NOT_NULL = new ErrorCode(1009002006, "ROOM_TYPE_CODE_NOT_NULL");
    ErrorCode ROOM_TYPE_IS_REFERENCE = new ErrorCode(1009002007, "ROOM_TYPE_IS_REFERENCE");
    ErrorCode OTA_ROOM_TYPE_REF_NOT_FOUND = new ErrorCode(1009002018, "OTA_ROOM_TYPE_REF_NOT_FOUND");
    // ========== 物理房型与虚拟房型关联  ==========
    ErrorCode ROOM_TYPE_VIRTUAL_RELE_NOT_EXISTS = new ErrorCode(1009002008, "ROOM_TYPE_VIRTUAL_RELE_NOT_EXISTS");

    // ========== 时租房房型  ==========
    ErrorCode HOUR_ROOM_TYPE_NOT_EXISTS = new ErrorCode(1009012003, "HOUR_ROOM_TYPE_NOT_EXISTS");
    ErrorCode HOUR_ROOM_TYPE_EXISTS = new ErrorCode(1009012004, "HOUR_ROOM_TYPE_EXISTS");

    // ========== 班次设置  ==========
    ErrorCode SHIFT_TIME_NOT_EXISTS = new ErrorCode(1009004001, "SHIFT_TIME_NOT_EXISTS");
    ErrorCode SHIFT_TIME_CLOSE_ERROR = new ErrorCode(1009004003, "SHIFT_TIME_CLOSE_ERROR");
    ErrorCode BIZ_DATE_NOT_EXISTS = new ErrorCode(1009004004, "BIZ_DATE_NOT_EXISTS");
    ErrorCode SHIFT_TIME_NOT_EXISTS2 = new ErrorCode(1009004005, "SHIFT_TIME_NOT_EXISTS2");
    ErrorCode SHIFT_TIME_NOT_EXISTS3 = new ErrorCode(1009004006, "SHIFT_TIME_NOT_EXISTS3");
    ErrorCode SHIFT_TIME_NOT_EXISTS4 = new ErrorCode(1009004007, "SHIFT_TIME_NOT_EXISTS4");
    ErrorCode SHIFT_TIME_LOST = new ErrorCode(1009004008, "SHIFT_TIME_LOST");
    ErrorCode SHIFT_TIME_NOT_CONTINUOUS = new ErrorCode(1009004009, "SHIFT_TIME_NOT_CONTINUOUS");
    ErrorCode SHIFT_TIME_OVERLAP = new ErrorCode(1009004010, "SHIFT_TIME_OVERLAP");
    ErrorCode SHIFT_TIME_NOT_NULL = new ErrorCode(1009004011, "SHIFT_TIME_NOT_NULL");
    // ========== 服务项目  ==========
    ErrorCode SERVICE_ITEMS_NOT_EXISTS = new ErrorCode(1009005001, "SERVICE_ITEMS_NOT_EXISTS");

    // ========== 夜审房间数设置  ==========
    ErrorCode ROOM_NIGHTS_NUM_SET_NOT_EXISTS = new ErrorCode(1009006001, "ROOM_NIGHTS_NUM_SET_NOT_EXISTS");
    // ========== 夜审设置  ==========
    ErrorCode NIGHT_AUDI_SET_NOT_EXISTS = new ErrorCode(1009006002, "NIGHT_AUDI_SET_NOT_EXISTS");
    ErrorCode NIGHT_AUDI_ERROR_NOW_IS_NIGHT_AUDI_DAY = new ErrorCode(1009006003, "NIGHT_AUDI_ERROR_NOW_IS_NIGHT_AUDI_DAY");
    ErrorCode NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR = new ErrorCode(1009006004, "NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR");
    ErrorCode NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR2 = new ErrorCode(1009006005, "NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR2");
    ErrorCode NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR3 = new ErrorCode(1009006006, "NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR3");
    ErrorCode NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR1 = new ErrorCode(1009006007, "NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR1");
    ErrorCode NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR4 = new ErrorCode(1009006008, "NIGHT_AUDI_ERROR_NIGHT_AUDI_SET_ERROR4");
    // ========== 物品分类  ==========
    ErrorCode THING_CLASS_NOT_EXISTS = new ErrorCode(1009007001, "THING_CLASS_NOT_EXISTS");
    ErrorCode THING_CLASS_REFERENCE_RETAIL_GOODS = new ErrorCode(1009017002, "THING_CLASS_REFERENCE_RETAIL_GOODS");
    ErrorCode THING_CLASS_REFERENCE_INDEM_GOODS = new ErrorCode(1009017003, "THING_CLASS_REFERENCE_INDEM_GOODS");
    ErrorCode THING_CLASS_NOT_ALLOW_DELETE = new ErrorCode(1009017004, "THING_CLASS_NOT_ALLOW_DELETE");
    // ========== 零售商品  ==========
    ErrorCode RETAIL_GOODS_NOT_EXISTS = new ErrorCode(1009007002, "RETAIL_GOODS_NOT_EXISTS");
    // ========== 租借物品  ==========
    ErrorCode RENT_GOODS_NOT_EXISTS = new ErrorCode(1009007003, "RENT_GOODS_NOT_EXISTS");
    // ========== 赔偿物品  ==========
    ErrorCode INDEMNITY_GOODS_NOT_EXISTS = new ErrorCode(1009007004, "INDEMNITY_GOODS_NOT_EXISTS");
    // ========== 用户参数设置  ==========
    ErrorCode USER_PARAM_CONFIG_NOT_EXISTS = new ErrorCode(1009008001, "USER_PARAM_CONFIG_NOT_EXISTS");
    // ========== 集团参数设置  ==========
    ErrorCode GROUP_PARAM_CONFIG_NOT_EXISTS = new ErrorCode(1009008002, "GROUP_PARAM_CONFIG_NOT_EXISTS");

    // ========== 集团全局配置  ==========
    ErrorCode GROUP_GLOBAL_CONFIG_NOT_EXISTS = new ErrorCode(1009008003, "GROUP_GLOBAL_CONFIG_NOT_EXISTS");
    // ========== 门店参数设置  ==========
    ErrorCode HOTEL_PARAM_CONFIG_NOT_EXISTS = new ErrorCode(1009008004, "HOTEL_PARAM_CONFIG_NOT_EXISTS");
    // ========== 通用配置  ==========
    ErrorCode GENERAL_CONFIG_NOT_EXISTS = new ErrorCode(1009008005, "GENERAL_CONFIG_NOT_EXISTS");

    // ========== 集团门店税率配置 ==========
    ErrorCode TAX_CONFIG_NOT_EXISTS = new ErrorCode(1009008006, "TAX_CONFIG_NOT_EXISTS");
    ErrorCode TAX_CONFIG_EXISTS = new ErrorCode(1009008007, "TAX_CONFIG_EXISTS");
    // ========== 酒店门市价  ==========
    ErrorCode PRICE_BASE_NOT_EXISTS = new ErrorCode(1009009001, "PRICE_BASE_NOT_EXISTS");
    // ========== 房价日历(放盘价)  ==========
    ErrorCode PRICE_CALENDAR_NOT_EXISTS = new ErrorCode(1009009002, "PRICE_CALENDAR_NOT_EXISTS");
    ErrorCode PRICE_CALENDAR_START_DATE_NULL = new ErrorCode(1009009013, "PRICE_CALENDAR_START_DATE_NULL");
    ErrorCode PRICE_CALENDAR_START_DATE_END_DATE_INVALID = new ErrorCode(1009009014, "PRICE_CALENDAR_START_DATE_END_DATE_INVALID");
    ErrorCode PRICE_CALENDAR_START_DATE_BEFORE_NOW = new ErrorCode(1009009015, "PRICE_CALENDAR_START_DATE_BEFORE_NOW");
    ErrorCode PRICE_CALENDAR_RT_CODE_AND_PRICE_NULL = new ErrorCode(1009009016, "PRICE_CALENDAR_RT_CODE_AND_PRICE_NULL");
    ErrorCode PRICE_CALENDAR_PRICE_NEGATIVE = new ErrorCode(1009009017, "PRICE_CALENDAR_PRICE_NEGATIVE");
    ErrorCode PRICE_CALENDAR_START_DATE_TOO_SHORT = new ErrorCode(1009009018, "PRICE_CALENDAR_START_DATE_TOO_SHORT");
    ErrorCode PRICE_CALENDAR_PRICE_NOT_SET = new ErrorCode(1009009019, "PRICE_CALENDAR_PRICE_NOT_SET");
    ErrorCode PRICE_CALENDAR_START_DATE_TOO_LONG = new ErrorCode(1009009020, "PRICE_CALENDAR_START_DATE_TOO_LONG");
    ErrorCode PRICE_CALENDAR_PRICE_TOO_LOW = new ErrorCode(1009009021, "PRICE_CALENDAR_PRICE_TOO_LOW");
    // ========== 房价日历修改日志  ==========
    ErrorCode PRICE_CALENDAR_LOG_NOT_EXISTS = new ErrorCode(1009009003, "PRICE_CALENDAR_LOG_NOT_EXISTS");
    // ========== 超卖日历  ==========
    ErrorCode OVERSELL_CALENDAR_NOT_EXISTS = new ErrorCode(1009009004, "OVERSELL_CALENDAR_NOT_EXISTS");
    // ========== 控房日历  ==========
    ErrorCode CONTROL_ROOM_CALENDAR_NOT_EXISTS = new ErrorCode(1009009005, "CONTROL_ROOM_CALENDAR_NOT_EXISTS");
    // ========== 浮动房价  ==========
    ErrorCode PRICE_FLOAT_NOT_EXISTS = new ErrorCode(1009009006, "PRICE_FLOAT_NOT_EXISTS");
    // ========== 房价策略  ==========
    ErrorCode PRICE_STRATEGY_NOT_EXISTS = new ErrorCode(1009009007, "PRICE_STRATEGY_NOT_EXISTS");
    ErrorCode PRICE_STRATEGY_DATE_ERROR = new ErrorCode(1009019007, "PRICE_STRATEGY_DATE_ERROR");
    ErrorCode GUEST_SRC_TYPE_NOT_NULL = new ErrorCode(1009019009, "GUEST_SRC_TYPE_NOT_NULL");

    // ========== 房价策略适用门店  ==========
    ErrorCode PRICE_STRATEGY_MERCHANT_NOT_EXISTS = new ErrorCode(1009009008, "PRICE_STRATEGY_MERCHANT_NOT_EXISTS");
    // ========== 渠道  ==========
    ErrorCode CHANNEL_NOT_EXISTS = new ErrorCode(1009010001, "CHANNEL_NOT_EXISTS");
    // ========== 日历  ==========
    ErrorCode CALENDAR_NOT_EXISTS = new ErrorCode(1009011001, "CALENDAR_NOT_EXISTS");
    // ========== 全天房计费规则  ==========
    ErrorCode PRICE_ALL_DAY_RULE_NOT_EXISTS = new ErrorCode(1009012001, "PRICE_ALL_DAY_RULE_NOT_EXISTS");
    // ========== 时租房计费规则  ==========
    ErrorCode PRICE_HOUR_RULE_NOT_EXISTS = new ErrorCode(1009012002, "PRICE_HOUR_RULE_NOT_EXISTS");
    // ========== 特殊房(白天房午夜房)计费规则  ==========
    ErrorCode PRICE_SPECIAL_RULE_NOT_EXISTS = new ErrorCode(1009012003, "PRICE_SPECIAL_RULE_NOT_EXISTS");
    // ========== 集团价格审批  ==========
    ErrorCode PRICE_APPROVAL_NOT_EXISTS = new ErrorCode(1009012004, "PRICE_APPROVAL_NOT_EXISTS");

    // ========== 协议单位、中介 ==========
    ErrorCode PROTOCOL_AGENT_NOT_EXISTS = new ErrorCode(1010002001, "PROTOCOL_AGENT_NOT_EXISTS");
    ErrorCode PROTOCOL_AGENT_CREATE_DATE_ERROR = new ErrorCode(1010002002, "PROTOCOL_AGENT_CREATE_DATE_ERROR");
    ErrorCode PROTOCOL_AGENT_CREATE_DATE_ERROR2 = new ErrorCode(1010002003, "PROTOCOL_AGENT_CREATE_DATE_ERROR2");
    ErrorCode PROTOCOL_AGENT_CREATE_DATE_ERROR3 = new ErrorCode(1010002004, "PROTOCOL_AGENT_CREATE_DATE_ERROR3");
    ErrorCode PROTOCOL_AGENT_BUILTIN_ERROR = new ErrorCode(1010002005, "PROTOCOL_AGENT_BUILTIN_ERROR");
    // ========== 协议单位、中介 客户名称已存在 ==========
    ErrorCode PROTOCOL_PANEM_EXISTS = new ErrorCode(**********, "PROTOCOL_PANEM_EXISTS");

    // ========== 佣金策略 ==========
    ErrorCode BROKERAGE_STRATEGY_NOT_EXISTS = new ErrorCode(**********, "BROKERAGE_STRATEGY_NOT_EXISTS");

    // ========== 客历 ==========
    ErrorCode CUSTOMER_NOT_EXISTS = new ErrorCode(**********, "CUSTOMER_NOT_EXISTS");

    // ========== 账务不能为空 ==========
    ErrorCode ACCOUNT_NOT_NULL = new ErrorCode(**********, "ACCOUNT_NOT_NULL");
    // ========== 账务 ==========
    ErrorCode ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "ACCOUNT_NOT_EXISTS");
    ErrorCode GOODS_DETAILS_NOT_EXISTS = new ErrorCode(**********, "GOODS_DETAILS_NOT_EXISTS");
    ErrorCode GOODS_DETAILS_TOTAL_PRICE_NOT_EQUAL_FEE = new ErrorCode(**********, "GOODS_DETAILS_TOTAL_PRICE_NOT_EQUAL_FEE");
    ErrorCode CONSUME_FEE_NOT_EQUAL_PAY_FEE = new ErrorCode(**********, "CONSUME_FEE_NOT_EQUAL_PAY_FEE");
    ErrorCode ACCOUNT_SEPARATE_ERROR = new ErrorCode(**********, "ACCOUNT_SEPARATE_ERROR");
    ErrorCode ACCOUNT_SEPARATE_ERROR_NO_PAY = new ErrorCode(**********, "ACCOUNT_SEPARATE_ERROR_NO_PAY");
    ErrorCode ACCOUNT_SEPARATE_ERROR_VERIFY = new ErrorCode(**********, "ACCOUNT_SEPARATE_ERROR_VERIFY");
    ErrorCode ACCOUNT_SEPARATE_ERROR_PAY = new ErrorCode(**********, "ACCOUNT_SEPARATE_ERROR_PAY");
    ErrorCode ACCOUNT_SEPARATE_ERROR_MEMBER_CARD = new ErrorCode(**********, "ACCOUNT_SEPARATE_ERROR_MEMBER_CARD");
    ErrorCode ACCOUNT_TRANSFER_ERROR_ACCOUNT = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_ACCOUNT");
    ErrorCode ACCOUNT_TRANSFER_ERROR_ACCOUNT2 = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_ACCOUNT2");
    ErrorCode ACCOUNT_TRANSFER_ERROR_ACCOUNT3 = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_ACCOUNT3");
    ErrorCode ACCOUNT_TRANSFER_ERROR_TEAM_STATE = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_TEAM_STATE");
    ErrorCode ACCOUNT_TRANSFER_ERROR_TOGETHER_STATE = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_TOGETHER_STATE");
    ErrorCode ACCOUNT_TRANSFER_ERROR_ACCOUNT4 = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_ACCOUNT4");
    ErrorCode ACCOUNT_TRANSFER_ERROR_ACCOUNT5 = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_ACCOUNT5");
    ErrorCode ACCOUNT_TRANSFER_ERROR_RNO = new ErrorCode(**********, "ACCOUNT_TRANSFER_ERROR_RNO");
    ErrorCode ROOM_NOT_EXIST = new ErrorCode(**********, "ROOM_NOT_EXIST");
    ErrorCode ACCOUNT_RED_ERROR_ACCOUNT = new ErrorCode(**********, "ACCOUNT_RED_ERROR_ACCOUNT");
    ErrorCode ACCOUNT_RED_ERROR_BIZ_DATE = new ErrorCode(**********, "ACCOUNT_RED_ERROR_BIZ_DATE");
    ErrorCode ACCOUNT_RED_ERROR_ACCOUNT2 = new ErrorCode(**********, "ACCOUNT_RED_ERROR_ACCOUNT2");
    ErrorCode ACCOUNT_RED_ERROR_BK_FEE = new ErrorCode(**********, "ACCOUNT_RED_ERROR_BK_FEE");
    ErrorCode ACCOUNT_TYPE_NOT_EXISTS = new ErrorCode(**********, "ACCOUNT_TYPE_NOT_EXISTS");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT2 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT2");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT3 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT3");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT4 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT4");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT5 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT5");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT6 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT6");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT7 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT7");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT8 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT8");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT9 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT9");
    ErrorCode ACCOUNT_NOT_UNCLOSED = new ErrorCode(**********, "ACCOUNT_NOT_UNCLOSED");
    ErrorCode ACCOUNT_NOT_PRE_AUTH = new ErrorCode(**********, "ACCOUNT_NOT_PRE_AUTH");
    ErrorCode ACCOUNT_PRE_AUTH_FEE_ERROR = new ErrorCode(**********, "ACCOUNT_PRE_AUTH_FEE_ERROR");
    ErrorCode ACCOUNT_PRE_AUTH_TYPE_ERROR = new ErrorCode(**********, "ACCOUNT_PRE_AUTH_TYPE_ERROR");
    ErrorCode ACCOUNT_NOT_UNCLOSED_EXISTS = new ErrorCode(**********, "ACCOUNT_NOT_UNCLOSED_EXISTS");
    ErrorCode ACCOUNT_NOT_CHECKED_EXISTS = new ErrorCode(**********, "ACCOUNT_NOT_CHECKED_EXISTS");
    ErrorCode ACCOUNT_PAY_CODE_NOT_ALLOW = new ErrorCode(**********, "ACCOUNT_PAY_CODE_NOT_ALLOW");
    ErrorCode ACCOUNT_PAY_CODE_NOT_NULL = new ErrorCode(**********, "ACCOUNT_PAY_CODE_NOT_NULL");
    ErrorCode ACCOUNT_TEAM_CLOSED = new ErrorCode(**********, "ACCOUNT_TEAM_CLOSED");
    ErrorCode ACCOUNT_ORDER_CHECK_OUT = new ErrorCode(**********, "ACCOUNT_ORDER_CHECK_OUT");
    ErrorCode ACCOUNT_TEAM_CLOSED2 = new ErrorCode(**********, "ACCOUNT_TEAM_CLOSED2");
    ErrorCode ACCOUNT_ORDER_CLOSED = new ErrorCode(**********, "ACCOUNT_ORDER_CLOSED");
    ErrorCode ACCOUNT_ORDER_CLOSED2 = new ErrorCode(**********, "ACCOUNT_ORDER_CLOSED2");
    ErrorCode ACCOUNT_ORDER_NOT_EXISTS = new ErrorCode(**********, "ACCOUNT_ORDER_NOT_EXISTS");
    ErrorCode BIZ_CREDIT_CHECK_OUT_ERROR = new ErrorCode(**********, "BIZ_CREDIT_CHECK_OUT_ERROR");
    ErrorCode ORDER_CREDIT_CHECK_OUT_ERROR = new ErrorCode(**********, "ORDER_CREDIT_CHECK_OUT_ERROR");
    ErrorCode ACCOUNT_NOT_CURRENT_SHIFT = new ErrorCode(**********, "ACCOUNT_NOT_CURRENT_SHIFT");
    ErrorCode ACCOUNT_PART_CLOSE_ERROR_ACCOUNT10 = new ErrorCode(**********, "ACCOUNT_PART_CLOSE_ERROR_ACCOUNT10");
    ErrorCode ACCOUNT_STATE_ERROR = new ErrorCode(**********, "ACCOUNT_STATE_ERROR");
    ErrorCode ORDER_BK_NUM_ERROR = new ErrorCode(**********, "ORDER_BK_NUM_ERROR");
    ErrorCode ORDER_STATUS_NOT_ALLOW_CONSUME = new ErrorCode(**********, "ORDER_STATUS_NOT_ALLOW_CONSUME");
    ErrorCode SCAN_GUN_PAY_CODE_NOT_EXISTS = new ErrorCode(**********, "SCAN_GUN_PAY_CODE_NOT_EXISTS");
    ErrorCode SCAN_GUN_PAY_CODE_ERROR = new ErrorCode(**********, "SCAN_GUN_PAY_CODE_ERROR");
    ErrorCode PAY_FAILURE = new ErrorCode(**********, "PAY_FAILURE");
    ErrorCode PRE_FINISH_ERROR = new ErrorCode(**********, "PRE_FINISH_ERROR");
    ErrorCode PRE_CANCEL_ERROR = new ErrorCode(**********, "PRE_CANCEL_ERROR");
    ErrorCode ACCOUNT_VERIFY_EXISTS = new ErrorCode(**********, "ACCOUNT_VERIFY_EXISTS");
    ErrorCode ACCOUNT_NOT_VERIFY = new ErrorCode(**********, "ACCOUNT_NOT_VERIFY");
    ErrorCode SCAN_GUN_PAY_ERROR = new ErrorCode(**********, "SCAN_GUN_PAY_ERROR");
    ErrorCode ACCOUNT_STATUS_ERROR = new ErrorCode(**********, "ACCOUNT_STATUS_ERROR");
    ErrorCode ACCOUNT_SUB_CODE_ERROR = new ErrorCode(**********, "ACCOUNT_SUB_CODE_ERROR");
    ErrorCode ACCOUNT_SUB_TYPE_ERROR = new ErrorCode(**********, "ACCOUNT_SUB_TYPE_ERROR");
    ErrorCode ACCOUNT_CREATE_TIME_ERROR = new ErrorCode(**********, "ACCOUNT_CREATE_TIME_ERROR");
    ErrorCode ACCOUNT_FEE_ERROR = new ErrorCode(**********, "ACCOUNT_FEE_ERROR");
    ErrorCode STORE_CARD_CONSUME_FAIL = new ErrorCode(**********, "STORE_CARD_CONSUME_FAIL");
    ErrorCode GENERALTE_FEE_TYPE_NOT_EXISTS = new ErrorCode(**********, "GENERALTE_FEE_TYPE_NOT_EXISTS");
    ErrorCode ACCOUNT_STATUS_NOT_UNCLOSED = new ErrorCode(**********, "ACCOUNT_STATUS_NOT_UNCLOSED");
    ErrorCode ACCOUNT_STATUS_NOT_UNCLOSED2 = new ErrorCode(**********, "ACCOUNT_STATUS_NOT_UNCLOSED2");
    ErrorCode ACCOUNT_FEE_NOT_EQUAL = new ErrorCode(**********, "ACCOUNT_FEE_NOT_EQUAL");
    ErrorCode ACCOUNT_CARD_NOT_EXISTS = new ErrorCode(**********, "ACCOUNT_CARD_NOT_EXISTS");
    ErrorCode MONEY_NOT_EQUAL = new ErrorCode(**********, "MONEY_NOT_EQUAL");
    ErrorCode PAY_TYPE_NOT_MATCH = new ErrorCode(**********, "PAY_TYPE_NOT_MATCH");
    ErrorCode MONEY_NOT_EQUAL2 = new ErrorCode(**********, "MONEY_NOT_EQUAL2");
    ErrorCode MONEY_NOT_NULL = new ErrorCode(**********, "MONEY_NOT_NULL");
    ErrorCode ACCOUNT_RED_ERROR_VERIFY = new ErrorCode(**********, "ACCOUNT_RED_ERROR_VERIFY");
    ErrorCode ACCOUNT_MERGE_NOT_SUPPORT = new ErrorCode(**********, "ACCOUNT_MERGE_NOT_SUPPORT");
    ErrorCode ACCOUNT_REFUND_ERROR_ACCOUNT = new ErrorCode(**********, "ACCOUNT_REFUND_ERROR_ACCOUNT");
    ErrorCode ACCOUNT_REFUND_ERROR_ACCOUNT2 = new ErrorCode(**********, "ACCOUNT_REFUND_ERROR_ACCOUNT2");
    ErrorCode ACCOUNT_REFUND_ERROR_ACCOUNT3 = new ErrorCode(**********, "ACCOUNT_REFUND_ERROR_ACCOUNT3");
    ErrorCode ACCOUNT_REFUND_NOT_RED = new ErrorCode(**********, "ACCOUNT_REFUND_NOT_RED");
    ErrorCode DEPOSIT_CONFIG_NOT_EXISTS = new ErrorCode(**********, "DEPOSIT_CONFIG_NOT_EXISTS");
    ErrorCode REFUND_AMOUNT_NEGATIVE = new ErrorCode(**********, "REFUND_AMOUNT_NEGATIVE");
    ErrorCode ACCOUNT_REFUND_AMOUNT_EQUAL_ZERO = new ErrorCode(**********, "ACCOUNT_REFUND_AMOUNT_EQUAL_ZERO");
    ErrorCode ACCOUNT_EXISTS = new ErrorCode(**********, "ACCOUNT_EXISTS");
    ErrorCode  PAYMENT_FAILED_INSERT_NOT_ALLOWED= new ErrorCode(**********, "PAYMENT_FAILED_INSERT_NOT_ALLOWED");
    ErrorCode QUERY_FAILURE = new ErrorCode(**********, "QUERY_FAILURE");
    ErrorCode  CONSUME_ACCOUNT_NOT_EQUAL= new ErrorCode(**********, "CONSUME_ACCOUNT_NOT_EQUAL");
    ErrorCode  ORDER_NOT_EXISTS_IN_DB= new ErrorCode(**********, "ORDER_NOT_EXISTS_IN_DB");
    ErrorCode  FAILURE_REASON= new ErrorCode(**********, "FAILURE_REASON");
    ErrorCode  RETURN_ACCOUNT_NOT_RED = new ErrorCode(**********, "RETURN_ACCOUNT_NOT_RED");
    ErrorCode  RETURN_ACCOUNT_NUM_ERROR = new ErrorCode(**********, "RETURN_ACCOUNT_NUM_ERROR");
    ErrorCode  COUPON_ACCOUNT_NOT_TRANSFER= new ErrorCode(**********, "COUPON_ACCOUNT_NOT_TRANSFER");
    // ========== 在住订单 ==========
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(**********, "ORDER_NOT_EXISTS");
    ErrorCode CHECKIN_DAYS_ERROR = new ErrorCode(**********, "CHECKIN_DAYS_ERROR");
    ErrorCode CHECKIN_TIME_ERROR = new ErrorCode(**********, "CHECKIN_TIME_ERROR");
    ErrorCode CHECKOUT_TIME_ERROR = new ErrorCode(**********, "CHECKOUT_TIME_ERROR");
    ErrorCode HOUR_ROOM_CODE_NOT_NULL = new ErrorCode(**********, "HOUR_ROOM_CODE_NOT_NULL");
    ErrorCode CHECKIN_TIME_NOT_IN_RULE = new ErrorCode(**********, "CHECKIN_TIME_NOT_IN_RULE");
    ErrorCode ORDER_IS_OUT_HOUSE = new ErrorCode(**********, "ORDER_IS_OUT_HOUSE");
    ErrorCode ORDER_TYPE_NOT_SUPPORT_EARLY_OUT = new ErrorCode(**********, "ORDER_TYPE_NOT_SUPPORT_EARLY_OUT");
    ErrorCode MERGE_ROOM_LAST_ROOM_OUT_HOUSE = new ErrorCode(**********, "MERGE_ROOM_LAST_ROOM_OUT_HOUSE");
    ErrorCode ROOM_NOT_CHECKIN = new ErrorCode(**********, "ROOM_NOT_CHECKIN");
    ErrorCode BREAKFAST_NOT_EXISTS = new ErrorCode(**********, "BREAKFAST_NOT_EXISTS");
    ErrorCode ORDER_AWAKEN_TIME_ERROR = new ErrorCode(1010006119, "ORDER_AWAKEN_TIME_ERROR");
    ErrorCode ORDER_AWAKEN_TIME_ERROR2 = new ErrorCode(1010006119, "ORDER_AWAKEN_TIME_ERROR2");
    ErrorCode NOTYPE_NO_SUPPORT = new ErrorCode(1010006120, "NOTYPE_NO_SUPPORT");
    ErrorCode RENT_LIST_NOT_DELETE = new ErrorCode(1010006121, "RENT_LIST_NOT_DELETE");
    ErrorCode RENT_LIST_RETURNED_NOT_ALLOW_COMPENSATION =  new ErrorCode(1010006122, "RENT_LIST_RETURNED_NOT_ALLOW_COMPENSATION");
    ErrorCode RENT_LIST_RETURNED_NOT_ALLOW_DEL = new ErrorCode(1010006123, "RENT_LIST_RETURNED_NOT_ALLOW_DEL");

    ErrorCode ORDER_NOT_CHANGE_ROOM_AFTER_OUT_HOUSE = new ErrorCode(1010006108, "ORDER_NOT_CHANGE_ROOM_AFTER_OUT_HOUSE");
    ErrorCode ROOM_IS_REPAIR2 = new ErrorCode(1010006110, "ROOM_IS_REPAIR2");
    ErrorCode ROOM_TYPE_NOT_SUPPORT_HOUR_ROOM = new ErrorCode(**********, "ROOM_TYPE_NOT_SUPPORT_HOUR_ROOM");
    ErrorCode ORDER_CONTINUE_IN_TIME_ERROR = new ErrorCode(**********, "ORDER_CONTINUE_IN_TIME_ERROR");
    ErrorCode ROOM_IS_CHECKIN = new ErrorCode(**********, "ROOM_IS_CHECKIN");
    ErrorCode ORDER_NOT_JOIN_ORDER = new ErrorCode(**********, "ORDER_NOT_JOIN_ORDER");
    ErrorCode ORDER_IS_GROUP_ORDER = new ErrorCode(**********, "ORDER_IS_GROUP_ORDER");
    ErrorCode ORDER_OCCUPIED = new ErrorCode(**********, "ORDER_OCCUPIED");
    ErrorCode ORDER_NOT_CHECKIN = new ErrorCode(**********, "ORDER_NOT_CHECKIN");
    ErrorCode ORDER_NOT_TOGETHER = new ErrorCode(**********, "ORDER_NOT_TOGETHER");
    ErrorCode ORDER_TYPE_NOT_EXISTS = new ErrorCode(**********, "ORDER_TYPE_NOT_EXISTS");
    ErrorCode ORDER_CHECKIN_TYPE_ERROR = new ErrorCode(**********, "ORDER_CHECKIN_TYPE_ERROR");
    ErrorCode ORDER_TEAM_MAIN_ACCOUNT_NOT_CLOSED = new ErrorCode(**********, "ORDER_TEAM_MAIN_ACCOUNT_NOT_CLOSED");
    ErrorCode ORDER_STATE_ERROR = new ErrorCode(**********, "ORDER_STATE_ERROR");
    ErrorCode ORDER_BIZ_DATE_ERROR = new ErrorCode(**********, "ORDER_BIZ_DATE_ERROR");
    ErrorCode ROOM_STATE_ERROR = new ErrorCode(**********, "ROOM_STATE_ERROR");
    ErrorCode ORDER_ROOM_IS_DISABLE = new ErrorCode(**********, "ORDER_ROOM_IS_DISABLE");
    ErrorCode NO_ORDER_PRICE = new ErrorCode(**********, "NO_ORDER_PRICE");
    ErrorCode STORECARD_PAY_ERROR = new ErrorCode(**********, "STORECARD_PAY_ERROR");
    ErrorCode ORDER_TOGETHER_NO_EXIST = new ErrorCode(**********, "ORDER_TOGETHER_NO_EXIST");
    ErrorCode CHECKED_ORDER_CHECKOUT = new ErrorCode(**********, "CHECKED_ORDER_CHECKOUT");
    ErrorCode ORDER_IS_NOT_CAN_QUIT_TEAM = new ErrorCode(**********, "ORDER_IS_NOT_CAN_QUIT_TEAM");
    ErrorCode IDNO_REQUIRED = new ErrorCode(**********, "IDNO_REQUIRED");
    ErrorCode GUEST_CODE_NULL = new ErrorCode(**********, "GUEST_CODE_NULL");
    ErrorCode GUEST_CODE_NULL2 = new ErrorCode(1010006039, "GUEST_CODE_NULL2");
    ErrorCode GUEST_CODE_NULL3 = new ErrorCode(1010006040, "GUEST_CODE_NULL3");
    ErrorCode HOUR_ROOM_TYPE_ERROR = new ErrorCode(1010006041, "HOUR_ROOM_TYPE_ERROR");
    ErrorCode HOUR_ROOM_TYPE_ERROR2 = new ErrorCode(1010006042, "HOUR_ROOM_TYPE_ERROR2");
    ErrorCode ROOM_IS_LOCKED = new ErrorCode(**********, "ROOM_IS_LOCKED");
    ErrorCode ORDER_PRICE_IS_NULL = new ErrorCode(**********, "ORDER_PRICE_IS_NULL");
    ErrorCode ORDER_CONTINUE_IN_PRICE_TYPE_ERROR = new ErrorCode(**********, "ORDER_CONTINUE_IN_PRICE_TYPE_ERROR");
    ErrorCode SYSTEM_ERROR = new ErrorCode(**********, "SYSTEM_ERROR");
    ErrorCode ORDER_CONTINUE_IN_TIME_ERROR2 = new ErrorCode(**********, "ORDER_CONTINUE_IN_TIME_ERROR2");
    ErrorCode BOOK_NOT_ACCOUNT = new ErrorCode(**********, "BOOK_NOT_ACCOUNT");
    ErrorCode SCAN_NOT_OPEN = new ErrorCode(**********, "SCAN_NOT_OPEN");
    ErrorCode STORE_CARD_NOT_OPEN = new ErrorCode(**********, "STORE_CARD_NOT_OPEN");
    ErrorCode IDNO_DATE_ERROR = new ErrorCode(**********, "IDNO_DATE_ERROR");
    ErrorCode ORDER_IS_AR_ACCOUNT = new ErrorCode(**********, "ORDER_IS_AR_ACCOUNT");
    ErrorCode  ORDER_NOT_UPDATE_CHECKIN_TYPE= new ErrorCode(**********, "ORDER_NOT_UPDATE_CHECKIN_TYPE");
    ErrorCode  COUPON_ONLY_SUPPORTED = new ErrorCode(**********, "COUPON_ONLY_SUPPORTED");
    ErrorCode ORDER_TOGETHER_LAST_ONE_CANNOT_REMOVE = new ErrorCode(**********, "ORDER_TOGETHER_LAST_ONE_CANNOT_REMOVE");
    ErrorCode  ONLY_BOOK_ORDER_CAN_REMOVE = new ErrorCode(**********, "ONLY_BOOK_ORDER_CAN_REMOVE");
    // ========== 订单日志 ==========
    ErrorCode ORDER_LOG_NOT_EXISTS = new ErrorCode(**********, "ORDER_LOG_NOT_EXISTS");
    // ========== 订单价格 ==========
    ErrorCode ORDER_PRICE_NOT_EXISTS = new ErrorCode(**********, "ORDER_PRICE_NOT_EXISTS");
    ErrorCode ORDER_PRICE_ROOM_LIST_EMPTY = new ErrorCode(**********, "ORDER_PRICE_ROOM_LIST_EMPTY");
    ErrorCode ORDER_PRICE_ORDER_LIST_EMPTY = new ErrorCode(**********, "ORDER_PRICE_ORDER_LIST_EMPTY");
    ErrorCode ORDER_TOGETHER_NOT_EXISTS = new ErrorCode(**********, "ORDER_TOGETHER_NOT_EXISTS");
    ErrorCode ORDER_IS_GROUP = new ErrorCode(**********, "ORDER_IS_GROUP");
    ErrorCode ORDER_IS_NOT_GROUP = new ErrorCode(**********, "ORDER_IS_NOT_GROUP");
    ErrorCode PRICE_NOT_EXISTS = new ErrorCode(**********, "PRICE_NOT_EXISTS");
    ErrorCode ROOM_ORDER_NOT_EXISTS = new ErrorCode(**********, "ROOM_ORDER_NOT_EXISTS");
    ErrorCode ROOM_ORDER_PLEASE_CONTINE_IN = new ErrorCode(**********, "ROOM_ORDER_PLEASE_CONTINE_IN");
    ErrorCode ORDER_NOT_JOIN = new ErrorCode(**********, "ORDER_NOT_JOIN");
    ErrorCode ORDER_IS_MAIN = new ErrorCode(**********, "ORDER_IS_MAIN");
    ErrorCode ORDER_TOGETHER_NOT_EXISTS2 = new ErrorCode(**********, "ORDER_TOGETHER_NOT_EXISTS2");
    // ========== 门店类型规则 ==========
    ErrorCode MERCHANT_TYPE_RULE_NOT_EXISTS = new ErrorCode(**********, "MERCHANT_TYPE_RULE_NOT_EXISTS");

    // ========== 现付账记录 ==========
    ErrorCode ACC_RECORD_NOT_EXISTS = new ErrorCode(**********, "ACC_RECORD_NOT_EXISTS");
    ErrorCode CASH_BILL_ORDER_NOT_ROOM_ACCOUNT = new ErrorCode(**********, "CASH_BILL_ORDER_NOT_ROOM_ACCOUNT");
    // ========== 现付账套 ==========
    ErrorCode ACC_SET_NOT_EXISTS = new ErrorCode(**********, "ACC_SET_NOT_EXISTS");
    ErrorCode ACCOUNT_CONSUME_ACCOUNT_NOT_EXISTS = new ErrorCode(**********, "ACCOUNT_CONSUME_ACCOUNT_NOT_EXISTS");
    ErrorCode ACCOUNT_CONSUME_ACCOUNT_REDEEMED = new ErrorCode(**********, "ACCOUNT_CONSUME_ACCOUNT_REDEEMED");
    // ========== 该科目现付账套已存在 ==========
    ErrorCode ACC_SET_EXISTS = new ErrorCode(**********, "ACC_SET_EXISTS");

    // ========== 应收账(账套) ==========
    // 应收账(账套)不存在
    ErrorCode AR_SET_NOT_EXISTS = new ErrorCode(**********, "AR_SET_NOT_EXISTS");
    // 应收账(账套)科目类型错误
    ErrorCode AR_SET_ACCOUNT_TYPE_ERROR = new ErrorCode(**********, "AR_SET_ACCOUNT_TYPE_ERROR");
    // 应收账(账套)操作类型错误
    ErrorCode AR_SET_HANDLE_TYPE_ERROR = new ErrorCode(**********, "AR_SET_HANDLE_TYPE_ERROR");
    // 应收账(账套)已停用，不允许操作
    ErrorCode AR_SET_INVALID = new ErrorCode(**********, "AR_SET_INVALID");
    // 当前{0}不在应收账(账套)的有效期内
    ErrorCode AR_SET_CREDIT_DATE_ERROR = new ErrorCode(**********, "AR_SET_CREDIT_DATE_ERROR");
    // {0} 不允许挂负账，如需挂负账，请修改应收账套设置
    ErrorCode AR_SET_CREDIT_QUOTA_ERROR = new ErrorCode(**********, "AR_SET_CREDIT_QUOTA_ERROR");
    // 挂账失败，挂账总金额已经超过了最大限额{0}
    ErrorCode AR_SET_CREDIT_MORE_QUOTA_ERROR = new ErrorCode(**********, "AR_SET_CREDIT_MORE_QUOTA_ERROR");
    // 挂账失败，账户余额不足，余额：{0}
    ErrorCode AR_SET_CREDIT_MORE_QUOTA_ERROR2 = new ErrorCode(**********, "AR_SET_CREDIT_MORE_QUOTA_ERROR2");
    // 该账务已被核销，无法冲调
    ErrorCode AR_SET_NOT_EQUAL = new ErrorCode(1051000008, "AR_SET_NOT_EQUAL");
    // 应收账(账套)名称已存在
    ErrorCode AR_SET_NAME_EXISTS = new ErrorCode(1051000009, "AR_SET_NAME_EXISTS");
    ErrorCode AR_SET_IS_BUILTIN = new ErrorCode(1051000010, "AR_SET_IS_BUILTIN");

    // ========== 现付账订单 ==========
    ErrorCode CASH_BILL_ORDER_NOT_EXISTS = new ErrorCode(1070000000, "CASH_BILL_ORDER_NOT_EXISTS");
    // ========== 现付账订单已被冲调 ==========
    ErrorCode CASH_BILL_ORDER_IS_REV = new ErrorCode(1071000000, "CASH_BILL_ORDER_IS_REV");
    // ========== 账务不可冲调 ==========
    ErrorCode CASH_BILL_ORDER_BIZ_DATE_NOT_EQUAL = new ErrorCode(1072000000, "CASH_BILL_ORDER_BIZ_DATE_NOT_EQUAL");
    // ========== 营业日记录 ==========
    ErrorCode BIZ_DAY_NOT_EXISTS = new ErrorCode(1090000000, "BIZ_DAY_NOT_EXISTS");
    // ========== 挂账起止日期不能为空 ==========
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_NULL = new ErrorCode(1100000000, "PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_NULL");
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_ERROR = new ErrorCode(1101000000, "PROTOCOL_AGENT_CREATE_CREDIT_END_DATE_ERROR");
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_START_DATE_ERROR = new ErrorCode(1102000000, "PROTOCOL_AGENT_CREATE_CREDIT_START_DATE_ERROR");
    // ========== 账期不能为空 ==========
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_PAY_FIX_NULL = new ErrorCode(1110000000, "PROTOCOL_AGENT_CREATE_CREDIT_PAY_FIX_NULL");
    // ========== 挂账设置不能为空 ==========
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_ZERO_NULL = new ErrorCode(1120000000, "PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_ZERO_NULL");

    // ========== 挂账账户类型不能为空 ==========
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_NULL = new ErrorCode(1121000000, "PROTOCOL_AGENT_CREATE_CREDIT_ACC_TYPE_NULL");

    // ========== 挂账额度不能为空 ==========
    ErrorCode PROTOCOL_AGENT_CREATE_CREDIT_QUOTA_NULL = new ErrorCode(1122000000, "PROTOCOL_AGENT_CREATE_CREDIT_QUOTA_NULL");

    // ========== 历史订单 ==========
    ErrorCode ORDER_HIS_NOT_EXISTS = new ErrorCode(1130000000, "ORDER_HIS_NOT_EXISTS");

    // ========== 历史订单价格  ==========
    ErrorCode ORDER_HIS_PRICE_NOT_EXISTS = new ErrorCode(1140000000, "ORDER_HIS_PRICE_NOT_EXISTS");

    // ========== 金额不能为负数  ==========
    ErrorCode THE_AMOUNT_CANNOT_BE_NEGATIVE = new ErrorCode(1*********, "THE_AMOUNT_CANNOT_BE_NEGATIVE");

    // ========== 账务类型有误  ==========
    ErrorCode ACC_TYPE_ERROR = new ErrorCode(1180000000, "ACC_TYPE_ERROR");

    // ========== 核销金额有误  ==========
    ErrorCode VERIFY_FEE_ERROR = new ErrorCode(1200000000, "VERIFY_FEE_ERROR");


    // ========== 更新失败 ==========
    ErrorCode UPDATE_FAILED = new ErrorCode(1121000000, "UPDATE_FAILED");

    // ========== 早餐设置不存在 ==========
    ErrorCode HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_EXISTS = new ErrorCode(1240000000, "HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_EXISTS");

    // ========== 该酒店不提供早餐 ==========
    ErrorCode HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_OPEN = new ErrorCode(1270000000, "HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_OPEN");

    // ========== {togetherCode.notempty} ==========
    ErrorCode TOGETHERCODE_NOT_NULL = new ErrorCode(1280000000, "TOGETHERCODE_NOT_NULL");
    ErrorCode TOGETHER_CODE_NOT_EXISTS = new ErrorCode(1280000001, "TOGETHER_CODE_NOT_EXISTS");
    ErrorCode MEMBER_GET_ERROR = new ErrorCode(1290000002, "MEMBER_GET_ERROR");
    // ========== 是否手动添加不能为空 ==========
    ErrorCode IS_MANUAL_NOT_NULL = new ErrorCode(1300000000, "IS_MANUAL_NOT_NULL");

    // ========== 代金卷{fee.notnull} ==========
    ErrorCode COUPON_MONEY_NOT_NULL = new ErrorCode(1320000000, "COUPON_MONEY_NOT_NULL");
    // ========== 折扣额度不能为空 ==========
    ErrorCode COUPON_REBATE_NOT_NULL = new ErrorCode(140000000, "COUPON_REBATE_NOT_NULL");

    // ========== 发票配置 ==========
    ErrorCode INVOICE_CONFIG_EXISTS = new ErrorCode(141000000, "INVOICE_CONFIG_EXISTS");
    ErrorCode INVOICE_CONFIG_NOT_EXISTS = new ErrorCode(141000001, "INVOICE_CONFIG_NOT_EXISTS");
    ErrorCode INVOICE_SERVICE_NOT_OPEN = new ErrorCode(141000002, "INVOICE_SERVICE_NOT_OPEN");
    ErrorCode INVOICE_NOT_FOUND = new ErrorCode(141000003, "INVOICE_NOT_FOUND");
    // ========== 开票信息 ==========
    ErrorCode INVOICE_NOT_EXISTS = new ErrorCode(143000000, "INVOICE_NOT_EXISTS");
    ErrorCode INVOICE_NOT_EXISTS2 = new ErrorCode(143000001, "INVOICE_NOT_EXISTS2");
    // ========== 开票记录  ==========
    ErrorCode INVOICE_LOG_NOT_EXISTS = new ErrorCode(144000000, "INVOICE_LOG_NOT_EXISTS");
    // ========== 寄存物品列  ==========
    ErrorCode DEPOSIT_LIST_NOT_EXISTS = new ErrorCode(142000000, "DEPOSIT_LIST_NOT_EXISTS");
    // ========== 商品销售记录 ==========
    ErrorCode GOODS_SELL_RECORD_NOT_EXISTS = new ErrorCode(143000000, "GOODS_SELL_RECORD_NOT_EXISTS");
    // ========== 遗留物品列  ==========
    ErrorCode LEAVE_LIST_NOT_EXISTS = new ErrorCode(144000000, "LEAVE_LIST_NOT_EXISTS");
    // ========== 租借物品列  ==========
    ErrorCode RENT_LIST_NOT_EXISTS = new ErrorCode(145000000, "RENT_LIST_NOT_EXISTS");
    // ========== 消费额度不足，不可使用该优惠卷  ==========
    ErrorCode COUPON_CONSUME_NOT_ENOUGH = new ErrorCode(146000000, "COUPON_CONSUME_NOT_ENOUGH");
    // ========== 该订单使用该优惠卷数以达到上限  ==========
    ErrorCode COUPON_NUM_NOT_ENOUGH = new ErrorCode(147000000, "COUPON_NUM_NOT_ENOUGH");

    // ========== 优惠劵已过期  ==========
    ErrorCode COUPON_EXPIRED = new ErrorCode(148000000, "COUPON_EXPIRED");
    // ========== 优惠劵使用券数超过上限  ==========
    ErrorCode COUPON_MULTIPLE_NOT_ALLOWED = new ErrorCode(149000000, "COUPON_MULTIPLE_NOT_ALLOWED");
    // ========== 入住类型不符优惠劵不可用  ==========
    ErrorCode CHECKINTYPE_COUPON_NOT_AVAILABLE = new ErrorCode(*********, "CHECKINTYPE_COUPON_NOT_AVAILABLE");

    // ========== 房型不符优惠劵不可用  ==========
    ErrorCode RTCODE_COUPON_NOT_AVAILABLE = new ErrorCode(*********, "RTCODE_COUPON_NOT_AVAILABLE");

    // ========== 单笔房费不能为空  ==========
    ErrorCode RFEE_NOT_NULL = new ErrorCode(154000000, "RFEE_NOT_NULL");

    // ========== 超预订配置 ==========
    ErrorCode OVER_BOOK_NOT_EXISTS = new ErrorCode(146000000, "OVER_BOOK_NOT_EXISTS");
    ErrorCode OVER_BOOK_EXISTS = new ErrorCode(147000000, "OVER_BOOK_EXISTS");
    // ========== 计费规则 ==========
    ErrorCode PRICE_CHARGE_RULE_NOT_EXISTS = new ErrorCode(148000000, "PRICE_CHARGE_RULE_NOT_EXISTS");
    ErrorCode BOOK_HOUR_CODE_REQUIRED = new ErrorCode(149000000, "BOOK_HOUR_CODE_REQUIRED");
    ErrorCode BOOK_GUEST_SRC_CODE_REQUIRED = new ErrorCode(*********, "BOOK_GUEST_SRC_CODE_REQUIRED");
    ErrorCode BOOK_OUT_ORDER_NO_REPEAT = new ErrorCode(151000001, "BOOK_OUT_ORDER_NO_REPEAT");
    // ========== 预订单(普通预订团队预订) ==========
    ErrorCode BOOK_NOT_EXISTS = new ErrorCode(149000000, "BOOK_NOT_EXISTS");
    ErrorCode BOOK_PLAN_CHECKIN_TIME_AFTER_PLAN_CHECKOUT_TIME = new ErrorCode(*********, "BOOK_PLAN_CHECKIN_TIME_AFTER_PLAN_CHECKOUT_TIME");
    ErrorCode BOOK_PLAN_CHECKOUT_TIME_BEFORE_NOW = new ErrorCode(*********, "BOOK_PLAN_CHECKOUT_TIME_BEFORE_NOW");
    ErrorCode BOOK_NO_CHECK_IN_ROOM = new ErrorCode(*********, "BOOK_NO_CHECK_IN_ROOM");
    ErrorCode ORDER_PLAN_CHECKOUT_TIME_BEFORE_PLAN_CHECKIN_TIME = new ErrorCode(*********, "ORDER_PLAN_CHECKOUT_TIME_BEFORE_PLAN_CHECKIN_TIME");
    ErrorCode BOOK_OVER_NOT_CHECKIN = new ErrorCode(*********, "BOOK_OVER_NOT_CHECKIN");
    ErrorCode BOOK_NO_ARRANGE_ROOM = new ErrorCode(*********, "BOOK_NO_ARRANGE_ROOM");
    ErrorCode BOOK_NO_CHECKIN_ROOM = new ErrorCode(*********, "BOOK_NO_CHECKIN_ROOM");
    ErrorCode BOOK_RETAIN_TIME_ERROR = new ErrorCode(*********, "BOOK_RETAIN_TIME_ERROR");
    ErrorCode BOOK_CHECKIN_TIME_NOT_IN_RULE = new ErrorCode(*********, "BOOK_CHECKIN_TIME_NOT_IN_RULE");
    ErrorCode BOOK_CHECKIN_TIME_NOT_IN_RULE2 = new ErrorCode(*********, "BOOK_CHECKIN_TIME_NOT_IN_RULE2");
    ErrorCode BOOK_STATUS_NOT_ALLOW_CONSUME = new ErrorCode(*********, "BOOK_STATUS_NOT_ALLOW_CONSUME");
    ErrorCode TEAM_STATUS_NOT_ALLOW_CONSUME = new ErrorCode(*********, "TEAM_STATUS_NOT_ALLOW_CONSUME");
    ErrorCode ACCOUNT_TYPE_NOT_SUPPORT = new ErrorCode(*********, "ACCOUNT_TYPE_NOT_SUPPORT");
    ErrorCode BOOK_BATCH_EMPTY = new ErrorCode(*********, "BOOK_BATCH_EMPTY");
    ErrorCode BOOK_ROOM_IN_USED = new ErrorCode(*********, "BOOK_ROOM_IN_USED");
    ErrorCode  BOOK_ACCOUNT_NOT_BALANCE= new ErrorCode(*********, "BOOK_ACCOUNT_NOT_BALANCE");
    ErrorCode  BOOK_ACCOUNT_PASSWORD_ERROR= new ErrorCode(*********, "BOOK_ACCOUNT_PASSWORD_ERROR");
    ErrorCode  BOOK_ORDER_EXISTS= new ErrorCode(*********, "BOOK_ORDER_EXISTS");
    ErrorCode  BOOK_ORDER_CANCEL_ERROR= new ErrorCode(*********, "BOOK_ORDER_CANCEL_ERROR");
    // ========== 预订排房  ==========
    ErrorCode BOOK_ROOM_NOT_EXISTS = new ErrorCode(*********, "BOOK_ROOM_NOT_EXISTS");
    ErrorCode BOOK_ROOM_STATE_ERROR = new ErrorCode(*********, "BOOK_ROOM_STATE_ERROR");
    ErrorCode BOOK_ROOM_STATE_NOT_ALLOW_DELETE = new ErrorCode(*********, "BOOK_ROOM_STATE_NOT_ALLOW_DELETE");
    ErrorCode BOOK_ROOM_PRICE_NOT_NULL = new ErrorCode(*********, "BOOK_ROOM_PRICE_NOT_NULL");
    ErrorCode BOOK_ROOM_PRICE_NOT_MATCH = new ErrorCode(*********, "BOOK_ROOM_PRICE_NOT_MATCH");
    ErrorCode BOOK_ROOM_PRICE_NOT_POSITIVE = new ErrorCode(*********, "BOOK_ROOM_PRICE_NOT_POSITIVE");
    ErrorCode BOOK_ROOM_DAY_PRICE_REQUIRED = new ErrorCode(*********, "BOOK_ROOM_DAY_PRICE_REQUIRED");
    ErrorCode BOOK_ROOM_NOT_ALLOW_ADD = new ErrorCode(*********, "BOOK_ROOM_NOT_ALLOW_ADD");
    ErrorCode BOOK_ROOM_LOCKED = new ErrorCode(*********, "BOOK_ROOM_LOCKED");
    ErrorCode BOOK_ROOM_REPAIR = new ErrorCode(*********, "BOOK_ROOM_REPAIR");
    ErrorCode BOOK_ROOM_DIRTY = new ErrorCode(*********, "BOOK_ROOM_DIRTY");
    ErrorCode BOOK_ROOM_BATCH_NOT_ALLOW_DELETE = new ErrorCode(*********, "BOOK_ROOM_BATCH_NOT_ALLOW_DELETE");
    // ========== 团队  ==========
    ErrorCode TEAM_NOT_EXISTS = new ErrorCode(*********, "TEAM_NOT_EXISTS");
    ErrorCode TEAM_IS_NOT_CHECK_IN = new ErrorCode(*********, "TEAM_IS_NOT_CHECK_IN");
    ErrorCode TEAM_ORDER_NOT_EXISTS = new ErrorCode(153000001, "TEAM_ORDER_NOT_EXISTS");
    // ========== 团队客人 ==========
    ErrorCode TEAM_GUEST_NOT_EXISTS = new ErrorCode(153000000, "TEAM_GUEST_NOT_EXISTS");
    ErrorCode BOOK_MEMBER_NOT_EXISTS = new ErrorCode(154000000, "BOOK_MEMBER_NOT_EXISTS");
    ErrorCode BOOK_PLAN_CHECKOUT_TIME_ERROR = new ErrorCode(156000000, "BOOK_PLAN_CHECKOUT_TIME_ERROR");
    ErrorCode BOOK_ROOM_TYPE_PRICE_NOT_EXISTS = new ErrorCode(157000000, "BOOK_ROOM_TYPE_PRICE_NOT_EXISTS");
    ErrorCode BOOK_ROOM_TYPE_PRICE_NOT_ENOUGH = new ErrorCode(157000001, "BOOK_ROOM_TYPE_PRICE_NOT_ENOUGH");
    ErrorCode BOOK_ROOM_IN_HOUSE_NOT_ALLOW_EDIT = new ErrorCode(159000000, "BOOK_ROOM_IN_HOUSE_NOT_ALLOW_EDIT");
    ErrorCode BOOK_ROOM_ARRANGE_CONFLICT = new ErrorCode(160000000, "BOOK_ROOM_ARRANGE_CONFLICT");
    ErrorCode BOOK_STATE_NOT_ALLOW_CANCEL = new ErrorCode(162000000, "BOOK_STATE_NOT_ALLOW_CANCEL");
    ErrorCode BOOK_STATE_NOT_ALLOW_CANCEL_GUARANTY = new ErrorCode(163000000, "BOOK_STATE_NOT_ALLOW_CANCEL_GUARANTY");
    ErrorCode BOOK_STATE_NOT_ALLOW_EDIT = new ErrorCode(164000000, "BOOK_STATE_NOT_ALLOW_EDIT");
    ErrorCode BOOK_PLAN_CHECKIN_TIME_NOT_IN_BOOK_TIME = new ErrorCode(166000000, "BOOK_PLAN_CHECKIN_TIME_NOT_IN_BOOK_TIME");
    ErrorCode BOOK_STATE_NOT_ALLOW_RECOVER = new ErrorCode(167000000, "BOOK_STATE_NOT_ALLOW_RECOVER");
    ErrorCode BOOK_PLAN_CHECKOUT_TIME_NOT_ALLOW_RECOVER = new ErrorCode(168000000, "BOOK_PLAN_CHECKOUT_TIME_NOT_ALLOW_RECOVER");
    ErrorCode BOOK_ROOM_BOOK_RT_NO_REQUIRED = new ErrorCode(169000000, "BOOK_ROOM_BOOK_RT_NO_REQUIRED");
    ErrorCode BOOK_ROOM_BOOK_NO_REQUIRED = new ErrorCode(170000000, "BOOK_ROOM_BOOK_NO_REQUIRED");
    ErrorCode BOOK_ROOM_GCODE_REQUIRED = new ErrorCode(171000000, "BOOK_ROOM_GCODE_REQUIRED");
    ErrorCode BOOK_ROOM_HCODE_REQUIRED = new ErrorCode(172000000, "BOOK_ROOM_HCODE_REQUIRED");
    ErrorCode BOOK_PLAN_CHECKOUT_TIME_ERRORX = new ErrorCode(173000000, "BOOK_PLAN_CHECKOUT_TIME_ERRORX");
    ErrorCode BOOK_ROOM_OCCUPIED = new ErrorCode(173000001, "BOOK_ROOM_OCCUPIED");
    ErrorCode BOOK_TEAM_NAME_EXISTS = new ErrorCode(174000000, "BOOK_TEAM_NAME_EXISTS");
    ErrorCode BOOK_ROOM_TEAM_CODE_REQUIRED = new ErrorCode(174000001, "BOOK_ROOM_TEAM_CODE_REQUIRED");
    ErrorCode BOOK_ROOM_LAST_ROOM_NOT_ALLOW_DELETE = new ErrorCode(174000002, "BOOK_ROOM_LAST_ROOM_NOT_ALLOW_DELETE");

    ErrorCode MERCHANT_NO_VISIT_ERROR = new ErrorCode(175000000, "MERCHANT_NO_VISIT_ERROR");
    ErrorCode PMS_SERVICE_EXPIRED = new ErrorCode(175000001, "PMS_SERVICE_EXPIRED");

    ErrorCode AR_SET_CODE_NOT_NULL = new ErrorCode(176000000, "AR_SET_CODE_NOT_NULL");

    ErrorCode LEVEL_LOG_NOT_EXIST = new ErrorCode(177000000, "LEVEL_LOG_NOT_EXIST");
    ErrorCode LEVEL_LOG_NOT_EQUAL = new ErrorCode(177000001, "LEVEL_LOG_NOT_EQUAL");
    ErrorCode HOTEL_LOCK_EXISTS = new ErrorCode(178000000, "HOTEL_LOCK_EXISTS");
    ErrorCode HOTEL_LOCK_NOT_EXISTS = new ErrorCode(178000003, "HOTEL_LOCK_NOT_EXISTS");
    ErrorCode ORDER_TYPE_ERROR = new ErrorCode(178000001, "ORDER_TYPE_ERROR");

    ErrorCode DEVICE_SET_NOT_EXISTS = new ErrorCode(180000000, "DEVICE_SET_NOT_EXISTS");
    ErrorCode DEVICE_SET_EXISTS= new ErrorCode(180000000, "DEVICE_SET_EXISTS");

    ErrorCode ORDER_NOT_IS_MAIN = new ErrorCode(181000000, "ORDER_NOT_IS_MAIN");

    ErrorCode DATE_ERROR = new ErrorCode(182000000, "DATE_ERROR");

    ErrorCode DATE_OVER_THREE_MONTHS = new ErrorCode(183000000, "DATE_OVER_THREE_MONTHS");

    ErrorCode ACCESS_NOT_EXISTS = new ErrorCode(184000000, "ACCESS_NOT_EXISTS");
    ErrorCode ORDER_COUNT_EXCEED_LIMIT = new ErrorCode(185000001, "ORDER_COUNT_EXCEED_LIMIT");
    ErrorCode INIT_MEMBER_BIZ_DATA_FAIL = new ErrorCode(186000000, "INIT_MEMBER_BIZ_DATA_FAIL");
    ErrorCode INIT_REPORT_BIZ_DATA_FAIL = new ErrorCode(186000000, "INIT_REPORT_BIZ_DATA_FAIL");
    ErrorCode INIT_ORDER_SYNC_BIZ_DATA_FAIL =  new ErrorCode(186000000, "INIT_ORDER_SYNC_BIZ_DATA_FAIL");
    // ========== 门店OTA关联 ==========
    ErrorCode MERCHANT_OTA_RELE_NOT_EXISTS = new ErrorCode(185000000, "MERCHANT_OTA_RELE_NOT_EXISTS");
    ErrorCode INIT_BIZ_DATA_FAIL = new ErrorCode(186000000, "INIT_BIZ_DATA_FAIL");
    ErrorCode INIT_BIZ_DATA_FAIL2 = new ErrorCode(186000001, "INIT_BIZ_DATA_FAIL2");

    // ========== 酒店房务配置 ==========
    ErrorCode HOUSEKEEPING_CONFIG_NOT_EXISTS = new ErrorCode(186000100, "HOUSEKEEPING_CONFIG_NOT_EXISTS");

    //==============短信=================
    ErrorCode SMS_TPL_NOT_ENABLE = new ErrorCode(191000001, "SMS_TPL_NOT_ENABLE");
    ErrorCode SMS_CODE_ERROR = new ErrorCode(191000002, "SMS_CODE_ERROR");

    //==============短信签名==============
    ErrorCode SMS_SIGN_NOT_ENABLE = new ErrorCode(192000001, "SMS_SIGN_NOT_ENABLE");

    // ========== OTA_api ==========
    ErrorCode OTA_API_NOT_EXISTS = new ErrorCode(192000010, "OTA_API_NOT_EXISTS");
    ErrorCode SERVICE_INTEGRATION_NOT_EXISTS = new ErrorCode(192000111, "OTA_API_NOT_ENABLE");
    ErrorCode SERVICE_INTEGRATION_EXISTS = new ErrorCode(192000112, "SERVICE_INTEGRATION_EXISTS");
    ErrorCode UNSUPPORTED_CHANNEL = new ErrorCode(192000113, "UNSUPPORTED_CHANNEL");
    ErrorCode GET_ROOM_TYPE_FAIL = new ErrorCode(192000114, "GET_ROOM_TYPE_FAIL");
    // ========== OTA房型关联表;ota房型可以关联多个酒店房型 ==========
    ErrorCode ROOM_TYPE_REF_NOT_EXISTS = new ErrorCode(192000011, "ROOM_TYPE_REF_NOT_EXISTS");

    ErrorCode REDIS_KEY_NULL = new ErrorCode(192000012, "REDIS_KEY_NULL");

    // =========== 储值卡 ====================
    ErrorCode MEMBER_PAY_ERROR = new ErrorCode(193000001, "MEMBER_PAY_ERROR");

    // ========== OTA酒店关联 ==========
    ErrorCode HOTEL_REF_NOT_EXISTS = new ErrorCode(193000101, "HOTEL_REF_NOT_EXISTS");
    ErrorCode HOTEL_REF_ALREADY_EXISTS = new ErrorCode(193000102, "HOTEL_REF_ALREADY_EXISTS");

    ErrorCode MERCHANT_NOT_FOUND = new ErrorCode(193000103, "MERCHANT_NOT_FOUND");

    ErrorCode DATE_MISSING = new ErrorCode(193000104, "DATE_MISSING");

    // ========== 任务 ==========
    ErrorCode TASK_NOT_EXISTS = new ErrorCode(194000101, "TASK_NOT_EXISTS");
    ErrorCode TASK_STATUS_NOT_CLEAN = new ErrorCode(194000102, "TASK_STATUS_NOT_CLEAN");
    ErrorCode TASK_STATUS_NOT_UPDATE = new ErrorCode(194000103, "TASK_STATUS_NOT_UPDATE");
    ErrorCode TASK_LOGIN_EXPIRE = new ErrorCode(194000104, "TASK_LOGIN_EXPIRE");
    ErrorCode TASK_STATUS_ERROR = new ErrorCode(194000104, "TASK_STATUS_ERROR");
    ErrorCode TASK_STATUS_NOT_START = new ErrorCode(194000105, "TASK_STATUS_NOT_START");
    ErrorCode TASK_STATUS_NOT_CLEANING = new ErrorCode(194000106, "TASK_STATUS_NOT_CLEANING");
    ErrorCode  TASK_STATUS_NOT_AUDIT= new ErrorCode(194000107, "TASK_STATUS_NOT_AUDIT");
    ErrorCode  TASK_STATUS_NOT_CLEANING_OR_CLEANING= new ErrorCode(194000108, "TASK_STATUS_NOT_CLEANING_OR_CLEANING");

    // ================ 周边酒店数据处理 =============
    ErrorCode HOTEL_DATA_COMBO_ERROR = new ErrorCode(194000101, "HOTEL_DATA_COMBO_ERROR");

    // ========== 房价牌不存在  ==========
    ErrorCode PRICE_PANEL_NOT_EXISTS = new ErrorCode(195000101, "PRICE_PANEL_NOT_EXISTS");
    ErrorCode PRICE_PANEL_BASE_NOT_EXISTS = new ErrorCode(195000102, "PRICE_PANEL_BASE_NOT_EXISTS");



    // ============  爬取酒店 =======================
    ErrorCode OTA_HOTEL_NOT_HAVE = new ErrorCode(196000101, "OTA_HOTEL_NOT_HAVE");
    ErrorCode OTA_CHANNEL_NOT_EXISTS = new ErrorCode(196000102, "OTA_CHANNEL_NOT_EXISTS");

    ErrorCode ROOM_CARD_LOG_NOT_EXISTS = new ErrorCode(197000101, "ROOM_CARD_LOG_NOT_EXISTS");
}
