package info.qizhi.aflower.module.pms.api.cashbillorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 现付账订单 Response DTO")
@Data
public class CashBillOrderSaveRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20244")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mcode;

    @Schema(description = "现付账订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cashBillOrderNo;

    @Schema(description = "付款方式")
    private String payMethod;

    @Schema(description = "付款金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long payFee;

    @Schema(description = "消费金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long buyFee;

    @Schema(description = "AR账账户")
    private String arSetCode;

    @Schema(description = "银行卡号")
    private String bankNo;

    @Schema(description = "银行代码")
    private String bankType;

    @Schema(description = "结账时间")
    private LocalDateTime payTime;

    @Schema(description = "结账交班号")
    private String payShiftNo;

    @Schema(description = "是否被冲调;0:否 1：是")
    private String isRev;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "现付账购买商品内容（存商品信息）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String buyContent;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "营业日期")
    private LocalDate bizDate;

    @Schema(description = "现付账套代码")
    private String accCode;

    @Schema(description = "会员储值卡号")
    private String storeCardNo;

}
