package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 房型的床型 DO
 *
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomTypeBedDTO {
    @Schema(description = "床型代码")
    private String bedTypeCode;

    @Schema(description = "床型名称")
    private String bedTypeName;

    @Schema(description = "尺寸代码")
    private String sizeCode;

    @Schema(description = "尺寸名称")
    private String sizeName;

    @Schema(description = "数量")
    private Integer num;

}