package info.qizhi.aflower.module.pms.api.account.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第三步 确认房费 Request VO")
@Data
public class ConfirmRoomFeeReqDTO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "生成房费方式(消费科目), add_half_day:加收半天 add_all_day:加收全天 0:不加收房费 room_fee:系统计费规则 hand_input_room_fee:手工房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{roomFeeSubCode.notempty}")
    private String subCode;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    @Size(max = 255, message = "{remark.size}")
    private String remark;

    @Schema(description = "宾客列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    @NotEmpty(message = "{orderTogethers.notempty}")
    private List<OrderTogether> orderTogethers;

    @Data
    public static class OrderTogether {

        @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{no.notempty}")
        private String no;

        @Schema(description = "宾客代码")
        @NotEmpty(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{rCode.notempty}")
        @JsonProperty(value = "rCode")
        private String rCode;

        @Schema(description = "房间号", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{rNo.notempty}")
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotNull(message = "{roomFee.notnull}")
        @Min(value = 0L, message = "{roomFee.min}")
        @JsonDeserialize(using = YuanToFenDeserializer.class)
        private Long fee;
    }

}