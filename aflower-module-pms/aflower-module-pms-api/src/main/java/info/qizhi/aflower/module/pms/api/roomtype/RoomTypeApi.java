package info.qizhi.aflower.module.pms.api.roomtype;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeOpenRespDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 房型信息")
public interface RoomTypeApi {
    String PREFIX = ApiConstants.PREFIX + "/pms/room-type";

    @GetMapping(PREFIX + "/get-list")
    @Operation(summary = "获取房型列表")
    CommonResult<List<RoomTypeRespDTO>> getRoomTypeList(@Valid @SpringQueryMap RoomTypeReqDTO reqDTO);

    @GetMapping(PREFIX + "list-room-num-include-grt")
    @Operation(summary = "获得房型列表,含房间数、门市价，包含集团房型")
    CommonResult<List<RoomTypeRespDTO>> getBasePriceRoomTypeList(@Valid @SpringQueryMap RoomTypeReqDTO reqDTO);

    @GetMapping(PREFIX + "/open/get-room-type")
    @Operation(summary = "hiii获取房型")
    CommonResult<List<RoomTypeOpenRespDTO>> getRoomType(@Valid @SpringQueryMap RoomTypeOpenReqDTO reqDTO);

    @GetMapping(PREFIX+"/list-physics")
    @Operation(summary = "获得物理房型列表")
    CommonResult<List<RoomTypeRespDTO>> getPhysicsRoomTypeList(@Valid @SpringQueryMap RoomTypeReqDTO reqVO);
}
