package info.qizhi.aflower.module.pms.api.roomtype;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.roomtype.dto.HourRoomTypeReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.HourRoomTypeRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 房型信息")
public interface HourRoomTypeApi {
    String PREFIX = ApiConstants.PREFIX + "/pms/hour-room-type";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得时租房房型列表")
    CommonResult<List<HourRoomTypeRespDTO>> getHourRoomTypeList(@Valid @SpringQueryMap HourRoomTypeReqDTO reqDTO);
}
