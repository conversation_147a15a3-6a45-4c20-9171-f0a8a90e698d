package info.qizhi.aflower.module.pms.api.roomstatus;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.*;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 房态信息")
public interface RoomStatusApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/room-status";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获取实时房型列表")
    CommonResult<List<RealTimeRoomStatusRespDTO>> getRealTimeRoomTypeList(@Valid @SpringQueryMap RealTimeRoomStatusReqDTO reqVO);

    @GetMapping(PREFIX + "/future-room-status")
    @Operation(summary = "远期房态")
    CommonResult<FutureRoomStatusRespDTO> getFutureRoomStatus(@Valid @SpringQueryMap FutureRoomStatusReqDTO reqVO);

    @PostMapping(PREFIX + "/predict-room")
    @Operation(summary = "房类预测")
    CommonResult<RoomPredictionRespDTO> predictRoomClass(@Valid @RequestBody RoomClassPredictionReqDTO reqVO);
}
