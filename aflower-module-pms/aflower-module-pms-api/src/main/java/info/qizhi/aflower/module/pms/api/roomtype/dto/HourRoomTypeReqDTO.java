package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 时租房房型 Request VO")
@Data
@ToString(callSuper = true)
public class HourRoomTypeReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "时租房代码")
    private String hourCode;

    @Schema(description = "房型代码")
    private String rtCode;

}