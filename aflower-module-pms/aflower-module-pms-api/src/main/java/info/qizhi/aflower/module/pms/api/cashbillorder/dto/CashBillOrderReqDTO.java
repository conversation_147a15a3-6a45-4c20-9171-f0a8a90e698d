package info.qizhi.aflower.module.pms.api.cashbillorder.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 现付账订单 Request DTO")
@Data
@ToString(callSuper = true)
public class CashBillOrderReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单号列表")
    private List<String> cashBillOrderNos;

    @Schema(description = "营业日期")
    private LocalDate bizDate;

}