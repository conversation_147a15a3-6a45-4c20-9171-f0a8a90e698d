package info.qizhi.aflower.module.pms.api.report;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.vo.ardetail.ArDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.ardetail.ArDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arentrydetail.ArEntryDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arentrydetail.ArEntryDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arverifydetail.ArVerifyDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arverifydetail.ArVerifyDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BreakfastPrepareReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BreakfastPrepareReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BuyBkOrReturnBkDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BuyBkOrReturnBkDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.goodsselldetail.GoodsSellDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.goodsselldetail.GoodsSellDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.nightpriceprequalify.NightPricePrequalifyReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.nightpriceprequalify.NightPricePrequalifyReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.settledetail.SettleDetailReportReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.settledetail.SettleDetailReportRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 报表")
public interface ReportDetailApi {
    String PREFIX = ApiConstants.PREFIX + "/pms/report";

    @GetMapping(PREFIX+"/goods")
    @Operation(summary = "获得商品销售明细报表")
    CommonResult<GoodsSellDetailReportRespDTO> getGoodsSellDetail(@Valid @SpringQueryMap GoodsSellDetailReqDTO reqVO);

    @GetMapping(PREFIX+"/ar-sell")
    @Operation(summary = "获得AR账发生明细报表")
    CommonResult<ArDetailReportRespDTO> getArSellDetail(@Valid @SpringQueryMap ArDetailReqDTO reqVO);


    @GetMapping(PREFIX+"/settle-detail")
    @Operation(summary = "获得前台结账报表")
    CommonResult<SettleDetailReportRespDTO> getSettleDetailReport(@Valid @SpringQueryMap SettleDetailReportReqDTO reqVO);

    @GetMapping(PREFIX+"/member-card")
    @Operation(summary = "获得会员卡销售明细报表")
    CommonResult<MemberCardSellDetailReportRespDTO> getMemberCardSellDetailReport(@Valid @SpringQueryMap MemberCardSellDetailReqDTO reqVO);


    @GetMapping(PREFIX+"/member-recharge")
    @Operation(summary = "获得会员充值&支付明细报表")
    CommonResult<MemberRechargeDetailReportRespDTO> getMemberRechargeDetail(@Valid @SpringQueryMap MemberRechargeDetailReqDTO reqVO);

    @GetMapping(PREFIX+"/breakfast-prepare")
    @Operation(summary = "获得早餐备餐报表")
    CommonResult<BreakfastPrepareReportRespDTO> getBreakfastPrepareReport(@Valid @SpringQueryMap BreakfastPrepareReqDTO reqVO) ;

    @GetMapping(PREFIX+"/night-prequalify")
    @Operation(summary = "获得夜审房价预审报表")
    CommonResult<NightPricePrequalifyReportRespDTO> getNightPricePrequalifyReport(@Valid @SpringQueryMap NightPricePrequalifyReqDTO reqVO);

    @GetMapping(PREFIX+"/ar-verify")
    @Operation(summary = "获得AR核销明细报表")
    CommonResult<ArVerifyDetailReportRespDTO> getArVerifyDetail(@Valid @SpringQueryMap ArVerifyDetailReqDTO reqVO) ;

    @GetMapping(PREFIX+"/ar-entry")
    @Operation(summary = "获得AR账收款明细报表")
    CommonResult<ArEntryDetailReportRespDTO> getArEntryDetailReport(@Valid @SpringQueryMap ArEntryDetailReqDTO reqVO);

    @GetMapping(PREFIX+"/buybk-or-returnbk")
    @Operation(summary = "获得购早/退早明细报表")
    CommonResult<BuyBkOrReturnBkDetailReportRespDTO> getArEntryDetailReport(@Valid @SpringQueryMap BuyBkOrReturnBkDetailReqDTO reqVO);
}
