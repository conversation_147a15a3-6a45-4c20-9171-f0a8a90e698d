package info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 会员充值&支付明细 Response VO")
@Data
public class MemberRechargeDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "会员代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memberCode;

    @Schema(description = "会员姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String memberName;

    @Schema(description = "会员级别代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "会员级别名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtName;

    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "操作门店", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operateHcode;

    @Schema(description = "操作门店名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operateHname;

    @Schema(description = "充值渠道", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeChannel;

    @Schema(description = "充值渠道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rechargeChannelName;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long rechargeFee;

    @Schema(description = "充值赠送金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long rechargeGiveFee;

    @Schema(description = "支付金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long payFee;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "操作时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime operateTime;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String operator;

}
