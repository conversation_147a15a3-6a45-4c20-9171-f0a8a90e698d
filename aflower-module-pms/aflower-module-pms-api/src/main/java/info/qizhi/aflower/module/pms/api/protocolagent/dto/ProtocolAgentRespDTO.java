package info.qizhi.aflower.module.pms.api.protocolagent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 协议单位、中介 Response DTO")
@Data
public class ProtocolAgentRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4871")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "所属酒店代码")
    private String belongHcode;

    @Schema(description = "所属酒店")
    private String belongHname;

    @Schema(description = "中介/协议单位代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String paCode;

    @Schema(description = "中介/协议单位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String paName;

    @Schema(description = "简称", example = "王五")
    private String shortName;

    @Schema(description = "法人;公司法人")
    private String legalPerson;

    @Schema(description = "电话;公司电话")
    private String telephone;

    @Schema(description = "地址;公司地址")
    private String address;

    @Schema(description = "类型;0：协议单位 1：中介", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String paType;

    @Schema(description = "渠道分类;如：旅行社", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String channelType;

    @Schema(description = "渠道;中介才有,存储渠道代码")
    private String channel;

    @Schema(description = "是否共享;0:单店使用 1：集团共享；0时挂账的酒店范围就是单店", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isShare;

    @Schema(description = "联系人姓名")
    private String contact;

    @Schema(description = "联系人电话")
    private String phone;

    @Schema(description = "联系人email")
    private String email;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endDate;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isEnable;

    @Schema(description = "登记单隐藏房价;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED, example = "17953")
    private String isHidePrice;

    @Schema(description = "是否允许挂账;0：否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isCredit;

    @Schema(description = "销售等级")
    private String sellLevel;

    @Schema(description = "佣金等级")
    private String commissionLevel;

    @Schema(description = "挂账账户名称", example = "赵六")
    private String creditAccName;

    @Schema(description = "挂账账户类型;0：信用账户 1：预付账户", example = "1")
    private String creditAccType;

    @Schema(description = "挂账:结算账期;0：固定账期 1：永久账期；credit_acc_type=0需要设置；")
    private String creditPayDays;

    @Schema(description = "挂账:固定账期值;credit_acc_type=0需要设置；credit_pay_days=0，该字段有值")
    private Integer creditPayFix;

    @Schema(description = "挂账最大限额类型;credit_acc_type=0需要设置；0：不限额度 1：限制额度", example = "2")
    private String creditQuotaType;

    @Schema(description = "挂账:最大限额;credit_acc_type=0需要设置；credit_quota_type=1，该字段需要设置最大额度")
    private Integer creditQuota;

    @Schema(description = "挂账:有效时间类型;credit_acc_type=0需要设置；0：永久有效 1：固定有效", example = "2")
    private String creditValidType;

    @Schema(description = "挂账：有效时间开始;credit_acc_type=0需要设置；credit_valid_type=1,需要设置")
    private LocalDate creditStartDate;

    @Schema(description = "挂账：有效时间结束;credit_acc_type=0需要设置；credit_valid_type=1,需要设置")
    private LocalDate creditEndDate;

    @Schema(description = "挂账：允许负账;0：不允许 1：允许")
    private String creditMinusAcc;

    @Schema(description = "开票信息代码;关联invoice表invoice_code字段")
    private String invoiceCode;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}