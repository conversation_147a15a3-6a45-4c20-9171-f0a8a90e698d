package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 17:14
*/
@Schema(description = "实时经营数据 Response VO")
@Data
public class RealBizKpiRespDTO {

	@Schema(description = "门店收入", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long totalFee;

	@Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal nightNum;

	@Schema(description = "时租房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long hourRooms;

	@Schema(description = "全日房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long allRooms;

	@Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long roomFee;

	@Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long revPar;

	@Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long avgRoomFee;

	@Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal occ;

	@Schema(description = "实时房态", requiredMode = Schema.RequiredMode.REQUIRED)
	private RealTimeRoomDTO realTimeRoom;

	@Schema(description = "房型出租率")
	List<RealOccDTO> realOccList;
}
