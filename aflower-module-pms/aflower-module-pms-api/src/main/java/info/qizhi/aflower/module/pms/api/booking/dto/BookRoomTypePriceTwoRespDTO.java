package info.qizhi.aflower.module.pms.api.booking.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 可预订房型房价 Response VO")
@Data
public class BookRoomTypePriceTwoRespDTO {
    @Schema(description = "货币单位")
    private String currencyUnit;

    @Schema(description = "可预订房型列表")
    private List<BookRoomTypePriceRespDTO> bookRoomTypePriceList;
}
