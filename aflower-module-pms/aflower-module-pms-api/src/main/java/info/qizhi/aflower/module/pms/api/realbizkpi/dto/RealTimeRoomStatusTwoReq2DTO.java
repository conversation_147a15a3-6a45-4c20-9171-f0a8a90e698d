package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/18 9:14
*/
@Schema(description = "前台营业 - 实时房态 Request DTO")
@Data
public class RealTimeRoomStatusTwoReq2DTO {

	@Schema(description = "集团代码")
	@NotEmpty(message = "{gcode.notblank}")
	private String gcode;

	@Schema(description = "门店代码")
	@NotEmpty(message = "{hcode.notblank}")
	private String hcode;

	@Schema(description = "统计类型")
	@NotEmpty(message = "统计类型不能为空")
	private List<String> statTypes;

}
