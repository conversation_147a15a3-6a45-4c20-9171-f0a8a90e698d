package info.qizhi.aflower.module.pms.api.roomtype.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.framework.common.pojo.ImageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 房型 Response DTO")
@Data
public class RoomTypeRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6025")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码;如果当前是门店房型，该字段为门店代码，否则填0", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String rtName;

    @Schema(description = "房型简称", example = "王五")
    private String shortName;

    @Schema(description = "基础价格", example = "16863")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long basePrice;

    @Schema(description = "面积")
    private BigDecimal area;

    @Schema(description = "容纳人数")
    private Integer peopleNum;

    @Schema(description = "是否有窗;是否有窗(0是，1否，2部分有窗)")
    private String isWindow;

    @Schema(description = "是否有卫生间;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isRestRoom;

    @Schema(description = "是否虚拟房;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isVirtual;

    @Schema(description = "是否集团房型;1是 0否，1时hcode为空", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isGRt;

    @Schema(description = "是否有效;1是 0否", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isEnable;

    @Schema(description = "图片")
    private List<ImageVO> pics;

    @Schema(description = "备注")
    private String intro;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "房间数量")
    private Long roomNum;

    @Schema(description = "房间床型")
    private List<RoomTypeBedDTO> roomTypeBeds;


}