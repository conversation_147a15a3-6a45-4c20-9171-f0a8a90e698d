package info.qizhi.aflower.module.pms.api.roomstatus.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/18 18:57
*/
@Schema(description = "前台营业 - 房类预测 Request DTO")
@Data
public class RoomClassPredictionReqDTO {

	@Schema(description = "集团代码")
	@NotEmpty(message = "{gcode.notblank}")
	private String gcode;

	@Schema(description = "门店代码")
	@NotEmpty(message = "{hcode.notblank}")
	private String hcode;

	@Schema(description = "开始日期")
	@NotNull(message = "{startDate.notnull}")
	private LocalDate startDate;

	@Schema(description = "房型代码")
	private String rtCode;
}
