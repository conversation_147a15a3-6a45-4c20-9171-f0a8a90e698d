package info.qizhi.aflower.module.pms.api.cashbillorder;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderReqDTO;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderSaveReqDTO;
import info.qizhi.aflower.module.pms.api.cashbillorder.dto.CashBillOrderSaveRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 创建现付账订单")
public interface CashBillOrderApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/cash-bill-order";
    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建现付账订单")
    CommonResult<CashBillOrderSaveRespDTO> createCashBillOrder(@Valid @RequestBody CashBillOrderSaveReqDTO createReqVO);

    @GetMapping(PREFIX + "/get-list")
    @Operation(summary = "获取现付账列表")
    CommonResult<List<CashBillOrderSaveRespDTO>> getCashBillOrderList(@Valid @RequestBody CashBillOrderReqDTO createReqVO);

}
