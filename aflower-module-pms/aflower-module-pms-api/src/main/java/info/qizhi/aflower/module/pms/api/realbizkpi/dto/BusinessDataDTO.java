package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 19:32
*/
@Schema(description = "小程序 - 经营数据分类统计 Response VO")
@Data
public class BusinessDataDTO {

	@Schema(description = "分类类别", requiredMode = Schema.RequiredMode.REQUIRED)
	private String cateGory;

	@Schema(description = "子类类别", requiredMode = Schema.RequiredMode.REQUIRED)
	private String classificationStatistics;

	@Schema(description = "入住类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
	private String checkinTypeName;

	@Schema(description = "房型统计", requiredMode = Schema.RequiredMode.REQUIRED)
	private RTCodeRoomDTO rtCodeRoom;

	@Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal nightNum;

	@Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long avgRoomFee;

	@Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
	private BigDecimal occ;

	@Schema(description = "总费用", requiredMode = Schema.RequiredMode.REQUIRED)
	private Long totalFee;

	@Schema(description = "统计类型", requiredMode = Schema.RequiredMode.REQUIRED)
	private String statType;
}
