package info.qizhi.aflower.module.pms.api.roomstatus.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 实时房态 Response VO")
@Data
public class RealTimeRoomStatusRespDTO {
    @Schema(description = "房间信息")
    private Room room;

    @Schema(description = "订单信息")
    private Order order;

    @Schema(description = "房态颜色")
    private RoomStatusColor roomColor;

}

