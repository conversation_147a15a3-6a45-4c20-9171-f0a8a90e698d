package info.qizhi.aflower.module.pms.enums;

/**
 * @Author: TY
 * @CreateTime: 2024-06-12
 * @Description: 日志记录常量
 * @Version: 1.0
 */
public interface LogRecordConstants {

    //=============房间操作日志==========

    String PMS_ROOM_SUBTYPE = "roomLogServiceImpl";
    String PMS_ROOM_property = "{{#roomDO.gcode}},{{#roomDO.hcode}},{{#roomDO.rCode}},{{#roomDO.rNo}}";


    String PMS_CREATE_ROOM_CREATE = "创建房间";
    String PMS_CREATE_ROOM_SUCCESS = "创建房间,房型代码为:{{#roomDO.rtCode}}, 房间号：{{#roomDO,rNo}}";

    /**
     * 房间备注修改操作
     */
    String PMS_REMARK_UPDATE = "修改房间{{#roomDO.rNo}} ： {_DIFF{#reqVO}}";
    String PMS_ROOM_TYPE = "修改房间备注";


    /**
     * 房间状态更新操作
     */
    String PMS_ROOM_UPDATE_TYPE = "修改房态-{{#newState}}";
    String PMS_ROOM_UPDATE_SUCCESS = "原房态：{{#oldState}} 房态变更为：{{#newState}} 备注：{{#remark}}";
    String PMS_ROOM_COMPLETE_OO_TYPE ="修改房态-完成维修";

    /**
     * 房间状态批量更新操作
     */
    String PMS_ROOMS_UPDATE_TYPE = "{{#rooms}}";
    String PMS_ROOMS_UPDATE_SUCCESS = "{{#roomDOList}}至{{#updateRoomDOList}}";
    String PMS_ROOMS_UPDATE_LOG_TEMPLATE = "原房态：{} 房态变更为：{} 备注：{}";

    //=============操作模块类型==========修改表的输入



    // =========== 订单日志 ============
    String PMS_ORDER_SUBTYPE = "orderLogServiceImpl";
    String PMS_ORDER_property = "{{#order.gcode}},{{#order.hcode}},{{#orderNo}}";

    /**
     * 新增预订单
     */
    String PMS_ORDER_CREATE_TYPE = "create_book";
    String PMS_ORDER_CREATE_SUCCESS_TEMPLATE = "新增预订单 (预订单号：{}, 预抵时间:{{order.planCheckinTime}} ~ 预离时间:{{order.planCheckoutTime}}, 入住类型:{{checkinType}},担保类型:{{guarantyStyle}})\n" +
            "{{#order.batches}}"+
                "{{#bookRoomTypes}}"+
                    "房型:{{rtName}} 房间数量:{{roomNum}}\n" +
                        "{{#dayPrices}}"+
                            "房型:{{rtName}} 日期:{{priceDate}} 价格:{{vipPrice}}(单位分)\n\n"+
                        "{{/dayPrices}}"+
                "{{/bookRoomTypes}}"+
            "{{/order.batches}}"+
            "联系人:{{order.contact}} 手机:{{order.phone}}\n" +
            "销售员:{{order.seller}}";

    String PMS_ORDER_CREATE_SUCCESS ="{{#content}}";

    /**
     * 新增房间
     */
    String PMS_ORDER_ADD_BOOK_ROOM_TYPE = "add_book_room";
    String PMS_ORDER_ADD_BOOK_ROOM_SUCCESS_TEMPLATE = "新增房间 (预抵时间:{{order.planCheckinTime}} ~ 预离时间:{{order.planCheckoutTime}}, 入住天数:{{order.days}})\n" +
            "{{#order.roomTypes}}"+
            "房型:{{rtCode}} 房间数量:{{roomNum}}\n" +
            "{{#dayPrices}}"+
            "日期:{{priceDate}} 价格:{{vipPrice}}(单位分)\n\n"+
            "{{/dayPrices}}"+
            "{{/order.roomTypes}}";

    String PMS_ORDER_ADD_BOOK_ROOM_SUCCESS ="{{#content}}";


    /**
     * 修改排房
     */
    String PMS_ORDER_UPDATE_ROOM_TYPE = "update_room";
    String PMS_ORDER_UPDATE_ROOM_SUCCESS ="{{#content}} ";
    String PMS_ORDER_UPDATE_ROOM_TEMPLATE ="修改排房 " +
            "{{#oldRooms}}"+
                "房号:原值({{rNo}})," +
            "{{/oldRooms}}"+
            "{{#newRooms}}"+
                "现值({{rNo}}})" +
            "{{/newRooms}}";

    /**
     * 自动排房
     */
    String PMS_ORDER_AUTO_ARRANGE_ROOM_TYPE = "auto_arrange_room";
    String PMS_ORDER_AUTO_ARRANGE_ROOM_SUCCESS ="{{#content}} ";
    String PMS_ORDER_AUTO_ARRANGE_ROOM_TEMPLATE ="自动排房 " +
            "{{#newRooms}}"+
            "新增排房，房号:({{rNo}})," +
            "{{/newRooms}}";

    /**
     * 批量赠早
     */
    String PMS_ORDER_BATCH_GIVE_BK_TYPE = "批量赠早";
    String PMS_ORDER_BATCH_GIVE_BK_SUCCESS ="{{#content}} ";
    String PMS_ORDER_BATCH_GIVE_BK_TEMPLATE ="自动排房 " +
            "{{#newRooms}}"+
            "新增排房，房号:({{rNo}})," +
            "{{/newRooms}}";

    /**
     * 修改价格
     */
    String PMS_ORDER_UPDATE_PRICE_TYPE = "update_price";
    String PMS_ORDER_UPDATE_PRICE_SUCCESS ="房型编号:{{#order.rtCode}},房间:{{#order.rNo}}\n{{#content}}备注：{{#remark}}";
    String PMS_ORDER_UPDATE_PRICE_TEMPLATE=
            "{{#oldPrices}}"+
                "日期:{{priceDate}} "+
                "原价格:{{vipPrice}}(单位分), " +
            "{{/oldPrices}}"+
            "{{#newPrices}}"+
                "现价格:{{vipPrice}}(单位分)\n" +
            "{{/newPrices}}";

    /**
     * 批量改价
     */
    String PMS_ORDER_BATCH_UPDATE_PRICE_TYPE = "batch_update_price";
    String PMS_ORDER_BATCH_UPDATE_PRICE_SUCCESS ="{{#content}}";
    String PMS_ORDER_BATCH_UPDATE_PRICE_TEMPLATE=
            "{{#orders}}"+
                    "单号:{{orderNo}} "+
                "{{#oldPrices}}"+
                    "日期:{{priceDate}} "+
                    "原价格:{{vipPrice}}(单位分), " +
                "{{/oldPrices}}"+
                "{{#newPrices}}"+
                    "现价格:{{vipPrice}}(单位分)\n" +
                "{{/newPrices}}"+
            "{{/orders}}";

    /**
     * 删除房间
     */
    String PMS_ORDER_DELETE_TEAM_BOOK_ROOM_TYPE = "delete_team_book_room";
    String PMS_ORDER_DELETE_TEAM_BOOK_ROOM_SUCCESS ="删除房间 （房型编号：{{#order.rtCode}}，房号：{{#order.rNo}}）";

    /**
     * 删除排房
     */
    String PMS_ORDER_DELETE_ARRANGE_BOOK_ROOM_TYPE = "delete_arrange_book_room";
    String PMS_ORDER_DELETE_ARRANGE_BOOK_ROOM_SUCCESS ="删除排房 （房型编号：{{#order.rtCode}}，房号：{{#order.rNo}}）";


    /**
     * 批量删除房间
     */
    String PMS_ORDER_BATCH_DELETE_TEAM_BOOK_ROOM_TYPE = "batch_delete_team_book_room";
    String PMS_ORDER_BATCH_DELETE_TEAM_BOOK_ROOM_SUCCESS ="{{#content}}";
    String PMS_ORDER_BATCH_DELETE_TEAM_BOOK_ROOM_SUCCESS_TEMPLATE =
            "{{#orders}}"+
                 "订单号:{{orderNo}}, 房号:{{rNo}}, 状态:{{state}}, 入住/预计离店日期:{{planCheckinTime}}~{{planCheckoutTime}}\n" +
            "{{/orders}}";

    /**
     * 添加同住
     */
    String PMS_ORDER_ADD_TOGETHER_TYPE = "add_together";
    String PMS_ORDER_ADD_TOGETHER_SUCCESS ="新增 入住 （房号：{{#reqVO.rNo}}，到店：{{#now}}}，离店：，" +
            "入住人：[姓名：{{#reqVO.name}}，手机：{{#reqVO.phone}}, " +
            "证件类型：{{#idType}}，证件号码：{{#reqVO.idNo}}]）";

    /**
     * 移除同住人
     */
    String PMS_ORDER_REMOVE_TOGETHER_TYPE = "remove_together";
    String PMS_ORDER_REMOVE_TOGETHER_SUCCESS = "移除同住人 （房号：{{#orderTogether.rNo}}，" +
            "入住人：[姓名：{{#orderTogether.name}}，手机：{{#orderTogether.phone}}, " +
            "证件类型：{{#orderTogether.idType}}，证件号码：{{#orderTogether.idNo}}]，" +
            "{{#isMainGuest ? '原主客人，已重新指定主客人' : '非主客人'}}";

    /**
     * 加入联房
     */
    String PMS_ORDER_JOIN_MERGEROOM_TYPE = "join_mergeroom";
    String PMS_ORDER_JOIN_MERGEROOM_SUCCESS="{{#content}}";
    String PMS_ORDER_JOIN_MERGEROOM_SUCCESS_TEMPLATE =
            "{{#orderList}}"+
                "订单号:{{orderNo}}, 房号:{{rNo}}, 状态:{{state}}, 入住/离店日期:{{checkinTime}}~{{checkoutTime}}\n" +
             "{{/orderList}}";

    /**
     * 更改销售员
     */
    String PMS_ORDER_UPDATE_SELLER_TYPE = "update_seller";
    /**
     * 更改客源类型
     */
    String PMS_ORDER_UPDATE_GUEST_SRC_TYPE = "update_guest_src";


    String PMS_ORDER_UPDATE_SUCCESS="更改{{#operation}} 原值({{#old}}) 现值({{#new}})";

    /**
     * 更改客人信息
     */
    String PMS_ORDER_UPDATE_GUEST_INFO_TYPE = "update_guest_info";
    String PMS_ORDER_UPDATE_GUEST_INFO_SUCCESS="房号:{{#order.rNo}} 姓名:{{#reqVO.name}}  " +
            "证件号码:原值({{#order.idNo}}),现值({{#reqVO.idNo}}) 性别:原值({{#order.sex}}),现值({{#reqVO.sex}})";

    /**
     * 重新入住
     */
    String PMS_ORDER_RE_CHECKIN_TYPE = "re_checkin";
    String PMS_ORDER_RE_CHECKIN_SUCCESS="客户:{{#order.name}} 房间:{{#order.rNo}}";


    /**
     * 批量占房
     */
    String PMS_ORDER_OCC_ROOMS_TYPE = "occ_rooms";
    String PMS_ORDER_OCC_ROOMS_SUCCESS= "{{#content}}";
    String PMS_ORDER_OCC_ROOMS_SUCCESS_TEMPLATE=
            "{{#bookRoomList}}" +
                    "新增 每日占房 (房型:{{rtName}}\n"+
                    "{{#dayPrices}}" +
                        "房价:{{vipPrice}}(单位分),房价日期:{{priceDate}})\n" +
                    "{{/dayPrices}}"+
            "{{/bookRoomList}}";

    /**
     * 续住
     */
    String PMS_ORDER_CONTINUEIN_TYPE = "continuein";
    String PMS_ORDER_CONTINUEIN_SUCCESS= "{{#content}}更改离店时间：原值：{{#order.planCheckoutTime}} 现值：{{#reqVO.planCheckoutTime}} 原因：{{#reqVO.remark}}";
    String PMS_ORDER_CONTINUEIN_SUCCESS_TEMPLATE=
            "{{#dayPrices}}" +
                "新增 每日占房 (房号:{{order.rNo}},房价:{{vipPrice}}(单位分),房价日期:{{priceDate}})\n" +
            "{{/dayPrices}}";

    /**
     * 提前
     */
    String PMS_ORDER_EARLY_OUT_TYPE = "early_out";
    String PMS_ORDER_EARLY_OUT_SUCCESS= "{{#content}}更改离店时间：原值：{{#order.planCheckoutTime}} 现值：{{#reqVO.planCheckoutTime}} 原因：{{#reqVO.remark}}";
    String PMS_ORDER_EARLY_OUT_SUCCESS_TEMPLATE=
            "{{#dayPrices}}" +
                " 删除 每日占房 (房号:{{order.rNo}},房价:{{vipPrice}}(单位分),房价日期:{{priceDate}})\n" +
            "{{/dayPrices}}";

    /**
     * 换房
     */
    String PMS_ORDER_CHANGE_ROOM_TYPE = "change_room";
    String PMS_ORDER_CHANGE_ROOM_SUCCESS= "由({{#order.rtCode}})房型的({{#order.rNo}})房间换房到({{#room.rtCode}})房型的({{#room.rNo}})房间";


    /**
     * 退出团队
     */
    String PMS_ORDER_QUIT_TEAM_TYPE = "quit_team";
    String PMS_ORDER_QUIT_TEAM_SUCCESS= "团队:{{#order.teamCode}}, 房号:{{#order.rNo}}";

    /**
     * 修改备注
     */
    String PMS_ORDER_UPDATE_ORDER_REMARK_TYPE = "update_order_remark";
    String PMS_ORDER_UPDATE_ORDER_REMARK_SUCCESS= "原备注：{{#order.remark}}, 新备注：{{#newRemark}}";

    /**
     * 修改外部订单号
     */
    String PMS_ORDER_UPDATE_ORDER_OUT_ORDER_NO_TYPE = "update_order_out_order_no";
    String PMS_ORDER_UPDATE_ORDER_OUT_ORDER_NO_SUCCESS= "原外部订单号：{{#order.outOrderNo}}, 新外部订单号：{{#newOutOrderNo}}";

    /**
     * 增早
     */
    String PMS_ORDER_GIVE_BK_TYPE = "give_bk";
    String PMS_ORDER_BK_SUCCESS= "{{#content}}";
    String PMS_ORDER_BK_SUCCESS_TEMPLATE=
            "{{#updateOrderPriceList}}" +
                "日期：{{priceDate}}, 早餐数：{{bkNum}}\n" +
            "{{/updateOrderPriceList}}";

    /**
     * 购早
     */
    String PMS_ORDER_BUY_BK_TYPE = "buy_bk";
    String PMS_ORDER_BUY_BK_SUCCESS= "日期：{{priceDate}}, 早餐数：{{buyBkNum}}";

    /**
     * 退早
     */
    String PMS_ORDER_RETURN_BK_TYPE = "return_bk";
    String PMS_ORDER_RETURN_BK_SUCCESS= "日期：{{priceDate}}, 早餐数：{{buyBkNum}}";

    /**
     * 叫醒修改
     */
    String PMS_ORDER_UPDATE_AWAKEN_TIME_TYPE = "update_awaken_time";
    String PMS_ORDER_UPDATE_AWAKEN_TIME_SUCCESS= "原时间：{{#order.awakenTime}}, 新时间：{{#newAwakenTime}}";


    /**
     * 修改订单保密
     */
    String PMS_ORDER_UPDATE_ORDER_SECRECY_TYPE = "update_order_secrecy";
    String PMS_ORDER_UPDATE_ORDER_SECRECY_SUCCESS= "宾客代码：{{#order.togetherCode}},修改为保密";


    /**
     * 修改订单免打扰
     */
    String PMS_ORDER_UPDATE_ORDER_DISTURBING_TYPE = "update_order_disturbing";
    String PMS_ORDER_UPDATE_ORDER_DISTURBING_SUCCESS= "宾客代码：{{#order.togetherCode}},修改为免打扰";



    // =========== 订单日志账务类型 ============
    /**
     * 部分结账
     */
    String PMS_ORDER_PART_CLOSE_ACCOUNT_TYPE = "part_close_account";
    String PMS_ORDER_PART_CLOSE_ACCOUNT_SUCCESS= "{{#content}}备注:{{#order.remark}}";
    String PMS_ORDER_PART_CLOSE_ACCOUNT_SUCCESS_TEMPLATE=
            "{{#accounts}}" +
               "部分结账账单号:{{accNo}}\n" +
             "{{/accounts}}";

    /**
     * 结账
     */
    String PMS_ORDER_PAY_CHECKOUT_TYPE = "pay_checkout";
    String PMS_PAY_CHECKOUT_SUCCESS= "批量结账， 订单号：{{#content}}";
    String PMS_PAY_CHECKOUT_SUCCESS_TEMPLATE=
            "{{#orderTogethers}}" +
                "{{no}}\n" +
             "{{/orderTogethers}}";

    /**
     * 挂账退房
     */
    String PMS_ORDER_CREDIT_CHECKOUT_TYPE = "credit_checkout";
    String PMS_CREDIT_CHECKOUT_SUCCESS= "挂账退房， {{#content}}\n备注：{{#remark}}";
    String PMS_CREDIT_CHECKOUT_SUCCESS_TEMPLATE=
            "{{#orderList}}" +
                "订单号：{{orderNo}}  房号：{{rNo}}\n" +
            "{{/orderList}}";

    /**
     * 付款、预授权操作
     */
    String PMS_ORDER_PAY_TYPE = "order_pay";
    String PMS_PAY_SUCCESS= "付款或添加预授权，类型:{{#subCode}} 金额:{{#order.fee}}(单位分) 账单号:{{#order.accNo}}";

    /**
     * 补录操作
     */
    String PMS_ORDER_RECORDING_TYPE = "recording";
    String PMS_RECORDING_SUCCESS= "补录操作，类型:{{#subCode}} 金额:{{#order.fee}}(单位分) 账单号:{{#order.accNo}}";

    /**
     * 生成房费
     */
    String PMS_ORDER_CONFIRM_ROOM_FEE_TYPE = "confirm_room_fee";
    String PMS_CONFIRM_ROOM_FEE_SUCCESS= "{{#content}}";
    String PMS_CONFIRM_ROOM_FEE_SUCCESS_TEMPLATE=
            "{{#accountList}}" +
            "新增 帐务 （金额：{{fee}}(单位分)，房间号：{{rNo}}，客户：{{guestName}}，类型：{{subCodeName}}）\n" +
            "新增 税额明细 （税种：增值税，税后金额：{{afterTaxFee}}(单位分)）"+
            "{{/accountList}}";


    /**
     * 入账
     */
    String PMS_ORDER_CONSUME_TYPE = "consume";
    String PMS_ORDER_CONSUME_SUCCESS= "新增 帐务 （金额：{{#account.fee}}(单位分)，房间号：{{#account.rNo}}，客户：{{#account.guestName}}，类型：{{#subCode}}\n" +
            "新增 税额明细 （税种：增值税，税后金额：{{#account.afterTaxFee}}(单位分)）";

    /**
     * 撤销结账
     */
    String PMS_ORDER_UNDO_PAY_TYPE = "undo_pay";
    String PMS_ORDER_UNDO_PAY_SUCCESS= "撤销结账 账号：{{#payNo}}";


    /**
     * 账务操作
     */
    String PMS_ORDER_ACCOUNT_OPERATION_SUCCESS = "{{#content}}";
    String PMS_ORDER_ACCOUNT_OPERATION_SUCCESS_TEMPLATE =
            "{{#oldAccounts}}" +
                "原有账套：{{accNo}}  " +
            "{{/oldAccounts}}"+
            "{{#newAccountList}}"+
                "新账套:{{accNo}}\n" +
            "{{/newAccountList}}";

    //冲账
    String PMS_ORDER_RED_ACCOUNT_TYPE = "red_account";

    //转账
    String PMS_ORDER_TRANSFER_ACCOUNT_TYPE = "transfer_account";

    //拆账
    String PMS_ORDER_SPLIT_ACCOUNT_TYPE = "split_account";

    // 退款
    String PMS_ORDER_REFUND_ACCOUNT_TYPE = "refund_account";
    String PMS_ORDER_REFUND_ACCOUNT_SUCCESS = "{{#content}}";
    String PMS_ORDER_REFUND_ACCOUNT_SUCCESS_TEMPLATE =
                    "{{#oldAccounts}}" +
                        "原有账套：{{accNo}}  " +
                    "{{/oldAccounts}}"+
                    "{{#newAccountList}}"+
                        "新账套:{{accNo}} "+
                        "退款金额:{{fee}}(单位：分)\n"+
                    "{{/newAccountList}}";


    // =========== 房价日历日志 ============
    String PMS_PRICE_CALENDAR_SUBTYPE = "priceCalendarLogServiceImpl";
    String PMS_PRICE_CALENDAR_property = "{{#reqVO.gcode}},{{#reqVO.hcode}}";
    String PMS_PRICE_CALENDAR_TYPE="{{#type}}";
    String PMS_PRICE_CALENDAR_UPDATE_SUCCESS = "删除列表:{{#priceCalendarsToDelete}}插入列表:{{#priceCalendarsToInsert}}";

    String PMS_PRICE_CALENDAR_UPDATE_UNIFY_TYPE = "每天统一售价";

    String PMS_PRICE_CALENDAR_UPDATE_DIFFERENT_TYPE = "在周内设置不同价格";
    String PMS_PRICE_CALENDAR_UPDATE_DIFFERENT_SUCCESS_TEMPLATE =
            "{{#rtCodePrices}}" +
            "房价一:{{price1}}(单位分)(每周{{week1}})\n" +
            "房价二:{{price2}}(单位分)(每周{{week2}})\n" +
            "房型：{{rtName}}\n"+
            "生效日期：{{start}}~{{end}}\n"+
            "{{/rtCodePrices}}";


    String PMS_PRICE_CALENDAR_UPDATE_BASE_PRICES_TYPE = "修改门市价";
    String PMS_PRICE_CALENDAR_UPDATE_BASE_PRICES_SUCCESS= "原值:{{#oldRoomTypePrice}}新值:{{#newRoomTypePrice}}";













}
