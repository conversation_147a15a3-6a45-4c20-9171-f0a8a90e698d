package info.qizhi.aflower.module.pms.api.account;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.account.dto.*;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 账务")
public interface AccountApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/account";

    @GetMapping(PREFIX + "/get-room")
    @Operation(summary = "获得房间账务")
    CommonResult<AccountRespDTO> getAccount(@Valid @SpringQueryMap AccountReqDTO reqDTO);

    @GetMapping(PREFIX + "/get-list")
    @Operation(summary = "hiii获得房间账务列表")
    CommonResult<List<AccountRespDTO>> getAccountList(@Valid @SpringQueryMap AccountListReqDTO reqVO);

    @PostMapping(PREFIX + "/create-accouts")
    @Operation(summary = "批量创建账务")
    CommonResult<Boolean> createAccounts(@Valid @RequestBody List<AccountSaveReqDTO> createAccountList);

    @PostMapping(PREFIX + "/consume")
    @Operation(summary = "消费操作")
    CommonResult<Boolean> createConsumeAccount(@Valid @RequestBody ConsumeAccountSaveReqDTO createReqVO);

    @PostMapping(PREFIX + "/handover-report")
    @Operation(summary = "交班报表")
    CommonResult<HandoverReportRespDTO> handoverReport(@Valid @RequestBody HandoverReportReqDTO reqVO);

    @GetMapping(PREFIX + "/get-account-list")
    @Operation(summary = "获得账务列表")
    CommonResult<List<AccountRespDTO>> getPayOrConsumeAccountList(@Valid @SpringQueryMap PayOrConsumeDetailReqDTO reqVO);

    @PostMapping(PREFIX + "/pay")
    @Operation(summary = "付款、预授权操作")
    CommonResult<String> createPayAccount(@Valid @RequestBody PayAccountSaveReqDTO createReqVO);

    @PostMapping(PREFIX + "/credit-check-out")
    @Operation(summary = "挂账退房")
    CommonResult<Boolean> creditCheckOut(@Valid @RequestBody ConfirmRoomFeeReqDTO reqVO);


    // ------>>> 结账退房、挂账退房
    @GetMapping(PREFIX + "/get-checkout-order-list")
    @Operation(summary = "结账退房-结账(挂账)账号列表")
    @Parameter(name = "togetherCode", description = "宾客代码", required = true)
    CommonResult<OrderCheckOutGuestRespDTO> getCheckOutGuestList(@RequestParam("togetherCode") String togetherCode , @RequestParam("state") String state);

    @PostMapping(PREFIX + "/get-confirm-room-fee")
    @Operation(summary = "结账退房-计算房费")
    CommonResult<List<ConfirmCheckOutAccountRespDTO>> getConfirmRoomFeeList(@Valid @RequestBody ConfirmCheckOutAccountReqDTO r);

    @PostMapping(PREFIX + "/confirm-room-fee")
    @Operation(summary = "结账退房-确认生成房费")
    CommonResult<Boolean> confirmRoomFee(@Valid @RequestBody ConfirmRoomFeeReqDTO reqVO);

    @GetMapping(PREFIX + "/get-confirm-preauth")
    @Operation(summary = "结账退房-预授权列表")
    CommonResult<List<ConfirmCheckOutPreAuthRespDTO>> getConfirmPreAuthList(@Valid @SpringQueryMap ConfirmPreAuthListReqDTO reqVO);

    @PostMapping(PREFIX + "/confirm-preauth")
    @Operation(summary = "结账退房-确认预授权（收款金额）")
    CommonResult<Boolean> confirmPreAuth(@Valid @RequestBody ConfirmPreAuthReqDTO r);

    @PutMapping(PREFIX + "/cancel-preauth")
    @Operation(summary = "取消预授权")
    CommonResult<Boolean> cancelPreAuth(@RequestParam("accNo") String accNo);

    @PostMapping(PREFIX + "/stat-account")
    @Operation(summary = "统计付款金额(完成结账时需要)")
    CommonResult<StatCheckOutAccountRespDTO> statCheckOutAccount(@Valid @RequestBody FinishCheckOutAccountReqDTO reqVO);

    @PostMapping(PREFIX + "/pay-check-out")
    @Operation(summary = "结账退房")
    CommonResult<Boolean> payCheckOut(@Valid @RequestBody PayCheckOutAccountRepDTO r);
    // <<<-------结账退房、挂账退房
}