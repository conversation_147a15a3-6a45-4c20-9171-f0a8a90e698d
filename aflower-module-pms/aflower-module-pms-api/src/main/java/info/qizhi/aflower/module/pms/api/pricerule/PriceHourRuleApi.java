package info.qizhi.aflower.module.pms.api.pricerule;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.pricerule.dto.PriceHourRuleRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 时租房计费规则")
public interface PriceHourRuleApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/price-hour-rule";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得时租房计费规则")
    CommonResult<PriceHourRuleRespDTO> getPriceHourRule(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode);
}
