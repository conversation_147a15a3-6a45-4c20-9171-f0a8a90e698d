package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 17:10
*/
@Schema(description = "实时房态 Request VO")
@Data
public class RealTimeRoomStatusReqDTO {

	@Schema(description = "集团代码")
	@NotEmpty(message = "{gcode.notblank}")
	private String gcode;

	@Schema(description = "门店代码")
	@NotEmpty(message = "{hcode.notblank}")
	private String hcode;

	@Schema(description = "房间代码列表,如果不传，则所有的房间")
	@JsonProperty(value = "rCodes")
	private List<String> rCodes;
}
