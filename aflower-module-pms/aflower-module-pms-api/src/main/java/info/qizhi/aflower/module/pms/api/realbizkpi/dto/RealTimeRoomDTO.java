package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 17:19
*/
@Schema(description = "小程序 - 实时房态 Response VO")
@Data
public class RealTimeRoomDTO {

	@Schema(description = "可售房间数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer availableRoomNum;

	@Schema(description = "今日预离数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer checkoutRoomNum;

	@Schema(description = "没有自用房的今日预离数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer noSelfcheckoutRoomNum;

	@Schema(description = "今日预抵数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer todayBookingRoomNum;

	@Schema(description = "在住客房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer occupiedRoomNum;

	@Schema(description = "维修房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer OORoomNum;

	@Schema(description = "自用房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer selfUseRoomNum;

	@Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer totalRoomNum;

	@Schema(description = "今日入住总价格")
	private Long inHouseTotal;

	@Schema(description = "今日离店总价格")
	private Long todayCheckoutTotal;

	@Schema(description = "今日预抵总价格")
	private Long todayBookingTotal;
}
