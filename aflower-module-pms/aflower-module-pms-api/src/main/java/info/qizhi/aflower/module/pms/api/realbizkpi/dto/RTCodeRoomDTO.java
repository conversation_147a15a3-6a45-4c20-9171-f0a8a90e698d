package info.qizhi.aflower.module.pms.api.realbizkpi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 19:32
*/
@Schema(description = "小程序 - 实时房态 Response VO")
@Data
public class RTCodeRoomDTO {
	@Schema(description = "可售房间数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer availableRoomNum;

	@Schema(description = "在住房间数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer occupiedRoomNum;

	@Schema(description = "今日预抵房间数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer todayBookingRoomNum;

	@Schema(description = "钟点房数", requiredMode = Schema.RequiredMode.REQUIRED)
	private Integer hourRoomNum;
}
