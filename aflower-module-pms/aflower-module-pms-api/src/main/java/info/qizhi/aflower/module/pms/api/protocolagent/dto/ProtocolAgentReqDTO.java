package info.qizhi.aflower.module.pms.api.protocolagent.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 协议单位、中介 Request DTO")
@Data
@ToString(callSuper = true)
public class ProtocolAgentReqDTO {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "所属酒店代码,不为空查询所属门店单位和集团共享单位")
    private String belongHcode;

    @Schema(description = "类型;0：协议单位 1：中介", example = "2")
    private String paType;

    @Schema(description = "状态;0：无效 1：有效")
    private String isEnable;

    @Schema(description = "0:单店使用 1：集团共享；0时挂账的酒店范围就是单店")
    private String isShare;

    @Schema(description = "是否允许挂账")
    private String isCredit;

    @Schema(description = "销售等级")
    private String sellLevel;

}