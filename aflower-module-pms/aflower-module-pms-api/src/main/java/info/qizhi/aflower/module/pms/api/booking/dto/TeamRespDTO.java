package info.qizhi.aflower.module.pms.api.booking.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 团队 Response DTO")
@Data
public class TeamRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10137")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String teamCode;

    @Schema(description = "团队名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String teamName;

    @Schema(description = "团队类型;请看数据字典", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String teamType;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "客源类型;请看数据字典", example = "2")
    private String guestSrcType;

    @Schema(description = "入住时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime checkinTime;

    @Schema(description = "预离时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE)
    private LocalDateTime planCheckoutTime;

    @Schema(description = "状态;请看数据字典", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "账务状态 open:未结 closed:已结. 团队主单退房结账完成后，账务状态变为closed")
    private String accState;

    @Schema(description = "结账时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime payTime;

    @Schema(description = "结账班次")
    private String payShiftNo;

    @Schema(description = "结账操作人")
    private String payOperator;

    @Schema(description = "客人账号;如果客源类别为会员 账号为会员的账号 为单位则为单位账号 为中介则为中介账号", example = "5438")
    private String guestCode;

    @Schema(description = "总消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

    @Schema(description = "销售员")
    private String seller;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}