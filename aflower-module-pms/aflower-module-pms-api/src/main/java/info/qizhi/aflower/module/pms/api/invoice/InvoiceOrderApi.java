package info.qizhi.aflower.module.pms.api.invoice;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.invoice.dto.*;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2025/01/24 10:43
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - 发票基础的API")
public interface InvoiceOrderApi {

    String PREFIX = ApiConstants.PREFIX + "/invoice";

    @PostMapping(PREFIX + "/self")
    @Operation(summary = "自助发票申请")
    CommonResult<InvoiceOrderSelfRespDTO> self(@RequestBody InvoiceOrderReqDTO reqDTO);

    @PostMapping(PREFIX + "/blue")
    @Operation(summary = "蓝字发票开具")
    CommonResult<InvoiceOrderBlueRespDTO> blue(@RequestBody InvoiceOrderBlueReqDTO reqDTO);

    @PostMapping(PREFIX + "/red")
    @Operation(summary = "冲红")
    CommonResult<InvoiceOrderRedRespDTO> red(@RequestBody InvoiceOrderRedReqDTO reqDTO);

    @GetMapping(PREFIX + "/enterprise")
    @Operation(summary = "查询企业抬头信息")
    CommonResult<List<EnterpriseTitleRespDTO>> queryEnterprise(@RequestBody String name);
}
