package info.qizhi.aflower.module.pms.api.booking.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 预订单相关分页 Response VO")
@Data
public class BookPageRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8352")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String bookNo;

    @Schema(description = "外部订单号")
    private String outOrderNo;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelName;

    @Schema(description = "预订人(联系人);如果是团队预订，这里保存团队的联系人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contact;

    @Schema(description = "预订人电话;联系电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String phone;

    @Schema(description = "入住人姓名")
    private String checkinPerson;

    @Schema(description = "入住人电话")
    private String checkinPhone;

    @Schema(description = "预抵时间;普通预订存储，团队预订低离时间存储在预订房型中", requiredMode = Schema.RequiredMode.REQUIRED)
   // @JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private String planCheckinTime;

    @Schema(description = "预离时间;普通预订存储，团队预订低离时间存储在预订房型中")
    //@JsonSerialize(using = CustomLocalDateTimeSerializer.class)
    private String planCheckoutTime;

    @Schema(description = "入住类型;当预订类型为团队预订时，入住类型为旅行团队或会议团队", example = "2")
    private String checkinType;

    @Schema(description = "入住类型名称", example = "2")
    private String checkinTypeName;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String guestSrcType;

    @Schema(description = "客源类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String guestSrcTypeName;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "保留时间")
    @JsonFormat(pattern = FORMAT_HOUR_MINUTE_SECOND)
    private LocalDateTime retainTime;

    @Schema(description = "团队类型")
    private String teamType;

    @Schema(description = "团队代码")
    private String teamCode;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "房型信息")
    private List<RoomInfo> roomTypes;

    @Schema(description = "房型分类")
    private List<RoomTypeClass> roomTypeClass;


    @Data
    public static class RoomInfo {

        @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
        private String orderNo;

        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String rtName;

        @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonProperty(value = "rNo")
        private String rNo;

        @Schema(description = "房价(原价)", requiredMode = Schema.RequiredMode.REQUIRED)
//        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long price;

        @Schema(description = "天数")
        private Integer days;

    }

    @Data
    public static class RoomTypeClass {

        @Schema(description = "房型代码")
        private String rtCode;

        @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String rtName;

        @Schema(description = "间数")
        private Integer num;

        @Schema(description = "房价(如果多天取最低的价格)", requiredMode = Schema.RequiredMode.REQUIRED)
//        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long price;

    }

}
