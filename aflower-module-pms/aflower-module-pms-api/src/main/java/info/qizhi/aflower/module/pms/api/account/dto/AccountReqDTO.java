package info.qizhi.aflower.module.pms.api.account.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

@Schema(description = "biz - 账务 Request DTO")
@Data
public class AccountReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "账单号;系统生成唯一标识，每笔账都生成一个")
    private String accNo;

    @Schema(description = "预订单号: 预订时产生的账务。订单号：入住后产生的账务。团队代码：账务类型为团队主单时。" )
    private String no;

    @Schema(description = "单号列表")
    private List<String> noList;

    @Schema(description = "账务号列表")
    private List<String> accNoList;

    @Schema(description = "账务状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private String state;

    @Schema(description = "账务类型;general:订单 book:预订单 group:团队主单", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accType;

    @Schema(description = "房号;当该账务为团队主账时，该字段名为'团队主账',否则为房号")
    private String rNo;


}
