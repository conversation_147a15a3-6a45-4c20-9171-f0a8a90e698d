package info.qizhi.aflower.module.pms.api.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 确认预授权 Request VO")
@Data
public class ConfirmPreAuthReqDTO {

    @Schema(description = "账务号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{accountNo.notempty}")
    private String accNo;

    @Schema(description = "实收金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    @Min(value = 0L, message = "{fee.min}")
    private Long fee;

}