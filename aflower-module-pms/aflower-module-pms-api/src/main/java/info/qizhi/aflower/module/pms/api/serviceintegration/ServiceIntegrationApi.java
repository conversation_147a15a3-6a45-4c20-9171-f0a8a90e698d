package info.qizhi.aflower.module.pms.api.serviceintegration;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationReqDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationSaveRespDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 服务对接")
public interface ServiceIntegrationApi {

    String PREFIX = ApiConstants.PREFIX + "/pms/service-integration";

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得对接服务")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    CommonResult<ServiceIntegrationRespDTO> getServiceIntegration(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode, @RequestParam("type") String type);

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得服务列表")
    @Parameters({
            @Parameter(name = "gcode", description = "集团编码"),
            @Parameter(name = "hcode", description = "酒店编码"),
            @Parameter(name = "type", description = "类型"),
            @Parameter(name = "state", description = "状态")
    })
    CommonResult<List<ServiceIntegrationRespDTO>> getServiceIntegrationList(@RequestParam(name = "gcode", required = false) String gcode,
                                                                                  @RequestParam( name = "hcode", required = false) String hcode,
                                                                                  @RequestParam("type") String type,
                                                                            @RequestParam(value = "state") String state
                                                                            );

    @GetMapping(PREFIX + "/service/list")
    @Operation(summary = "获得服务列表")
    CommonResult<List<ServiceIntegrationRespDTO>> getServiceIntegrationList(@Valid @SpringQueryMap ServiceIntegrationReqDTO reqDTO);

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建对接服务")
    CommonResult<Boolean> createServiceIntegration(@Valid @RequestBody ServiceIntegrationSaveRespDTO createReqVO);

    @PostMapping(PREFIX + "/batch-create")
    @Operation(summary = "批量创建对接服务, 暂时只能创建pms服务")
    CommonResult<Boolean> batchCreateServiceIntegration(@Valid @RequestBody List<ServiceIntegrationSaveRespDTO> createReqVOs);

}
