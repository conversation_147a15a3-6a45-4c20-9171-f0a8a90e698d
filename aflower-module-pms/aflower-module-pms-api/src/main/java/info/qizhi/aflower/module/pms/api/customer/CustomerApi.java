package info.qizhi.aflower.module.pms.api.customer;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.customer.dto.CustomerDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 客历")
public interface CustomerApi {
    String PREFIX = ApiConstants.PREFIX + "/pms/customer";

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建客历")
    CommonResult<Long> createCustomer(@Valid @RequestBody CustomerDTO createReqVO);

    @GetMapping(PREFIX + "/get")
    @Operation(summary = "获得客历")
    CommonResult<CustomerDTO> getCustomer(@RequestParam("idNo") String idNo, @RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode);

    @GetMapping(PREFIX + "/get-phone")
    @Operation(summary = "获得客历")
    CommonResult<CustomerDTO> getCustomerByPhone(@RequestParam("phone") String phone, @RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode);

    @PutMapping(PREFIX + "/update")
    @Operation(summary = "更新客历")
    CommonResult<Boolean> updateCustomer(@Valid @RequestBody CustomerDTO updateReqVO);
}
