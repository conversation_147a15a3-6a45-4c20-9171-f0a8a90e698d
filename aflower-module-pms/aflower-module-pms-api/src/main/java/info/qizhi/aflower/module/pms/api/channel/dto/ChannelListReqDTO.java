package info.qizhi.aflower.module.pms.api.channel.dto;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 渠道 Request DTO")
@Data
public class ChannelListReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "渠道名称", example = "芋艿")
    private String channelName;

    @Schema(description = "是否有效;0:无效 1:有效")
    private String isEnable;

    @Schema(description = "是否集团渠道;0:否 1:是")
    private String isG;

}