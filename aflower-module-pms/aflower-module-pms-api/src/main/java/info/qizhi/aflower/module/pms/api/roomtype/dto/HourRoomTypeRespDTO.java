package info.qizhi.aflower.module.pms.api.roomtype.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 时租房房型 Response VO")
@Data
public class HourRoomTypeRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7143")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "时租房代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hourCode;

    @Schema(description = "时租房名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String hourName;

    @Schema(description = "小时数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer hour;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<RoomTypeSimpleRespDTO> rts;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}