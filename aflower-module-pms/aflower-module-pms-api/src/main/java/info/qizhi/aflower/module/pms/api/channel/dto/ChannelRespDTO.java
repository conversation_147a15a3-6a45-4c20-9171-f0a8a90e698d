package info.qizhi.aflower.module.pms.api.channel.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 渠道新增/修改 Response O")
@Data
public class ChannelRespDTO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19070")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码;当is_g=0时，当前字段有值")
    private String hcode;

    @Schema(description = "渠道代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String channelCode;

    @Schema(description = "渠道名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String channelName;

    @Schema(description = "是否有效;0:无效 1:有效", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isEnable;

    @Schema(description = "是否集团渠道;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isG;

    @Schema(description = "类型;0:直营 1：分销", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private String channelType;

    @Schema(description = "是否系统初始;0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
    private String isSys;
}
