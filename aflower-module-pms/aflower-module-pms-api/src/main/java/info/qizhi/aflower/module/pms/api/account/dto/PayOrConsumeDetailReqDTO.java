package info.qizhi.aflower.module.pms.api.account.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 付款/消费明细报表 Response VO")
@Data
public class PayOrConsumeDetailReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "报表操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "入账人")
    private String recorder;

    @Schema(description = "科目类型;0: 消费科目 1：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String subType;

    @Schema(description = "日期类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    @NotEmpty(message = "日期类型不能为空")
    private String timeType;

    @Schema(description = "班次", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private List<String> shiftNos;

    @Schema(description = "客源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private String guestSrcType;

    @Schema(description = "科目代码", example = "100")
    private List<String > subCodes;

    @Schema(description = "统计渠道",  example = "100")
    private String statChannel;

    @Schema(description = "营业日")
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String startDate;

    @Schema(description = "结束日期")
    //@DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private String endDate;

}
