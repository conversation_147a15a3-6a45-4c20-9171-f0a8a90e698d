package info.qizhi.aflower.module.pms.api.roomstatus.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "前台营业 - 远期房态 Request VO")
@Data
public class FutureRoomStatusReqDTO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "开始日期")
    @NotNull(message = "{startDate.notnull}")
    private LocalDate startDate;

}
