package info.qizhi.aflower.module.pms.api.booking;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomArrangeRespDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomArrangeSaveReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookRoomTypeArrangeReqDTO;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 预订排房")
public interface BookRoomApi {

    String PREFIX = ApiConstants.PREFIX + "/book-room";

    @PutMapping(PREFIX + "/arrange")
    @Operation(summary = "排房")
    CommonResult<Boolean> arrangeRoom(@Valid @RequestBody BookRoomArrangeSaveReqDTO reqVO);

    @GetMapping(PREFIX+"/can-book-rooms")
    @Operation(summary = "获得可预订的房间列表")
    CommonResult<List<BookRoomArrangeRespDTO>> getBookRoom(@Valid @SpringQueryMap BookRoomTypeArrangeReqDTO reqVO);

}
