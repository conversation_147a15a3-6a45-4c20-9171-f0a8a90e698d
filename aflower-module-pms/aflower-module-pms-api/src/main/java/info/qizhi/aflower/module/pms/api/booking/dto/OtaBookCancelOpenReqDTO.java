package info.qizhi.aflower.module.pms.api.booking.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import info.qizhi.aflower.framework.common.enums.PlatformEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 预订单(普通预订团队预订)取消 Request DTO")
@Data
public class OtaBookCancelOpenReqDTO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "平台")
    @InStringEnum(value = PlatformEnum.class, message = "{platform.instringenum}")
    private String platform;

    @Schema(description = "预订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bookingNumber.notempty}")
    private String bookNo;

    @Schema(description = "结算罚金")
    @Min(value = 0L, message = "{roomFee.min}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long penalty;

    @Schema(description = "货币单位")
    private String currencyUnit;

}