package info.qizhi.aflower.module.pms.api.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 第四步 完成结账 Request VO")
@Data
public class FinishCheckOutAccountReqDTO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notblank}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notblank}")
    private String hcode;

    @Schema(description = "订单类型,general：普通订单 join：联房订单 group：团队订单")
    private String orderType;

    @Schema(description = "付款科目代码")
    private String subCode;

    @Schema(description = "退房结账列表,列表中是需要退房的账单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    @NotEmpty(message = "{checkoutOrderTogethers.notempty}")
    private List<OrderTogether> orderTogethers;

    @Data
    public static class OrderTogether {

        @Schema(description = "订单号, 如果是团队主账时，订单号为团队代码")
        @NotEmpty(message = "{orderNumber.notempty}")
        private String no;

        @Schema(description = "宾客代码,如果是团队主账时，宾客代码为团队代码", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{togetherCode.notempty}")
        private String togetherCode;

        @Schema(description = "是否团队主账,0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{isTeam.notempty}")
        private String isTeam;

        @Schema(description = "是否主客单,0:否 1:是", requiredMode = Schema.RequiredMode.REQUIRED)
        @NotEmpty(message = "{isMain.notempty}")
        private String isMain;

    }

}