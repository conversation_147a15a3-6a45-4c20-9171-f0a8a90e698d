package info.qizhi.aflower.module.pms.api.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Schema(description = "biz - 完成预授权 Response VO")
@Data
public class ConfirmCheckOutPreAuthRespDTO {

    @Schema(description = "宾客代码")
    private String togetherCode;

    @Schema(description = "账务号")
    private String accNo;

    @Schema(description = "状态, open:预收 closed:完成")
    private String state;

    @Schema(description = "付款科目")
    private String subCode;

    @Schema(description = "授权类型")
    private String subName;

    @Schema(description = "预授权金额")
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long preAuth;

    @Schema(description = "已确认金额")
    //@JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

}