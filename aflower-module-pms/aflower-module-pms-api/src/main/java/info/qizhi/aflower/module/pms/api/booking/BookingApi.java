package info.qizhi.aflower.module.pms.api.booking;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.pms.api.booking.dto.*;
import info.qizhi.aflower.module.pms.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = ApiConstants.NAME) // TODO 芋艿：fallbackFactory =
@Tag(name = "RPC 服务 - 预订单")
public interface BookingApi {

    String PREFIX = ApiConstants.PREFIX + "/book";

    @GetMapping(PREFIX + "/list")
    @Operation(summary = "获得今日预抵预订单(普通预订团队预订)列表")
    CommonResult<List<BookTodayRespDTO>> getTodayBookList(@Valid @SpringQueryMap BookListReqDTO reqVO);

    @PostMapping(PREFIX + "/open/creat")
    @Operation(summary = "hiii创建预订单(普通预订团队预订)")
    CommonResult<OtaBookOpenRespDTO> createBook(@Valid @RequestBody OtaBookOpenReqDTO createReqVO);

    @PutMapping(PREFIX + "/open/update")
    @Operation(summary = "hiii更新预订单(普通预订)")
    CommonResult<OtaBookOpenRespDTO> updateGeneralBook(@Valid @RequestBody OtaBookOpenReqDTO createReqVO);

    @PutMapping(PREFIX + "/open/cancel")
    @Operation(summary = "hiii取消订单")
    CommonResult<OtaBookCancelOpenRespDTO> cancleBook(@Valid @RequestBody OtaBookCancelOpenReqDTO reqVO);

    @GetMapping(PREFIX + "/page")
    @Operation(summary = "获得预订单分页")
    CommonResult<PageResult<BookPageRespDTO>> getBookPage(@Valid @SpringQueryMap BookPageReqDTO pageReqVO);

    @GetMapping(PREFIX+ "/get/roomtype")
    @Operation(summary = "获得可预订的房型列表(房型、售价、可售数、可超数、预计间数、排房信息、赠早数)")
    CommonResult<BookRoomTypePriceTwoRespDTO> getBookRoomTypeList(@Valid @SpringQueryMap BookReqDTO reqVO);

}
