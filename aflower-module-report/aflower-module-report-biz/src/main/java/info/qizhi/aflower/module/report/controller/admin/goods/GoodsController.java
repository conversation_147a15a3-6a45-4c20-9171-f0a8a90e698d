package info.qizhi.aflower.module.report.controller.admin.goods;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.goodsselldetail.GoodsSellDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.goodsselldetail.GoodsSellDetailReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 商品销售明细报表")
@RestController
@RequestMapping("s/report/good")
@Validated
public class GoodsController {

    @Resource
    ReportDetailApi reportDetailApi;

    @Deprecated
    @GetMapping("/get")
    @Operation(summary = "获得商品销售明细报表")
    //@PreAuthorize("@ss.hasPermission('report:goods:query')")
    public CommonResult<GoodsSellDetailReportRespDTO> getGoodsSellDetail(@Valid GoodsSellDetailReqDTO reqVO) {
        return reportDetailApi.getGoodsSellDetail(reqVO);
    }
}
