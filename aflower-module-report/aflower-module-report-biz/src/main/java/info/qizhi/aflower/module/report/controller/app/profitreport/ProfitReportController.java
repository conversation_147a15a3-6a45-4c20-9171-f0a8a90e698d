/*
package info.qizhi.aflower.module.report.controller.app.profitreport;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.managerdaily.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.managerdaily.vo.ManagerDailyRespVO;
import info.qizhi.aflower.module.report.controller.app.profitreport.vo.DayOnDayCountVo;
import info.qizhi.aflower.module.report.controller.app.profitreport.vo.RevenueCountVo;
import info.qizhi.aflower.module.report.service.profitreport.ProfitReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 收益早报")
@RestController("appProfitReportController")
@RequestMapping("/report/profit-report")
@Validated
public class ProfitReportController {

    @Resource
    private ProfitReportService profitReportService;


    @GetMapping("/get-guest-room-kpi")
    @Operation(summary = "获得每日客房指标报")
    @PreAuthorize("@ss.hasPermission('report:profit-report:query')")
    public CommonResult<ManagerDailyRespVO> getGuestRoomKpiDaily(@Valid @SpringQueryMap ManagerDailyReqVO reqVO) {
        ManagerDailyRespVO managerDailyRespVO= profitReportService.getGuestRoomKpiDaily(reqVO);
        return success(managerDailyRespVO);
    }

    @GetMapping("/get-management-kpi")
    @Operation(summary = "获得每日经营指标报")
    @PreAuthorize("@ss.hasPermission('report:management:query')")
    public CommonResult<RevenueCountVo> getManagementKpiDaily(@Valid @SpringQueryMap ManagerDailyReqVO reqVO) {
        RevenueCountVo revenueCountVo= profitReportService.getManagementKpiDaily(reqVO);
        return success(revenueCountVo);
    }

    @GetMapping("/get-day-on-day")
    @Operation(summary = "获得环比数据")
    @PreAuthorize("@ss.hasPermission('report:management:query')")
    public CommonResult<DayOnDayCountVo> getDayOnDay (@Valid @SpringQueryMap ManagerDailyReqVO reqVO) {
        DayOnDayCountVo dayOnDayCountVo= profitReportService.getDayOnDay(reqVO);
        return success(dayOnDayCountVo);
    }



}
*/
