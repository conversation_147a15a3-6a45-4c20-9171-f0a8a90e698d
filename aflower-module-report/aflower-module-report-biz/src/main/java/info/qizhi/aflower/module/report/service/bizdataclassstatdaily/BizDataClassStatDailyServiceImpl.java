package info.qizhi.aflower.module.report.service.bizdataclassstatdaily;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdataclassstatdaily.BizDataClassStatDailyDO;
import info.qizhi.aflower.module.report.dal.mysql.bizdataclassstatdaily.BizDataClassStatDailyMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;


/**
 * 每日经营数据分类统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDataClassStatDailyServiceImpl implements BizDataClassStatDailyService {

    @Resource
    private BizDataClassStatDailyMapper bizDataClassStatDailyMapper;

    @Override
    public Long createBizDataClassStatDaily(BizDataClassStatDailySaveReqVO createReqVO) {
        // 插入
        BizDataClassStatDailyDO bizDataClassStatDaily = BeanUtils.toBean(createReqVO, BizDataClassStatDailyDO.class);
        bizDataClassStatDailyMapper.insert(bizDataClassStatDaily);
        // 返回
        return bizDataClassStatDaily.getId();
    }

    @Override
    public Boolean createBizDataClassStatDailies(List<BizDataClassStatDailySaveReqVO> bizDataClassStatDailySaveReqVOList) {
        List<BizDataClassStatDailyDO> bizDataClassStatDailyDOList = BeanUtils.toBean(bizDataClassStatDailySaveReqVOList, BizDataClassStatDailyDO.class);
        if (CollUtil.isNotEmpty(bizDataClassStatDailyDOList)) {
            return bizDataClassStatDailyMapper.insertBatch(bizDataClassStatDailyDOList);
        }
        return true;
    }

    @Override
    public BizDataClassStatDailyDO getBizDataClassStatDaily(Long id) {
        return bizDataClassStatDailyMapper.selectById(id);
    }

    @Override
    public List<BizDataClassStatDailyDO> getBizDataClassStatDailyList(BizDataClassStatDailyReqVO reqVO) {
        return bizDataClassStatDailyMapper.selectList(reqVO);
    }

    @Override
    public List<BizDataClassStatDailyDO> getYearlyBizDataClassStatDailyList(BizDataClassStatDailyReqVO reqVO) {
        return bizDataClassStatDailyMapper.selectYearList(reqVO);
    }

}