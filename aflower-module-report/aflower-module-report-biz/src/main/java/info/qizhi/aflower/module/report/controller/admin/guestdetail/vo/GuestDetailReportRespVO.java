package info.qizhi.aflower.module.report.controller.admin.guestdetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 宾客明细报表[固化] Response VO")
@Data
public class GuestDetailReportRespVO {

    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "营业日", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", example = "报表操作员")
    private String operator;

    @Schema(description = "宾客明细报表列表")
    private List<GuestDetailRespVO> list;

}
