package info.qizhi.aflower.module.report.controller.admin.member;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 会员相关报表")
@RestController
@RequestMapping("/report/member")
@Validated
public class MemberController {

    @Resource
    ReportDetailApi reportDetailApi;

    @GetMapping("/card")
    @Operation(summary = "获得会员卡销售明细报表")
    //@PreAuthorize("@ss.hasPermission('report:member:query')")
    public CommonResult<MemberCardSellDetailReportRespDTO> getMemberCardDetail(@Valid MemberCardSellDetailReqDTO reqVO) {
        return reportDetailApi.getMemberCardSellDetailReport(reqVO);
    }


    @GetMapping("/recharge")
    @Operation(summary = "获得会员充值&支付明细报表")
    //@PreAuthorize("@ss.hasPermission('report:member:query')")
    public CommonResult<MemberRechargeDetailReportRespDTO> getMemberRechargeDetail(@Valid MemberRechargeDetailReqDTO reqVO) {
        return reportDetailApi.getMemberRechargeDetail(reqVO);
    }
}
