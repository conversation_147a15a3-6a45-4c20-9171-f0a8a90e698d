package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.BigDecimalSerializer;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 经营数据分类统计 Response VO")
@Data
public class ClassificationBusinessData {
    @Schema(description = "子类类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String classificationStatistics;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal occ;

    @Schema(description = "总费用", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

}
