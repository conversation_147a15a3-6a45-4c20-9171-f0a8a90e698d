package info.qizhi.aflower.module.report.dal.dataobject.channelstatdaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日渠道统计报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_channel_stat_daily")
@KeySequence("rp_channel_stat_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelStatDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 星期
     */
    private Integer week;
    /**
     * 渠道代码
     */
    private String channelCode;
    /**
     * 渠道名称
     */
    private String channelName;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 平均房价
     */
    private Long avgRoomFee;
    /**
     * 出租率
     */
    private BigDecimal occ;
    /**
     * 间夜占比
     */
    private BigDecimal nightProportion;

}