package info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH;

/**
 * 经理综合日报表(固化) DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataClassStatMonthVO{
    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "这月总间夜数")
    private BigDecimal nightNum;

    @Schema(description = "这月总房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "这月平均房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH)
    private LocalDate month;

    @Schema(description = "分类数据", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataClassStatMonth> dataClassStatMonthList;

}