package info.qizhi.aflower.module.report.controller.admin.guestdetail.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 宾客明细报表[固化]新增/修改 Request VO")
@Data
public class GuestDetailSaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "联系人")
    private String contact;

    @Schema(description = "客人姓名", example = "赵六")
    private String name;

    @Schema(description = "房间代码")
    @JsonProperty("rCode")
    private String rCode;

    @Schema(description = "房号")
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "团队代码")
    private String teamCode;

    @Schema(description = "团队名称", example = "王五")
    private String teamName;

    @Schema(description = "入住时间")
    private LocalDateTime checkinTime;

    @Schema(description = "离店时间")
    private LocalDateTime checkoutTime;

    @Schema(description = "今日发生消费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todayConsume.notnull}")
    private Long todayConsume;

    @Schema(description = "今日发生付款", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todayPay.notnull}")
    private Long todayPay;

    @Schema(description = "宾客账", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{customerBill.notnull}")
    private Long customerBill;

    @Schema(description = "订单状态")
    private String state;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "是否现付账")
    private String isCash;

    @Schema(description = "账务类型 general:订单 book:预订单 group:团队主单")
    private String accType;
}