package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 经营数据分类统计 Response VO")
@Data
public class BusinessData {

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate bizDate;

    @Schema(description = "分类类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String cateGory;

    @Schema(description = "子类类别", requiredMode = Schema.RequiredMode.REQUIRED)
    private String classificationStatistics;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "总费用", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

}
