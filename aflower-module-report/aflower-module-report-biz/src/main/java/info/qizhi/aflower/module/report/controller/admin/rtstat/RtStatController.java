package info.qizhi.aflower.module.report.controller.admin.rtstat;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatRespVO;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.rtstat.RtStatDO;
import info.qizhi.aflower.module.report.service.rtstat.RtStatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 房型统计报")
@RestController
@RequestMapping("/report/rt-stat")
@Validated
public class RtStatController {

    @Resource
    private RtStatService rtStatService;

    @PostMapping("/create")
    @Operation(summary = "创建房型统计报")
    @PreAuthorize("@ss.hasPermission('rp:rt-stat:create')")
    public CommonResult<Integer> createRtStat(@Valid @RequestBody RtStatSaveReqVO createReqVO) {
        return success(rtStatService.createRtStat(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新房型统计报")
    @PreAuthorize("@ss.hasPermission('rp:rt-stat:update')")
    public CommonResult<Boolean> updateRtStat(@Valid @RequestBody RtStatSaveReqVO updateReqVO) {
        rtStatService.updateRtStat(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除房型统计报")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('rp:rt-stat:delete')")
    public CommonResult<Boolean> deleteRtStat(@RequestParam("id") Integer id) {
        rtStatService.deleteRtStat(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得房型统计报")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('rp:rt-stat:query')")
    public CommonResult<RtStatRespVO> getRtStat(@RequestParam("id") Integer id) {
        RtStatDO rtStat = rtStatService.getRtStat(id);
        return success(BeanUtils.toBean(rtStat, RtStatRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得房型统计报分页")
    //@PreAuthorize("@ss.hasPermission('rp:rt-stat:query')")
    public CommonResult<PageResult<RtStatRespVO>> getRtStatPage(@Valid RtStatPageReqVO pageReqVO) {
        PageResult<RtStatDO> pageResult = rtStatService.getRtStatPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RtStatRespVO.class));
    }


}