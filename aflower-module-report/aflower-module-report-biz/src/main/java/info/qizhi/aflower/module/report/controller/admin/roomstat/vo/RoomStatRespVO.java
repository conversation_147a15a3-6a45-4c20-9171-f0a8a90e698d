package info.qizhi.aflower.module.report.controller.admin.roomstat.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 客房营收统计报表(固化) Request VO")
@Data
public class RoomStatRespVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2221")
    private Long id;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtName;

    @Schema(description = "楼栋代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String buildingCode;

    @Schema(description = "楼栋名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String buildingName;

    @Schema(description = "楼层代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String floorCode;

    @Schema(description = "楼层名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String floorName;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "客房数")
    private Integer roomNum;

    @Schema(description = "间夜数")
    private BigDecimal nightNum;

    @Schema(description = "出租率")
    private BigDecimal occ;

    @Schema(description = "房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "平均房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "其他消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherFee;

    @Schema(description = "RevPar")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;

    @Schema(description = "自用房")
    private Integer selfNum;

    @Schema(description = "维修房")
    private Integer repairNum;

}
