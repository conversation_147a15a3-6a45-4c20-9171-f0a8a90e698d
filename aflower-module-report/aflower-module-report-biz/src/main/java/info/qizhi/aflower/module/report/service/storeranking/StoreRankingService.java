package info.qizhi.aflower.module.report.service.storeranking;

import info.qizhi.aflower.module.report.controller.admin.storeranking.vo.StoreRespVO;
import info.qizhi.aflower.module.report.controller.admin.storeranking.vo.StoreTotalBizKpiDailyRespVO;
import jakarta.validation.Valid;

import java.util.List;

public interface StoreRankingService {

    /**
     * 门店排名
     * @param reqVO
     * @return
     */
    List<StoreTotalBizKpiDailyRespVO> getStoreRankingDaily(@Valid StoreRespVO reqVO);
}
