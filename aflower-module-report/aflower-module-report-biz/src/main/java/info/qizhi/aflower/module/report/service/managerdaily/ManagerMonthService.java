package info.qizhi.aflower.module.report.service.managerdaily;


import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerMonthSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth.ManagerMonthReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth.ManagerMonthReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.managermonth.ManagerMonthDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 经理综合日报表(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface ManagerMonthService {

    /**
     * 创建月报表(固化)
     * @param reqVO
     * @return
     */
    void createManagerMonthReport(@Valid ManagerMonthSaveReqVO reqVO);

    /**
     * 获得月报表(固化)
     * @param reqVO
     * @return
     */
    ManagerMonthReportRespVO getManagerMonthReport(@Valid ManagerMonthReqVO reqVO);

    /**
     * 获得经理报表(月报)(固化)(数据对比)
     * @param reqVO
     * @return
     */
    List<ManagerMonthDO> getManagerMonthDataList(@Valid ManagerMonthReqVO reqVO);
}