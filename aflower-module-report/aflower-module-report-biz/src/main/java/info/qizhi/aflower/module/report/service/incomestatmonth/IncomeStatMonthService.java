package info.qizhi.aflower.module.report.service.incomestatmonth;

import info.qizhi.aflower.module.report.controller.admin.incomestatmonth.vo.IncomeStatMonthSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatmonth.vo.IncomeStatReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatmonth.IncomeStatMonthDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 每日收入统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface IncomeStatMonthService {

    /**
     * 批量创建每月收入统计报
     *
     * @param incomeStatDailySaveReqVOList
     * @return 成功true 失败false
     */
    Boolean createIncomeStatMonths(@Valid List<IncomeStatMonthSaveReqVO> incomeStatDailySaveReqVOList);
    /**
     * 获得本年至去年累计收入统计报集合
     *
     * @return 每日收入统计报
     */
    List<IncomeStatMonthDO> getIncomeStatList(IncomeStatReqVO reqVO);


}