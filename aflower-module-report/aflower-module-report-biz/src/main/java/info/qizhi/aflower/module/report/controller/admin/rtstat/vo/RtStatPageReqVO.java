package info.qizhi.aflower.module.report.controller.admin.rtstat.vo;


import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 房型统计报分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class RtStatPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "营业日")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate[] bizDate;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房型名称", example = "芋艿")
    private String rtName;

    @Schema(description = "本日间夜数")
    private BigDecimal todayNightNum;

    @Schema(description = "本月间夜数")
    private BigDecimal curMonthNightNum;

    @Schema(description = "本年间夜数")
    private BigDecimal curYearNightNum;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}