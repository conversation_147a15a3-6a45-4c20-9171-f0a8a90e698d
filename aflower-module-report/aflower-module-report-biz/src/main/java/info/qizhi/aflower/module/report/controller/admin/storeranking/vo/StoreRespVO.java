package info.qizhi.aflower.module.report.controller.admin.storeranking.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "小程序 - 每日门店排名前端参数 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StoreRespVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    // 定义排序字段枚举
    public enum SortField {
        TOTAL_FEE,
        NIGHT_NUM,
        OCC,
        AVG_ROOM_FEE,
        REV_PAR
    }

    @Schema(description = "排序字段", requiredMode = Schema.RequiredMode.REQUIRED)
    private SortField sortField;

}
