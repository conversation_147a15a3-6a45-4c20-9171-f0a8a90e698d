package info.qizhi.aflower.module.report.service.rtstat;


import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.rtstat.RtStatDO;
import info.qizhi.aflower.module.report.dal.mysql.rtstat.RtStatMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.RT_STAT_NOT_EXISTS;


/**
 * 房型统计报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RtStatServiceImpl implements RtStatService {

    @Resource
    private RtStatMapper rtStatMapper;

    @Override
    public Integer createRtStat(RtStatSaveReqVO createReqVO) {
        // 插入
        RtStatDO rtStat = BeanUtils.toBean(createReqVO, RtStatDO.class);
        rtStatMapper.insert(rtStat);
        // 返回
        return rtStat.getId();
    }

    @Override
    public void updateRtStat(RtStatSaveReqVO updateReqVO) {
        // 校验存在
        validateRtStatExists(updateReqVO.getId());
        // 更新
        RtStatDO updateObj = BeanUtils.toBean(updateReqVO, RtStatDO.class);
        rtStatMapper.updateById(updateObj);
    }

    @Override
    public void deleteRtStat(Integer id) {
        // 校验存在
        validateRtStatExists(id);
        // 删除
        rtStatMapper.deleteById(id);
    }

    private void validateRtStatExists(Integer id) {
        if (rtStatMapper.selectById(id) == null) {
            throw exception(RT_STAT_NOT_EXISTS);
        }
    }

    @Override
    public RtStatDO getRtStat(Integer id) {
        return rtStatMapper.selectById(id);
    }

    @Override
    public PageResult<RtStatDO> getRtStatPage(RtStatPageReqVO pageReqVO) {
        return rtStatMapper.selectPage(pageReqVO);
    }

}