package info.qizhi.aflower.module.report.controller.admin.zeroroomrate;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReqVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateSaveReqVO;
import info.qizhi.aflower.module.report.service.zeroroomrate.ZeroRoomRateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 零房费&负房费报表")
@RestController
@RequestMapping("/report/zero-room-rate")
@Validated
public class ZeroRoomRateController {
    @Resource
    private ZeroRoomRateService zeroRoomRateService;

    @PostMapping("/create")
    @Operation(summary = "创建零房费&负房费报表")
    @PreAuthorize("@ss.hasPermission('report:zero-room-rate:create')")
    public CommonResult<Long> createZeroRoomRate(@Valid @RequestBody ZeroRoomRateSaveReqVO createReqVO) {
        return success(zeroRoomRateService.createZeroRoomRate(createReqVO));
    }

    @GetMapping("/list")
    @Operation(summary = "获得零房费&负房费报表")
    @PreAuthorize("@ss.hasPermission('report:zero-room-rate:list')")
    public CommonResult<ZeroRoomRateReportRespVO> getZeroRoomRate(@Valid ZeroRoomRateReqVO reqVO) {
        return success(zeroRoomRateService.getZeroRoomRate(reqVO));
    }
}
