package info.qizhi.aflower.module.report.service.storeranking;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.storeranking.vo.StoreRespVO;
import info.qizhi.aflower.module.report.controller.admin.storeranking.vo.StoreTotalBizKpiDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.totalbizkpidaily.vo.TotalBizKpiDailyRespVO;
import info.qizhi.aflower.module.report.dal.dataobject.totalbizkpidaily.TotalBizKpiDailyDO;
import info.qizhi.aflower.module.report.service.totalbizkpidaily.TotalBizKpiDailyService;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Validated
public class StoreRankingServiceImpl implements StoreRankingService {

    @Resource
    private TotalBizKpiDailyService totalBizKpiDailyService;

    @Resource
    private MerchantApi merchantApi;

    @Override
    public List<StoreTotalBizKpiDailyRespVO> getStoreRankingDaily(StoreRespVO reqVO) {
        List<TotalBizKpiDailyDO> storeTotalBizKpiDaily = totalBizKpiDailyService.getStoreTotalBizKpiDaily(new TotalBizKpiDailyRespVO().setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()));
        //获得所有的门店列表
        List<MerchantRespDTO> merchantRespDTOList = merchantApi.getMerchantList(reqVO.getGcode()).getData();
        Map<String, MerchantRespDTO> merchantRespMap = CollectionUtils.convertMap(merchantRespDTOList, MerchantRespDTO::getGcode);


        List<TotalBizKpiDailyDO> collect=new ArrayList<>();

        if (CollUtil.isNotEmpty(storeTotalBizKpiDaily)) {
            collect = storeTotalBizKpiDaily.stream()
                    .sorted((o1, o2) -> switch (reqVO.getSortField()) {
                        case TOTAL_FEE -> o2.getTotalFee().compareTo(o1.getTotalFee());
                        case NIGHT_NUM -> o2.getNightNum().compareTo(o1.getNightNum());
                        case OCC -> o2.getOcc().compareTo(o1.getOcc());
                        case AVG_ROOM_FEE -> o2.getAvgRoomFee().compareTo(o1.getAvgRoomFee());
                        case REV_PAR -> o2.getRevPar().compareTo(o1.getRevPar());
                        default -> 0;
                    })
                    .collect(Collectors.toList());
        }

        List<StoreTotalBizKpiDailyRespVO> respVOS = BeanUtils.toBean(collect, StoreTotalBizKpiDailyRespVO.class);
        for (StoreTotalBizKpiDailyRespVO respVO : respVOS) {
            if(merchantRespMap.containsKey(respVO.getHcode())){
                respVO.setHname(merchantRespMap.get(respVO.getHcode()).getHname());
            }
        }
        return respVOS;
    }
}
