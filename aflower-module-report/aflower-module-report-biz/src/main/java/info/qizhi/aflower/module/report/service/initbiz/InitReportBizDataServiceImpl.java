package info.qizhi.aflower.module.report.service.initbiz;

import info.qizhi.aflower.module.report.dal.mysql.initbiz.InitReportBizDataMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * @Author: TY
 * @CreateTime: 2024-08-24
 * @Description: 初始化报表数据
 * @Version: 1.0
 */
@Service
@Validated
public class InitReportBizDataServiceImpl implements InitReportBizDataService{

    @Resource
    private InitReportBizDataMapper initReportBizDataMapper;

    @Override
    public void initReportBizData(String gcode,String hcode) {
        initReportBizDataMapper.deleteBizDaily(gcode,hcode);
        initReportBizDataMapper.deleteArDetail(gcode,hcode);
        initReportBizDataMapper.deleteBizDataClassStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteChannelStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteCheckInTypeStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteConsumeDetail(gcode,hcode);
        initReportBizDataMapper.deleteGoodsSellDetail(gcode,hcode);
        initReportBizDataMapper.deleteGuestDetail(gcode,hcode);
        initReportBizDataMapper.deleteGuestSrcStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteIncomeStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteManagerDaily(gcode,hcode);
        initReportBizDataMapper.deleteMemberCardSellDetail(gcode,hcode);
        initReportBizDataMapper.deleteMemberRechargeDetail(gcode,hcode);
        initReportBizDataMapper.deleteNightAudiRoomType(gcode,hcode);
        initReportBizDataMapper.deletePayDetail(gcode,hcode);
        initReportBizDataMapper.deleteRoomRateDaily(gcode,hcode);
        initReportBizDataMapper.deleteRoomTypeStatDaily(gcode,hcode);
        initReportBizDataMapper.deleteRtStat(gcode,hcode);
        initReportBizDataMapper.deleteSettleDetail(gcode,hcode);
        initReportBizDataMapper.deleteTotalBizKpiDaily(gcode,hcode);
        initReportBizDataMapper.deleteZeroRoomRate(gcode,hcode);
        initReportBizDataMapper.deleteHotelCheckInDetail(gcode,hcode);
        initReportBizDataMapper.deleterBizDataClassStatMonth(gcode,hcode);
        initReportBizDataMapper.deleterManagerMonth(gcode,hcode);
        initReportBizDataMapper.deleteRpRoomStat(gcode,hcode);
        initReportBizDataMapper.deleteRpIncomeStatMonth(gcode,hcode);
    }
}
