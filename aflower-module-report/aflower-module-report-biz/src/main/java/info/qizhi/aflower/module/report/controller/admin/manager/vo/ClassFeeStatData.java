package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 经营数据分类统计 Response VO")
@Data
public class ClassFeeStatData {

    @Schema(description = "统计代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statCode;

    @Schema(description = "统计名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statName;

    @Schema(description = "本日房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayRoomFeeOrAvgRoomFee;

    @Schema(description = "本月房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long monthRoomFeeOrAvgRoomFee;

    @Schema(description = "去年同期房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long lastYearSamePeriodRoomFeeOrAvgRoomFee;

    @Schema(description = "上月房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long lastMonthRoomFeeOrAvgRoomFee;

    @Schema(description = "本年房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long yearRoomFeeOrAvgRoomFee;

    @Schema(description = "上年房费或者平均房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long lastYearRoomFeeOrAvgRoomFee;

}
