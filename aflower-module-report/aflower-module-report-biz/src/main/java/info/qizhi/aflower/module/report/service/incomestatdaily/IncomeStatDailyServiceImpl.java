package info.qizhi.aflower.module.report.service.incomestatdaily;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatdaily.IncomeStatDailyDO;
import info.qizhi.aflower.module.report.dal.mysql.incomestatdaily.IncomeStatDailyMapper;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerDailyService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.INCOME_STAT_DAILY_NOT_EXISTS;

/**
 * 每日收入统计报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class IncomeStatDailyServiceImpl implements IncomeStatDailyService {

    @Resource
    private IncomeStatDailyMapper incomeStatDailyMapper;
    @Resource
    @Lazy
    private ManagerDailyService managerDailyService;

    @Override
    public Long createIncomeStatDaily(IncomeStatDailySaveReqVO createReqVO) {
        // 插入
        IncomeStatDailyDO incomeStatDaily = BeanUtils.toBean(createReqVO, IncomeStatDailyDO.class);
        incomeStatDailyMapper.insert(incomeStatDaily);
        // 返回
        return incomeStatDaily.getId();
    }

    @Override
    public Boolean createIncomeStatDailies(List<IncomeStatDailySaveReqVO> incomeStatDailySaveReqVOList) {
        List<IncomeStatDailyDO> incomeStatDailyDOList = BeanUtils.toBean(incomeStatDailySaveReqVOList, IncomeStatDailyDO.class);
        if (CollUtil.isNotEmpty(incomeStatDailyDOList)) {
            return incomeStatDailyMapper.insertBatch(incomeStatDailyDOList);
        }
        return true;
    }

    private void validateIncomeStatDailyExists(Long id) {
        if (incomeStatDailyMapper.selectById(id) == null) {
            throw exception(INCOME_STAT_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public IncomeStatDailyDO getIncomeStatDaily(Long id) {
        return incomeStatDailyMapper.selectById(id);
    }

    @Override
    public List<IncomeStatDailyDO> getIncomeStatDailyList(IncomeStatDailyReqVO reqVO) {
        return incomeStatDailyMapper.selectList(reqVO);
    }

    @Override
    public List<IncomeStatDailyDO> getIncomeStatDailyListByYear(IncomeStatDailyReqVO reqVO) {
        return incomeStatDailyMapper.selectYearList(reqVO);
    }


}