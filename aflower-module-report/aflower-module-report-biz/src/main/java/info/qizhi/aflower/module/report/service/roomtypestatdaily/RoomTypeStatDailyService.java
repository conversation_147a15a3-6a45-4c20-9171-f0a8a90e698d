package info.qizhi.aflower.module.report.service.roomtypestatdaily;

import java.util.*;
import jakarta.validation.*;
import info.qizhi.aflower.module.report.controller.admin.roomtypestatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.roomtypestatdaily.RoomTypeStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * 每日房型统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface RoomTypeStatDailyService {

    /**
     * 创建每日房型统计报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRoomTypeStatDaily(@Valid RoomTypeStatDailySaveReqVO createReqVO);

    /**
     * 更新每日房型统计报
     *
     * @param updateReqVO 更新信息
     */
    void updateRoomTypeStatDaily(@Valid RoomTypeStatDailySaveReqVO updateReqVO);

    /**
     * 删除每日房型统计报
     *
     * @param id 编号
     */
    void deleteRoomTypeStatDaily(Long id);

    /**
     * 获得每日房型统计报
     *
     * @param id 编号
     * @return 每日房型统计报
     */
    RoomTypeStatDailyDO getRoomTypeStatDaily(Long id);

    /**
     * 获得每日房型统计报分页
     *
     * @param pageReqVO 分页查询
     * @return 每日房型统计报分页
     */
    PageResult<RoomTypeStatDailyDO> getRoomTypeStatDailyPage(RoomTypeStatDailyPageReqVO pageReqVO);

}