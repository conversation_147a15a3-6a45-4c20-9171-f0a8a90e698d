package info.qizhi.aflower.module.report.service.channelstatdaily;

import jakarta.validation.*;
import info.qizhi.aflower.module.report.controller.admin.channelstatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.channelstatdaily.ChannelStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;

/**
 * 每日渠道统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface ChannelStatDailyService {

    /**
     * 创建每日渠道统计报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createChannelStatDaily(@Valid ChannelStatDailySaveReqVO createReqVO);

    /**
     * 更新每日渠道统计报
     *
     * @param updateReqVO 更新信息
     */
    void updateChannelStatDaily(@Valid ChannelStatDailySaveReqVO updateReqVO);

    /**
     * 删除每日渠道统计报
     *
     * @param id 编号
     */
    void deleteChannelStatDaily(Long id);

    /**
     * 获得每日渠道统计报
     *
     * @param id 编号
     * @return 每日渠道统计报
     */
    ChannelStatDailyDO getChannelStatDaily(Long id);

    /**
     * 获得每日渠道统计报分页
     *
     * @param pageReqVO 分页查询
     * @return 每日渠道统计报分页
     */
    PageResult<ChannelStatDailyDO> getChannelStatDailyPage(ChannelStatDailyPageReqVO pageReqVO);

}