package info.qizhi.aflower.module.report.controller.admin.rtstat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 房型统计报新增/修改 Request VO")
@Data
public class RtStatSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9539")
    private Integer id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{rtCode.notempty}")
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "{rtName.notempty}")
    private String rtName;

    @Schema(description = "本日间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todayNightNum.notnull}")
    private BigDecimal todayNightNum;

    @Schema(description = "本月间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{curMonthNightNum.notnull}")
    private BigDecimal curMonthNightNum;

    @Schema(description = "本年间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{curYearNightNum.notnull}")
    private BigDecimal curYearNightNum;

}