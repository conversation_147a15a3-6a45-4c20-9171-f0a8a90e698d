package info.qizhi.aflower.module.report.controller.admin.manager;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReportMonthRespVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerReportRespVO;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerDailyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 经理综合日报表(固化)")
@RestController
@RequestMapping("/report/manager-daily")
@Validated
public class ManagerDailyController {

    @Resource
    private ManagerDailyService managerDailyService;

    @GetMapping("/get")
    @Operation(summary = "获得经理日报(固化)")
    @PreAuthorize("@ss.hasPermission('report:manager:get')")
    public CommonResult<ManagerDailyReportRespVO> getManagerDaily(@Valid ManagerDailyReqVO reqVO) {
        ManagerDailyReportRespVO managerDaily = managerDailyService.getManagerDailyReport(reqVO);
        return success(managerDaily);
    }

    @GetMapping("/list")
    @Operation(summary = "获得经理月报详情列表")
    @PreAuthorize("@ss.hasPermission('report:manager:list')")
    public CommonResult<ManagerDailyReportMonthRespVO> getManagerMonthDetailList(@Valid ManagerDailyReqVO reqVO) {
        ManagerDailyReportMonthRespVO monthRespVO = managerDailyService.getManagerMonthDetailList(reqVO);
        return success(monthRespVO);
    }

    @GetMapping("/get/daily")
    @Operation(summary = "获得经理报表（单日）")
    @PreAuthorize("@ss.hasPermission('report:manager:list')")
    public CommonResult<ManagerReportRespVO> getManagerReport(@Valid ManagerDailyReqVO reqVO) {
        ManagerReportRespVO monthRespVO = managerDailyService.getManagerReport(reqVO);
        return success(monthRespVO);
    }


  /*  @GetMapping("/month/list")
    @Operation(summary = "获得经理月报表(固化)")
    @PreAuthorize("@ss.hasPermission('report:manager:list')")
    public CommonResult<ManagerMonthReportRespVO> getManagerMonthList(@Valid ManagerMonthReqVO reqVO) {
        ManagerMonthReportRespVO monthRespVO = managerDailyService.getManagerMonthReport(reqVO);
        return success(monthRespVO);
    }*/

}