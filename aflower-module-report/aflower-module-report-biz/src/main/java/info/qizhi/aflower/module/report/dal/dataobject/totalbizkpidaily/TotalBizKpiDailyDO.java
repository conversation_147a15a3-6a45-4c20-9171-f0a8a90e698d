package info.qizhi.aflower.module.report.dal.dataobject.totalbizkpidaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日总营业指标报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_total_biz_kpi_daily")
@KeySequence("rp_total_biz_kpi_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TotalBizKpiDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 星期;周一:1....周日:7
     */
    private Integer week;
    /**
     * 客房数
     */
    private Integer roomNum;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 免房数
     */
    private Integer freeNum;
    /**
     * 自用数
     */
    private Integer selfNum;
    /**
     * 维修房数
     */
    private Integer repairNum;
    /**
     * 过夜数
     */
    private Integer overnightNum;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 平均房价
     */
    private Long avgRoomFee;
    /**
     * RevPar
     */
    private BigDecimal revPar;
    /**
     * 出租率
     */
    private BigDecimal occ;
    /**
     * 出租率（扣维修房）
     */
    private BigDecimal occNoRepair;
    /**
     * 过夜出租率
     */
    private BigDecimal overnightOcc;
    /**
     * 小商品
     */
    private Long goodsFee;
    /**
     * 会员卡
     */
    private Long memberCardFee;
    /**
     * 其他消费
     */
    private Long otherFee;
    /**
     * 会议室
     */
    private Long meetingRoomFee;
    /**
     * 赔偿费
     */
    private Long indemnityFee;
    /**
     * 门店收入
     */
    private Long totalFee;
    /**
     * 会员充值
     */
    private Long rechargeFee;
    /**
     * 综合RevPar
     */
    private BigDecimal compRevPar;

}