package info.qizhi.aflower.module.report.service.roomstat;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.room.RoomApi;
import info.qizhi.aflower.module.pms.api.room.dto.RoomRespDTO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.roomstat.RoomStatDO;
import info.qizhi.aflower.module.report.dal.mysql.roomstat.RoomStatMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Validated
public class RoomStatServiceImpl implements RoomStatService{
    @Resource
    private RoomStatMapper roomStatMapper;
    @Resource
    private RoomApi roomApi;
    @Resource
    private MerchantApi merchantApi;

    @Override
    public void createRoomStat(RoomStatSaveReqVO reqVO) {
        roomStatMapper.insert(BeanUtils.toBean(reqVO, RoomStatDO.class));
    }

    @Override
    public boolean batchCreateRoomStat(List<RoomStatSaveReqVO> reqVOList) {
        if (CollUtil.isNotEmpty(reqVOList)) {
            List<RoomStatDO> roomStatDOList = BeanUtils.toBean(reqVOList, RoomStatDO.class);
            return roomStatMapper.insertBatch(roomStatDOList);
        }
        return true;
    }

    @Override
    public RoomStatReportRespVO getRoomStatList(RoomStatReqVO reqVO) {
        RoomStatReportRespVO roomStatReportRespVO = new RoomStatReportRespVO();
        // 填充门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        roomStatReportRespVO.setHname(merchant.getHname())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setLastSelectTime(LocalDateTime.now());
        List<RoomStatDO> roomStatDOList = roomStatMapper.selectList(reqVO);

        Map<String, List<RoomStatDO>> roomStatDOMap = CollectionUtils.convertMultiMap(roomStatDOList, RoomStatDO::getRCode);

        // 获得房间列表
        List<RoomRespDTO> roomList = roomApi.getRoomList2(reqVO.getGcode(), reqVO.getHcode()).getData();
        //Map<String, RoomRespDTO> roomMap = CollectionUtils.convertMap(roomList, RoomRespDTO::getRCode);


        List<RoomStatRespVO> list = new ArrayList<>();
        int roomNum = reqVO.getEndDate().getDayOfYear() - reqVO.getStartDate().getDayOfYear() + 1;
        for (RoomRespDTO room : roomList) {
            List<RoomStatDO> roomStatDOS = roomStatDOMap.getOrDefault(room.getRCode(), new ArrayList<>());

            RoomStatRespVO roomStatRespVO =new RoomStatRespVO();
            BigDecimal nightNum = roomStatDOS.stream().map(RoomStatDO::getNightNum).reduce(BigDecimal.ZERO, BigDecimal::add);

            roomStatRespVO.setRCode(room.getRCode());
            roomStatRespVO.setRNo(room.getRNo());
            roomStatRespVO.setRtCode(room.getRtCode());
            roomStatRespVO.setRtName(room.getRtName());
            roomStatRespVO.setBuildingCode(room.getBuildingCode());
            roomStatRespVO.setBuildingName(room.getBuildingName());
            roomStatRespVO.setFloorCode(room.getFloorCode());
            roomStatRespVO.setFloorName(room.getFloorName());
            // 查了几天就有几间房数
            roomStatRespVO.setRoomNum(roomNum);
            roomStatRespVO.setOcc(NumberUtils.occ(nightNum, roomStatRespVO.getRoomNum()));
            roomStatRespVO.setNightNum(nightNum);
            roomStatRespVO.setRoomFee(roomStatDOS.stream().mapToLong(RoomStatDO::getRoomFee).sum());
            roomStatRespVO.setAvgRoomFee(NumberUtils.adr(roomStatRespVO.getRoomFee(), nightNum));
            roomStatRespVO.setOtherFee(roomStatDOS.stream().mapToLong(RoomStatDO::getOtherFee).sum());
            roomStatRespVO.setRevPar(NumberUtils.revPar(roomStatRespVO.getRoomFee(), roomStatRespVO.getRoomNum()));
            roomStatRespVO.setSelfNum(roomStatDOS.stream().mapToInt(RoomStatDO::getSelfNum).sum());
            roomStatRespVO.setRepairNum(roomStatDOS.stream().mapToInt(RoomStatDO::getRepairNum).sum());
            list.add(roomStatRespVO);
        }
        /*for (Map.Entry<String, List<RoomStatDO>> entry : roomStatDOMap.entrySet()) {
            String rCode = entry.getKey();
            List<RoomStatDO> roomStatDOS = entry.getValue();

            // 获得房间信息
            RoomRespDTO room = roomMap.getOrDefault(rCode, new RoomRespDTO());

            RoomStatRespVO roomStatRespVO =new RoomStatRespVO();
            BigDecimal nightNum = roomStatDOS.stream().map(RoomStatDO::getNightNum).reduce(BigDecimal.ZERO, BigDecimal::add);
            roomStatRespVO.setOcc(NumberUtils.occ(nightNum, roomList.size()));
            roomStatRespVO.setNightNum(nightNum);
            roomStatRespVO.setRoomNum(roomList.size());
            roomStatRespVO.setRCode(rCode);
            roomStatRespVO.setRNo(room.getRNo());
            roomStatRespVO.setRtCode(room.getRtCode());
            roomStatRespVO.setRtName(room.getRtName());
            roomStatRespVO.setBuildingCode(room.getBuildingCode());
            roomStatRespVO.setBuildingName(room.getBuildingName());
            roomStatRespVO.setFloorCode(room.getFloorCode());
            roomStatRespVO.setFloorName(room.getFloorName());
            roomStatRespVO.setRoomFee(roomStatDOS.stream().mapToLong(RoomStatDO::getRoomFee).sum());
            roomStatRespVO.setAvgRoomFee(NumberUtils.adr(roomStatRespVO.getRoomFee(), nightNum));
            roomStatRespVO.setOtherFee(roomStatDOS.stream().mapToLong(RoomStatDO::getOtherFee).sum());
            roomStatRespVO.setRevPar(NumberUtils.revPar((roomStatRespVO.getRoomFee() + roomStatRespVO.getOtherFee()), roomList.size()));
            roomStatRespVO.setSelfNum(roomStatDOS.stream().mapToInt(RoomStatDO::getSelfNum).sum());
            roomStatRespVO.setRepairNum(roomStatDOS.stream().mapToInt(RoomStatDO::getRepairNum).sum());
            list.add(roomStatRespVO);
        }*/

        // 如果楼栋代码不为空，过滤楼栋
        if (StrUtil.isNotBlank(reqVO.getBuildingCode())) {
            list = CollectionUtils.filterList(list, roomStat -> reqVO.getBuildingCode().equals(roomStat.getBuildingCode()));
        }

        return roomStatReportRespVO.setList(list);
    }
}
