package info.qizhi.aflower.module.report.service.incomestatmonth;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.incomestatmonth.vo.IncomeStatMonthSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatmonth.vo.IncomeStatReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatmonth.IncomeStatMonthDO;
import info.qizhi.aflower.module.report.dal.mysql.incomestatmonth.IncomeStatMonthMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@Service
@Validated
public class IncomeStatMonthServiceImpl implements IncomeStatMonthService{
    @Resource
    private IncomeStatMonthMapper incomeStatMonthMapper;

    @Override
    public Boolean createIncomeStatMonths(List<IncomeStatMonthSaveReqVO> incomeStatDailySaveReqVOList) {
        if (CollUtil.isNotEmpty(incomeStatDailySaveReqVOList)) {
            List<IncomeStatMonthDO> incomeStatMonthDOList = BeanUtils.toBean(incomeStatDailySaveReqVOList, IncomeStatMonthDO.class);
            return incomeStatMonthMapper.insertBatch(incomeStatMonthDOList);
        }
        return true;
    }

    @Override
    public List<IncomeStatMonthDO> getIncomeStatList(IncomeStatReqVO reqVO) {
        return incomeStatMonthMapper.selectList(reqVO);
    }
}
