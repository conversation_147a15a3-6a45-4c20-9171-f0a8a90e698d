package info.qizhi.aflower.module.report.service.guestdetail;

import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 宾客明细报表[固化] Service 接口
 *
 * <AUTHOR>
 */
public interface GuestDetailService {

    /**
     * 创建宾客明细报表[固化]
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createGuestDetail(@Valid GuestDetailSaveReqVO createReqVO);

    /**
     * 批量创建宾客明细报表[固化]
     * @param guestDetailSaveReqVOList
     * @return 成功true 失败false
     */
    Boolean createGuestDetails(@Valid List<GuestDetailSaveReqVO> guestDetailSaveReqVOList);

    /**
     * 获得宾客明细报表[固化]
     *
     * @return 宾客明细报表[固化]
     */
    GuestDetailReportRespVO getGuestDetailReport(GuestDetailReportReqVO reqVO);



}