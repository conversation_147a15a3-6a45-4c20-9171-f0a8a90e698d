package info.qizhi.aflower.module.report.service.bizdaily;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailyPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdaily.BizDailyDO;
import info.qizhi.aflower.module.report.dal.mysql.bizdaily.BizDailyMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.BIZ_DAILY_NOT_EXISTS;

/**
 * 营业日报(固化) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizDailyServiceImpl implements BizDailyService {

    @Resource
    private BizDailyMapper bizDailyMapper;

    @Override
    public Long createBizDaily(BizDailySaveReqVO createReqVO) {
        // 插入
        BizDailyDO bizDaily = BeanUtils.toBean(createReqVO, BizDailyDO.class);
        bizDailyMapper.insert(bizDaily);
        // 返回
        return bizDaily.getId();
    }

    @Override
    public void updateBizDaily(BizDailySaveReqVO updateReqVO) {
        // 校验存在
        validateBizDailyExists(updateReqVO.getId());
        // 更新
        BizDailyDO updateObj = BeanUtils.toBean(updateReqVO, BizDailyDO.class);
        bizDailyMapper.updateById(updateObj);
    }

    @Override
    public void deleteBizDaily(Long id) {
        // 校验存在
        validateBizDailyExists(id);
        // 删除
        bizDailyMapper.deleteById(id);
    }

    private void validateBizDailyExists(Long id) {
        if (bizDailyMapper.selectById(id) == null) {
            throw exception(BIZ_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public BizDailyDO getBizDaily(Long id) {
        return bizDailyMapper.selectById(id);
    }

    @Override
    public PageResult<BizDailyDO> getBizDailyPage(BizDailyPageReqVO pageReqVO) {
        return bizDailyMapper.selectPage(pageReqVO);
    }

}