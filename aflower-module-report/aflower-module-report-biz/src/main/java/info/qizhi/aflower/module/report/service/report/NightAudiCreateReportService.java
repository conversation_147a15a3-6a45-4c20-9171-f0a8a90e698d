package info.qizhi.aflower.module.report.service.report;

import info.qizhi.aflower.module.report.service.report.bo.NightAudiReportReqBO;

import java.time.LocalDate;

/**
 * @Author: TY
 * @CreateTime: 2024-07-08
 * @Description: 夜审生成报表服务
 * @Version: 1.0
 */
public interface NightAudiCreateReportService {

    /**
     * 批量创建报表
     * @param nightAudiReportReqBO 各个报表封装的数据
     */
    Boolean batchCreateReports(NightAudiReportReqBO nightAudiReportReqBO);

    /**
     * 创建月报表固化
     */
    void createManagerMonth(LocalDate bizDate, String gcode, String hcode);
}
