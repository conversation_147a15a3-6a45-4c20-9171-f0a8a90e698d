package info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth;

import com.fasterxml.jackson.annotation.JsonFormat;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerMonthRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.*;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经理日报(固化) Response VO")
@Data
public class ManagerMonthReportRespVO {

    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "营业日", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate startDate;

    @Schema(description = "营业日", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", example = "报表操作员")
    private String operator;

    @Schema(description = "客房营业收入指标")
    private List<ManagerMonthRespVO> roomRevenueIndexList;

    @Schema(description = "订单来源分类统计")
    private List<DataClassStatMonthVO> orderSrcList;

    @Schema(description = "订单来源分类数据合计")
    private List<DataClassStatMonth> orderSrcSumList;

    @Schema(description = "房型分类统计")
    private List<DataClassStatMonthVO> rtCodeList;

    @Schema(description = "订单来源分类数据合计")
    private List<DataClassStatMonth> rtCodeSumList;

    @Schema(description = "客源分类统计")
    private List<DataClassStatMonthVO> gsrcList;

    @Schema(description = "客源分类统计数据合计")
    private List<DataClassStatMonth> gsrcSumList;

    @Schema(description = "渠道分类统计")
    private List<DataClassStatMonthVO> statChannelList;

    @Schema(description = "渠道分类统计数据合计")
    private List<DataClassStatMonth> statChannelSumList;

    @Schema(description = "入住类型分类统计")
    private List<DataClassStatMonthVO> inTypeList;

    @Schema(description = "入住类型分类统计合计")
    private List<DataClassStatMonth> inTypeSumList;

}