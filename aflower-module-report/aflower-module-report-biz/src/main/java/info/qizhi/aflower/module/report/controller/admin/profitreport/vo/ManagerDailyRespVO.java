package info.qizhi.aflower.module.report.controller.admin.profitreport.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 经理综合日报表(固化) Response VO")
@Data
@ExcelIgnoreUnannotated
public class ManagerDailyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31216")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间夜数")
    private BigDecimal nightNum;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("RevPar")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平均房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出租率")
    private BigDecimal occ;

    @Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总房数")
    private Integer totalRoomNum;

    @Schema(description = "空房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("空房数")
    private Integer emptyNum;

    @Schema(description = "维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("维修房")
    private Integer repairNum;

    @Schema(description = "自用房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自用房")
    private Integer selfNum;

    @Schema(description = "过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜房")
    private Integer overnightNum;

    @Schema(description = "过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜房出租率")
    private BigDecimal overnightOcc;

    @Schema(description = "开房数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开房数(自然日)")
    private Integer openRoomNum;

    @Schema(description = "上门散客数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("上门散客数(自然日)")
    private Integer workInNum;

    @Schema(description = "预订入住(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订入住(自然日)")
    private Integer bookInNum;

    @Schema(description = "取消预订(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("取消预订(自然日)")
    private Integer cancelBookNum;

    @Schema(description = "NoShow", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("NoShow")
    private Integer noShowNum;

    @Schema(description = "会员卡销售数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员卡销售数")
    private Integer memberCardSellNum;

    @Schema(description = "挂账数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("挂账数")
    private Integer creditNum;

    @Schema(description = "免费升级数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("免费升级数")
    private Integer freeUpNum;

    @Schema(description = "会员卡销售金额")
    @ExcelProperty("会员卡销售金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberCardFee;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}