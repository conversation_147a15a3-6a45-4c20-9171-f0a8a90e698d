package info.qizhi.aflower.module.report.service.roomtypestatdaily;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import info.qizhi.aflower.module.report.controller.admin.roomtypestatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.roomtypestatdaily.RoomTypeStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.report.dal.mysql.roomtypestatdaily.RoomTypeStatDailyMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.*;

/**
 * 每日房型统计报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoomTypeStatDailyServiceImpl implements RoomTypeStatDailyService {

    @Resource
    private RoomTypeStatDailyMapper roomTypeStatDailyMapper;

    @Override
    public Long createRoomTypeStatDaily(RoomTypeStatDailySaveReqVO createReqVO) {
        // 插入
        RoomTypeStatDailyDO roomTypeStatDaily = BeanUtils.toBean(createReqVO, RoomTypeStatDailyDO.class);
        roomTypeStatDailyMapper.insert(roomTypeStatDaily);
        // 返回
        return roomTypeStatDaily.getId();
    }

    @Override
    public void updateRoomTypeStatDaily(RoomTypeStatDailySaveReqVO updateReqVO) {
        // 校验存在
        validateRoomTypeStatDailyExists(updateReqVO.getId());
        // 更新
        RoomTypeStatDailyDO updateObj = BeanUtils.toBean(updateReqVO, RoomTypeStatDailyDO.class);
        roomTypeStatDailyMapper.updateById(updateObj);
    }

    @Override
    public void deleteRoomTypeStatDaily(Long id) {
        // 校验存在
        validateRoomTypeStatDailyExists(id);
        // 删除
        roomTypeStatDailyMapper.deleteById(id);
    }

    private void validateRoomTypeStatDailyExists(Long id) {
        if (roomTypeStatDailyMapper.selectById(id) == null) {
            throw exception(ROOM_TYPE_STAT_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public RoomTypeStatDailyDO getRoomTypeStatDaily(Long id) {
        return roomTypeStatDailyMapper.selectById(id);
    }

    @Override
    public PageResult<RoomTypeStatDailyDO> getRoomTypeStatDailyPage(RoomTypeStatDailyPageReqVO pageReqVO) {
        return roomTypeStatDailyMapper.selectPage(pageReqVO);
    }

}