package info.qizhi.aflower.module.report.dal.dataobject.roomstat;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * CREATE TABLE rp_room_stat(
 *     `id` bigint(20) NOT NULL AUTO_INCREMENT  COMMENT 'id' ,
 *     `gcode` VARCHAR(32) NOT NULL   COMMENT '集团代码' ,
 *     `hcode` VARCHAR(32) NOT NULL   COMMENT '门店代码' ,
 *     `biz_date` date NOT NULL   COMMENT '营业日' ,
 *     `rt_code` VARCHAR(32) NOT NULL   COMMENT '房型代码' ,
 *     `r_code` VARCHAR(32) NOT NULL   COMMENT '房间代码' ,
 *     `room_num` INT    COMMENT '客房数' ,
 *     `night_num` DECIMAL(24,2)    COMMENT '间夜数' ,
 *     `room_fee` bigint(20)    COMMENT '房费' ,
 *     `other_fee` bigint(20)    COMMENT '其他消费' ,
 *     `rev_par` bigint(20) NOT NULL   COMMENT 'RevPar' ,
 *     `self_num` INT NOT NULL   COMMENT '自用房' ,
 *     `repair_num` INT NOT NULL   COMMENT '维修房' ,
 *     `creator` VARCHAR(32)    COMMENT '创建人' ,
 *     `create_time` DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
 *     `update_time` DATETIME NOT NULL  DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间' ,
 *     `tenant_id` bigint(20) NOT NULL  DEFAULT 0 COMMENT '租户编号' ,
 *     PRIMARY KEY (id)
 * )  COMMENT = '客房营收统计报表(固化)';
 */

@TableName("rp_room_stat")
@KeySequence("rp_room_stat_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomStatDO {
    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 房型代码
     */
    private String rtCode;
    /**
     * 房间代码
     */
    private String rCode;
    /**
     * 客房数
     */
    private Integer roomNum;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 其他消费
     */
    private Long otherFee;
    /**
     * RevPar
     */
    private BigDecimal revPar;
    /**
     * 自用房
     */
    private Integer selfNum;
    /**
     * 维修房
     */
    private Integer repairNum;
}
