package info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 零房费&负房费报表(固化) Response VO")
@Data
public class ZeroRoomRateRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17817")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty(value="rNo")
    private String rNo ;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no ;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestName ;

    @Schema(description = "客源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcType ;

    @Schema(description = "客源名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcTypeName;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinType ;

    @Schema(description = "入住类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinTypeName;

    @Schema(description = "客源关联账号")
    private String guestCode;

    @Schema(description = "超链接", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "会员级别或公司", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String levelOrCompanyName;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkinTime ;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkoutTime ;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark ;

}