package info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH;

@Schema(description = "管理后台 - 经理月报(固化)(数据对比) Response VO")
@Data
@ExcelIgnoreUnannotated
public class ManagerMonthDataRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31216")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> nightNumData;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> roomFeeData;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> revParData;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> avgRoomFeeData;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> occData;

    @Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> totalRoomNumData;

    @Schema(description = "空房数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> emptyNumData;

    @Schema(description = "维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> repairNumData;

    @Schema(description = "自用房", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> selfNumData;

    @Schema(description = "过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> overnightNumData;

    @Schema(description = "过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> overnightOccData;

    @Schema(description = "开房数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> openRoomNumData;

    @Schema(description = "上门散客数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> workInNumData;

    @Schema(description = "预订入住(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> bookInNumData;

    @Schema(description = "取消预订(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> cancelBookNumData;

    @Schema(description = "NoShow", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> noShowNumData;

    @Schema(description = "会员卡销售数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> memberCardSellNumData;

    @Schema(description = "挂账数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> creditNumData;

    @Schema(description = "免费升级数", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> freeUpNumData;

    @Schema(description = "会员卡销售金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> memberCardFeeData;

    @Schema(description = "月份", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH)
    private LocalDate month;

    @Schema(description = "会员卡", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> memberCardData;

    @Schema(description = "小商品", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> goodData;

    @Schema(description = "餐饮", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> cateringData;

    @Schema(description = "赔偿", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> indemnityFeeData;

    @Schema(description = "其他消费", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> otherFeeData;

    @Schema(description = "门店收入", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> lobbyFeeData;

    @Schema(description = "会员充值", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<DataComparisonRespVO> memberRechargeData;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}