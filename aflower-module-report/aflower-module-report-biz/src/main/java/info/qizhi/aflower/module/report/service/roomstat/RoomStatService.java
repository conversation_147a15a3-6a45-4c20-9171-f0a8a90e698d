package info.qizhi.aflower.module.report.service.roomstat;

import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

public interface RoomStatService {
    /**
     * 创建客房营收统计报表(固化)
     * @param reqVO
     */
    void createRoomStat(@Valid RoomStatSaveReqVO reqVO);

    /**
     * 批量创建客房营收统计报表(固化)
     * @param reqVOList
     */
    boolean batchCreateRoomStat(@Valid List<RoomStatSaveReqVO> reqVOList);

    /**
     * 获得客房营收统计报表(固化)
     * @param reqVO
     * @return
     */
    RoomStatReportRespVO getRoomStatList(@Valid RoomStatReqVO reqVO);
}
