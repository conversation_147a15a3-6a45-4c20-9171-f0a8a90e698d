package info.qizhi.aflower.module.report.dal.dataobject.nightaudiroomtype;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 夜审房态表(固化) DO
 *
 * <AUTHOR>
 */
@TableName("rp_night_audi_room_type")
@KeySequence("rp_night_audi_room_type_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NightAudiRoomTypeDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 楼栋代码
     */
    private String buildCode;
    /**
     * 楼栋名称
     */
    private String buildName;
    /**
     * 楼层代码
     */
    private String floorCode;
    /**
     * 楼层名称
     */
    private String floorName;
    /**
     * 房间代码
     */
    private String rCode;
    /**
     * 房号
     */
    private String rNo;
    /**
     * 房型代码
     */
    private String rtCode;
    /**
     * 房型名称
     */
    private String rtName;
    /**
     * 房态
     */
    private String state;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 备注
     */
    private String remark;

}