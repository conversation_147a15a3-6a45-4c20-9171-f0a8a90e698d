package info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 每日收入统计报新增/修改 Request VO")
@Data
public class IncomeStatDailyReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notempty}")
    private String hcode;

   /* @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate bizDate;*/

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate startDate;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDate endDate;

    @Schema(description = "科目类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String subType;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String subName;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long fee;

}