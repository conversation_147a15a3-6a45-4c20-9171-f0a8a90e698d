package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经理综合日报表(固化)新增/修改 Request VO")
@Data
public class ManagerDailySaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightNum.notnull}")
    private BigDecimal nightNum;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{roomFee.notnull}")
    private Long roomFee;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{revPar.notnull}")
    private Long revPar;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{avgRoomFee.notblank}")
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{statName.notblank}")
    private BigDecimal occ;

    @Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总房数不能为空")
    private Integer totalRoomNum;

    @Schema(description = "空房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "空房数不能为空")
    private Integer emptyNum;

    @Schema(description = "维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{repairNum.notnull}")
    private Integer repairNum;

    @Schema(description = "自用房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "自用房不能为空")
    private Integer selfNum;

    @Schema(description = "过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "过夜房不能为空")
    private Integer overnightNum;

    @Schema(description = "过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "过夜房出租率不能为空")
    private BigDecimal overnightOcc;

    @Schema(description = "开房数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开房数(自然日)不能为空")
    private Integer openRoomNum;

    @Schema(description = "退房数(自然日)")
    private Integer checkOutNum;

    @Schema(description = "上门散客数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "上门散客数(自然日)不能为空")
    private Integer workInNum;

    @Schema(description = "预订入住(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预订入住(自然日)不能为空")
    private Integer bookInNum;

    @Schema(description = "取消预订(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "取消预订(自然日)不能为空")
    private Integer cancelBookNum;

    @Schema(description = "NoShow", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "NoShow不能为空")
    private Integer noShowNum;

    @Schema(description = "会员卡销售数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "会员卡销售数不能为空")
    private Integer memberCardSellNum;

    @Schema(description = "挂账数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "挂账数不能为空")
    private Integer creditNum;

    @Schema(description = "免费升级数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "免费升级数不能为空")
    private Integer freeUpNum;

    @Schema(description = "会员卡销售金额")
    private Long memberCardFee;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

}