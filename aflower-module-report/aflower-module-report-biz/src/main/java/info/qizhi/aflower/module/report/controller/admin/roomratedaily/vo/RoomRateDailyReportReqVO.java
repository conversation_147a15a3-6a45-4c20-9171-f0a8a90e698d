package info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo;


import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 房费日报表(固化) Request VO")
@Data
public class RoomRateDailyReportReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "房型代码")
    private String rtCode;

    @Schema(description = "房号")
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "客源")
    private String guestSrcType;

    @Schema(description = "订单来源")
    private String orderSrc;

    @Schema(description = "入住类型", example = "2")
    private String checkinType;

    @Schema(description = "创建渠道")
    private String createChannel;

    @Schema(description = "统计渠道")
    private String statChannel;

    @Schema(description = "操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "营业日")
    //@NotNull(message = "{bizDate.notnull}")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

}