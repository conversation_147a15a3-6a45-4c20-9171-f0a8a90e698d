package info.qizhi.aflower.module.report.service.totalbizkpidaily;


import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.report.controller.admin.totalbizkpidaily.vo.TotalBizKpiDailyPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.totalbizkpidaily.vo.TotalBizKpiDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.totalbizkpidaily.vo.TotalBizKpiDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.totalbizkpidaily.TotalBizKpiDailyDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 每日总营业指标报 Service 接口
 *
 * <AUTHOR>
 */
public interface TotalBizKpiDailyService {

    /**
     * 创建每日总营业指标报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTotalBizKpiDaily(@Valid TotalBizKpiDailySaveReqVO createReqVO);

    /**
     * 更新每日总营业指标报
     *
     * @param updateReqVO 更新信息
     */
    void updateTotalBizKpiDaily(@Valid TotalBizKpiDailySaveReqVO updateReqVO);

    /**
     * 删除每日总营业指标报
     *
     * @param id 编号
     */
    void deleteTotalBizKpiDaily(Long id);

    /**
     * 获得每日总营业指标报
     *
     * @param id 编号
     * @return 每日总营业指标报
     */
    TotalBizKpiDailyDO getTotalBizKpiDaily(Long id);

    /**
     * 获得每日总营业指标报分页
     *
     * @param pageReqVO 分页查询
     * @return 每日总营业指标报分页
     */
    PageResult<TotalBizKpiDailyDO> getTotalBizKpiDailyPage(TotalBizKpiDailyPageReqVO pageReqVO);


    /**
     * 获得每日每个门店的总营业指标报
     * @param reqVO
     * @return
     */
    List<TotalBizKpiDailyDO> getStoreTotalBizKpiDaily(TotalBizKpiDailyRespVO reqVO);

}