package info.qizhi.aflower.module.report.controller.admin.subsummary.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 酒店科目汇总表(固化) Response VO")
@Data
@ExcelIgnoreUnannotated
public class SubSummaryRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15720")
    @ExcelProperty("id")
    private Integer id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("科目代码")
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("科目名称")
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("科目类型;consume_account: 消费科目 pay_account：付款科目")
    private String subType;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDay;

    @Schema(description = "本月发生累计", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本月发生累计")
    private Long curMonthFee;

    @Schema(description = "本年发生累计", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本年发生累计")
    private Long curYearFee;

    @Schema(description = "昨日余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("昨日余额")
    private Long yesterdayBalance;

    @Schema(description = "今日发生", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("今日发生")
    private Long todayOccur;

    @Schema(description = "今日(结账)收回", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("今日(结账)收回")
    private Long todaySettle;

    @Schema(description = "今日余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("今日余额")
    private Long todayBalance;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}