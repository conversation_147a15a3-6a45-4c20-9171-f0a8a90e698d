package info.qizhi.aflower.module.report.service.hotelcheckindetail;

import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberListReqDTO;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.AccountListReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.AccountRespDTO;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.OrderPriceApi;
import info.qizhi.aflower.module.pms.api.order.OrderTogetherApi;
import info.qizhi.aflower.module.pms.api.order.dto.*;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentRespDTO;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeRespDTO;
import info.qizhi.aflower.module.report.controller.admin.hotelcheckindetail.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.hotelcheckindetail.HotelCheckInDetailDO;
import info.qizhi.aflower.module.report.dal.mysql.hotelcheckindetail.HotelCheckInDailyMapper;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.dict.dto.DictDataRespDTO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.user.AdminUserApi;
import info.qizhi.aflower.module.system.api.user.dto.UserSimpleRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Service
@Validated
public class HotelCheckInDailyServiceImpl implements HotelCheckInDailyService {

    @Resource
    private HotelCheckInDailyMapper hotelCheckInDailyMapper;
    @Resource
    private OrderApi orderApi;
    @Resource
    private AccountApi accountApi;
    @Resource
    private MemberApi memberApi;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private OrderTogetherApi orderTogetherApi;
    @Resource
    private DictDataApi dictDataApi;
    @Resource
    @Lazy
    private OrderPriceApi orderPriceApi;
    @Resource
    private RoomTypeApi roomTypeApi;
    @Resource
    private AdminUserApi userApi;

    @Override
    public void createDailyReport(HotelCheckInSaveReqVO reqVO) {
        hotelCheckInDailyMapper.insert(BeanUtils.toBean(reqVO, HotelCheckInDetailDO.class));
    }

    @Override
    public void createBatchDailyReport(List<HotelCheckInSaveReqVO> reqVOs) {
        hotelCheckInDailyMapper.insertBatch(BeanUtils.toBean(reqVOs, HotelCheckInDetailDO.class));
    }

    @Override
    public HotelCheckInReportRespVO getReportList(HotelCheckInReqVO reqVO) {
        HotelCheckInReportRespVO hotelCheckInReportRespVO = new HotelCheckInReportRespVO();
        // 填充门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        hotelCheckInReportRespVO.setHname(merchant.getHname())
                .setStartTime(convertStringToLocalDate(reqVO.getStartDate(), reqVO.getTimeType()))
                .setEndTime(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()))
                .setOperator(reqVO.getCreator())
                .setLastSelectTime(LocalDateTime.now());

        List<HotelCheckInDetailDO> hotelCheckInDetailDOS = hotelCheckInDailyMapper.selectList(reqVO);
        List<String> orderNos = hotelCheckInDetailDOS.stream()
                .map(HotelCheckInDetailDO::getOrderNo)  // 提取订单号
                .filter(orderNo -> orderNo != null && !orderNo.isEmpty())  // 过滤掉 null 和空字符串
                .distinct()  // 去重
                .toList();  // 转为 List

        // 获得所以中介列表
        List<ProtocolAgentRespDTO> protocolAgentList = protocolAgentApi.getProtocolAgentList(new ProtocolAgentReqDTO().setGcode(reqVO.getGcode())).getData();
        Map<String, ProtocolAgentRespDTO> agentMap = CollectionUtils.convertMap(protocolAgentList, ProtocolAgentRespDTO::getPaCode);
        // 获得所以会员列表
        List<MemberAndStoreCardRespDTO> memberList = memberApi.getMemberList(new MemberListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())).getData();
        Map<String, MemberAndStoreCardRespDTO> memberMap = CollectionUtils.convertMap(memberList, MemberAndStoreCardRespDTO::getMcode);

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nicknameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        List<OrderRespDTO> orders = orderApi.getOrderList(new OrderReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos)).getData();
        Map<String, OrderRespDTO> orderMap = CollectionUtils.convertMap(orders, OrderRespDTO::getOrderNo);

        // 获取订单价格
        List<OrderPriceRespDTO> orderPriceList = orderPriceApi.getOrderPriceList(new OrderPriceReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos)).getData();
        // 获取每个订单号的最早价格记录
        Map<String, OrderPriceRespDTO> orderPriceMap = orderPriceList.stream()
                // 按orderNo分组，并找出每组中priceDate最小的记录
                .collect(Collectors.toMap(
                        OrderPriceRespDTO::getOrderNo,  // Key: 订单号
                        price -> price,                 // Value: 记录本身
                        (existing, replacement) ->      // 合并函数：保留更早日期的记录
                                existing.getPriceDate().isBefore(replacement.getPriceDate()) ?
                                        existing : replacement
                ));

        List<OrderTogetherRespDTO> orderTogethers = orderTogetherApi.getOrderTogetherList(new OrderTogetherReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos).setIsMain(NumberEnum.ONE.getNumber())).getData();
        Map<String, OrderTogetherRespDTO> orderTogetherMap = CollectionUtils.convertMap(orderTogethers, OrderTogetherRespDTO::getOrderNo);


        List<RoomTypeRespDTO> convertMap = roomTypeApi.getRoomTypeList(new RoomTypeReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())).getData();
        Map<String, RoomTypeRespDTO> roomTypeMap = CollectionUtils.convertMap(convertMap, RoomTypeRespDTO::getRtCode);

        List<AccountRespDTO> accounts = accountApi.getAccountList(new AccountListReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setNos(orderNos).setSubType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode())).getData();

        //获取房费科目
        List<DictDataRespDTO> dictData = dictDataApi.getDictDataListByParentCode(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode(), DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT_ROOM_FEE.getCode()).getData();
        // 1. 获取房费科目代码列表
        List<String> roomFeeSubCodes = dictData.stream()
                .map(DictDataRespDTO::getValue)
                .toList();
        // 使用收集器同时计算两种费用
        // 正确分组统计各单号的房费和其他费用
        Map<String, Map<Boolean, Long>> feeSumsByNo = Optional.ofNullable(accounts)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.groupingBy(
                        AccountRespDTO::getNo,
                        Collectors.partitioningBy(
                                acc -> roomFeeSubCodes.contains(acc.getSubCode()),
                                Collectors.summingLong(AccountRespDTO::getFee)
                        )
                ));
        // 转换为最终结果
        Map<String, Long> roomFeeSumMap = new HashMap<>();
        Map<String, Long> otherFeeSumMap = new HashMap<>();

        feeSumsByNo.forEach((no, feeMap) -> {
            roomFeeSumMap.put(no, feeMap.getOrDefault(true, 0L));
            otherFeeSumMap.put(no, feeMap.getOrDefault(false, 0L));
        });

        List<HotelCheckInRespVO> hotelCheckInRespVOS = buildHotelCheckInRespVO(hotelCheckInDetailDOS, memberMap, agentMap, orderMap,
                roomFeeSumMap, otherFeeSumMap, orderTogetherMap, roomTypeMap, orderPriceMap, nicknameMap);

        hotelCheckInReportRespVO.setList(filterHotelCheckInList(hotelCheckInRespVOS, reqVO));
        return hotelCheckInReportRespVO;
    }

    @Override
    public HotelCheckInContinueReportRespVO getContinueInReportList(HotelCheckInReqVO reqVO) {
        if (StrUtil.isEmpty(reqVO.getOperateType())) {
            reqVO.setOperateTypeList(List.of(HotelCheckInTypeEnum.EXTEND_STAY.getCode(), HotelCheckInTypeEnum.AUTO_EXTEND_BY_NIGHT_AUDIT.getCode()));
        }
        HotelCheckInContinueReportRespVO hotelCheckInContinueReportRespVO = new HotelCheckInContinueReportRespVO();
        // 填充门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        hotelCheckInContinueReportRespVO.setHname(merchant.getHname())
                .setStartTime(convertStringToLocalDate(reqVO.getStartDate(), reqVO.getTimeType()))
                .setEndTime(convertStringToLocalDate(reqVO.getEndDate(), reqVO.getTimeType()))
                .setOperator(reqVO.getCreator())
                .setLastSelectTime(LocalDateTime.now());

        List<HotelCheckInDetailDO> hotelCheckInDetailDOS = hotelCheckInDailyMapper.selectList(reqVO);
        List<String> orderNos = hotelCheckInDetailDOS.stream()
                .map(HotelCheckInDetailDO::getOrderNo)  // 提取订单号
                .filter(orderNo -> orderNo != null && !orderNo.isEmpty())  // 过滤掉 null 和空字符串
                .distinct()  // 去重
                .toList();  // 转为 List


        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(reqVO.getGcode(), null).getData();
        Map<String, String> nicknameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        List<OrderRespDTO> orders = orderApi.getOrderList(new OrderReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos)).getData();
        Map<String, OrderRespDTO> orderMap = CollectionUtils.convertMap(orders, OrderRespDTO::getOrderNo);

        // 获取订单价格
        List<OrderPriceRespDTO> orderPriceList = orderPriceApi.getOrderPriceList(new OrderPriceReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos)).getData();
        Map<LocalDate, OrderPriceRespDTO> orderPriceMap = CollectionUtils.convertMap(orderPriceList, OrderPriceRespDTO::getPriceDate);

        // 获得宾客列表
        List<OrderTogetherRespDTO> orderTogethers = orderTogetherApi.getOrderTogetherList(new OrderTogetherReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode()).setOrderNos(orderNos).setIsMain(NumberEnum.ONE.getNumber())).getData();
        Map<String, OrderTogetherRespDTO> orderTogetherMap = CollectionUtils.convertMap(orderTogethers, OrderTogetherRespDTO::getOrderNo);

        // 获得房型列表
        List<RoomTypeRespDTO> roomTypeList = roomTypeApi.getRoomTypeList(new RoomTypeReqDTO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())).getData();
        Map<String, RoomTypeRespDTO> roomTypeMap = CollectionUtils.convertMap(roomTypeList, RoomTypeRespDTO::getRtCode);

        List<HotelCheckInContinueRespVO> hotelCheckInContinueRespVO = buildHotelCheckInContinueRespVO(hotelCheckInDetailDOS, orderMap, orderTogetherMap,
                roomTypeMap, orderPriceMap, nicknameMap);

        hotelCheckInContinueReportRespVO.setList(filterHotelCheckInContinueList(hotelCheckInContinueRespVO, reqVO));
        return hotelCheckInContinueReportRespVO;
    }

    private List<HotelCheckInContinueRespVO> filterHotelCheckInContinueList(List<HotelCheckInContinueRespVO> hotelCheckInContinueRespVO, HotelCheckInReqVO reqVO) {
        return hotelCheckInContinueRespVO.stream()
                .filter(item ->
                        // 入住类型（不为空时才过滤）
                        (StrUtil.isEmpty(reqVO.getCheckinType()) ||
                                (item.getCheckinType() != null && item.getCheckinType().equals(reqVO.getCheckinType())))
                )
                .toList();
    }

    private List<HotelCheckInRespVO> filterHotelCheckInList(List<HotelCheckInRespVO> hotelCheckInRespVOS, HotelCheckInReqVO reqVO) {
        return hotelCheckInRespVOS.stream()
                .filter(item ->
                        // 1. 关键字过滤（keyWords不为空时，才检查rNo或name是否包含）
                        (StrUtil.isEmpty(reqVO.getKeyWords()) ||
                                (StrUtil.isNotBlank(item.getRNo()) && item.getRNo().contains(reqVO.getKeyWords())) ||
                                (StrUtil.isNotBlank(item.getGuestName()) && item.getGuestName().contains(reqVO.getKeyWords()))) &&

                                // 2. 入住类型（不为空时才过滤）
                                (StrUtil.isEmpty(reqVO.getCheckinType()) ||
                                        (item.getCheckinType() != null && item.getCheckinType().equals(reqVO.getCheckinType()))) &&

                                // 3. 销售员（不为空时才过滤）
                                (StrUtil.isEmpty(reqVO.getSeller()) ||
                                        (item.getSeller() != null && item.getSeller().equals(reqVO.getSeller()))) &&

                                // 4. 班次（不为空时才过滤）
                                (StrUtil.isEmpty(reqVO.getShiftNo()) ||
                                        (item.getShiftNo() != null && item.getShiftNo().equals(reqVO.getShiftNo())))
                )
                .toList();
    }

    private List<HotelCheckInContinueRespVO> buildHotelCheckInContinueRespVO(
            List<HotelCheckInDetailDO> hotelCheckInDetailDOS,
            Map<String, OrderRespDTO> orderMap,
            Map<String, OrderTogetherRespDTO> orderTogetherMap,
            Map<String, RoomTypeRespDTO> roomTypeMap,
            Map<LocalDate, OrderPriceRespDTO> orderPriceMap,
            Map<String, String> nicknameMap) {


        List<HotelCheckInContinueRespVO> list = new ArrayList<>();
        hotelCheckInDetailDOS.forEach(hotelCheckInDetailDO -> {
            OrderRespDTO order = orderMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderRespDTO());
            OrderTogetherRespDTO orderTogether = orderTogetherMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderTogetherRespDTO());
            //OrderRespDTO order = orderMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderRespDTO());


            HotelCheckInContinueRespVO hotelCheckInRespVO = BeanUtils.toBean(hotelCheckInDetailDO, HotelCheckInContinueRespVO.class);
            hotelCheckInRespVO.setGuestSrcType(order.getGuestSrcType());
            hotelCheckInRespVO.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(order.getGuestSrcType()));
            hotelCheckInRespVO.setStatChannelCodeName(ChannelEnum.getNameByCode(order.getStatChannelCode()));
            hotelCheckInRespVO.setRNo(order.getRNo());
            hotelCheckInRespVO.setRtCode(order.getRtCode());
            hotelCheckInRespVO.setRtName(roomTypeMap.getOrDefault(order.getRtCode(), new RoomTypeRespDTO()).getRtName());
            hotelCheckInRespVO.setGuestName(orderTogether.getName());
            hotelCheckInRespVO.setSeller(order.getSeller());
            hotelCheckInRespVO.setSellerName(nicknameMap.getOrDefault(order.getSeller(), ""));
            hotelCheckInRespVO.setCreatorName(nicknameMap.getOrDefault(hotelCheckInRespVO.getCreator(), ""));
            hotelCheckInRespVO.setCheckinType(order.getCheckinType());
            hotelCheckInRespVO.setCheckinTypeName(CheckInTypeEnum.getNameByCode(order.getCheckinType()));
            hotelCheckInRespVO.setOperateTypeName(HotelCheckInTypeEnum.getNameByCode(hotelCheckInDetailDO.getOperateType()));
            hotelCheckInRespVO.setPrice(orderPriceMap.getOrDefault(hotelCheckInDetailDO.getBizDate(), new OrderPriceRespDTO()).getVipPrice());

            list.add(hotelCheckInRespVO);
        });
        return list;
    }


    private List<HotelCheckInRespVO> buildHotelCheckInRespVO(
            List<HotelCheckInDetailDO> hotelCheckInDetailDOS,
            Map<String, MemberAndStoreCardRespDTO> memberMap,
            Map<String, ProtocolAgentRespDTO> agentMap,
            Map<String, OrderRespDTO> orderMap,
            Map<String, Long> roomFeeSumMap,
            Map<String, Long> otherFeeSumMap,
            Map<String, OrderTogetherRespDTO> orderTogetherMap,
            Map<String, RoomTypeRespDTO> roomTypeMap,
            Map<String, OrderPriceRespDTO> orderPriceMap,
            Map<String, String> nicknameMap) {


        List<HotelCheckInRespVO> list = new ArrayList<>();
        hotelCheckInDetailDOS.forEach(hotelCheckInDetailDO -> {
            OrderRespDTO order = orderMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderRespDTO());
            OrderTogetherRespDTO orderTogether = orderTogetherMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderTogetherRespDTO());
            //OrderRespDTO order = orderMap.getOrDefault(hotelCheckInDetailDO.getOrderNo(), new OrderRespDTO());


            HotelCheckInRespVO hotelCheckInRespVO = BeanUtils.toBean(hotelCheckInDetailDO, HotelCheckInRespVO.class);
            hotelCheckInRespVO.setGuestSrcType(order.getGuestSrcType());
            hotelCheckInRespVO.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(order.getGuestSrcType()));
            hotelCheckInRespVO.setStatChannelCodeName(ChannelEnum.getNameByCode(order.getStatChannelCode()));
            hotelCheckInRespVO.setRNo(order.getRNo());
            hotelCheckInRespVO.setRtCode(order.getRtCode());
            hotelCheckInRespVO.setRtName(roomTypeMap.getOrDefault(order.getRtCode(), new RoomTypeRespDTO()).getRtName());
            hotelCheckInRespVO.setShiftName(ShiftTypeEnum.getNameByCode(hotelCheckInRespVO.getShiftNo()));
            hotelCheckInRespVO.setGuestName(orderTogether.getName());
            hotelCheckInRespVO.setSeller(order.getSeller());
            hotelCheckInRespVO.setSellerName(nicknameMap.getOrDefault(order.getSeller(), ""));
            hotelCheckInRespVO.setCreatorName(nicknameMap.getOrDefault(hotelCheckInRespVO.getCreator(), ""));
            hotelCheckInRespVO.setCheckinType(order.getCheckinType());
            hotelCheckInRespVO.setCheckinTypeName(CheckInTypeEnum.getNameByCode(order.getCheckinType()));
            hotelCheckInRespVO.setOperateTypeName(HotelCheckInTypeEnum.getNameByCode(hotelCheckInDetailDO.getOperateType()));
            hotelCheckInRespVO.setPrice(orderPriceMap.getOrDefault(order.getOrderNo(), new OrderPriceRespDTO()).getVipPrice());
            hotelCheckInRespVO.setRoomFee(roomFeeSumMap.getOrDefault(order.getOrderNo(), 0L));
            hotelCheckInRespVO.setOtherFee(otherFeeSumMap.getOrDefault(order.getOrderNo(), 0L));
            hotelCheckInRespVO.setConsumeFee(hotelCheckInRespVO.getOtherFee() + hotelCheckInRespVO.getRoomFee());

            if (GuestSrcTypeEnum.MEMBER.getCode().equals(order.getGuestSrcType())) {
                String mtName = null;

                if (order.getGuestCode() != null && memberMap.get(order.getGuestCode()) != null) {
                    mtName = memberMap.get(order.getGuestCode()).getMtName();
                }
                hotelCheckInRespVO.setLevelOrCompanyName(mtName);
            }
            if (GuestSrcTypeEnum.AGENT.getCode().equals(order.getGuestSrcType())) {
                hotelCheckInRespVO.setLevelOrCompanyName(agentMap.getOrDefault(order.getGuestCode(), new ProtocolAgentRespDTO()).getPaName());
            }

            list.add(hotelCheckInRespVO);
        });
        return list;
    }

    public LocalDate convertStringToLocalDate(String dateString, String timeType) {
        if (dateString == null || dateString.isEmpty()) {
            return null;
        }
        try {
            DateTimeFormatter formatter;
            if (timeType.equals(NumberEnum.TWO.getNumber())) {
                // 日期字符串包含时间部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND);
                // 解析为 LocalDateTime，然后提取 LocalDate
                return LocalDateTime.parse(dateString, formatter).toLocalDate();
            } else {
                // 只有日期部分
                formatter = DateTimeFormatter.ofPattern(FORMAT_YEAR_MONTH_DAY);
                return LocalDate.parse(dateString, formatter);
            }
        } catch (DateTimeParseException e) {
            // 处理日期格式错误的情况
            throw new IllegalArgumentException("日期格式无效。请使用'yyyy-MM-dd'或'yyyy-MM-dd HH:mm:ss'格式", e);
        }
    }
}