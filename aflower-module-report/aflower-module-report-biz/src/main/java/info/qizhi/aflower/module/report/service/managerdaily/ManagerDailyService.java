package info.qizhi.aflower.module.report.service.managerdaily;


import info.qizhi.aflower.module.report.controller.admin.manager.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.managerdaily.ManagerDailyDO;
import jakarta.validation.Valid;

import java.time.LocalDate;
import java.util.List;

/**
 * 经理综合日报表(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface ManagerDailyService {

    /**
     * 创建经理综合日报表(固化)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createManagerDaily(@Valid ManagerDailySaveReqVO createReqVO);


    /**
     * 获得经理日报(固化)
     *
     * @return 经理日报(固化)
     */
    ManagerDailyReportRespVO getManagerDailyReport(ManagerDailyReqVO reqVO);

    /**
     * 获得日报
     * @param reqVO
     * @return
     */
    ManagerDailyDO getManagerDaily(ManagerDailyReqVO reqVO);

    /**
     * 获得日报集合
     * @param reqVO
     * @return
     */
    List<ManagerDailyDO> getManagerDailyList(ManagerDailyReqVO reqVO);


    /**
     * 获得月报列表
     * @param reqVO
     * @return
     */
    ManagerDailyReportMonthRespVO getManagerMonthDetailList(@Valid ManagerDailyReqVO reqVO);

    /**
     * 获得经理报表（单日）
     * @param reqVO
     * @return
     */
    ManagerReportRespVO getManagerReport(@Valid ManagerDailyReqVO reqVO);

    /**
     * 生成营业日报表
     * @param gcode
     * @param hcode
     * @param bizDate
     * @return
     */
    BusinessDailyReportVO generateBusinessDailyReport(String gcode, String hcode, LocalDate bizDate);
}