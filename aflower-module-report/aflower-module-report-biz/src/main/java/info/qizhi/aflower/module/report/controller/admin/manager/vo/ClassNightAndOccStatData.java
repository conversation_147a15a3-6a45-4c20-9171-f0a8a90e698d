package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 经营数据分类统计 Response VO")
@Data
public class ClassNightAndOccStatData {

    @Schema(description = "统计代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statCode;

    @Schema(description = "统计名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statName;

    @Schema(description = "本日间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal todayNightNumOrOcc;

    @Schema(description = "本月间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal monthNightNumOrOcc;

    @Schema(description = "去年同期间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lastYearSamePeriodNightNumOrOcc;

    @Schema(description = "上月间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lastMonthNightNumOrOcc;

    @Schema(description = "本年间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal yearNightNumOrOcc;

    @Schema(description = "上年间夜数或者出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal lastYearNightNumOrOcc;
}
