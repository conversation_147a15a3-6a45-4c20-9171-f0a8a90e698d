package info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 零房费&负房费报表新增/修改 Request VO")
@Data
public class ZeroRoomRateSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17817")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rNo ;

    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String no ;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestName ;

    @Schema(description = "客源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String guestSrcType ;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinType ;

    @Schema(description = "客源关联账号")
    private String guestCode;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime checkinTime ;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime checkoutTime ;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{feeSub.notblank}")
    private Long roomFee;

    @Schema(description = "房价", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long price;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED)
    private String remark ;

}