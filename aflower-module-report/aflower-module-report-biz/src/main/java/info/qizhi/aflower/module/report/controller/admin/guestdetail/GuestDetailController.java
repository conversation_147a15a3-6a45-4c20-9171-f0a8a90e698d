package info.qizhi.aflower.module.report.controller.admin.guestdetail;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailSaveReqVO;
import info.qizhi.aflower.module.report.service.guestdetail.GuestDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 宾客明细报表[固化]")
@RestController
@RequestMapping("/report/guest-detail")
@Validated
public class GuestDetailController {

    @Resource
    private GuestDetailService guestDetailService;

    @PostMapping("/create")
    @Operation(summary = "创建宾客明细报表[固化]")
    @PreAuthorize("@ss.hasPermission('report:guest-detail:create')")
    public CommonResult<Long> createGuestDetail(@Valid @RequestBody GuestDetailSaveReqVO createReqVO) {
        return success(guestDetailService.createGuestDetail(createReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得宾客明细报表[固化]")
    @PreAuthorize("@ss.hasPermission('report:guest-detail:get')")
    public CommonResult<GuestDetailReportRespVO> getGuestDetail(@Valid GuestDetailReportReqVO reqVO) {
        GuestDetailReportRespVO guestDetailReport = guestDetailService.getGuestDetailReport(reqVO);
        return success(guestDetailReport);
    }


}