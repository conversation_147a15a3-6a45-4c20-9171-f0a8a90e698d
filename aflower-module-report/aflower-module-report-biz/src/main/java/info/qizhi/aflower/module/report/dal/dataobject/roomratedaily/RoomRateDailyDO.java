package info.qizhi.aflower.module.report.dal.dataobject.roomratedaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 房费日报表(固化) DO
 *
 * <AUTHOR>
 */
@TableName("rp_room_rate_daily")
@KeySequence("rp_room_rate_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomRateDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 房号
     */
    private String rNo;
    /**
     * 房型代码
     */
    private String rtCode;
    /**
     * 房型
     */
    private String rtName;
    /**
     * 客人姓名
     */
    private String name;
    /**
     * 客源
     */
    private String gSrc;
    /**
     * 订单来源
     */
    private String orderSrc;
    /**
     * 创建渠道
     */
    private String createChannel;
    /**
     * 统计渠道
     */
    private String statChannel;
    /**
     * 入住时间
     */
    private LocalDateTime checkInTime;
    /**
     * 离店时间
     */
    private LocalDateTime checkOutTime;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 门市价
     */
    private Integer price;
    /**
     * 本日价格
     */
    private Long todayPrice;
    /**
     * 入住类型
     */
    private String inType;
    /**
     * 房费科目
     */
    private String feeSub;
    /**
     * 房费科目名称
     */
    private String feeSubName;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 房费
     */
    private Integer fee;
    /**
     * 操作员
     */
    private String operator;
    /**
     * 备注
     */
    private String remark;
    /**
     * 房价类型
     */
    private String priceType;

}