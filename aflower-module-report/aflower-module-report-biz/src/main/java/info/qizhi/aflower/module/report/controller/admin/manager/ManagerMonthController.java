package info.qizhi.aflower.module.report.controller.admin.manager;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth.ManagerMonthReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth.ManagerMonthReqVO;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerMonthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 经理综合月报表(固化)")
@RestController
@RequestMapping("/report/manager-month")
@Validated
public class ManagerMonthController {

    @Resource
    private ManagerMonthService managerMonthService;

    @GetMapping("/month/list")
    @Operation(summary = "获得经理月报表(固化)")
    @PreAuthorize("@ss.hasPermission('report:manager-month:list')")
    public CommonResult<ManagerMonthReportRespVO> getManagerMonthList(@Valid ManagerMonthReqVO reqVO) {
        ManagerMonthReportRespVO monthRespVO = managerMonthService.getManagerMonthReport(reqVO);
        return success(monthRespVO);
    }

/*    @GetMapping("/month/data/list")
    @Operation(summary = "获得经理报表(月报)(固化)(数据对比)")
    @PreAuthorize("@ss.hasPermission('report:manager:list')")
    public CommonResult<ManagerMonthDataReportRespVO> getManagerMonthDataList(@Valid ManagerMonthReqVO reqVO) {
        *//*ManagerMonthDataReportRespVO monthRespVO = managerMonthService.getManagerMonthDataList(reqVO);
        return success(monthRespVO);*//*
    }*/

}