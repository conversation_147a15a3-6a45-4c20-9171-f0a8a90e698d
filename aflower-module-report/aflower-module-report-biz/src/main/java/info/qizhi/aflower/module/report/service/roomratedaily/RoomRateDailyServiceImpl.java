package info.qizhi.aflower.module.report.service.roomratedaily;


import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.AccountListReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.AccountRespDTO;
import info.qizhi.aflower.module.pms.api.channel.ChannelApi;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelReqDTO;
import info.qizhi.aflower.module.pms.api.channel.dto.ChannelRespDTO;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeReqDTO;
import info.qizhi.aflower.module.pms.api.roomtype.dto.RoomTypeRespDTO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.roomratedaily.RoomRateDailyDO;
import info.qizhi.aflower.module.report.dal.mysql.roomratedaily.RoomRateDailyMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import info.qizhi.aflower.module.system.api.user.AdminUserApi;
import info.qizhi.aflower.module.system.api.user.dto.UserSimpleRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 房费日报表(固化) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class RoomRateDailyServiceImpl implements RoomRateDailyService {

    @Resource
    private RoomRateDailyMapper roomRateDailyMapper;

    @Resource
    private MerchantApi merchantApi;
    @Resource
    private RoomTypeApi roomTypeApi;
    @Resource
    private ChannelApi channelApi;
    @Resource
    private AccountApi accountApi;
    @Resource
    private AdminUserApi userApi;


    @Override
    public RoomRateDailyReportRespVO getRateDailyReport(RoomRateDailyReportReqVO reqVO) {
        RoomRateDailyReportRespVO roomRateDailyReportRespVO = new RoomRateDailyReportRespVO();
        // 填充门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        roomRateDailyReportRespVO.setHname(merchant.getHname())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());
        // 获取房费日报表列表
        List<RoomRateDailyDO> roomRateDailyDOList = roomRateDailyMapper.selectList(reqVO);
        List<RoomRateDailyRespVO> list = BeanUtils.toBean(roomRateDailyDOList, RoomRateDailyRespVO.class);
        // 获得账务优惠卷抵扣科目集合
        List<AccountRespDTO> accountList = accountApi.getAccountList(new AccountListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
                .setStartDate(reqVO.getStartDate()).setEndDate(reqVO.getEndDate())
                .setSubCodes(CollUtil.newArrayList(ConsumeAccountEnum.COUPON_DEDUCTION.getCode()))).getData();
        Map<String, Long> couponDeductionMap = CollectionUtils.convertMap(accountList, AccountRespDTO::getNo, AccountRespDTO::getFee);
        convertToName(list, couponDeductionMap, reqVO.getGcode(), reqVO.getHcode());
        // 设置总费用，总间夜数
        roomRateDailyReportRespVO.setList(list)
                .setTotalFee(list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum())
                .setTotalNightNum(list.stream()
                        .map(RoomRateDailyRespVO::getNightNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)); // 使用 reduce 求和);
        return roomRateDailyReportRespVO;
    }

    private List<RoomRateDailyRespVO> convertToName(List<RoomRateDailyRespVO> list, Map<String, Long> couponDeductionMap,
                                                    String gcode, String hcode) {
        // 获得渠道列表
        List<ChannelRespDTO> channelList = channelApi.getChannelList(new ChannelReqDTO().setGcode(gcode).setHcode(hcode).setIsEnable(NumberEnum.ONE.getNumber())).getData();
        Map<String, ChannelRespDTO> channelMap = CollectionUtils.convertMap(channelList, ChannelRespDTO::getChannelCode);
        // 获得房型列表
        List<RoomTypeRespDTO> roomTypeList = roomTypeApi.getPhysicsRoomTypeList(new RoomTypeReqDTO().setGcode(gcode).setHcode(hcode)).getData();
        Map<String, String> roomNameMap = CollectionUtils.convertMap(roomTypeList, RoomTypeRespDTO::getRtCode, RoomTypeRespDTO::getRtName);

        // 获得用户昵称
        List<UserSimpleRespDTO> userList = userApi.getSimpleUserList(gcode, null).getData();
        Map<String, String> nickNameMap = CollectionUtils.convertMap(userList, UserSimpleRespDTO::getUsername, UserSimpleRespDTO::getNickname);

        for (RoomRateDailyRespVO data : list) {
            data.setOrderSrcName(OrderSrcEnum.getNameByCode(data.getOrderSrc()));
            data.setRtName(roomNameMap.get(data.getRtCode()));
            data.setGSrcName(GuestSrcTypeEnum.getLabelByCode(data.getGSrc()));
            data.setOperatorName(nickNameMap.getOrDefault(data.getOperator(), ""));
            data.setUrl(String.format(
                    OrderUrlEnum.URL.getCode(),
                    data.getOrderNo(),
                    NoTypeEnum.ORDER.getCode()
            ));
            if (data.getCreateChannel() != null) {
                data.setCreateChannelName(channelMap.getOrDefault(data.getCreateChannel(), new ChannelRespDTO()).getChannelName());
            }
            if (data.getStatChannel() != null) {
                data.setStatChannelName(channelMap.getOrDefault(data.getStatChannel(), new ChannelRespDTO()).getChannelName());
            }
            data.setInTypeName(CheckInTypeEnum.getNameByCode(data.getInType()));
            data.setCouponDeduction(couponDeductionMap.getOrDefault(data.getOrderNo(), 0L));

        }
        return list;
    }

    @Override
    public Boolean createRoomRateDailies(List<RoomRateDailySaveReqVO> roomRateDailySaveReqVOList) {
        List<RoomRateDailyDO> roomRateDailyDOList = BeanUtils.toBean(roomRateDailySaveReqVOList, RoomRateDailyDO.class);
        if (CollUtil.isNotEmpty(roomRateDailyDOList)) {
            return roomRateDailyMapper.insertBatch(roomRateDailyDOList);
        }
        return true;
    }


}