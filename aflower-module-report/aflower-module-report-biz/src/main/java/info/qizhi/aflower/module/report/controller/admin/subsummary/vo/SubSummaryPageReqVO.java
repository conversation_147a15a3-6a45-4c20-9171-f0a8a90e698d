package info.qizhi.aflower.module.report.controller.admin.subsummary.vo;


import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;


@Schema(description = "管理后台 - 酒店科目汇总表(固化)分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SubSummaryPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "科目代码")
    private String subCode;

    @Schema(description = "科目名称", example = "芋艿")
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", example = "2")
    private String subType;

    @Schema(description = "营业日")
    private LocalDate bizDay;

    @Schema(description = "本月发生累计")
    private Long curMonthFee;

    @Schema(description = "本年发生累计")
    private Long curYearFee;

    @Schema(description = "昨日余额")
    private Long yesterdayBalance;

    @Schema(description = "今日发生")
    private Long todayOccur;

    @Schema(description = "今日(结账)收回")
    private Long todaySettle;

    @Schema(description = "今日余额")
    private Long todayBalance;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}