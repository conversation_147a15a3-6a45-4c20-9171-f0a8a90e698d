package info.qizhi.aflower.module.report.service.checkintypestatdaily;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import info.qizhi.aflower.module.report.controller.admin.checkintypestatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.checkintypestatdaily.CheckInTypeStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.report.dal.mysql.checkintypestatdaily.CheckInTypeStatDailyMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.*;

/**
 * 每日入住类型统计报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CheckInTypeStatDailyServiceImpl implements CheckInTypeStatDailyService {

    @Resource
    private CheckInTypeStatDailyMapper checkInTypeStatDailyMapper;

    @Override
    public Long createCheckInTypeStatDaily(CheckInTypeStatDailySaveReqVO createReqVO) {
        // 插入
        CheckInTypeStatDailyDO checkInTypeStatDaily = BeanUtils.toBean(createReqVO, CheckInTypeStatDailyDO.class);
        checkInTypeStatDailyMapper.insert(checkInTypeStatDaily);
        // 返回
        return checkInTypeStatDaily.getId();
    }

    @Override
    public void updateCheckInTypeStatDaily(CheckInTypeStatDailySaveReqVO updateReqVO) {
        // 校验存在
        validateCheckInTypeStatDailyExists(updateReqVO.getId());
        // 更新
        CheckInTypeStatDailyDO updateObj = BeanUtils.toBean(updateReqVO, CheckInTypeStatDailyDO.class);
        checkInTypeStatDailyMapper.updateById(updateObj);
    }

    @Override
    public void deleteCheckInTypeStatDaily(Long id) {
        // 校验存在
        validateCheckInTypeStatDailyExists(id);
        // 删除
        checkInTypeStatDailyMapper.deleteById(id);
    }

    private void validateCheckInTypeStatDailyExists(Long id) {
        if (checkInTypeStatDailyMapper.selectById(id) == null) {
            throw exception(CHECK_IN_TYPE_STAT_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public CheckInTypeStatDailyDO getCheckInTypeStatDaily(Long id) {
        return checkInTypeStatDailyMapper.selectById(id);
    }

    @Override
    public PageResult<CheckInTypeStatDailyDO> getCheckInTypeStatDailyPage(CheckInTypeStatDailyPageReqVO pageReqVO) {
        return checkInTypeStatDailyMapper.selectPage(pageReqVO);
    }

}