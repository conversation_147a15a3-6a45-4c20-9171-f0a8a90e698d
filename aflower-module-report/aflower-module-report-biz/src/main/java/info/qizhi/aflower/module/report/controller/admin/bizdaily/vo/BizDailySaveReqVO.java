package info.qizhi.aflower.module.report.controller.admin.bizdaily.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 营业日报(固化)新增/修改 Request VO")
@Data
public class BizDailySaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19539")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "日期类型;0：本日 1：本月 2：本年", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "{dateType.notempty}")
    private String dateType;

    @Schema(description = "最后查询时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{lastQueryTime.notnull}")
    private LocalDateTime lastQueryTime;

    @Schema(description = "总营业指标:客房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{roomNum.notnull}")
    private Integer roomNum;

    @Schema(description = "总营业指标:维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{repairNum.notnull}")
    private Integer repairNum;

    @Schema(description = "总营业指标:过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightNum.notnull}")
    private Integer nightNum;

    @Schema(description = "总营业指标:过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightOcc.notnull}")
    private BigDecimal nightOcc;

    @Schema(description = "总营业指标:间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightRoomNum.notempty}")
    private BigDecimal nightRoomNum;

    @Schema(description = "总营业指标:房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{roomFee.notnull}")
    private Long roomFee;

    @Schema(description = "总营业指标:平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{adr.notnull}")
    private Long adr;

    @Schema(description = "总营业指标:出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{occ.notnull}")
    private BigDecimal occ;

    @Schema(description = "总营业指标:RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{revPar.notnull}")
    private BigDecimal revPar;

    @Schema(description = "总营业指标:现付账房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{accFee.notnull}")
    private Long accFee;

    @Schema(description = "非门店收入:会员充值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{recharge.notnull}")
    private Long recharge;

    @Schema(description = "客源:中介", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{agent.notnull}")
    private Long agent;

    @Schema(description = "客源:协议单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{protocol.notnull}")
    private Long protocol;

    @Schema(description = "客源:散客", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{workIn.notnull}")
    private Long workIn;

    @Schema(description = "客源:会员", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{member.notnull}")
    private Long member;

    @Schema(description = "入住类型:免费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTypeFree.notnull}")
    private Long checkInTypeFree;

    @Schema(description = "入住类型:时租", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTypeHour.notnull}")
    private Long checkInTypeHour;

    @Schema(description = "入住类型:正常", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTypeNormal.notnull}")
    private Long checkInTypeNormal;

    @Schema(description = "入住类型:旅行团", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTypeTour.notnull}")
    private Long checkInTypeTour;

    @Schema(description = "入住类型:会议", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTypeMeeting.notnull}")
    private Long checkInTypeMeeting;


}