package info.qizhi.aflower.module.report.controller.admin.guestdetail.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 宾客明细报表[固化]分页 Request VO")
@Data
public class GuestDetailReportReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "操作员")
    //@NotEmpty(message = "{operator.notempty}")
    private String operator;

    @Schema(description = "营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

}
