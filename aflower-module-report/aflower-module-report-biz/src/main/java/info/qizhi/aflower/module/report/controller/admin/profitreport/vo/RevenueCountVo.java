package info.qizhi.aflower.module.report.controller.admin.profitreport.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import info.qizhi.aflower.module.report.api.managerdaily.dto.BusinessData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Schema(description = "小程序 - 经营指标日报表(固化) Response VO")
@Data
@ExcelIgnoreUnannotated
public class RevenueCountVo {

    @Schema(description = "消费科目总金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionTypeTotalFee;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "非房收", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("非房收")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long notRoomFee;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("RevPar")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平均房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出租率")
    private BigDecimal occ;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间夜数")
    private BigDecimal nightNum;

    @Schema(description = "经营数据分类统计")
    private List<BusinessData> businessData;

}
