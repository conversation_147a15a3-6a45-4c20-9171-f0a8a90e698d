package info.qizhi.aflower.module.report.controller.admin.bizdaily;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailyPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdaily.BizDailyDO;
import info.qizhi.aflower.module.report.service.bizdaily.BizDailyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 营业日报(固化)")
@RestController
@RequestMapping("/report/biz-daily")
@Validated
public class BizDailyController {

    @Resource
    private BizDailyService bizDailyService;

    @PostMapping("/create")
    @Operation(summary = "创建营业日报(固化)")
    @PreAuthorize("@ss.hasPermission('rp:biz-daily:create')")
    public CommonResult<Long> createBizDaily(@Valid @RequestBody BizDailySaveReqVO createReqVO) {
        return success(bizDailyService.createBizDaily(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新营业日报(固化)")
    @PreAuthorize("@ss.hasPermission('rp:biz-daily:update')")
    public CommonResult<Boolean> updateBizDaily(@Valid @RequestBody BizDailySaveReqVO updateReqVO) {
        bizDailyService.updateBizDaily(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除营业日报(固化)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('rp:biz-daily:delete')")
    public CommonResult<Boolean> deleteBizDaily(@RequestParam("id") Long id) {
        bizDailyService.deleteBizDaily(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得营业日报(固化)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('rp:biz-daily:query')")
    public CommonResult<BizDailyRespVO> getBizDaily(@RequestParam("id") Long id) {
        BizDailyDO bizDaily = bizDailyService.getBizDaily(id);
        return success(BeanUtils.toBean(bizDaily, BizDailyRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得营业日报(固化)分页")
    //@PreAuthorize("@ss.hasPermission('rp:biz-daily:query')")
    public CommonResult<PageResult<BizDailyRespVO>> getBizDailyPage(@Valid BizDailyPageReqVO pageReqVO) {
        PageResult<BizDailyDO> pageResult = bizDailyService.getBizDailyPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, BizDailyRespVO.class));
    }

}