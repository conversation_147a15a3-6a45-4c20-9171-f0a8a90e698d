package info.qizhi.aflower.module.report.service.handoverreport;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.handoverreport.HandoverReportDO;
import info.qizhi.aflower.module.report.dal.mysql.handoverreport.HandoverReportMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.HANDOVER_REPORT_NOT_EXISTS;

/**
 * 交班报表[固化] Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HandoverReportServiceImpl implements HandoverReportService {

    @Resource
    private HandoverReportMapper handoverReportMapper;

    @Override
    public Long createHandoverReport(HandoverReportSaveReqVO createReqVO) {
        // 插入
        HandoverReportDO handoverReport = BeanUtils.toBean(createReqVO, HandoverReportDO.class);
        handoverReportMapper.insert(handoverReport);
        // 返回
        return handoverReport.getId();
    }

    @Override
    public void updateHandoverReport(HandoverReportSaveReqVO updateReqVO) {
        // 校验存在
        validateHandoverReportExists(updateReqVO.getId());
        // 更新
        HandoverReportDO updateObj = BeanUtils.toBean(updateReqVO, HandoverReportDO.class);
        handoverReportMapper.updateById(updateObj);
    }

    @Override
    public void deleteHandoverReport(Long id) {
        // 校验存在
        validateHandoverReportExists(id);
        // 删除
        handoverReportMapper.deleteById(id);
    }

    private void validateHandoverReportExists(Long id) {
        if (handoverReportMapper.selectById(id) == null) {
            throw exception(HANDOVER_REPORT_NOT_EXISTS);
        }
    }

    @Override
    public HandoverReportDO getHandoverReport(Long id) {
        return handoverReportMapper.selectById(id);
    }

    @Override
    public PageResult<HandoverReportDO> getHandoverReportPage(HandoverReportPageReqVO pageReqVO) {
        return handoverReportMapper.selectPage(pageReqVO);
    }

    @Override
    public List<HandoverReportDO> getHandoverReportList(HandoverReportPageReqVO reqVO) {
        return handoverReportMapper.selectList(reqVO);
    }

    @Override
    public List<HandoverReportDO> getHandoverReportListByBizDate(String gcode, String hcode, LocalDate bizDate) {
        return handoverReportMapper.selectListByGcodeAndHcodeAndBizDate(gcode, hcode, bizDate);
    }

    @Override
    public List<HandoverReportDO> getHandoverReportListByShiftNo(String gcode, String hcode, String shiftNo, LocalDate bizDate) {
        return handoverReportMapper.selectListByShiftNo(gcode, hcode, shiftNo, bizDate);
    }

    @Override
    public void createBatchHandoverReport(List<HandoverReportSaveReqVO> createReqVOs) {
        for (HandoverReportSaveReqVO createReqVO : createReqVOs) {
            createHandoverReport(createReqVO);
        }
    }
}
