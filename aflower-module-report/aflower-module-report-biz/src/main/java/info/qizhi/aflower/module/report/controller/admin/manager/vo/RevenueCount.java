package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "report - 收入统计 Response VO")
@Data
public class RevenueCount {

    @Schema(description = "付款科目")
    private List<Detail> paymentDetails;

    @Schema(description = "消费科目")
    private List<Detail> consumptionDetails;

    @Schema(description = "付款科目总金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long paymentTypeTotalFee;

    @Schema(description = "消费科目总金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumptionTypeTotalFee;

    @Schema(description = "会员充值总金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeTotalFee;

    @Schema(description = "门店充值金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mMemberRechargeTotalFee;

    @Schema(description = "集团充值金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    @JsonProperty(value = "gMemberRechargeTotalFee")
    private Long gMemberRechargeTotalFee;

    @Schema(description = "宾客账总金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long customerBillTotalFee;

    @Data
    public static class Detail {

        @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subCode;

        @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED)
        private String subName;

        @Schema(description = "科目合计", requiredMode = Schema.RequiredMode.REQUIRED)
        @JsonSerialize(using = FenToYuanSerializer.class)
        private Long totalFee;
    }
}
