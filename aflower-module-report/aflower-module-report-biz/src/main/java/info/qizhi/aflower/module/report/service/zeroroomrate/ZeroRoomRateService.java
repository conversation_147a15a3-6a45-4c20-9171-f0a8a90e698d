package info.qizhi.aflower.module.report.service.zeroroomrate;


import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReqVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 每日总营业指标报 Service 接口
 *
 * <AUTHOR>
 */
public interface ZeroRoomRateService {

    /**
     * 创建零房费&负房费报表
     * @param createReqVO
     * @return
     */
    Long createZeroRoomRate(@Valid ZeroRoomRateSaveReqVO createReqVO);

    /**
     * 批量创建零房费&负房费报表
     * @param createReqVOS
     * @return
     */
    Boolean batchCreateZeroRoomRate(@Valid List<ZeroRoomRateSaveReqVO> createReqVOS);

    /**
     * 获得零房费&负房费报表
     * @param reqVO
     * @return
     */
    ZeroRoomRateReportRespVO getZeroRoomRate(@Valid ZeroRoomRateReqVO reqVO);
}