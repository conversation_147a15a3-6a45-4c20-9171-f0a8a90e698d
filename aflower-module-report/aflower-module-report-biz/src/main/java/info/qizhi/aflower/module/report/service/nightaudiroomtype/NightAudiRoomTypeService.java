package info.qizhi.aflower.module.report.service.nightaudiroomtype;


import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 夜审房态表(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface NightAudiRoomTypeService {

    /**
     * 创建夜审房态表(固化)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNightAudiRoomType(@Valid NightAudiRoomTypeSaveReqVO createReqVO);

    /**
     * 批量创建
     *
     * @param nightAudiRoomTypeSaveReqVOList
     * @return 成功返回true 失败false
     */
    Boolean createNightAudiRoomTypes(@Valid List<NightAudiRoomTypeSaveReqVO> nightAudiRoomTypeSaveReqVOList);

    /**
     * 夜审房态表(固化)
     */
    NightAudiRoomTypeReportRespVO getNightAudiRoomTypeReport(NightAudiRoomTypeReportReqVO reqVO);

}