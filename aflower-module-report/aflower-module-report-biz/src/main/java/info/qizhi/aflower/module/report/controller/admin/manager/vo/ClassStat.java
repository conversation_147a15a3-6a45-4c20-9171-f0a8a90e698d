package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 经营数据分类统计 Response VO")
@Data
public class ClassStat {

    @Schema(description = "统计类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statType;

    @Schema(description = "统计类型名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String statTypeName;

    @Schema(description = "间夜数分类数据", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ClassNightAndOccStatData> nightNumData;

    @Schema(description = "出租率分类数据", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ClassNightAndOccStatData> occData;

    @Schema(description = "房费分类数据", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ClassFeeStatData> roomFeeData;

    @Schema(description = "平均房价分类数据", requiredMode = Schema.RequiredMode.REQUIRED)
    List<ClassFeeStatData> avgRoomFeeData;
}
