package info.qizhi.aflower.module.report.service.report;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.ConsumeAccountEnum;
import info.qizhi.aflower.framework.common.enums.DictTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.PayAccountEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.AccountRespDTO;
import info.qizhi.aflower.module.pms.api.account.dto.PayOrConsumeDetailReqDTO;
import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatmonth.vo.IncomeStatMonthSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerMonthSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdataclassstatdaily.BizDataClassStatDailyDO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatdaily.IncomeStatDailyDO;
import info.qizhi.aflower.module.report.dal.dataobject.managerdaily.ManagerDailyDO;
import info.qizhi.aflower.module.report.service.bizdataclassstatdaily.BizDataClassStatDailyService;
import info.qizhi.aflower.module.report.service.guestdetail.GuestDetailService;
import info.qizhi.aflower.module.report.service.incomestatdaily.IncomeStatDailyService;
import info.qizhi.aflower.module.report.service.incomestatmonth.IncomeStatMonthService;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerDailyService;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerMonthService;
import info.qizhi.aflower.module.report.service.nightaudiroomtype.NightAudiRoomTypeService;
import info.qizhi.aflower.module.report.service.report.bo.NightAudiReportReqBO;
import info.qizhi.aflower.module.report.service.roomratedaily.RoomRateDailyService;
import info.qizhi.aflower.module.report.service.roomstat.RoomStatService;
import info.qizhi.aflower.module.report.service.zeroroomrate.ZeroRoomRateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: TY
 * @CreateTime: 2024-07-08
 * @Description: 报表服务
 * @Version: 1.0
 */
@Service
@Validated
@Slf4j
public class NightAudiCreateReportServiceImpl implements NightAudiCreateReportService {

    @Resource
    private RoomRateDailyService roomRateDailyService;
    @Resource
    private NightAudiRoomTypeService nightAudiRoomTypeService;
    @Resource
    private GuestDetailService guestDetailsService;
    @Resource
    private ManagerDailyService managerDayService;
    @Resource
    private IncomeStatDailyService incomeStatsService;
    @Resource
    private BizDataClassStatDailyService bizDataClassStatDailyService;
    @Resource
    private ManagerMonthService managerMonthService;
    @Resource
    private ZeroRoomRateService zeroRoomRateService;
    @Resource
    private RoomStatService roomStatService;
    @Resource
    private IncomeStatMonthService incomeStatMonthService;
    @Resource
    private AccountApi accountApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean batchCreateReports(NightAudiReportReqBO nightAudiReportReqBO) {
        boolean success = true;
        success = roomRateDailyService.createRoomRateDailies(nightAudiReportReqBO.getRoomRateDailyList());
        if (!success) {
            return false;
        }
        success = nightAudiRoomTypeService.createNightAudiRoomTypes(nightAudiReportReqBO.getNightAudiRoomTypeList());
        if (!success) {
            return false;
        }
        success = guestDetailsService.createGuestDetails(nightAudiReportReqBO.getGuestDetailList());
        if (!success) {
            return false;
        }
        Long id = managerDayService.createManagerDaily(nightAudiReportReqBO.getManagerDaily());
        if (id == null) {
            success = false;
        }
        success = incomeStatsService.createIncomeStatDailies(nightAudiReportReqBO.getIncomeStatDailyList());
        if (!success) {
            return false;
        }
        success = bizDataClassStatDailyService.createBizDataClassStatDailies(nightAudiReportReqBO.getBizDataClassStatDailyList());
        if (!success) {
            return false;
        }
        success = zeroRoomRateService.batchCreateZeroRoomRate(nightAudiReportReqBO.getZeroRoomRateSaveList());
        if (!success) {
            return false;
        }
        success = roomStatService.batchCreateRoomStat(nightAudiReportReqBO.getRoomStatSaveList());
        if (!success) {
            return false;
        }
        LocalDate bizDate = nightAudiReportReqBO.getBizDate();
        if (bizDate != null && bizDate.equals(bizDate.with(TemporalAdjusters.lastDayOfMonth()))) {
            try {
                createManagerMonth(bizDate, nightAudiReportReqBO.getGcode(), nightAudiReportReqBO.getHcode());
            } catch (Exception e) {
                log.info("月度报告生成失败: {}", e.getMessage(), e);
                //throw new ServiceException("月度报告生成失败: " + e.getMessage());
            }
        }
        return success;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createManagerMonth(LocalDate bizDate, String gcode, String hcode) {
        // 1. 计算当前月份时间范围
        LocalDate firstDayOfMonth = bizDate.withDayOfMonth(1);
        LocalDate lastDayOfMonth = bizDate.with(TemporalAdjusters.lastDayOfMonth());


        List<ManagerDailyDO> dailyList = queryDailyData(gcode, hcode, firstDayOfMonth, lastDayOfMonth);
        if(CollUtil.isEmpty(dailyList)){
            return;
        }

        // 2. 查询基础数据
        List<BizDataClassStatDailyDO> bizDataList = queryBizData(gcode, hcode, firstDayOfMonth, lastDayOfMonth);

        // 获得房间账务
        List<AccountRespDTO> accountDOList = accountApi.getPayOrConsumeAccountList(new PayOrConsumeDetailReqDTO()
                .setGcode(gcode).setHcode(hcode).setStartDate(firstDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .setEndDate(lastDayOfMonth.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).setTimeType(NumberEnum.ONE.getNumber())).getData();
        // 过滤出只有未结的预授权
        // 排除所有预授权类型账务
        List<AccountRespDTO> filteredAccountDOList = accountDOList.stream()
                .filter(account -> !Set.of(
                        PayAccountEnum.BANK_PRE_AUTH.getCode(),
                        PayAccountEnum.SCAN_GUN_PRE_AUTH.getCode()
                ).contains(account.getSubCode()))
                .toList();

        // 3. 构建月度报告对象
        ManagerMonthSaveReqVO monthlyReport = buildMonthlyReport(gcode, hcode, firstDayOfMonth, dailyList, bizDataList, filteredAccountDOList);

        // 4. 保存数据
        managerMonthService.createManagerMonthReport(monthlyReport);
        // log.info("成功生成{}月份经营报告", firstDayOfMonth);

        // 查询一个月的收入统计，保存到收入月报表中
        createIncomeStatMonth(gcode, hcode, firstDayOfMonth, lastDayOfMonth);


    }

    private void createIncomeStatMonth(String gcode, String hcode, LocalDate firstDayOfMonth, LocalDate lastDayOfMonth) {
        List<IncomeStatDailyDO> incomeStatDailyList = incomeStatsService.getIncomeStatDailyList(new IncomeStatDailyReqVO()
                .setGcode(gcode).setHcode(hcode).setStartDate(firstDayOfMonth).setEndDate(lastDayOfMonth));
        if(CollUtil.isEmpty(incomeStatDailyList)){
            return;
        }
        // 按每个门店的科目subCode统计之和
        Map<String, IncomeStatDailyDO> incomeStatMap = CollectionUtils.convertMap(incomeStatDailyList, IncomeStatDailyDO::getSubCode);
        Map<String, Map<String, Long>> incomeStatDailyMap = incomeStatDailyList.stream()
                .collect(Collectors.groupingBy(IncomeStatDailyDO::getHcode,
                        Collectors.groupingBy(IncomeStatDailyDO::getSubCode,
                                Collectors.summingLong(IncomeStatDailyDO::getFee))));
        List<IncomeStatMonthSaveReqVO> incomeStatMonthSaveReqVOList = new ArrayList<>();
        incomeStatDailyMap.forEach((hcode1, subCodeMap) -> {
            subCodeMap.forEach((subCode, fee) -> {
                IncomeStatMonthSaveReqVO incomeStatMonthSaveReqVO = new IncomeStatMonthSaveReqVO();
                incomeStatMonthSaveReqVO.setGcode(gcode);
                incomeStatMonthSaveReqVO.setHcode(hcode1);
                incomeStatMonthSaveReqVO.setMonth(firstDayOfMonth);
                incomeStatMonthSaveReqVO.setSubCode(subCode);
                incomeStatMonthSaveReqVO.setSubName(incomeStatMap.getOrDefault(subCode, new IncomeStatDailyDO()).getSubName());
                incomeStatMonthSaveReqVO.setSubType(incomeStatMap.getOrDefault(subCode, new IncomeStatDailyDO()).getSubType());
                incomeStatMonthSaveReqVO.setFee(fee);
                incomeStatMonthSaveReqVOList.add(incomeStatMonthSaveReqVO);
            });
        });
        incomeStatMonthService.createIncomeStatMonths(incomeStatMonthSaveReqVOList);
    }


    private List<BizDataClassStatDailyDO> queryBizData(String gcode,
                                                       String hcode,
                                                       LocalDate startDate,
                                                       LocalDate endDate) {
        BizDataClassStatDailyReqVO queryVO = new BizDataClassStatDailyReqVO()
                .setGcode(gcode)
                .setHcode(hcode)
                .setStartDate(startDate)
                .setEndDate(endDate);

        return bizDataClassStatDailyService.getBizDataClassStatDailyList(queryVO);
    }

    private List<ManagerDailyDO> queryDailyData(String gcode,
                                                String hcode,
                                                LocalDate startDate,
                                                LocalDate endDate) {
        ManagerDailyReqVO queryVO = new ManagerDailyReqVO()
                .setGcode(gcode)
                .setHcode(hcode)
                .setStartDate(startDate)
                .setEndDate(endDate);

        return managerDayService.getManagerDailyList(queryVO);
    }

    private ManagerMonthSaveReqVO buildMonthlyReport(String gcode,
                                                     String hcode,
                                                     LocalDate month,
                                                     List<ManagerDailyDO> dailyList,
                                                     List<BizDataClassStatDailyDO> bizDataList,
                                                     List<AccountRespDTO> accountDOList) {

        ManagerMonthSaveReqVO report = new ManagerMonthSaveReqVO();
        // 基础信息
        report.setGcode(gcode);
        report.setHcode(hcode);
        report.setMonth(month);

        // 核心指标计算
        calculateCoreMetrics(dailyList, report, accountDOList);

        // 分类统计数据
        report.setList(convertBizData(bizDataList, report.getTotalRoomNum(), month));


        return report;
    }

    private void calculateCoreMetrics(List<ManagerDailyDO> dailyList,
                                      ManagerMonthSaveReqVO report,
                                      List<AccountRespDTO> accountDOList) {
        // 数值型指标求和
        report.setNightNum(sumBigDecimal(dailyList, ManagerDailyDO::getNightNum));
        report.setRoomFee(sumLong(dailyList, ManagerDailyDO::getRoomFee));
        report.setMemberCardFee(sumLong(dailyList, ManagerDailyDO::getMemberCardFee));

        // 计数型指标求和
        report.setTotalRoomNum(sumInteger(dailyList, ManagerDailyDO::getTotalRoomNum));
        report.setEmptyNum(sumInteger(dailyList, ManagerDailyDO::getEmptyNum));
        report.setRepairNum(sumInteger(dailyList, ManagerDailyDO::getRepairNum));
        report.setSelfNum(sumInteger(dailyList, ManagerDailyDO::getSelfNum));
        report.setOvernightNum(sumInteger(dailyList, ManagerDailyDO::getOvernightNum));
        report.setOpenRoomNum(sumInteger(dailyList, ManagerDailyDO::getOpenRoomNum));
        report.setWorkInNum(sumInteger(dailyList, ManagerDailyDO::getWorkInNum));
        report.setBookInNum(sumInteger(dailyList, ManagerDailyDO::getBookInNum));
        report.setCancelBookNum(sumInteger(dailyList, ManagerDailyDO::getCancelBookNum));
        report.setNoShowNum(sumInteger(dailyList, ManagerDailyDO::getNoShowNum));
        report.setMemberCardSellNum(sumInteger(dailyList, ManagerDailyDO::getMemberCardSellNum));
        report.setCreditNum(sumInteger(dailyList, ManagerDailyDO::getCreditNum));
        report.setFreeUpNum(sumInteger(dailyList, ManagerDailyDO::getFreeUpNum));

        // 比率型指标计算月平均值
        report.setOcc(NumberUtils.occ(report.getNightNum(), report.getTotalRoomNum()));
        report.setOvernightOcc(NumberUtils.occ(new BigDecimal(report.getOvernightNum()), report.getTotalRoomNum()));
        report.setAvgRoomFee(NumberUtils.div(report.getRoomFee(), report.getNightNum(), 0).longValue());
        report.setRevPar(NumberUtils.revPar(report.getRoomFee(), report.getTotalRoomNum()));

        // 会员卡
 /*       long memberCard = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.MEMBER_CARD.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();*/
        // 小商品
        long good = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.GOODS.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 餐饮
        long catering = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.CATERING.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 赔偿
        long indemnityFee = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.INDEMNITY_FEE.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 会员充值
        long memberRecharge = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.MEMBER_RECHARGE.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 优惠卷抵扣
        long couponDeduction = CollectionUtils.filterList(accountDOList,
                item -> ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(item.getSubCode())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 消费
        long consume = CollectionUtils.filterList(accountDOList,
                item -> DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode().equals(item.getSubType())).stream().mapToLong(AccountRespDTO::getFee).sum();
        // 其他消费
        long otherFee = consume - report.getMemberCardFee() - good - catering - indemnityFee - memberRecharge - report.getRoomFee();
        // 门店收入
        long lobbyFee = consume - memberRecharge;

        report.setGood(good);
        report.setCatering(catering);
        report.setIndemnityFee(indemnityFee);
        report.setMemberRecharge(memberRecharge);
        report.setCouponDeduction(couponDeduction);
        report.setOtherFee(otherFee);
        report.setLobbyFee(lobbyFee);
    }

    private List<ManagerMonthSaveReqVO.BizDataClassStatMonthSaveReqVO> convertBizData(List<BizDataClassStatDailyDO> source, Integer totalRoomNum, LocalDate month) {
        // 按 statType 和 statCode 分组并合并数据
        Map<String, Map<String, List<BizDataClassStatDailyDO>>> groupedData = source.stream()
                .collect(Collectors.groupingBy(
                        BizDataClassStatDailyDO::getStatType,
                        Collectors.groupingBy(BizDataClassStatDailyDO::getStatCode)
                ));

        // 将分组后的数据转换为合并后的VO列表
        return groupedData.values().stream()
                .flatMap(innerMap -> innerMap.values().stream())
                .map(group -> {
                    // 取组内第一条记录作为基础
                    BizDataClassStatDailyDO first = group.getFirst();

                    ManagerMonthSaveReqVO.BizDataClassStatMonthSaveReqVO vo = new ManagerMonthSaveReqVO.BizDataClassStatMonthSaveReqVO();
                    vo.setGcode(first.getGcode());
                    vo.setHcode(first.getHcode());
                    vo.setMonth(month);
                    vo.setStatType(first.getStatType());
                    vo.setStatTypeName(first.getStatTypeName());
                    vo.setStatCode(first.getStatCode());
                    vo.setStatName(first.getStatName());

                    // 计算合并值
                    vo.setNightNum(sumBigDecimal(group, BizDataClassStatDailyDO::getNightNum));
                    vo.setRoomFee(sumLong(group, BizDataClassStatDailyDO::getRoomFee));
                    vo.setAvgRoomFee(NumberUtils.div(vo.getRoomFee(), vo.getNightNum(), 0).longValue());
                    vo.setOcc(NumberUtils.occ(vo.getNightNum(), totalRoomNum));

                    return vo;
                })
                .collect(Collectors.toList());
    }

    // 以下为工具方法
    // 辅助方法：计算平均房价
    private Long calculateAvgRoomFee(List<BizDataClassStatDailyDO> group) {
        long totalRoomFee = sumLong(group, BizDataClassStatDailyDO::getRoomFee);
        BigDecimal totalNightNum = sumBigDecimal(group, BizDataClassStatDailyDO::getNightNum);

        if (totalNightNum.compareTo(BigDecimal.ZERO) == 0) {
            return 0L;
        }
        return totalRoomFee / totalNightNum.longValue();
    }

    // 辅助方法：计算平均出租率
    private BigDecimal calculateAvgOcc(List<BizDataClassStatDailyDO> group) {
        BigDecimal totalOcc = sumBigDecimal(group, BizDataClassStatDailyDO::getOcc);
        return totalOcc.divide(new BigDecimal(group.size()), 2, RoundingMode.HALF_UP);
    }


    /**
     * BigDecimal类型求和（线程安全）
     * @param <T> 集合元素类型
     */
    public static <T> BigDecimal sumBigDecimal(Collection<T> collection,
                                               Function<? super T, BigDecimal> mapper) {
        // 参数校验
        Objects.requireNonNull(collection, "集合不能为null");
        Objects.requireNonNull(mapper, "映射函数不能为null");

        // 并行流处理（线程安全）
        return collection.parallelStream()
                .map(mapper)                 // 转换为BigDecimal
                .filter(Objects::nonNull)     // 过滤null值
                .reduce(BigDecimal.ZERO,     // 初始值
                        BigDecimal::add,      // 累加器
                        BigDecimal::add);     // 合并器（并行流使用）
    }

    public static <T> Long sumLong(Collection<T> collection,
                                   Function<? super T, Long> mapper) {
        // 参数校验
        Objects.requireNonNull(collection, "集合不能为null");
        Objects.requireNonNull(mapper, "映射函数不能为null");

        // 并行流处理（线程安全）
        return collection.parallelStream()
                .map(mapper)          // 转换为Long
                .filter(Objects::nonNull) // 过滤null值
                .mapToLong(Long::longValue) // 拆箱为long
                .sum();               // 求和
    }

    private Integer sumInteger(List<ManagerDailyDO> list, Function<ManagerDailyDO, Integer> mapper) {
        return list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
    }

    private BigDecimal avgBigDecimal(List<ManagerDailyDO> list, Function<ManagerDailyDO, BigDecimal> mapper) {
        List<BigDecimal> values = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .toList();

        if (values.isEmpty()) return BigDecimal.ZERO;

        BigDecimal sum = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        return sum.divide(new BigDecimal(values.size()), 2, RoundingMode.HALF_UP);
    }

    private Long avgLong(List<ManagerDailyDO> list, Function<ManagerDailyDO, Long> mapper) {
        List<Long> values = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .toList();

        if (values.isEmpty()) return 0L;

        return (long) values.stream()
                .mapToLong(Long::longValue)
                .average()
                .orElse(0);
    }
}
