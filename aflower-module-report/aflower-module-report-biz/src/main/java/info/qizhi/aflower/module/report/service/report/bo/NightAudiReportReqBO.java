package info.qizhi.aflower.module.report.service.report.bo;

import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailySaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailySaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailySaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailySaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatSaveReqVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * @Author: TY
 * @CreateTime: 2024-07-08
 * @Description: 夜审产生报表DTO
 * @Version: 1.0
 */
@Schema(description = "管理后台 - 夜审产生报表 Request BO")
@Data
public class NightAudiReportReqBO {
    @Schema(description = "集团代码")
    private String gcode;
    @Schema(description = "门店代码")
    private String hcode;
    @Schema(description = "营业日")
    private LocalDate bizDate;

    @Schema(description = "夜审房态表[固化]列表")
    private List<NightAudiRoomTypeSaveReqVO> nightAudiRoomTypeList;

    @Schema(description = "宾客明细报表[固化]列表")
    private List<GuestDetailSaveReqVO> guestDetailList;

    @Schema(description = "房费日报表[固化]列表")
    private List<RoomRateDailySaveReqVO> roomRateDailyList;

    @Schema(description = "零房费&负房费报表[固化]列表")
    private List<ZeroRoomRateSaveReqVO> zeroRoomRateSaveList;

    @Schema(description = "客房营收统计报表[固化]列表")
    private List<RoomStatSaveReqVO> roomStatSaveList;

    @Schema(description = "经理综合日报表[固化]")
    private ManagerDailySaveReqVO managerDaily;

    @Schema(description = "每日收入统计报表")
    private List<IncomeStatDailySaveReqVO> incomeStatDailyList;

    @Schema(description = "每日经营数据分类统计报表")
    private List<BizDataClassStatDailySaveReqVO> bizDataClassStatDailyList;
}
