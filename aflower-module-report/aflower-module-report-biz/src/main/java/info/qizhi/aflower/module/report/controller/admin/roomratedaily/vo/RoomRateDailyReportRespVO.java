package info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 房费日报表(固化) Response VO")
@Data
public class RoomRateDailyReportRespVO {

    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "开始日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", example = "报表操作员")
    private String operator;

    @Schema(description = "总费用", example = "20.0")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalFee;

    @Schema(description = "总间夜数", example = "20.0")
    private BigDecimal totalNightNum;

    @Schema(description = "房费日报表列表")
    private List<RoomRateDailyRespVO> list;
}
