package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 收入统计 Request VO")
@Data
public class ManagerIncomeRespVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间夜数")
    private BigDecimal nightNum;

    @Schema(description = "总房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "优惠券抵扣", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("优惠券抵扣")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long couponDeduction;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("RevPar")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平均房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出租率")
    private BigDecimal occ;

    @Schema(description = "总房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总房数")
    private Integer totalRoomNum;

    @Schema(description = "空房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("空房数")
    private Integer emptyNum;

    @Schema(description = "维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("维修房")
    private Integer repairNum;

    @Schema(description = "自用房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自用房")
    private Integer selfNum;

    @Schema(description = "过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜房")
    private Integer overnightNum;

    @Schema(description = "过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜房出租率")
    private BigDecimal overnightOcc;

    @Schema(description = "开房数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("开房数(自然日)")
    private Integer openRoomNum;

    @Schema(description = "上门散客数(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("上门散客数(自然日)")
    private Integer workInNum;

    @Schema(description = "预订入住(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预订入住(自然日)")
    private Integer bookInNum;

    @Schema(description = "取消预订(自然日)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("取消预订(自然日)")
    private Integer cancelBookNum;

    @Schema(description = "NoShow", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("NoShow")
    private Integer noShowNum;

    @Schema(description = "会员卡销售数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员卡销售数")
    private Integer memberCardSellNum;

    @Schema(description = "挂账数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("挂账数")
    private Integer creditNum;

    @Schema(description = "免费升级数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("免费升级数")
    private Integer freeUpNum;

    @Schema(description = "会员卡销售金额")
    @ExcelProperty("会员卡销售金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberCardFee;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /*@Schema(description = "房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;*/

/*    @Schema(description = "会员卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberCardFee;*/

    @Schema(description = "小商品")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long goodFee;

    @Schema(description = "餐饮")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long cateringFee;

    @Schema(description = "赔偿费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long indemnityFee;

    @Schema(description = "其他消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherFee;

    @Schema(description = "消费合计")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long consumeTotalFee;

    @Schema(description = "会员充值")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeFee;

    @Schema(description = "现金")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long cashFee;

    @Schema(description = "银行卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long bankCardFee;

    @Schema(description = "AR账")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long arFee;

    @Schema(description = "在线支付")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long onlinePayFee;

    @Schema(description = "微信支付")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long wxPayFee;

    @Schema(description = "支付宝支付")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long alipayPayFee;

    @Schema(description = "储值卡")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long storeCardFee;

    @Schema(description = "其他收款")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherReceiptFee;

    @Schema(description = "收款合计")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long receiptTotalFee;
}
