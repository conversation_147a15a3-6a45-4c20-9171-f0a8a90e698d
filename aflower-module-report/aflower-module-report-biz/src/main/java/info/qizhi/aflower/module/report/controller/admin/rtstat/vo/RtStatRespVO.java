package info.qizhi.aflower.module.report.controller.admin.rtstat.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 房型统计报 Response VO")
@Data
@ExcelIgnoreUnannotated
public class RtStatRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9539")
    @ExcelProperty("id")
    private Integer id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房型代码")
    private String rtCode;

    @Schema(description = "房型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @ExcelProperty("房型名称")
    private String rtName;

    @Schema(description = "本日间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本日间夜数")
    private BigDecimal todayNightNum;

    @Schema(description = "本月间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本月间夜数")
    private BigDecimal curMonthNightNum;

    @Schema(description = "本年间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("本年间夜数")
    private BigDecimal curYearNightNum;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}