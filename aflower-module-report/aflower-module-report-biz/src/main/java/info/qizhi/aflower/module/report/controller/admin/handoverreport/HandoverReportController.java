package info.qizhi.aflower.module.report.controller.admin.handoverreport;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.handoverreport.HandoverReportDO;
import info.qizhi.aflower.module.report.service.handoverreport.HandoverReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 交班报表[固化]")
@RestController
@RequestMapping("/report/handover-report")
@Validated
public class HandoverReportController {

    @Resource
    private HandoverReportService handoverReportService;

    @PostMapping("/create")
    @Operation(summary = "创建交班报表[固化]")
    @PreAuthorize("@ss.hasPermission('report:handover-report:create')")
    public CommonResult<Long> createHandoverReport(@Valid @RequestBody HandoverReportSaveReqVO createReqVO) {
        return success(handoverReportService.createHandoverReport(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新交班报表[固化]")
    @PreAuthorize("@ss.hasPermission('report:handover-report:update')")
    public CommonResult<Boolean> updateHandoverReport(@Valid @RequestBody HandoverReportSaveReqVO updateReqVO) {
        handoverReportService.updateHandoverReport(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除交班报表[固化]")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('report:handover-report:delete')")
    public CommonResult<Boolean> deleteHandoverReport(@RequestParam("id") Long id) {
        handoverReportService.deleteHandoverReport(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得交班报表[固化]")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('report:handover-report:query')")
    public CommonResult<HandoverReportRespVO> getHandoverReport(@RequestParam("id") Long id) {
        HandoverReportDO handoverReport = handoverReportService.getHandoverReport(id);
        return success(BeanUtils.toBean(handoverReport, HandoverReportRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得交班报表[固化]分页")
    @PreAuthorize("@ss.hasPermission('report:handover-report:query')")
    public CommonResult<PageResult<HandoverReportRespVO>> getHandoverReportPage(@Valid HandoverReportPageReqVO pageReqVO) {
        PageResult<HandoverReportDO> pageResult = handoverReportService.getHandoverReportPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, HandoverReportRespVO.class));
    }

    @GetMapping("/list")
    @Operation(summary = "获得交班报表[固化]列表")
    @PreAuthorize("@ss.hasPermission('report:handover-report:query')")
    public CommonResult<List<HandoverReportRespVO>> getHandoverReportList(@Valid HandoverReportPageReqVO reqVO) {
        List<HandoverReportDO> list = handoverReportService.getHandoverReportList(reqVO);
        return success(BeanUtils.toBean(list, HandoverReportRespVO.class));
    }

    @GetMapping("/list-by-biz-date")
    @Operation(summary = "根据营业日期获得交班报表[固化]列表")
    @PreAuthorize("@ss.hasPermission('report:handover-report:query')")
    public CommonResult<List<HandoverReportRespVO>> getHandoverReportListByBizDate(
            @RequestParam("gcode") String gcode,
            @RequestParam("hcode") String hcode,
            @RequestParam("bizDate") LocalDate bizDate) {
        List<HandoverReportDO> list = handoverReportService.getHandoverReportListByBizDate(gcode, hcode, bizDate);
        return success(BeanUtils.toBean(list, HandoverReportRespVO.class));
    }

    @GetMapping("/list-by-shift-no")
    @Operation(summary = "根据班次号获得交班报表[固化]列表")
    @PreAuthorize("@ss.hasPermission('report:handover-report:query')")
    public CommonResult<List<HandoverReportRespVO>> getHandoverReportListByShiftNo(
            @RequestParam("gcode") String gcode,
            @RequestParam("hcode") String hcode,
            @RequestParam("shiftNo") String shiftNo,
            @RequestParam("bizDate") LocalDate bizDate) {
        List<HandoverReportDO> list = handoverReportService.getHandoverReportListByShiftNo(gcode, hcode, shiftNo, bizDate);
        return success(BeanUtils.toBean(list, HandoverReportRespVO.class));
    }

}
