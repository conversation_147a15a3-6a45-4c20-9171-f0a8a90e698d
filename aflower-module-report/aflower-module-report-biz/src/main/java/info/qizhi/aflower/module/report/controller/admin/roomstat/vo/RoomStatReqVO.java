package info.qizhi.aflower.module.report.controller.admin.roomstat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 客房营收统计报表(固化) Request VO")
@Data
public class RoomStatReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2221")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "楼栋代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String buildingCode;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

}
