package info.qizhi.aflower.module.report.dal.dataobject.checkintypestatdaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日入住类型统计报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_check_in_type_stat_daily")
@KeySequence("rp_check_in_type_stat_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CheckInTypeStatDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 星期
     */
    private Integer week;
    /**
     * 时租-间夜数
     */
    private BigDecimal hourNightNum;
    /**
     * 时租-房费
     */
    private Long hourRoomFee;
    /**
     * 时租-平均房价
     */
    private Long hourAvgRoomFee;
    /**
     * 时租-出租率
     */
    private BigDecimal hourOcc;
    /**
     * 全天房-间夜数
     */
    private BigDecimal allDayNightNum;
    /**
     * 全天房-房费
     */
    private Long allDayRoomFee;
    /**
     * 全天房-平均房价
     */
    private Long allDayAvgRoomFee;
    /**
     * 全天房-出租率
     */
    private BigDecimal allDayOcc;
    /**
     * 长包-间夜数
     */
    private BigDecimal longStayNightNum;
    /**
     * 长包-房费
     */
    private Long longStayRoomFee;
    /**
     * 长包-平均房价
     */
    private Long longStayAvgRoomFee;
    /**
     * 长包-出租率
     */
    private BigDecimal longStayOcc;

}