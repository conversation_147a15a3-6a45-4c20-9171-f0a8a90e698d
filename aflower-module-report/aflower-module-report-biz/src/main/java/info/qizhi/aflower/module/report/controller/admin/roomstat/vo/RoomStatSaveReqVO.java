package info.qizhi.aflower.module.report.controller.admin.roomstat.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Schema(description = "管理后台 - 客房营收统计报表(固化)新增/修改 Request VO")
@Data
public class RoomStatSaveReqVO {
    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "2221")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{rtCode.notempty}")
    private String rtCode;

    @Schema(description = "房间代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{rCode.notempty}")
    private String rCode;

    @Schema(description = "客房数")
    private Integer roomNum;

    @Schema(description = "间夜数")
    private BigDecimal nightNum;

    @Schema(description = "房费")
    private Long roomFee;

    @Schema(description = "其他消费")
    private Long otherFee;

    @Schema(description = "RevPar")
    private Long revPar;

    @Schema(description = "自用房")
    private Integer selfNum;

    @Schema(description = "维修房")
    private Integer repairNum;

}
