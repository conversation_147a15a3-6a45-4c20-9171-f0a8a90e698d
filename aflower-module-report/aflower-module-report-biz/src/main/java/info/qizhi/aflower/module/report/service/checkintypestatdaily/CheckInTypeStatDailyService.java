package info.qizhi.aflower.module.report.service.checkintypestatdaily;

import java.util.*;
import jakarta.validation.*;
import info.qizhi.aflower.module.report.controller.admin.checkintypestatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.checkintypestatdaily.CheckInTypeStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * 每日入住类型统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface CheckInTypeStatDailyService {

    /**
     * 创建每日入住类型统计报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCheckInTypeStatDaily(@Valid CheckInTypeStatDailySaveReqVO createReqVO);

    /**
     * 更新每日入住类型统计报
     *
     * @param updateReqVO 更新信息
     */
    void updateCheckInTypeStatDaily(@Valid CheckInTypeStatDailySaveReqVO updateReqVO);

    /**
     * 删除每日入住类型统计报
     *
     * @param id 编号
     */
    void deleteCheckInTypeStatDaily(Long id);

    /**
     * 获得每日入住类型统计报
     *
     * @param id 编号
     * @return 每日入住类型统计报
     */
    CheckInTypeStatDailyDO getCheckInTypeStatDaily(Long id);

    /**
     * 获得每日入住类型统计报分页
     *
     * @param pageReqVO 分页查询
     * @return 每日入住类型统计报分页
     */
    PageResult<CheckInTypeStatDailyDO> getCheckInTypeStatDailyPage(CheckInTypeStatDailyPageReqVO pageReqVO);

}