package info.qizhi.aflower.module.report.service.bizdaily;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailyPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.bizdaily.vo.BizDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdaily.BizDailyDO;
import jakarta.validation.Valid;

/**
 * 营业日报(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface BizDailyService {

    /**
     * 创建营业日报(固化)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBizDaily(@Valid BizDailySaveReqVO createReqVO);

    /**
     * 更新营业日报(固化)
     *
     * @param updateReqVO 更新信息
     */
    void updateBizDaily(@Valid BizDailySaveReqVO updateReqVO);

    /**
     * 删除营业日报(固化)
     *
     * @param id 编号
     */
    void deleteBizDaily(Long id);

    /**
     * 获得营业日报(固化)
     *
     * @param id 编号
     * @return 营业日报(固化)
     */
    BizDailyDO getBizDaily(Long id);

    /**
     * 获得营业日报(固化)分页
     *
     * @param pageReqVO 分页查询
     * @return 营业日报(固化)分页
     */
    PageResult<BizDailyDO> getBizDailyPage(BizDailyPageReqVO pageReqVO);

}