package info.qizhi.aflower.module.report.controller.admin.subsummary;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummaryPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummaryRespVO;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummarySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.subsummary.SubSummaryDO;
import info.qizhi.aflower.module.report.service.subsummary.SubSummaryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 酒店科目汇总表(固化)")
@RestController
@RequestMapping("/report/sub-summary")
@Validated
public class SubSummaryController {

    @Resource
    private SubSummaryService subSummaryService;

    @PostMapping("/create")
    @Operation(summary = "创建酒店科目汇总表(固化)")
    @PreAuthorize("@ss.hasPermission('rp:sub-summary:create')")
    public CommonResult<Integer> createSubSummary(@Valid @RequestBody SubSummarySaveReqVO createReqVO) {
        return success(subSummaryService.createSubSummary(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新酒店科目汇总表(固化)")
    @PreAuthorize("@ss.hasPermission('rp:sub-summary:update')")
    public CommonResult<Boolean> updateSubSummary(@Valid @RequestBody SubSummarySaveReqVO updateReqVO) {
        subSummaryService.updateSubSummary(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除酒店科目汇总表(固化)")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('rp:sub-summary:delete')")
    public CommonResult<Boolean> deleteSubSummary(@RequestParam("id") Integer id) {
        subSummaryService.deleteSubSummary(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得酒店科目汇总表(固化)")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('rp:sub-summary:query')")
    public CommonResult<SubSummaryRespVO> getSubSummary(@RequestParam("id") Integer id) {
        SubSummaryDO subSummary = subSummaryService.getSubSummary(id);
        return success(BeanUtils.toBean(subSummary, SubSummaryRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得酒店科目汇总表(固化)分页")
    //@PreAuthorize("@ss.hasPermission('rp:sub-summary:query')")
    public CommonResult<PageResult<SubSummaryRespVO>> getSubSummaryPage(@Valid SubSummaryPageReqVO pageReqVO) {
        PageResult<SubSummaryDO> pageResult = subSummaryService.getSubSummaryPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SubSummaryRespVO.class));
    }



}