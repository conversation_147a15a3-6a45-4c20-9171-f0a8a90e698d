package info.qizhi.aflower.module.report.controller.admin.handoverreport.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 交班报表[固化]新增/修改 Request VO")
@Data
public class HandoverReportSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "集团代码不能为空")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "门店代码不能为空")
    private String hcode;

    @Schema(description = "班次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "班次号不能为空")
    private String shiftNo;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "营业日期不能为空")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "现金金额")
    private Long cashAmount;

    @Schema(description = "银行卡金额")
    private Long bankCardAmount;

    @Schema(description = "微信金额")
    private Long wechatAmount;

    @Schema(description = "支付宝金额")
    private Long alipayAmount;

}
