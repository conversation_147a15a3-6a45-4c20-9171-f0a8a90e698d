package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 经理日报(固化)(小程序查看) Response VO")
@Data
public class ManagerRespVO {

    @Schema(description = "客房营业收入指标")
    private ManagerDailyRespVO roomRevenueIndex;

    @Schema(description = "本日账务")
    private ManagerIncomeRespVO incomeStat;

    @Schema(description = "本月累计")
    private ManagerIncomeRespVO monthIncomeStat;

    @Schema(description = "去年同期")
    private ManagerIncomeRespVO lastYearSamePeriodIncomeStat;

    @Schema(description = "上月累计")
    private ManagerIncomeRespVO lastMonthIncomeStat;

    @Schema(description = "本年累计")
    private ManagerIncomeRespVO yearIncomeStat;

    @Schema(description = "上年累计")
    private ManagerIncomeRespVO lastYearIncomeStat;

//    @Schema(description = "收入统计")
//    private RevenueCount revenueCount;

    /*@Schema(description = "经营数据分类统计")
    private List<MonthBusinessData> businessData;*/

    @Schema(description = "分类统计数据")
    private List<ClassStat> classStatDataList;


}
