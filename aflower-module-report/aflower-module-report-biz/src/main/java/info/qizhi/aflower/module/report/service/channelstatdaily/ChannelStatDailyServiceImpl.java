package info.qizhi.aflower.module.report.service.channelstatdaily;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import info.qizhi.aflower.module.report.controller.admin.channelstatdaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.channelstatdaily.ChannelStatDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.report.dal.mysql.channelstatdaily.ChannelStatDailyMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.*;

/**
 * 每日渠道统计报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ChannelStatDailyServiceImpl implements ChannelStatDailyService {

    @Resource
    private ChannelStatDailyMapper channelStatDailyMapper;

    @Override
    public Long createChannelStatDaily(ChannelStatDailySaveReqVO createReqVO) {
        // 插入
        ChannelStatDailyDO channelStatDaily = BeanUtils.toBean(createReqVO, ChannelStatDailyDO.class);
        channelStatDailyMapper.insert(channelStatDaily);
        // 返回
        return channelStatDaily.getId();
    }

    @Override
    public void updateChannelStatDaily(ChannelStatDailySaveReqVO updateReqVO) {
        // 校验存在
        validateChannelStatDailyExists(updateReqVO.getId());
        // 更新
        ChannelStatDailyDO updateObj = BeanUtils.toBean(updateReqVO, ChannelStatDailyDO.class);
        channelStatDailyMapper.updateById(updateObj);
    }

    @Override
    public void deleteChannelStatDaily(Long id) {
        // 校验存在
        validateChannelStatDailyExists(id);
        // 删除
        channelStatDailyMapper.deleteById(id);
    }

    private void validateChannelStatDailyExists(Long id) {
        if (channelStatDailyMapper.selectById(id) == null) {
            throw exception(CHANNEL_STAT_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public ChannelStatDailyDO getChannelStatDaily(Long id) {
        return channelStatDailyMapper.selectById(id);
    }

    @Override
    public PageResult<ChannelStatDailyDO> getChannelStatDailyPage(ChannelStatDailyPageReqVO pageReqVO) {
        return channelStatDailyMapper.selectPage(pageReqVO);
    }

}