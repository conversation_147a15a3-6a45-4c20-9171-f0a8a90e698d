package info.qizhi.aflower.module.report.service.rtstat;


import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.rtstat.vo.RtStatSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.rtstat.RtStatDO;
import jakarta.validation.Valid;

/**
 * 房型统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface RtStatService {

    /**
     * 创建房型统计报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createRtStat(@Valid RtStatSaveReqVO createReqVO);

    /**
     * 更新房型统计报
     *
     * @param updateReqVO 更新信息
     */
    void updateRtStat(@Valid RtStatSaveReqVO updateReqVO);

    /**
     * 删除房型统计报
     *
     * @param id 编号
     */
    void deleteRtStat(Integer id);

    /**
     * 获得房型统计报
     *
     * @param id 编号
     * @return 房型统计报
     */
    RtStatDO getRtStat(Integer id);

    /**
     * 获得房型统计报分页
     *
     * @param pageReqVO 分页查询
     * @return 房型统计报分页
     */
    PageResult<RtStatDO> getRtStatPage(RtStatPageReqVO pageReqVO);

}