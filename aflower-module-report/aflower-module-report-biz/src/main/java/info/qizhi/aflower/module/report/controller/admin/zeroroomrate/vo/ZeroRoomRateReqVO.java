package info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 零房费&负房费报表 Request VO")
@Data
public class ZeroRoomRateReqVO {

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码")
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "入住类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checkinType;

    @Schema(description = "开始日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;

    @Schema(description = "操作员")
    private String operator;

}