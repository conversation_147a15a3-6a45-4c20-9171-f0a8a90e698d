package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 业务日报数据传输对象（BusinessDailyReportVO）用于封装和传递每日业务报表的相关信息。
 *
 * 包含的主要业务模块有：
 * - 总营业收入
 * - 商场收入（小商品）
 * - 会籍服务
 * - 会员充值
 * - 其他收入
 * - 物品赔偿
 * - 会议相关收入
 * - 餐饮收入（包括早餐和正餐）
 * - 客房相关数据及其统计指标
 *
 * 此外，还包含了与同比、月累计、年累计相关的各类收入和客房数据的统计值。
 *
 * <AUTHOR>
 * @version 1.0
 */

@Data
public class BusinessDailyReportVO {
    // 报表基本信息
    @Schema(description = "报表日期", example = "2024-07-04")
    private LocalDate bizDate;
    @Schema(description = "报表打印时间", example = "2024-07-04 17:35:53")
    private LocalDate printDate;

    // 一、总营业收入
    @Schema(description = "总营业收入", example = "1000")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalIncome;
    @Schema(description = "总营业收入(月)", example = "1000")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalIncomeMonth;
    @Schema(description = "总营业收入(年)", example = "1000")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalIncomeLastYear;
    @Schema(description = "总营业额(年)", example = "1000")
    private BigDecimal totalIncomeYoY;
    @Schema(description = "总营业额(月)", example = "1000")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalIncomeYear;
    @Schema(description = "总营业额(日)", example = "1000")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long totalIncomeYearLastYear;
    private BigDecimal totalIncomeYearYoY;

    // 二、商场收入（小商品）
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mallIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mallIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mallIncomeLastYear;
    private BigDecimal mallIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mallIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long mallIncomeYearLastYear;
    private BigDecimal mallIncomeYearYoY;

    // 三、会籍服务
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long membershipServiceIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long membershipServiceIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long membershipServiceIncomeLastYear;
    private BigDecimal membershipServiceIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long membershipServiceIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long membershipServiceIncomeYearLastYear;
    private BigDecimal membershipServiceIncomeYearYoY;

    // 会员充值
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRecharge;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeLastYear;
    private BigDecimal memberRechargeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long memberRechargeYearLastYear;
    private BigDecimal memberRechargeYearYoY;

    // 四、其他收入
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeLastYear;
    private BigDecimal otherIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeYearLastYear;
    private BigDecimal otherIncomeYearYoY;

    // 物品赔偿
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long itemCompensation;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long itemCompensationMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long itemCompensationLastYear;
    private BigDecimal itemCompensationYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long itemCompensationYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long itemCompensationYearLastYear;
    private BigDecimal itemCompensationYearYoY;

    // 会议
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long meetingIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long meetingIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long meetingIncomeLastYear;
    private BigDecimal meetingIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long meetingIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long meetingIncomeYearLastYear;
    private BigDecimal meetingIncomeYearYoY;

    // 其他
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeDetail;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeDetailMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeDetailLastYear;
    private BigDecimal otherIncomeDetailYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeDetailYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long otherIncomeDetailYearLastYear;
    private BigDecimal otherIncomeDetailYearYoY;

    // 五、餐饮收入
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long foodIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long foodIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long foodIncomeLastYear;
    private BigDecimal foodIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long foodIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long foodIncomeYearLastYear;
    private BigDecimal foodIncomeYearYoY;

    // 早餐
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long breakfastIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long breakfastIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long breakfastIncomeLastYear;
    private BigDecimal breakfastIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long breakfastIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long breakfastIncomeYearLastYear;
    private BigDecimal breakfastIncomeYearYoY;

    // 正餐
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long dinnerIncome;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long dinnerIncomeMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long dinnerIncomeLastYear;
    private BigDecimal dinnerIncomeYoY;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long dinnerIncomeYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long dinnerIncomeYearLastYear;
    private BigDecimal dinnerIncomeYearYoY;

    // 客房相关数据
    private Integer totalRoomNum;
    private Integer soldRoomNum;
    private Integer repairRoomNum;
    private Integer dailyRoomChangeNum;
    private Integer rentedRoomNum;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomRate;
    private BigDecimal occupancyRate;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revPar;
    private Integer guestCount;
    private Integer netRoomNum;

    // 客房数据的月累计和年累计
    private Integer rentedRoomNumMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomRateMonth;
    private BigDecimal occupancyRateMonth;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revParMonth;

    private Integer rentedRoomNumYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomRateYear;
    private BigDecimal occupancyRateYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revParYear;

    // 同比数据
    private Integer rentedRoomNumLastYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomRateLastYear;
    private BigDecimal occupancyRateLastYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revParLastYear;
    private BigDecimal rentedRoomNumYoY;
    private BigDecimal avgRoomRateYoY;
    private BigDecimal occupancyRateYoY;
    private BigDecimal revParYoY;

    private Integer rentedRoomNumYearLastYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomRateYearLastYear;
    private BigDecimal occupancyRateYearLastYear;
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long revParYearLastYear;
    private BigDecimal rentedRoomNumYearYoY;
    private BigDecimal avgRoomRateYearYoY;
    private BigDecimal occupancyRateYearYoY;
    private BigDecimal revParYearYoY;
}
