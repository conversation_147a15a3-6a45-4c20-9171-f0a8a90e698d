package info.qizhi.aflower.module.report.service.nightaudiroomtype;


import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.RoomStatusEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeRespVO;
import info.qizhi.aflower.module.report.controller.admin.nightaudiroomtype.vo.NightAudiRoomTypeSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.nightaudiroomtype.NightAudiRoomTypeDO;
import info.qizhi.aflower.module.report.dal.mysql.nightaudiroomtype.NightAudiRoomTypeMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.NIGHT_AUDI_ROOM_TYPE_NOT_EXISTS;

/**
 * 夜审房态表(固化) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NightAudiRoomTypeServiceImpl implements NightAudiRoomTypeService {

    @Resource
    private NightAudiRoomTypeMapper nightAudiRoomTypeMapper;

    @Resource
    private MerchantApi merchantApi;

    @Override
    public Long createNightAudiRoomType(NightAudiRoomTypeSaveReqVO createReqVO) {
        // 插入
        NightAudiRoomTypeDO nightAudiRoomType = BeanUtils.toBean(createReqVO, NightAudiRoomTypeDO.class);
        nightAudiRoomTypeMapper.insert(nightAudiRoomType);
        // 返回
        return nightAudiRoomType.getId();
    }

    @Override
    public Boolean createNightAudiRoomTypes(List<NightAudiRoomTypeSaveReqVO> nightAudiRoomTypeSaveReqVOList) {
        List<NightAudiRoomTypeDO> nightAudiRoomTypeDOList = BeanUtils.toBean(nightAudiRoomTypeSaveReqVOList, NightAudiRoomTypeDO.class);
        if (CollUtil.isNotEmpty(nightAudiRoomTypeDOList)) {
            return nightAudiRoomTypeMapper.insertBatch(nightAudiRoomTypeDOList);
        }
        return true;
    }

    private void validateNightAudiRoomTypeExists(Long id) {
        if (nightAudiRoomTypeMapper.selectById(id) == null) {
            throw exception(NIGHT_AUDI_ROOM_TYPE_NOT_EXISTS);
        }
    }

    @Override
    public NightAudiRoomTypeReportRespVO getNightAudiRoomTypeReport(NightAudiRoomTypeReportReqVO reqVO) {
        NightAudiRoomTypeReportRespVO nightAudiRoomTypeRespVO = new NightAudiRoomTypeReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        nightAudiRoomTypeRespVO.setHname(merchant.getHname())
                               .setBizDate(reqVO.getBizDate())
                               .setOperator(reqVO.getOperator())
                               .setLastSelectTime(LocalDateTime.now());
        // 夜审房态列表
        List<NightAudiRoomTypeDO> nightAudiRoomTypeDOList = nightAudiRoomTypeMapper.selectList(reqVO);
        Map<String, String> stateMap = CollectionUtils.convertMap(nightAudiRoomTypeDOList, NightAudiRoomTypeDO::getRNo, NightAudiRoomTypeDO::getState);
        List<NightAudiRoomTypeRespVO> bean = BeanUtils.toBean(nightAudiRoomTypeDOList, NightAudiRoomTypeRespVO.class);
        bean.forEach(item -> {
            //item.setRtStateName(Objects.requireNonNull(RoomStatusEnum.getRoomStatusEnumByCode(item.getRtState())).getLabel());
            item.setRtStateName(Objects.requireNonNull(RoomStatusEnum.getRoomStatusEnumByCode(stateMap.get(item.getRNo()))).getLabel());
            item.setRtState(stateMap.get(item.getRNo()));
        });
        // 客房总数
        int total = bean.size();
        // 空脏房
        int emptyDirtyRoom = CollectionUtils.filterList(bean, item -> RoomStatusEnum.VD.getCode().equals(item.getRtState())).size();
        // 空净房
        int emptyCleanRoom = CollectionUtils.filterList(bean, item -> RoomStatusEnum.VC.getCode().equals(item.getRtState())).size();
        // 维修房
        int repairRoom = CollectionUtils.filterList(bean, item -> RoomStatusEnum.OO.getCode().equals(item.getRtState())).size();
        // 住脏房
        int stayDirtyRoom = CollectionUtils.filterList(bean, item -> RoomStatusEnum.OD.getCode().equals(item.getRtState())).size();
        // 住净房
        int stayCleanRoom = CollectionUtils.filterList(bean, item -> RoomStatusEnum.OC.getCode().equals(item.getRtState())).size();
        // 过夜房数
        int overNightRoom = stayCleanRoom + stayDirtyRoom;
        // 过夜出租率
        BigDecimal overNightRate = NumberUtils.occ(BigDecimal.valueOf(overNightRoom), total);
        //double overNightRate = total==0? 0 :Double.valueOf(Math.divideExact(overNightRoom, total));
        nightAudiRoomTypeRespVO.setStat("客房总数=" + total + ", 空脏房=" + emptyDirtyRoom + ", 空净房=" + emptyCleanRoom + ", 维修房=" + repairRoom + ", 住脏房=" + stayDirtyRoom + ", 过夜房数=" + overNightRoom + ", 过夜出租率=" + overNightRate + "%");
        nightAudiRoomTypeRespVO.setList(bean);
        return nightAudiRoomTypeRespVO;
    }

}