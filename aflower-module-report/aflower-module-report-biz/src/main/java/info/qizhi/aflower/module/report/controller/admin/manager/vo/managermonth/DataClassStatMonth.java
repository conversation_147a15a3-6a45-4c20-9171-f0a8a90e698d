package info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class DataClassStatMonth {
    @Schema(description = "统计类型")
    private String statType;

    @Schema(description = "统计类型名称")
    private String statTypeName;

    @Schema(description = "统计代码")
    private String statCode;

    @Schema(description = "统计名称")
    private String statName;

    @Schema(description = "间夜数")
    private BigDecimal nightNum;

    @Schema(description = "出租率")
    private BigDecimal occ;

    @Schema(description = "房费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long roomFee;

    @Schema(description = "平均房价")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long avgRoomFee;
}
