package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 经理日报(固化)(小程序查看) Response VO")
@Data
public class ManagerDailyMonthRespVO {

    @Schema(description = "客房营业收入指标")
    private List<ManagerMonthDetailRespVO> roomRevenueIndex;

//    @Schema(description = "收入统计")
//    private RevenueCount revenueCount;

    /*@Schema(description = "经营数据分类统计")
    private List<MonthBusinessData> businessData;*/

    @Schema(description = "订单来源分类统计")
    private List<MonthBusinessData> orderSrcList;

    @Schema(description = "订单来源分类名称")
    private List<NameData> orderSrcNameList;

    @Schema(description = "房型分类统计")
    private List<MonthBusinessData> rtCodeList;

    @Schema(description = "房型分类名称")
    private List<NameData> rtCodeNameList;

    @Schema(description = "客源分类统计")
    private List<MonthBusinessData> gsrcList;

    @Schema(description = "客源分类名称")
    private List<NameData> gsrcNameList;

    @Schema(description = "渠道分类统计")
    private List<MonthBusinessData> statChannelList;

    @Schema(description = "渠道分类名称")
    private List<NameData> statChannelNameList;

    @Schema(description = "入住类型分类统计")
    private List<MonthBusinessData> inTypeList;

    @Schema(description = "入住类型分类名称")
    private List<NameData> inTypeNameList;
}
