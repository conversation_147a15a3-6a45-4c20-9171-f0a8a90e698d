/*
package info.qizhi.aflower.module.report.controller.app.storeranking;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.app.storeranking.vo.StoreRespVO;
import info.qizhi.aflower.module.report.controller.app.storeranking.vo.StoreTotalBizKpiDailyRespVO;
import info.qizhi.aflower.module.report.service.storeranking.StoreRankingService;
import info.qizhi.aflower.module.report.service.totalbizkpidaily.TotalBizKpiDailyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "小程序 - 门店排名")
@RestController("appStoreRankingController")
@RequestMapping("/report/store-ranking")
@Validated
public class StoreRankingController {

    @Resource
    private TotalBizKpiDailyService totalBizKpiDailyService;

    @Resource
    private StoreRankingService storeRankingService;

    @GetMapping("/get")
    @Operation(summary = "获得每日门店排名")
    @PreAuthorize("@ss.hasPermission('report:store-ranking:query')")
    public CommonResult<List<StoreTotalBizKpiDailyRespVO>> getStoreRankingDaily(@Valid StoreRespVO reqVO) {
        List<StoreTotalBizKpiDailyRespVO> list = storeRankingService.getStoreRankingDaily(reqVO);
        return success(list);
    }
}
*/
