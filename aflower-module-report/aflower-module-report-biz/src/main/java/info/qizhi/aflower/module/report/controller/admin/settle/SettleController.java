package info.qizhi.aflower.module.report.controller.admin.settle;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.settledetail.SettleDetailReportReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.settledetail.SettleDetailReportRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 前台结账报表")
@RestController
@RequestMapping("/report/settle")
@Validated
public class SettleController {

    @Resource
    ReportDetailApi reportDetailApi;

    @GetMapping("/get")
    @Operation(summary = "获得前台结账报表")
    //@PreAuthorize("@ss.hasPermission('report:settle:query')")
    public CommonResult<SettleDetailReportRespDTO> getSettleDetailReport(@Valid SettleDetailReportReqDTO reqVO) {
        return reportDetailApi.getSettleDetailReport(reqVO);
    }
}
