package info.qizhi.aflower.module.report.controller.admin.storeranking.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "小程序 - 每日每日门店排名 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StoreTotalBizKpiDailyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17817")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "门店名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String hname;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "星期;周一:1....周日:7", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("星期;周一:1....周日:7")
    private Integer week;

    @Schema(description = "客房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客房数")
    private Integer roomNum;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("间夜数")
    private BigDecimal nightNum;

    @Schema(description = "免房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("免房数")
    private Integer freeNum;

    @Schema(description = "自用数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("自用数")
    private Integer selfNum;

    @Schema(description = "维修房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("维修房数")
    private Integer repairNum;

    @Schema(description = "过夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜数")
    private Integer overnightNum;

    @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("房费")
    private Long roomFee;

    @Schema(description = "平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("平均房价")
    private Long avgRoomFee;

    @Schema(description = "RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("RevPar")
    private BigDecimal revPar;

    @Schema(description = "出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出租率")
    private BigDecimal occ;

    @Schema(description = "出租率（扣维修房）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出租率（扣维修房）")
    private BigDecimal occNoRepair;

    @Schema(description = "过夜出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("过夜出租率")
    private BigDecimal overnightOcc;

    @Schema(description = "小商品", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("小商品")
    private Long goodsFee;

    @Schema(description = "会员卡", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员卡")
    private Long memberCardFee;

    @Schema(description = "其他消费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("其他消费")
    private Long otherFee;

    @Schema(description = "会议室", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会议室")
    private Long meetingRoomFee;

    @Schema(description = "赔偿费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("赔偿费")
    private Long indemnityFee;

    @Schema(description = "门店收入", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店收入")
    private Long totalFee;

    @Schema(description = "会员充值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("会员充值")
    private Long rechargeFee;

    @Schema(description = "综合RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("综合RevPar")
    private BigDecimal compRevPar;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;



}