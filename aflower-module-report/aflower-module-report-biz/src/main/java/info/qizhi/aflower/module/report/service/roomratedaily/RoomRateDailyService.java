package info.qizhi.aflower.module.report.service.roomratedaily;

import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailySaveReqVO;

import java.util.List;

/**
 * 房费日报表(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface RoomRateDailyService {

    /**
     * 获得房费日报表(固化)
     *
     * @return 房费日报表(固化)
     */
    RoomRateDailyReportRespVO getRateDailyReport(RoomRateDailyReportReqVO reqVO);

    /**
     * 批量创建房费日报表(固化)
     *
     * @param roomRateDailySaveReqVOList
     * @return 成功true 失败false
     */
    Boolean createRoomRateDailies(List<RoomRateDailySaveReqVO> roomRateDailySaveReqVOList);

}