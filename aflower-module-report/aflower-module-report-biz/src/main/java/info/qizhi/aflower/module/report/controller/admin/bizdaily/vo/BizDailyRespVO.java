package info.qizhi.aflower.module.report.controller.admin.bizdaily.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 营业日报(固化) Response VO")
@Data
@ExcelIgnoreUnannotated
public class BizDailyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19539")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日")
    private LocalDate bizDate;

    @Schema(description = "日期类型;0：本日 1：本月 2：本年", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("日期类型;0：本日 1：本月 2：本年")
    private String dateType;

    @Schema(description = "最后查询时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最后查询时间")
    private LocalDateTime lastQueryTime;

    @Schema(description = "总营业指标:客房数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:客房数")
    private Integer roomNum;

    @Schema(description = "总营业指标:维修房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:维修房")
    private Integer repairNum;

    @Schema(description = "总营业指标:过夜房", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:过夜房")
    private Integer nightNum;

    @Schema(description = "总营业指标:过夜房出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:过夜房出租率")
    private BigDecimal nightOcc;

    @Schema(description = "总营业指标:间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:间夜数")
    private BigDecimal nightRoomNum;

    @Schema(description = "总营业指标:房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:房费")
    private Long roomFee;

    @Schema(description = "总营业指标:平均房价", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:平均房价")
    private Long adr;

    @Schema(description = "总营业指标:出租率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:出租率")
    private BigDecimal occ;

    @Schema(description = "总营业指标:RevPar", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:RevPar")
    private BigDecimal revPar;

    @Schema(description = "总营业指标:现付账房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总营业指标:现付账房费")
    private Long accFee;

    @Schema(description = "非门店收入:会员充值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("非门店收入:会员充值")
    private Long recharge;

    @Schema(description = "客源:中介", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客源:中介")
    private Long agent;

    @Schema(description = "客源:协议单位", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客源:协议单位")
    private Long protocol;

    @Schema(description = "客源:散客", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客源:散客")
    private Long workIn;

    @Schema(description = "客源:会员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("客源:会员")
    private Long member;

    @Schema(description = "入住类型:免费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住类型:免费")
    private Long checkInTypeFree;

    @Schema(description = "入住类型:时租", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住类型:时租")
    private Long checkInTypeHour;

    @Schema(description = "入住类型:正常", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住类型:正常")
    private Long checkInTypeNormal;

    @Schema(description = "入住类型:旅行团", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住类型:旅行团")
    private Long checkInTypeTour;

    @Schema(description = "入住类型:会议", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入住类型:会议")
    private Long checkInTypeMeeting;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}