package info.qizhi.aflower.module.report.controller.admin.breakfast;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BreakfastPrepareReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BreakfastPrepareReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BuyBkOrReturnBkDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.breakfast.BuyBkOrReturnBkDetailReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 早餐日报")
@RestController
@RequestMapping("/report/breakfast")
@Validated
public class BreakfastController {

    @Resource
    ReportDetailApi reportDetailApi;

    @GetMapping("/prepare")
    @Operation(summary = "获得早餐备餐报表")
    //@PreAuthorize("@ss.hasPermission('report:breakfast:query')")
    public CommonResult<BreakfastPrepareReportRespDTO> getBreakfastPrepareReport(@Valid BreakfastPrepareReqDTO reqVO) {
        return reportDetailApi.getBreakfastPrepareReport(reqVO);
    }

    @GetMapping("/buybk-or-returnbk")
    @Operation(summary = "获得购早/退早明细报表")
   // @PreAuthorize("@ss.hasPermission('report:breakfast:query')")
    public CommonResult<BuyBkOrReturnBkDetailReportRespDTO> getArEntryDetailReport(@Valid BuyBkOrReturnBkDetailReqDTO reqVO) {
        return reportDetailApi.getArEntryDetailReport(reqVO);
    }
}
