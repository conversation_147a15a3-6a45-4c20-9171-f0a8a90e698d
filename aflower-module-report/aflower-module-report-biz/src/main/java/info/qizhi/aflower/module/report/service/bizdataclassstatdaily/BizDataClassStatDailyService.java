package info.qizhi.aflower.module.report.service.bizdataclassstatdaily;

import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.bizdataclassstatdaily.vo.BizDataClassStatDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.bizdataclassstatdaily.BizDataClassStatDailyDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 每日经营数据分类统计 Service 接口
 *
 * <AUTHOR>
 */
public interface BizDataClassStatDailyService {

    /**
     * 创建每日经营数据分类统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBizDataClassStatDaily(@Valid BizDataClassStatDailySaveReqVO createReqVO);

    /**
     * 批量创建每日经营数据分类统计
     *
     * @param bizDataClassStatDailySaveReqVOList
     * @return 成功true 失败false
     */
    Boolean createBizDataClassStatDailies(@Valid List<BizDataClassStatDailySaveReqVO> bizDataClassStatDailySaveReqVOList);

    /**
     * 获得每日经营数据分类统计
     *
     * @param id 编号
     * @return 每日经营数据分类统计
     */
    BizDataClassStatDailyDO getBizDataClassStatDaily(Long id);

    /**
     * 获得每日经营数据分类统计集合
     *
     * @return 每日经营数据分类统计
     */
    List<BizDataClassStatDailyDO> getBizDataClassStatDailyList(BizDataClassStatDailyReqVO reqVO);
    /**
     * 获得本年至去年累计收入统计报集合
     *
     * @return 每日经营数据分类统计
     */
    List<BizDataClassStatDailyDO> getYearlyBizDataClassStatDailyList(BizDataClassStatDailyReqVO reqVO);


}