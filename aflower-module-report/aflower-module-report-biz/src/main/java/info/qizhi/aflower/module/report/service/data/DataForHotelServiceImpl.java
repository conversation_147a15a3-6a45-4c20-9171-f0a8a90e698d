package info.qizhi.aflower.module.report.service.data;

import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.StatTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.module.pms.api.realbizkpi.RealBizKpiApi;
import info.qizhi.aflower.module.pms.api.realbizkpi.dto.BusinessDataDTO;
import info.qizhi.aflower.module.pms.api.realbizkpi.dto.RealBizKpiRespDTO;
import info.qizhi.aflower.module.pms.api.realbizkpi.dto.RealTimeRoomStatusReqDTO;
import info.qizhi.aflower.module.pms.api.realbizkpi.dto.RealTimeRoomStatusTwoReq2DTO;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.membercardselldetail.MemberCardSellDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.memberrechargedetail.MemberRechargeDetailReqDTO;
import info.qizhi.aflower.module.pms.api.roomstatus.RoomStatusApi;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.RoomClassPredictionReqDTO;
import info.qizhi.aflower.module.pms.api.roomstatus.dto.RoomPredictionRespDTO;
import info.qizhi.aflower.module.report.controller.admin.data.vo.DataForHotelRespVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.managerdaily.ManagerDailyDO;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerDailyService;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

import static info.qizhi.aflower.framework.common.enums.SmsSceneEnum.REGISTER_MEMBER;
import static info.qizhi.aflower.framework.common.enums.StatTypeEnum.*;
import static java.math.RoundingMode.HALF_DOWN;

/*
    <AUTHOR>
    @description
    @create 2025 03 2025/3/17 15:18
*/
@Service
@Slf4j
public class DataForHotelServiceImpl implements DataForHotelService {

	@Resource
	ManagerDailyService managerDailyService;
	@Resource
	RealBizKpiApi realBizKpiApi;
	@Resource
	ReportDetailApi reportDetailApi;
	@Resource
	RoomStatusApi roomStatusApi;
	@Resource
	MerchantApi MerchantApi;
	@Override
	public DataForHotelRespVO getHotelData(String gcode, String hcode) {
		LocalDateTime dateTime = LocalDateTime.now();
		LocalDate today = LocalDate.now();
		LocalDate yesterday = today.minusDays(1);
		StringBuffer dataAll = new StringBuffer();
		// 格式化
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
		// 经理综合日报
		ManagerDailyDO manager = managerDailyService.getManagerDaily(new ManagerDailyReqVO().setGcode(gcode).setHcode(hcode).setBizDate(yesterday));
		// 房类预测（可售房数）
		RoomPredictionRespDTO roomPredictionRespDTOCommonResult = roomStatusApi.predictRoomClass(new RoomClassPredictionReqDTO().setGcode(gcode).setHcode(hcode).setStartDate(today)).getData();
		// 今日会员充值
		MemberRechargeDetailReportRespDTO memberRechargeDetail
				= reportDetailApi.getMemberRechargeDetail(
						new MemberRechargeDetailReqDTO()
								.setGcode(gcode)
								.setHcode(hcode)
								.setOperateType("0")
								.setBizDate(today))
				.getData();

		// 今日会员
		MemberCardSellDetailReportRespDTO memberCardSellDetail
				= reportDetailApi.getMemberCardSellDetailReport(
						new MemberCardSellDetailReqDTO()
								.setGcode(gcode)
								.setHcode(hcode)
								.setBizDate(today))
				.getData();

		// 昨日会员注册
		MemberRechargeDetailReportRespDTO memberRechargeDetailYestDay
				= reportDetailApi.getMemberRechargeDetail(
						new MemberRechargeDetailReqDTO()
								.setGcode(gcode)
								.setHcode(hcode)
								.setOperateType("0")
								.setBizDate(yesterday))
				.getData();

		// 昨日会员
		MemberCardSellDetailReportRespDTO memberCardSellDetailYestDay
				= reportDetailApi.getMemberCardSellDetailReport(
						new MemberCardSellDetailReqDTO()
								.setGcode(gcode)
								.setHcode(hcode)
								.setBizDate(yesterday))
				.getData();

		// 实时经营数据
		RealBizKpiRespDTO realBizKpi = realBizKpiApi.getRealBizKpi(new RealTimeRoomStatusReqDTO().setGcode(gcode).setHcode(hcode)).getData();
		// 酒店名称
		MerchantRespDTO hotelName = MerchantApi.getMerchant(hcode).getData();
		// 添加空检查
		if (hotelName != null && hotelName.getHname() != null) {
			dataAll.append(hotelName.getHname());
		} else {
			dataAll.append("未知酒店");
		}
		dataAll.append(" 数据汇报\u00A0\u00A0\u00A0\u00A0");

		// 时间星期
		dataAll.append(dateTime.format(formatter)).append("  ")
				.append(today.getDayOfWeek().getDisplayName(TextStyle.FULL, Locale.CHINESE)).append("\n");;
		dataAll.append("-------------今日-------------\n\n");

		// 处理出租率
		getToDayOcc(realBizKpi, dataAll);
		// 多个实时经营数据分类
		CommonResult<List<BusinessDataDTO>> result =
				realBizKpiApi.getMultipleRealBizKpiClassification(new RealTimeRoomStatusTwoReq2DTO()
						.setGcode(gcode)
						.setHcode(hcode)
						.setStatTypes(List.of(GUEST_SRC.getCode(),CHANNEL.getCode(),ROOM_TYPE.getCode())));
		// 过滤出GUEST_SRC.getCode()类型的数据
		List<BusinessDataDTO> guestSrcList = CollectionUtils.filterList(result.getData(), item -> Objects.equals(item.getStatType(), GUEST_SRC.getCode()));
		// 过滤出CHANNEL.getCode()类型的数据
		List<BusinessDataDTO> channelList = CollectionUtils.filterList(result.getData(), item -> Objects.equals(item.getStatType(), CHANNEL.getCode()));
		// 过滤出ROOM_TYPE.getCode()类型的数据
		List<BusinessDataDTO> roomTypeList = CollectionUtils.filterList(result.getData(), item -> Objects.equals(item.getStatType(), ROOM_TYPE.getCode()));
		// 处理客源入住
		getRoomByStatType(GUEST_SRC.getCode(), dataAll, realBizKpi, roomPredictionRespDTOCommonResult, guestSrcList);
		// 处理渠道入住
		getRoomByStatType(CHANNEL.getCode(), dataAll, realBizKpi, roomPredictionRespDTOCommonResult, channelList);
		// 处理房型可售
		getRoomByStatType( ROOM_TYPE.getCode(), dataAll, realBizKpi, roomPredictionRespDTOCommonResult, null);
		// 处理会员
		getMember(dataAll, memberCardSellDetail, memberRechargeDetail);

		dataAll.append("-------------昨日-------------\n\n");
		// 昨日营收
		getManager(manager, dataAll);
		// 昨日会员
		getMember(dataAll, memberCardSellDetailYestDay, memberRechargeDetailYestDay);
		return new DataForHotelRespVO().setAllData(dataAll);
	}

	private void getManager(ManagerDailyDO manager, StringBuffer dataAll) {
		if (!ObjectUtil.isEmpty(manager)) {
			// 实际营收计算
			if (ObjectUtil.isEmpty(manager.getRoomFee()) && ObjectUtil.isEmpty(manager.getMemberCardFee())) {
				dataAll.append("实际营收：0").append("\n");
			} else if (ObjectUtil.isEmpty(manager.getRoomFee())) {
				dataAll.append("实际营收：").append(new BigDecimal(manager.getMemberCardFee()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			} else if (ObjectUtil.isEmpty(manager.getMemberCardFee())) {
				dataAll.append("实际营收：").append(new BigDecimal(manager.getRoomFee()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			} else {
				// 只有当两者都不为空时才执行加法
				dataAll.append("实际营收：").append(new BigDecimal(manager.getRoomFee() + manager.getMemberCardFee()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			}

			// 其他字段的非空判断
			dataAll.append("房费收入：").append(ObjectUtil.isEmpty(manager.getRoomFee()) ? "0" : new BigDecimal(manager.getRoomFee()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			dataAll.append("平均房价：").append(ObjectUtil.isEmpty(manager.getAvgRoomFee()) ? "0" : new BigDecimal(manager.getAvgRoomFee()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			dataAll.append("RevPAR（总房数）：").append(ObjectUtil.isEmpty(manager.getRevPar()) ? "0" : new BigDecimal(manager.getRevPar()).divide(BigDecimal.valueOf(100), 2, HALF_DOWN)).append("\n");
			dataAll.append("出租率（总房数）：").append(ObjectUtil.isEmpty(manager.getOcc()) ? "0" : manager.getOcc()).append("%").append("\n");
			dataAll.append("过夜出租率：").append(ObjectUtil.isEmpty(manager.getOvernightOcc()) ? "0" : manager.getOvernightOcc()).append("%").append("\n");
		} else {
			// manager为空的情况保持不变
			dataAll.append("实际营收：0").append("\n");
			dataAll.append("房费收入：0").append("\n");
			dataAll.append("平均房价：0").append("\n");
			dataAll.append("RevPAR（总房数）：0%").append("\n");
			dataAll.append("出租率（总房数）：0%").append("\n");
			dataAll.append("过夜出租率：0%").append("\n");
		}
		dataAll.append("\n");
	}

	/**
	 * 处理会员信息
	 * @param dataAll
	 * @param memberCardSellDetail
	 * @param memberRechargeDetail
	 */
	private void getMember(StringBuffer dataAll, MemberCardSellDetailReportRespDTO memberCardSellDetail, MemberRechargeDetailReportRespDTO memberRechargeDetail){
		if(!ObjectUtil.isEmpty(memberCardSellDetail) && !ObjectUtil.isEmpty(memberCardSellDetail.getList())){
			// 会员总量
			dataAll.append("新发展会员: ").append(memberCardSellDetail.getList()
							.stream()
							.filter(member -> member != null &&
									!ObjectUtil.isEmpty(member.getGetMethod()) &&
									!ObjectUtil.isEmpty(REGISTER_MEMBER) &&
									!ObjectUtil.isEmpty(REGISTER_MEMBER.getName()) &&
									REGISTER_MEMBER.getName().equals(member.getGetMethod()))
							.count())
					.append("人\n");
			// 会员卡收入
			dataAll.append("会员卡收入：").append(memberCardSellDetail.getList()
							.stream()
							.filter(Objects::nonNull)
							.map(member -> ObjectUtil.isEmpty(member.getPayFee()) ? 0L : member.getPayFee())
							.reduce(0L, Long::sum))
					.append("元\n");
		} else {
			dataAll.append("新发展会员：0人\n")
					.append("会员卡收入：0元\n");
		}

		if(!ObjectUtil.isEmpty(memberRechargeDetail) && !ObjectUtil.isEmpty(memberRechargeDetail.getList())){
			// 会员卡充值
			dataAll.append("会员充值：").append(memberRechargeDetail.getList()
							.stream()
							.filter(Objects::nonNull)
							.map(member -> ObjectUtil.isEmpty(member.getRechargeFee()) ? 0L : member.getRechargeFee())
							.reduce(0L, Long::sum))
					.append("元\n");
			// 会员卡充值赠送金额
			dataAll.append("充值赠送：").append(memberRechargeDetail.getList()
							.stream()
							.filter(Objects::nonNull)
							.map(member -> ObjectUtil.isEmpty(member.getRechargeGiveFee()) ? 0L : member.getRechargeGiveFee())
							.reduce(0L, Long::sum))
					.append("元\n");

		} else {
			dataAll.append("会员充值：0元\n")
					.append("充值赠送：0元\n");
		}
		dataAll.append("\n");
	}

	/**
	 * 处理实时出租率方面
	 * @param realBizKpi
	 * @param dataAll
	 */
	private void getToDayOcc(RealBizKpiRespDTO realBizKpi, StringBuffer dataAll){
		if (ObjectUtil.isEmpty(realBizKpi) || ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom())) {
			dataAll.append("预期营收：0\n")
					.append("预期平均房价：0\n")
					.append("预期RevPAR：0\n")
					.append("预期出租率：0%\n")
					.append("总房数：0\n")
					.append("可售数：0\n")
					.append("在住客房：0\n")
					.append("今日预抵：0\n")
					.append("今日预离：0\n\n");
			return;
		}

		// 今日预抵
		int todayBooking = ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom().getTodayBookingRoomNum()) ?
				0 : realBizKpi.getRealTimeRoom().getTodayBookingRoomNum();
		// 在住客房数
		int occupiedRooms = ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom().getOccupiedRoomNum()) ?
				0 : realBizKpi.getRealTimeRoom().getOccupiedRoomNum();
		// 今日预离数
		int checkoutRooms = ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom().getCheckoutRoomNum()) ?
				0 : realBizKpi.getRealTimeRoom().getCheckoutRoomNum();
		// 可售房间数
		int availableRooms = ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom().getAvailableRoomNum()) ?
				0 : realBizKpi.getRealTimeRoom().getAvailableRoomNum();
		// 总房数
		int totalRoomNum = ObjectUtil.isEmpty(realBizKpi.getRealTimeRoom().getTotalRoomNum()) ?
				0 : realBizKpi.getRealTimeRoom().getTotalRoomNum();

		// 计算预期出租率
		BigDecimal occupancyRate = ObjectUtil.isEmpty(realBizKpi.getOcc()) ?
				BigDecimal.ZERO : realBizKpi.getOcc();

		BigDecimal totalFee = ObjectUtil.isEmpty(realBizKpi.getTotalFee()) ?
				BigDecimal.ZERO : new BigDecimal(realBizKpi.getTotalFee()).divide(new BigDecimal(100), 2, HALF_DOWN);

		BigDecimal avgRoomFee = ObjectUtil.isEmpty(realBizKpi.getAvgRoomFee()) ?
				BigDecimal.ZERO : new BigDecimal(realBizKpi.getAvgRoomFee()).divide(new BigDecimal(100), 2, HALF_DOWN);

		BigDecimal revPar = ObjectUtil.isEmpty(realBizKpi.getRevPar()) ?
				BigDecimal.ZERO : new BigDecimal(realBizKpi.getRevPar()).divide(new BigDecimal(100), 2, HALF_DOWN);

		// 格式化并追加到StringBuilder
		dataAll.append("预期营收：").append(totalFee).append("\n");
		dataAll.append("预期平均房价：").append(avgRoomFee).append("\n");
		dataAll.append("预期RevPAR：").append(revPar).append("\n");
		dataAll.append(String.format("预期出租率：%.2f%%", occupancyRate)).append("\n");
		dataAll.append("总房数：").append(totalRoomNum).append("\n");
		dataAll.append("可售数：").append(availableRooms).append("\n");
		dataAll.append("在住客房：").append(occupiedRooms).append("\n");
		dataAll.append("今日预抵：").append(todayBooking).append("\n");
		dataAll.append("今日预离：").append(checkoutRooms).append("\n");
		dataAll.append("\n");
	}

	/**
	 * 房屋数据控制器
	 * @param statType
	 * @param dataAll
	 * @param realBizKpi
	 */
	private void getRoomByStatType(String statType, StringBuffer dataAll, RealBizKpiRespDTO realBizKpi, RoomPredictionRespDTO roomPredictionRespDTOCommonResult, List<BusinessDataDTO> businessDataDTOS) {
		LocalDate today = LocalDate.now();

		// 计算总数
		BigDecimal totalNights = calculateTotal(businessDataDTOS);

		switch (StatTypeEnum.fromCode(statType)) {
			case GUEST_SRC:
				dataAll.append("已售房数：")
						.append(totalNights)
						.append("间(不含时租) 按照客源分类如下：\n");
				getGuestSrc(dataAll, businessDataDTOS, realBizKpi);
				break;

			case CHANNEL:
				dataAll.append("已售房数：")
						.append(totalNights)
						.append("间(不含时租) 按照渠道分类如下：\n");
				getChannel(dataAll, businessDataDTOS);
				break;

			case ROOM_TYPE:
				if (ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult) ||
						ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult.getTotalRoomInfo()) ||
						roomPredictionRespDTOCommonResult.getTotalRoomInfo().isEmpty() ||
						ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult.getTotalRoomInfo().getFirst()) ||
						ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult.getTotalRoomInfo().getFirst().getDateRoomInfos())) {
					dataAll.append("总可售数：0间，其中：\n");
					getRoomType(dataAll, roomPredictionRespDTOCommonResult);
					break;
				}

				Integer canSellNum = roomPredictionRespDTOCommonResult.getTotalRoomInfo()
						.getFirst()
						.getDateRoomInfos()
						.stream()
						.filter(info -> info != null &&
								info.getDate() != null &&
								info.getDate().equals(today))
						.findFirst()
						.map(dateRoomInfo -> ObjectUtil.isEmpty(dateRoomInfo.getCanSellNum()) ?
								0 : dateRoomInfo.getCanSellNum())
						.orElse(0);

				dataAll.append("总可售数：")
						.append(canSellNum)
						.append("间，其中:\n");
				getRoomType(dataAll, roomPredictionRespDTOCommonResult);
				break;

			default:
				dataAll.append("未知统计类型：").append(statType).append("\n\n");
				break;
		}
	}

	/**
	 * 计算总数量
	 */
	private BigDecimal calculateTotal(List<BusinessDataDTO> data) {
		if (ObjectUtil.isEmpty(data)) {
			return BigDecimal.ZERO;
		}

		return data.stream()
				.filter(Objects::nonNull)
				.map(dto -> ObjectUtil.isEmpty(dto.getNightNum()) ? BigDecimal.ZERO : dto.getNightNum())
				.reduce(BigDecimal.ZERO, BigDecimal::add);
	}

	/**
	 * 处理客源入住
	 */
	private void getGuestSrc(StringBuffer dataAll, List<BusinessDataDTO> data, RealBizKpiRespDTO realBizKpi) {
		if (ObjectUtil.isEmpty(data)) {
			dataAll.append("无客源数据\n\n");
			return;
		}

		// 处理客源订单
		data.stream()
				.filter(Objects::nonNull)
				.forEach(guestSrc ->
						dataAll.append(ObjectUtil.isEmpty(guestSrc.getClassificationStatistics()) ?
										"未知" : guestSrc.getClassificationStatistics())
								.append(": ")
								.append(ObjectUtil.isEmpty(guestSrc.getNightNum()) ?
										BigDecimal.ZERO : guestSrc.getNightNum())
								.append("间\n")
				);

		dataAll.append("额外：\n时租: ")
				.append(ObjectUtil.isEmpty(realBizKpi) || ObjectUtil.isEmpty(realBizKpi.getHourRooms()) ?
						0 : realBizKpi.getHourRooms())
				.append("间\n\n");
	}

	/**
	 * 处理渠道入住
	 */
	private void getChannel(StringBuffer dataAll, List<BusinessDataDTO> data) {
		if (ObjectUtil.isEmpty(data)) {
			dataAll.append("无渠道数据\n\n");
			return;
		}

		// 处理渠道类型
		data.stream()
				.filter(Objects::nonNull)
				.forEach(channel ->
						dataAll.append(ObjectUtil.isEmpty(channel.getClassificationStatistics()) ?
										"未知" : channel.getClassificationStatistics())
								.append(": ")
								.append(ObjectUtil.isEmpty(channel.getNightNum()) ?
										BigDecimal.ZERO : channel.getNightNum())
								.append("间\n")
				);

		dataAll.append("\n");
	}

	/**
	 * 处理房型可订
	 */
	private void getRoomType(StringBuffer dataAll, RoomPredictionRespDTO roomPredictionRespDTOCommonResult) {
		if (ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult) ||
				ObjectUtil.isEmpty(roomPredictionRespDTOCommonResult.getRoomInfos())) {
			dataAll.append("无房型数据\n\n");
			return;
		}

		LocalDate today = LocalDate.now();

		roomPredictionRespDTOCommonResult.getRoomInfos().stream()
				.filter(Objects::nonNull)
				.forEach(room -> {
					// 房型名称
					String rtName = ObjectUtil.isEmpty(room.getRtName()) ? "未知房型" : room.getRtName();

					// 获取可售数
					Integer canSellNum = 0;
					if (!ObjectUtil.isEmpty(room.getDateRoomInfos())) {
						canSellNum = room.getDateRoomInfos().stream()
								.filter(dateRoomInfo -> dateRoomInfo != null &&
										dateRoomInfo.getDate() != null &&
										dateRoomInfo.getDate().equals(today))
								.findFirst()
								.map(dateRoomInfo -> ObjectUtil.isEmpty(dateRoomInfo.getCanSellNum()) ?
										0 : dateRoomInfo.getCanSellNum())
								.orElse(0);
					}

					dataAll.append(rtName)
							.append(": ")
							.append(canSellNum)
							.append("间\n");
				});

		dataAll.append("\n");
	}
}