package info.qizhi.aflower.module.report.dal.dataobject.zeroroomrate;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 每日总营业指标报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_zero_room_rate")
@KeySequence("rp_zero_room_rate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ZeroRoomRateDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 房号
     */
    private String rNo ;
    /**
     * 订单号
     */
    private String no ;
    /**
     * 客人姓名
     */
    private String guestName ;
    /**
     * 客源
     */
    private String guestSrcType ;
    /**
     * 入住类型
     */
    private String checkinType ;
    /**
     * 客源关联账号
     */
    private String guestCode;
    /**
     * 入住时间
     */
    private LocalDateTime checkinTime ;
    /**
     * 离店时间
     */
    private LocalDateTime checkoutTime ;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 房价
     */
    private Long price;
    /**
     * 入账备注
     */
    private String remark ;

}