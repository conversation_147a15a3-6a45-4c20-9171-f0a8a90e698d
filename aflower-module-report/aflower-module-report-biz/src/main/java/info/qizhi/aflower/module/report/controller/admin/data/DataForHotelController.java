package info.qizhi.aflower.module.report.controller.admin.data;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.data.vo.DataForHotelRespVO;
import info.qizhi.aflower.module.report.service.data.DataForHotelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/*
    <AUTHOR>
    @description 
    @create 2025 03 2025/3/17 15:09
*/
@Tag(name = "酒店汇报数据")
@RestController
@RequestMapping("/report/data/hotel")
@Validated
public class DataForHotelController {

	@Resource
	DataForHotelService dataForHotelService;

	@GetMapping("/get")
	@Operation(summary = "获取酒店整体营收")
	public CommonResult<DataForHotelRespVO> dataForHotel(@RequestParam("gcode") String gcode, @RequestParam("hcode") String hcode){
		return success(dataForHotelService.getHotelData(gcode, hcode));
	}
}
