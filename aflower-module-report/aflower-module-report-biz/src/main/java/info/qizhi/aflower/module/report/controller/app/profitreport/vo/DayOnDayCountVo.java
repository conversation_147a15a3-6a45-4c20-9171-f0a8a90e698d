//package info.qizhi.aflower.module.report.controller.app.profitreport.vo;
//
//
//import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
//import com.alibaba.excel.annotation.ExcelProperty;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
//import info.qizhi.aflower.module.report.api.managerdaily.dto.BusinessData;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//
//import java.math.BigDecimal;
//import java.util.List;
//
//@Schema(description = "小程序 - 经营指标日报表(固化) Response VO")
//@Data
//@ExcelIgnoreUnannotated
//public class DayOnDayCountVo {
//    @Schema(description = "消费科目总金额环比数据")
//    private Long consumptionTypeTotalFeeDayOnDayData;
//
//    @Schema(description = "消费科目总金额环比率")
//    private BigDecimal consumptionTypeTotalFeeDayOnDayRatio;
//
//    @Schema(description = "总房费环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("总房费环比数据")
//    private Long roomFeeDayOnDayData;
//
//    @Schema(description = "总房费环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("总房费环比率")
//    private BigDecimal roomFeeDayOnDayRatio;
//
//    @Schema(description = "非房收环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("非房收环比数据")
//    private Long notRoomFeeDayOnDayData;
//
//    @Schema(description = "非房收环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("非房收环比率")
//    private BigDecimal notRoomFeeDayOnDayRatio;
//
//    @Schema(description = "RevPar环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("RevPar环比数据")
//    private Long revParDayOnDayData;
//
//    @Schema(description = "RevPar环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("RevPar环比率")
//    private BigDecimal revParDayOnDayRatio;
//
//    @Schema(description = "平均房价环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("平均房价环比数据")
//    private Long avgRoomFeeDayOnDayData;
//
//    @Schema(description = "平均房价环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("平均房价环比率")
//    private BigDecimal avgRoomFeeDayOnDayRatio;
//
//    @Schema(description = "出租率环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("出租率环比数据")
//    private BigDecimal occDayOnDayData;
//
//    @Schema(description = "出租率环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("出租率环比率")
//    private BigDecimal occDayOnDayRatio;
//
//    @Schema(description = "间夜数环比数据", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("间夜数环比数据")
//    private BigDecimal nightNumDayOnDayData;
//
//    @Schema(description = "间夜数环比率", requiredMode = Schema.RequiredMode.REQUIRED)
//    @ExcelProperty("间夜数环比率")
//    private BigDecimal nightNumDayOnDayRatio;
//
//}
