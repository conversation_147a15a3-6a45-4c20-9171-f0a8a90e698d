package info.qizhi.aflower.module.report.controller.admin.bizdaily.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 营业日报(固化)分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BizDailyPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码")
    private String hcode;

    @Schema(description = "营业日")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "日期类型;0：本日 1：本月 2：本年", example = "1")
    private String dateType;

    @Schema(description = "最后查询时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastQueryTime;

    @Schema(description = "总营业指标:客房数")
    private Integer roomNum;

    @Schema(description = "总营业指标:维修房")
    private Integer repairNum;

    @Schema(description = "总营业指标:过夜房")
    private Integer nightNum;

    @Schema(description = "总营业指标:过夜房出租率")
    private BigDecimal nightOcc;

    @Schema(description = "总营业指标:间夜数")
    private BigDecimal nightRoomNum;

    @Schema(description = "总营业指标:房费")
    private Long roomFee;

    @Schema(description = "总营业指标:平均房价")
    private Long adr;

    @Schema(description = "总营业指标:出租率")
    private BigDecimal occ;

    @Schema(description = "总营业指标:RevPar")
    private BigDecimal revPar;

    @Schema(description = "总营业指标:现付账房费")
    private Long accFee;

    @Schema(description = "非门店收入:会员充值")
    private Long recharge;

    @Schema(description = "客源:中介")
    private Long agent;

    @Schema(description = "客源:协议单位")
    private Long protocol;

    @Schema(description = "客源:散客")
    private Long workIn;

    @Schema(description = "客源:会员")
    private Long member;

    @Schema(description = "入住类型:免费")
    private Long checkInTypeFree;

    @Schema(description = "入住类型:时租")
    private Long checkInTypeHour;

    @Schema(description = "入住类型:正常")
    private Long checkInTypeNormal;

    @Schema(description = "入住类型:旅行团")
    private Long checkInTypeTour;

    @Schema(description = "入住类型:会议")
    private Long checkInTypeMeeting;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}