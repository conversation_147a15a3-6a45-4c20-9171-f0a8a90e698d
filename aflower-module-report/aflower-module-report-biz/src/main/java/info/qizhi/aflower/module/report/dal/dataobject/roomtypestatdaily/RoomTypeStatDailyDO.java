package info.qizhi.aflower.module.report.dal.dataobject.roomtypestatdaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日房型统计报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_room_type_stat_daily")
@KeySequence("rp_room_type_stat_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomTypeStatDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日
     */
    private LocalDate bizDate;
    /**
     * 星期
     */
    private Integer week;
    /**
     * 房型代码
     */
    private String rtCode;
    /**
     * 房型名称
     */
    private String rtName;
    /**
     * 客房数
     */
    private Integer roomNum;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 平均房价
     */
    private Long avgRoomFee;
    /**
     * 出租率
     */
    private BigDecimal occ;

}