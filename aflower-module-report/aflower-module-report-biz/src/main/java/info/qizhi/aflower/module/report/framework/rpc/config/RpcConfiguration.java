package info.qizhi.aflower.module.report.framework.rpc.config;

import info.qizhi.aflower.module.infra.api.file.FileApi;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.memberType.MemberTypeApi;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.accset.AccSetApi;
import info.qizhi.aflower.module.pms.api.booking.TeamApi;
import info.qizhi.aflower.module.pms.api.cashbillorder.CashBillOrderApi;
import info.qizhi.aflower.module.pms.api.channel.ChannelApi;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.OrderPriceApi;
import info.qizhi.aflower.module.pms.api.order.OrderTogetherApi;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.realbizkpi.RealBizKpiApi;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.room.RoomApi;
import info.qizhi.aflower.module.pms.api.roomstatus.RoomStatusApi;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.system.api.dict.DictDataApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.user.AdminUserApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class,MerchantApi.class, DictDataApi.class,
        TeamApi.class, MemberTypeApi.class, ProtocolAgentApi.class, AccountApi.class, OrderTogetherApi.class,
        RoomTypeApi.class, CashBillOrderApi.class, AccSetApi.class, OrderApi.class, ReportDetailApi.class
		, ChannelApi.class, RealBizKpiApi.class, RoomStatusApi.class, MemberApi.class, OrderPriceApi.class, AdminUserApi.class, RoomApi.class})
public class RpcConfiguration {
}
