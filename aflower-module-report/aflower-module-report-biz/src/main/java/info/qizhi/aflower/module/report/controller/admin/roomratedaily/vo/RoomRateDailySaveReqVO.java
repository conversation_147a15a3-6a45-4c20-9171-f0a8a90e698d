package info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 房费日报表(固化)新增/修改 Request VO")
@Data
public class RoomRateDailySaveReqVO {

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{orderNo.notblank}")
    private String orderNo;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{rNo.notempty}")
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{rtCode.notempty}")
    private String rtCode;

    @Schema(description = "房型", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotBlank(message = "{rtName.notempty}")
    private String rtName;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotBlank(message = "{name.notempty}")
    private String name;

    @Schema(description = "客源")
    @JsonProperty("gSrc")
    private String gSrc;

    @Schema(description = "客源名称")
    @JsonProperty("gSrcName")
    private String gSrcName;

    @Schema(description = "会员级别/公司", example = "1")
    private String memberType;

    @Schema(description = "订单来源")
    private String orderSrc;

    @Schema(description = "订单来源名称")
    private String orderSrcName;

    @Schema(description = "创建渠道")
    private String createChannel;

    @Schema(description = "创建渠道名称")
    private String createChannelName;

    @Schema(description = "统计渠道")
    private String statChannel;

    @Schema(description = "统计渠道名称")
    private String statChannelName;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkInTime.notnull}")
    private LocalDateTime checkInTime;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{checkOutTime.notnull}")
    private LocalDateTime checkOutTime;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDate;

    @Schema(description = "门市价", requiredMode = Schema.RequiredMode.REQUIRED, example = "17263")
    @NotNull(message = "{price.notnull}")
    private Integer price;

    @Schema(description = "房价类型")
    private String priceType;

    @Schema(description = "房价类型名称")
    private String priceTypeName;

    @Schema(description = "本日价格")
    private Long todayPrice;

    @Schema(description = "入住类型", example = "2")
    private String inType;

    @Schema(description = "入住类型名称")
    private String inTypeName;

    @Schema(description = "房费科目", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "{feeSub.notblank}")
    private String feeSub;

    @Schema(description = "房费科目名称")
    private String feeSubName;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{nightNum.notnull}")
    private BigDecimal nightNum;

    @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{fee.notnull}")
    private Integer fee;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "备注")
    private String remark;
}