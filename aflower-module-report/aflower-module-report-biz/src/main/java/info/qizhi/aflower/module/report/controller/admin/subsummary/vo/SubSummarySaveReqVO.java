package info.qizhi.aflower.module.report.controller.admin.subsummary.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 酒店科目汇总表(固化)新增/修改 Request VO")
@Data
public class SubSummarySaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "15720")
    private Integer id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "科目代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{subCode.notempty}")
    private String subCode;

    @Schema(description = "科目名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "{subName.notempty}")
    private String subName;

    @Schema(description = "科目类型;consume_account: 消费科目 pay_account：付款科目", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{subType.notempty}")
    private String subType;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{bizDate.notnull}")
    private LocalDate bizDay;

    @Schema(description = "本月发生累计", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{curMonthFee.notnull}")
    private Long curMonthFee;

    @Schema(description = "本年发生累计", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{curYearFee.notnull}")
    private Long curYearFee;

    @Schema(description = "昨日余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{yesterdayBalance.notnull}")
    private Long yesterdayBalance;

    @Schema(description = "今日发生", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todayOccur.notnull}")
    private Long todayOccur;

    @Schema(description = "今日(结账)收回", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todaySettle.notnull}")
    private Long todaySettle;

    @Schema(description = "今日余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{todayBalance.notnull}")
    private Long todayBalance;

}