package info.qizhi.aflower.module.report.dal.dataobject.managermonth;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 经理综合日报表(固化) DO
 *
 * <AUTHOR>
 */
@TableName("rp_biz_data_class_stat_month")
@KeySequence("rp_biz_data_class_stat_month_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataClassStatMonthDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 月份
     */
    private LocalDate month;
    /**
     * 统计类型
     */
    private String statType;
    /**
     * 统计类型名称
     */
    private String statTypeName;
    /**
     * 统计代码
     */
    private String statCode;
    /**
     * 统计名称
     */
    private String statName;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 出租率
     */
    private BigDecimal occ;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 平均房价
     */
    private Long avgRoomFee;
}