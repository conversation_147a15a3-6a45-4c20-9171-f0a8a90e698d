package info.qizhi.aflower.module.report.service.profitreport;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.ChannelEnum;
import info.qizhi.aflower.framework.common.enums.CheckInTypeEnum;
import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.OrderSrcEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.HandoverReportReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.HandoverReportRespDTO;
import info.qizhi.aflower.module.pms.api.roomtype.RoomTypeApi;
import info.qizhi.aflower.module.report.api.managerdaily.ManagerDailyReportApi;
import info.qizhi.aflower.module.report.api.managerdaily.dto.BusinessData;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReportRespDTO;
import info.qizhi.aflower.module.report.api.managerdaily.dto.ManagerDailyReqDTO;
import info.qizhi.aflower.module.report.api.managerdaily.dto.RoomTypeData;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.profitreport.vo.DayOnDayCountVo;
import info.qizhi.aflower.module.report.controller.admin.profitreport.vo.RevenueCountVo;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyRespVO;
import info.qizhi.aflower.module.report.dal.dataobject.managerdaily.ManagerDailyDO;
import info.qizhi.aflower.module.report.service.managerdaily.ManagerDailyService;
import info.qizhi.aflower.module.report.service.roomratedaily.RoomRateDailyService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Validated
public class ProfitReportServiceImpl implements ProfitReportService {

    @Resource
    private ManagerDailyReportApi managerDailyReportApi;
    @Resource
    private RoomTypeApi roomTypeApi;
    @Resource
    private RoomRateDailyService roomRateDailyService;
    @Resource
    private AccountApi accountApi;
    @Resource
    private ManagerDailyService managerDailyService;

    public static final String ORDERSRC = "订单来源";
    public static final String RTCODE = "房型";
    public static final String GSRC = "客源";
    public static final String CHANNEL = "渠道";
    public static final String INTYPE = "入住类型";

    @Override
    public ManagerDailyRespVO getGuestRoomKpiDaily(ManagerDailyReqVO reqVO) {
        ManagerDailyReportRespDTO dailyReport = managerDailyReportApi.getDailyReport(BeanUtils.toBean(reqVO, ManagerDailyReqDTO.class)).getData();
        return BeanUtils.toBean(dailyReport.getRoomRevenueIndex(), ManagerDailyRespVO.class);
    }

    @Override
    public RevenueCountVo getManagementKpiDaily(ManagerDailyReqVO reqVO) {
        //reqVO.setOperator(null);
        HandoverReportRespDTO handoverReport = accountApi.handoverReport(new HandoverReportReqDTO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()).setOperator(reqVO.getOperator())).getData();
        ManagerDailyDO managerDaily = managerDailyService.getManagerDaily(reqVO);
        /*   ManagerDailyReportDTO dailyReport = managerDailyReportApi.getDailyReport(BeanUtils.toBean(reqVO, ManagerDailyReqDTO.class)).getData();*/
        RevenueCountVo revenueCountVo = BeanUtils.toBean(managerDaily, RevenueCountVo.class);
        if (revenueCountVo != null) {
            // 消费科目总金额
            if (handoverReport.getConsumptionTypeTotalFee() != null) {
                revenueCountVo.setConsumptionTypeTotalFee(handoverReport.getConsumptionTypeTotalFee());
            }

            //非房收
            if (revenueCountVo.getConsumptionTypeTotalFee() == null) {
                revenueCountVo.setNotRoomFee(0L);
            } else {
                revenueCountVo.setNotRoomFee(revenueCountVo.getConsumptionTypeTotalFee() - revenueCountVo.getRoomFee());
            }

            //经营数据分类统计
            //RevenueCountVo countVo = BeanUtils.toBean(dailyReport, RevenueCountVo.class);
          /*  //获得物理房型列表
            List<RoomTypeRespDTO> roomTypeList = roomTypeApi.getPhysicsRoomTypeList(new RoomTypeReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())).getData();
            Map<String, String> roomNameMap = CollectionUtils.convertMap(roomTypeList, RoomTypeRespDTO::getRtCode, RoomTypeRespDTO::getRtName);*/
            List<BusinessData> businessDataList = getBusinessData(reqVO);
            for (BusinessData businessData : businessDataList) {
                businessData.setOcc(NumberUtils.occ(businessData.getNightNum(), managerDaily.getTotalRoomNum()));
                BigDecimal avgRoomFee = NumberUtils.div(businessData.getTotalFee(), businessData.getNightNum(), 2);
                businessData.setAvgRoomFee(avgRoomFee.longValue());

                if (businessData.getRoomTypeData() != null) {
                    businessData.getRoomTypeData().stream()
                            .forEach(roomTypeDatum ->
                                    roomTypeDatum.setOcc(NumberUtils.occ(
                                            businessData.getNightNum(),
                                            managerDaily.getTotalRoomNum()
                                    ))
                            );
                }


            }
            revenueCountVo.setBusinessData(businessDataList);

        }

        return revenueCountVo;
    }

    private List<BusinessData> transformationRevenueCountVo(List<BusinessData> businessData, Map<String, String> roomNameMap) {
        for (BusinessData data : businessData) {
            if (ORDERSRC.equals(data.getCateGory())) {
                data.setClassificationStatistics(OrderSrcEnum.getNameByCode(data.getClassificationStatistics()));
            }
            if (RTCODE.equals(data.getCateGory())) {
                data.setClassificationStatistics(roomNameMap.get(data.getClassificationStatistics()));
            }
            if (GSRC.equals(data.getCateGory())) {
                data.setClassificationStatistics(GuestSrcTypeEnum.getLabelByCode(data.getClassificationStatistics()));
            }
            if (CHANNEL.equals(data.getCateGory())) {
                data.setClassificationStatistics(ChannelEnum.getNameByCode(data.getClassificationStatistics()));
            }
            if (INTYPE.equals(data.getCateGory())) {
                data.setClassificationStatistics(CheckInTypeEnum.getNameByCode(data.getClassificationStatistics()));
            }
        }
        return businessData;
    }

    @Override
    public DayOnDayCountVo getDayOnDay(ManagerDailyReqVO reqVO) {
        RevenueCountVo managementKpiDaily = getManagementKpiDaily(reqVO);

        //获得昨日日期
        LocalDate localDate = reqVO.getBizDate().minusDays(1);
        reqVO.setBizDate(localDate);

        RevenueCountVo yesterdayManagementKpiDaily = getManagementKpiDaily(reqVO);

        //计算环比数据和环比率
        return calculateDayOnDayData(managementKpiDaily, yesterdayManagementKpiDaily);


    }

    private DayOnDayCountVo calculateDayOnDayData(RevenueCountVo managementKpiDaily, RevenueCountVo yesterdayManagementKpiDaily) {
        // 创建 DayOnDayCountVo 对象
        DayOnDayCountVo dayOnDayCountVo = new DayOnDayCountVo();

        if (ObjectUtils.isEmpty(managementKpiDaily) || ObjectUtils.isEmpty(yesterdayManagementKpiDaily)) {
            return dayOnDayCountVo;
        }

        // 计算消费科目总金额的环比数据和环比率
        if (managementKpiDaily.getConsumptionTypeTotalFee() != null && yesterdayManagementKpiDaily.getConsumptionTypeTotalFee() != null) {
            dayOnDayCountVo.setConsumptionTypeTotalFeeDayOnDayData(
                    managementKpiDaily.getConsumptionTypeTotalFee() - yesterdayManagementKpiDaily.getConsumptionTypeTotalFee());
            dayOnDayCountVo.setConsumptionTypeTotalFeeDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getConsumptionTypeTotalFee(), yesterdayManagementKpiDaily.getConsumptionTypeTotalFee()));
        }

        // 计算总房费的环比数据和环比率
        if (managementKpiDaily.getRoomFee() != null && yesterdayManagementKpiDaily.getRoomFee() != null) {
            dayOnDayCountVo.setRoomFeeDayOnDayData(
                    managementKpiDaily.getRoomFee() - yesterdayManagementKpiDaily.getRoomFee());
            dayOnDayCountVo.setRoomFeeDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getRoomFee(), yesterdayManagementKpiDaily.getRoomFee()));
        }

        // 计算非房收的环比数据和环比率
        if (managementKpiDaily.getNotRoomFee() != null && yesterdayManagementKpiDaily.getNotRoomFee() != null) {
            dayOnDayCountVo.setNotRoomFeeDayOnDayData(
                    managementKpiDaily.getNotRoomFee() - yesterdayManagementKpiDaily.getNotRoomFee());
            dayOnDayCountVo.setNotRoomFeeDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getNotRoomFee(), yesterdayManagementKpiDaily.getNotRoomFee()));
        }

        // 计算 RevPar 的环比数据和环比率
        if (managementKpiDaily.getRevPar() != null && yesterdayManagementKpiDaily.getRevPar() != null) {
            dayOnDayCountVo.setRevParDayOnDayData(
                    managementKpiDaily.getRevPar() - yesterdayManagementKpiDaily.getRevPar());
            dayOnDayCountVo.setRevParDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getRevPar(), yesterdayManagementKpiDaily.getRevPar()));
        }

        // 计算平均房价的环比数据和环比率
        if (managementKpiDaily.getAvgRoomFee() != null && yesterdayManagementKpiDaily.getAvgRoomFee() != null) {
            dayOnDayCountVo.setAvgRoomFeeDayOnDayData(
                    managementKpiDaily.getAvgRoomFee() - yesterdayManagementKpiDaily.getAvgRoomFee());
            dayOnDayCountVo.setAvgRoomFeeDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getAvgRoomFee(), yesterdayManagementKpiDaily.getAvgRoomFee()));
        }

        // 计算出租率的环比数据和环比率
        if (managementKpiDaily.getOcc() != null && yesterdayManagementKpiDaily.getOcc() != null) {
            dayOnDayCountVo.setOccDayOnDayData(
                    managementKpiDaily.getOcc().subtract(yesterdayManagementKpiDaily.getOcc()));
            dayOnDayCountVo.setOccDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getOcc(), yesterdayManagementKpiDaily.getOcc()));
        }

        // 计算间夜数的环比数据和环比率
        if (managementKpiDaily.getNightNum() != null && yesterdayManagementKpiDaily.getNightNum() != null) {
            dayOnDayCountVo.setNightNumDayOnDayData(
                    managementKpiDaily.getNightNum().subtract(yesterdayManagementKpiDaily.getNightNum()));
            dayOnDayCountVo.setNightNumDayOnDayRatio(
                    calculateRatio(managementKpiDaily.getNightNum(), yesterdayManagementKpiDaily.getNightNum()));
        }

        return dayOnDayCountVo;
    }

    // 环比率计算函数（用于 Long）
    private BigDecimal calculateRatio(Long todayValue, Long yesterdayValue) {
        if (yesterdayValue == 0) {
            return BigDecimal.ZERO; // 或返回其他合适的默认值，比如 0 或抛出异常
        }
        return new BigDecimal(todayValue - yesterdayValue)
                .multiply(new BigDecimal(100))
                .divide(new BigDecimal(yesterdayValue), 2, RoundingMode.HALF_UP);
    }

    // 环比率计算函数（用于 BigDecimal）
    private BigDecimal calculateRatio(BigDecimal todayValue, BigDecimal yesterdayValue) {
        if (yesterdayValue.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO; // 或返回其他合适的默认值，比如 0 或抛出异常
        }
        return todayValue.subtract(yesterdayValue)
                .multiply(new BigDecimal(100))
                .divide(yesterdayValue, 2, RoundingMode.HALF_UP);
    }


    @NotNull
    private List<BusinessData> getBusinessData(ManagerDailyReqVO reqVO) {
        List<BusinessData> business = CollUtil.newArrayList();
        // 获取房费日报表信息
        RoomRateDailyReportRespVO rateDailyReport = roomRateDailyService.getRateDailyReport(new RoomRateDailyReportReqVO().setHcode(reqVO.getHcode()).setGcode(reqVO.getGcode()).setBizDate(reqVO.getBizDate()).setOperator(reqVO.getOperator()));
        List<RoomRateDailyRespVO> rateDailyList = rateDailyReport.getList();

        // 1. 根据 订单来源分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> orderSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc);
        Map<String, String> orderSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getOrderSrc, RoomRateDailyRespVO::getOrderSrcName);
        List<BusinessData> orderSrc = classification(orderSrcMap, ORDERSRC, orderSrcNameMap);
        business.addAll(orderSrc);
        // 2. 根据 房型分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> rtcodeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getRtCode);
        Map<String, String> rtcodeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getRtCode, RoomRateDailyRespVO::getRtName);
        List<BusinessData> rtCode = roomClassification(rtcodeMap, RTCODE, rtcodeNameMap);
        business.addAll(rtCode);
        // 3. 根据 客源分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> gSrcMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getGSrc);
        Map<String, String> gSrcNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getGSrc, RoomRateDailyRespVO::getGSrcName);
        List<BusinessData> gsrc = classification(gSrcMap, GSRC, gSrcNameMap);
        business.addAll(gsrc);
        // 4. 根据 渠道分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> statChannelMap = rateDailyList.stream()
                .filter(roomRate -> roomRate.getStatChannel() != null)
                .collect(Collectors.groupingBy(RoomRateDailyRespVO::getStatChannel));

        Map<String, String> statChannelNameMap = rateDailyList.stream()
                .filter(roomRate -> roomRate.getStatChannel() != null && roomRate.getStatChannelName() != null) // 过滤掉 statChannel 为 null 的记录
                .collect(Collectors.toMap(
                        RoomRateDailyRespVO::getStatChannel,    // 键：statChannel
                        RoomRateDailyRespVO::getStatChannelName, // 值：statChannelName
                        (existing, replacement) -> existing     // 如果有重复键，保留第一个值
                ));

        List<BusinessData> statChannel = classification(statChannelMap, CHANNEL, statChannelNameMap);
        business.addAll(statChannel);
        // 5. 根据 入住类型分类 转换为BusinessData对象 集合
        Map<String, List<RoomRateDailyRespVO>> inTypeMap = CollectionUtils.convertMultiMap(rateDailyList, RoomRateDailyRespVO::getInType);
        Map<String, String> inTypeNameMap = CollectionUtils.convertMap(rateDailyList, RoomRateDailyRespVO::getInType, RoomRateDailyRespVO::getInTypeName);
        List<BusinessData> inType = classification(inTypeMap, INTYPE, inTypeNameMap);
        business.addAll(inType);
        return business;
    }

    private List<BusinessData> classification(Map<String, List<RoomRateDailyRespVO>> listMap, String category, Map<String, String> nameMap) {
        return listMap.entrySet().stream().map(entry -> {
            List<RoomRateDailyRespVO> list = entry.getValue();
            // 计算总间夜数和总费用
            BigDecimal totalNightNum = list.stream().map(RoomRateDailyRespVO::getNightNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            long totalFee = list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();
            // 创建BusinessData对象
            BusinessData businessData = new BusinessData();
            businessData.setCateGory(category);
            businessData.setClassificationStatistics(nameMap.get(entry.getKey()));
            businessData.setNightNum(totalNightNum);
            businessData.setTotalFee(totalFee);
            return businessData;
        }).collect(Collectors.toList());
    }

    private List<BusinessData> roomClassification(
            Map<String, List<RoomRateDailyRespVO>> listMap,
            String category,
            Map<String, String> nameMap) {
        return listMap.entrySet().stream().map(entry -> {
            List<RoomRateDailyRespVO> list = entry.getValue();

            // 统计所有房型的总间夜数和总费用
            BigDecimal totalNightNum = list.stream().map(RoomRateDailyRespVO::getNightNum)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            long totalFee = list.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();

            // 根据入住类型分类
            Map<String, List<RoomRateDailyRespVO>> inTypeMap = list.stream()
                    .collect(Collectors.groupingBy(RoomRateDailyRespVO::getInType));
            List<RoomTypeData> roomTypeData = inTypeMap.entrySet().stream().map(inTypeEntry -> {
                List<RoomRateDailyRespVO> inTypeList = inTypeEntry.getValue();
                BigDecimal nightNum = inTypeList.stream().map(RoomRateDailyRespVO::getNightNum)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                long fee = inTypeList.stream().mapToLong(RoomRateDailyRespVO::getFee).sum();

                // 计算平均房价
                long avgRoomFee = nightNum.compareTo(BigDecimal.ZERO)==0 ? 0 : NumberUtils.div(fee, nightNum,2).longValue();

                // 创建 RoomTypeData 对象
                RoomTypeData roomType = new RoomTypeData();
                if (CheckInTypeEnum.HOUR_ROOM.getCode().equals(inTypeEntry.getKey())) {
                    roomType.setCheckInTypeName(CheckInTypeEnum.HOUR_ROOM.getName());
                } else {
                    roomType.setCheckInTypeName(CheckInTypeEnum.ALL_DAY.getName());
                }
                roomType.setNightNum(nightNum);
                roomType.setTotalFee(fee);
                roomType.setAvgRoomFee(avgRoomFee);
                return roomType;
            }).collect(Collectors.toList());

            // 创建 BusinessData 对象
            BusinessData businessData = new BusinessData();
            businessData.setCateGory(category);
            businessData.setClassificationStatistics(nameMap.get(entry.getKey())); // 房型名称
            businessData.setNightNum(totalNightNum); // 总间夜数
            businessData.setTotalFee(totalFee); // 总费用
            businessData.setRoomTypeData(roomTypeData); // 时租房与全天房的数据
            return businessData;
        }).collect(Collectors.toList());
    }

}
