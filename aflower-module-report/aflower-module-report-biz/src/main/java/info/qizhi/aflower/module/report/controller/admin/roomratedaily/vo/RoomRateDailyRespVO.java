package info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 房费日报表(固化) Response VO")
@Data
@ExcelIgnoreUnannotated
public class RoomRateDailyRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25901")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String hcode;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "超链接", requiredMode = Schema.RequiredMode.REQUIRED)
    private String url;

    @Schema(description = "房号", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonProperty("rNo")
    private String rNo;

    @Schema(description = "房型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rtCode;

    @Schema(description = "房型,当为团队订单时展示团队名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String rtName;

    @Schema(description = "客人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    private String name;

    @Schema(description = "客源")
    @JsonProperty("gSrc")
    private String gSrc;

    @Schema(description = "客源名称")
    @JsonProperty("gSrcName")
    private String gSrcName;

    @Schema(description = "订单来源")
    private String orderSrc;

    @Schema(description = "订单来源名称")
    private String orderSrcName;

    @Schema(description = "创建渠道")
    private String createChannel;

    @Schema(description = "创建渠道名称")
    private String createChannelName;

    @Schema(description = "统计渠道")
    private String statChannel;

    @Schema(description = "统计渠道名称")
    private String statChannelName;

    @Schema(description = "入住时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkInTime;

    @Schema(description = "离店时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime checkOutTime;

    @Schema(description = "营业日", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "门市价", requiredMode = Schema.RequiredMode.REQUIRED, example = "17263")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long price;

    @Schema(description = "房价类型")
    private String priceType;

    @Schema(description = "房价类型名称")
    private String priceTypeName;

    @Schema(description = "本日价格")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayPrice;

    @Schema(description = "入住类型", example = "2")
    private String inType;

    @Schema(description = "入住类型名称", example = "2")
    private String inTypeName;

    @Schema(description = "房费科目", requiredMode = Schema.RequiredMode.REQUIRED)
    private String feeSub;

    @Schema(description = "房费科目名称")
    private String feeSubName;

    @Schema(description = "间夜数", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal nightNum;

    @Schema(description = "房费", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "优惠卷抵扣")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long couponDeduction;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "操作员昵称")
    private String operatorName;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}