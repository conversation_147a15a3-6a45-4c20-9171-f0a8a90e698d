package info.qizhi.aflower.module.report.dal.dataobject.handoverreport;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 交班报表[固化] DO
 *
 * <AUTHOR>
 */
@TableName("rp_handover_report")
@KeySequence("rp_handover_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HandoverReportDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 班次号
     */
    private String shiftNo;
    /**
     * 营业日期
     */
    private LocalDate bizDate;
    /**
     * 现金金额
     */
    private Long cashAmount;
    /**
     * 银行卡金额
     */
    private Long bankCardAmount;
    /**
     * 微信金额
     */
    private Long wechatAmount;
    /**
     * 支付宝金额
     */
    private Long alipayAmount;

}