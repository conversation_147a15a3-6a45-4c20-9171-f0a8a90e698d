package info.qizhi.aflower.module.report.service.incomestatdaily;

import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.incomestatdaily.vo.IncomeStatDailySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.incomestatdaily.IncomeStatDailyDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 每日收入统计报 Service 接口
 *
 * <AUTHOR>
 */
public interface IncomeStatDailyService {

    /**
     * 创建每日收入统计报
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createIncomeStatDaily(@Valid IncomeStatDailySaveReqVO createReqVO);

    /**
     * 批量创建每日收入统计报
     *
     * @param incomeStatDailySaveReqVOList
     * @return 成功true 失败false
     */
    Boolean createIncomeStatDailies(@Valid List<IncomeStatDailySaveReqVO> incomeStatDailySaveReqVOList);


    /**
     * 获得每日收入统计报
     *
     * @param id 编号
     * @return 每日收入统计报
     */
    IncomeStatDailyDO getIncomeStatDaily(Long id);
    /**
     * 获得每日收入统计报集合
     *
     * @return 每日收入统计报
     */
    List<IncomeStatDailyDO> getIncomeStatDailyList(IncomeStatDailyReqVO reqVO);
    /**
     * 查询本年1月1号到bizDate,及去年1月1号到去年的那个bizDate的收入统计集合
     *
     * @return 每日收入统计报
     */
    List<IncomeStatDailyDO> getIncomeStatDailyListByYear(IncomeStatDailyReqVO reqVO);


}