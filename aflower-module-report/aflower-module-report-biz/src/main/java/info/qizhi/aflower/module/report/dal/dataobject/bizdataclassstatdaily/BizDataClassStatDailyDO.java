package info.qizhi.aflower.module.report.dal.dataobject.bizdataclassstatdaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 每日经营数据分类统计 DO
 *
 * <AUTHOR>
 */
@TableName("rp_biz_data_class_stat_daily")
@KeySequence("rp_biz_data_class_stat_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BizDataClassStatDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日期
     */
    private LocalDate bizDate;
    /**
     * 统计类型
     */
    private String statType;
    /**
     * 统计类型名称
     */
    private String statTypeName;
    /**
     * 统计代码
     */
    private String statCode;
    /**
     * 统计名称
     */
    private String statName;
    /**
     * 间夜数
     */
    private BigDecimal nightNum;
    /**
     * 出租率
     */
    private BigDecimal occ;
    /**
     * 房费
     */
    private Long roomFee;
    /**
     * 平均房价
     */
    private Long avgRoomFee;

}