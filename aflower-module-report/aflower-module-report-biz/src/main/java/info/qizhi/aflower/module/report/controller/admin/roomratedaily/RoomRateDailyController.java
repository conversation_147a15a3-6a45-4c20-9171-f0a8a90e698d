package info.qizhi.aflower.module.report.controller.admin.roomratedaily;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomratedaily.vo.RoomRateDailyReportRespVO;
import info.qizhi.aflower.module.report.service.roomratedaily.RoomRateDailyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 房费日报表(固化)")
@RestController
@RequestMapping("report/room/rate-daily")
@Validated
public class RoomRateDailyController {

    @Resource
    private RoomRateDailyService roomRateDailyService;

    @GetMapping("/get")
    @Operation(summary = "获得房费日报表(固化)")
    @PreAuthorize("@ss.hasPermission('report:room:rate-daily:get')")
    public CommonResult<RoomRateDailyReportRespVO> getRateDaily(@Valid RoomRateDailyReportReqVO reqVO) {
        RoomRateDailyReportRespVO rateDaily = roomRateDailyService.getRateDailyReport(reqVO);
        return success(rateDaily);
    }

}