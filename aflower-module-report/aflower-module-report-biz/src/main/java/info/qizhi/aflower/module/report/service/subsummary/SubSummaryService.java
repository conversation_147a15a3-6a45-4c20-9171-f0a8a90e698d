package info.qizhi.aflower.module.report.service.subsummary;


import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummaryPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummarySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.subsummary.SubSummaryDO;
import jakarta.validation.Valid;

/**
 * 酒店科目汇总表(固化) Service 接口
 *
 * <AUTHOR>
 */
public interface SubSummaryService {

    /**
     * 创建酒店科目汇总表(固化)
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createSubSummary(@Valid SubSummarySaveReqVO createReqVO);

    /**
     * 更新酒店科目汇总表(固化)
     *
     * @param updateReqVO 更新信息
     */
    void updateSubSummary(@Valid SubSummarySaveReqVO updateReqVO);

    /**
     * 删除酒店科目汇总表(固化)
     *
     * @param id 编号
     */
    void deleteSubSummary(Integer id);

    /**
     * 获得酒店科目汇总表(固化)
     *
     * @param id 编号
     * @return 酒店科目汇总表(固化)
     */
    SubSummaryDO getSubSummary(Integer id);

    /**
     * 获得酒店科目汇总表(固化)分页
     *
     * @param pageReqVO 分页查询
     * @return 酒店科目汇总表(固化)分页
     */
    PageResult<SubSummaryDO> getSubSummaryPage(SubSummaryPageReqVO pageReqVO);

}