package info.qizhi.aflower.module.report.service.zeroroomrate;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.CheckInTypeEnum;
import info.qizhi.aflower.framework.common.enums.GuestSrcTypeEnum;
import info.qizhi.aflower.framework.common.enums.NoTypeEnum;
import info.qizhi.aflower.framework.common.enums.OrderUrlEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.member.api.member.dto.MemberListReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentReqDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentRespDTO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateReqVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateRespVO;
import info.qizhi.aflower.module.report.controller.admin.zeroroomrate.vo.ZeroRoomRateSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.zeroroomrate.ZeroRoomRateDO;
import info.qizhi.aflower.module.report.dal.mysql.zeroroomrate.ZeroRoomRateMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Validated
public class ZeroRoomRateServiceImpl implements ZeroRoomRateService{
    @Resource
    private ZeroRoomRateMapper zeroRoomRateMapper;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private MemberApi memberApi;

    @Override
    public Long createZeroRoomRate(ZeroRoomRateSaveReqVO createReqVO) {
        zeroRoomRateMapper.insert(BeanUtils.toBean(createReqVO, ZeroRoomRateDO.class));
        return 0L;
    }

    @Override
    public Boolean batchCreateZeroRoomRate(List<ZeroRoomRateSaveReqVO> createReqVOS) {
        List<ZeroRoomRateDO> roomRateDOList = BeanUtils.toBean(createReqVOS, ZeroRoomRateDO.class);
        if(CollUtil.isNotEmpty(roomRateDOList)){
            return zeroRoomRateMapper.insertBatch(roomRateDOList);
        }
        return true;
    }

    @Override
    public ZeroRoomRateReportRespVO getZeroRoomRate(ZeroRoomRateReqVO reqVO) {
        ZeroRoomRateReportRespVO zeroRoomRateReportRespVO = new ZeroRoomRateReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        zeroRoomRateReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());

        List<ZeroRoomRateDO> zeroRoomRateDOS = zeroRoomRateMapper.selectList(reqVO);
        if(CollUtil.isEmpty(zeroRoomRateDOS)){
            return zeroRoomRateReportRespVO.setList(new ArrayList<>());
        }

        // 获得所以中介列表
        List<ProtocolAgentRespDTO> protocolAgentList = protocolAgentApi.getProtocolAgentList(new ProtocolAgentReqDTO().setGcode(reqVO.getGcode())).getData();
        Map<String, ProtocolAgentRespDTO> agentMap = CollectionUtils.convertMap(protocolAgentList, ProtocolAgentRespDTO::getPaCode);
        // 获得所以会员列表
        List<MemberAndStoreCardRespDTO> memberList = memberApi.getMemberList(new MemberListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())).getData();
        Map<String, MemberAndStoreCardRespDTO> memberMap = CollectionUtils.convertMap(memberList, MemberAndStoreCardRespDTO::getMcode);

        List<ZeroRoomRateRespVO> list = new ArrayList<>();
        zeroRoomRateDOS.forEach(zeroRoomRateDO -> {
            ZeroRoomRateRespVO roomRateRespVO = BeanUtils.toBean(zeroRoomRateDO, ZeroRoomRateRespVO.class);
            roomRateRespVO.setGuestSrcTypeName(GuestSrcTypeEnum.getLabelByCode(zeroRoomRateDO.getGuestSrcType()));
            roomRateRespVO.setCheckinTypeName(CheckInTypeEnum.getNameByCode(zeroRoomRateDO.getCheckinType()));

            String url = String.format(
                    OrderUrlEnum.URL.getCode(),
                    zeroRoomRateDO.getNo(),
                    NoTypeEnum.ORDER.getCode()
            );
            roomRateRespVO.setUrl(url);

            if (GuestSrcTypeEnum.MEMBER.getCode().equals(zeroRoomRateDO.getGuestSrcType())) {
                String mtName = null;

                if (zeroRoomRateDO.getGuestCode() != null && memberMap.get(zeroRoomRateDO.getGuestCode()) != null) {
                    mtName = memberMap.get(zeroRoomRateDO.getGuestCode()).getMtName();
                }
                roomRateRespVO.setLevelOrCompanyName(mtName);
            }
            if (GuestSrcTypeEnum.AGENT.getCode().equals(zeroRoomRateDO.getGuestSrcType())) {
                roomRateRespVO.setLevelOrCompanyName(agentMap.getOrDefault(zeroRoomRateDO.getGuestCode(), new ProtocolAgentRespDTO()).getPaName());
            }

            list.add(roomRateRespVO);
        });

        return zeroRoomRateReportRespVO.setList(list);
    }
}
