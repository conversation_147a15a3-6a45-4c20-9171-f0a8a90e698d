package info.qizhi.aflower.module.report.dal.dataobject.incomestatdaily;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;

/**
 * 每日收入统计报 DO
 *
 * <AUTHOR>
 */
@TableName("rp_income_stat_daily")
@KeySequence("rp_income_stat_daily_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IncomeStatDailyDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 营业日期
     */
    private LocalDate bizDate;
    /**
     * 科目类型
     */
    private String subType;
    /**
     * 科目代码
     */
    private String subCode;
    /**
     * 科目名称
     */
    private String subName;
    /**
     * 金额
     */
    private Long fee;

}