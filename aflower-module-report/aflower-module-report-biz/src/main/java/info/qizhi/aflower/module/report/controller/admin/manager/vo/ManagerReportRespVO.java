package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_TWO;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经理日报(固化)(小程序查看) Response VO")
@Data
public class ManagerReportRespVO {

    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "开始日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_TWO)
    private LocalDate endDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "数据")
    private ManagerRespVO managerRespVO;

}