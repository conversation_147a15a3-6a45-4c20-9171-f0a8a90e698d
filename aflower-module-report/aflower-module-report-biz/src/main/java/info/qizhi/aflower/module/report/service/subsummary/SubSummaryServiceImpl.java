package info.qizhi.aflower.module.report.service.subsummary;


import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummaryPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.subsummary.vo.SubSummarySaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.subsummary.SubSummaryDO;
import info.qizhi.aflower.module.report.dal.mysql.subsummary.SubSummaryMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.SUB_SUMMARY_NOT_EXISTS;

/**
 * 酒店科目汇总表(固化) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SubSummaryServiceImpl implements SubSummaryService {

    @Resource
    private SubSummaryMapper subSummaryMapper;

    @Override
    public Integer createSubSummary(SubSummarySaveReqVO createReqVO) {
        // 插入
        SubSummaryDO subSummary = BeanUtils.toBean(createReqVO, SubSummaryDO.class);
        subSummaryMapper.insert(subSummary);
        // 返回
        return subSummary.getId();
    }

    @Override
    public void updateSubSummary(SubSummarySaveReqVO updateReqVO) {
        // 校验存在
        validateSubSummaryExists(updateReqVO.getId());
        // 更新
        SubSummaryDO updateObj = BeanUtils.toBean(updateReqVO, SubSummaryDO.class);
        subSummaryMapper.updateById(updateObj);
    }

    @Override
    public void deleteSubSummary(Integer id) {
        // 校验存在
        validateSubSummaryExists(id);
        // 删除
        subSummaryMapper.deleteById(id);
    }

    private void validateSubSummaryExists(Integer id) {
        if (subSummaryMapper.selectById(id) == null) {
            throw exception(SUB_SUMMARY_NOT_EXISTS);
        }
    }

    @Override
    public SubSummaryDO getSubSummary(Integer id) {
        return subSummaryMapper.selectById(id);
    }

    @Override
    public PageResult<SubSummaryDO> getSubSummaryPage(SubSummaryPageReqVO pageReqVO) {
        return subSummaryMapper.selectPage(pageReqVO);
    }

}