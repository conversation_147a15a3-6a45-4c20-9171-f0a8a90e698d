package info.qizhi.aflower.module.report.controller.admin.ar;


import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.pms.api.report.ReportDetailApi;
import info.qizhi.aflower.module.pms.api.report.vo.ardetail.ArDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.ardetail.ArDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arentrydetail.ArEntryDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arentrydetail.ArEntryDetailReqDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arverifydetail.ArVerifyDetailReportRespDTO;
import info.qizhi.aflower.module.pms.api.report.vo.arverifydetail.ArVerifyDetailReqDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - AR账务相关报表")
@RestController
@RequestMapping("/report/ar")
@Validated
public class ARController {

    @Resource
    ReportDetailApi reportDetailApi;

    @GetMapping("/sell")
    @Operation(summary = "获得AR账发生明细报表")
    //@PreAuthorize("@ss.hasPermission('report:ar:query')")
    public CommonResult<ArDetailReportRespDTO> getArSellDetail(@Valid ArDetailReqDTO reqVO) {
        return reportDetailApi.getArSellDetail(reqVO);
    }

    @GetMapping("/verify")
    @Operation(summary = "获得AR核销明细报表")
    //@PreAuthorize("@ss.hasPermission('report:ar:query')")
    public CommonResult<ArVerifyDetailReportRespDTO> getArVerifyDetail(@Valid ArVerifyDetailReqDTO reqVO) {
        return  reportDetailApi.getArVerifyDetail(reqVO);
    }

    @GetMapping("/ar-entry")
    @Operation(summary = "获得AR账收款明细报表")
    //@PreAuthorize("@ss.hasPermission('report:ar:query')")
    public CommonResult<ArEntryDetailReportRespDTO> getArEntryDetailReport(@Valid ArEntryDetailReqDTO reqVO) {
        return reportDetailApi.getArEntryDetailReport(reqVO);
    }
}