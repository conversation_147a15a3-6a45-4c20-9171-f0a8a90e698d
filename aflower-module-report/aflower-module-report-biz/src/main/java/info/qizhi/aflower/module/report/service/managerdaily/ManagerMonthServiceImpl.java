package info.qizhi.aflower.module.report.service.managerdaily;


import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.StatTypeEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.number.NumberUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.*;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.managermonth.*;
import info.qizhi.aflower.module.report.dal.dataobject.managermonth.DataClassStatMonthDO;
import info.qizhi.aflower.module.report.dal.dataobject.managermonth.ManagerMonthDO;
import info.qizhi.aflower.module.report.dal.mysql.managermonth.DataClassStatMonthMapper;
import info.qizhi.aflower.module.report.dal.mysql.managermonth.ManagerMonthMapper;
import info.qizhi.aflower.module.report.service.report.NightAudiCreateReportService;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static info.qizhi.aflower.framework.common.enums.StatTypeEnum.*;

/**
 * 经理综合日报表(固化) Service 接口
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ManagerMonthServiceImpl implements ManagerMonthService{

    @Resource
    private ManagerMonthMapper managerMonthMapper;
    @Resource
    private DataClassStatMonthMapper dataClassStatMonthMapper;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    @Lazy
    private NightAudiCreateReportService nightAudiCreateReportService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createManagerMonthReport(ManagerMonthSaveReqVO reqVO) {
        if(reqVO == null){
            return;
        }
        ManagerMonthDO managerMonthDO = BeanUtils.toBean(reqVO, ManagerMonthDO.class);
        List<DataClassStatMonthDO> dataClassStatMonthDOS = BeanUtils.toBean(reqVO.getList(), DataClassStatMonthDO.class);
        managerMonthMapper.insert(managerMonthDO);
        dataClassStatMonthMapper.insertBatch(dataClassStatMonthDOS);
    }

    @Override
    public ManagerMonthReportRespVO getManagerMonthReport(ManagerMonthReqVO reqVO) {
        ManagerMonthReportRespVO managerMonthReportRespVO = new ManagerMonthReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        managerMonthReportRespVO.setHname(merchant.getHname())
                .setHcode(merchant.getHcode())
                .setStartDate(reqVO.getStartDate())
                .setEndDate(reqVO.getEndDate())
                .setOperator(reqVO.getOperator())
                .setLastSelectTime(LocalDateTime.now());
        List<ManagerMonthDO> managerMonthDOS = managerMonthMapper.selectList(reqVO);
        // 获得每月分类统计数据
        List<DataClassStatMonthDO> dataClassStatMonthDOS = dataClassStatMonthMapper.selectList(reqVO);

        if(CollUtil.isEmpty(managerMonthDOS)){
            // 1. 获取当前月份（去掉日期部分）
            LocalDate currentMonth = LocalDate.now().withDayOfMonth(1);
            // 2. 获取月份范围（含跨年情况）
            LocalDate start = reqVO.getStartDate().withDayOfMonth(1); // 确保从当月1号开始
            LocalDate end = reqVO.getEndDate().withDayOfMonth(1);
            // 3. 按月循环调用
            for (LocalDate date = start; !date.isAfter(end); date = date.plusMonths(1)) {
                // 跳过当前月份
                if (date.isEqual(currentMonth)) {
                    continue;
                }
                nightAudiCreateReportService.createManagerMonth(
                        date,
                        reqVO.getGcode(),
                        reqVO.getHcode()
                );
            }

            managerMonthDOS = managerMonthMapper.selectList(reqVO);
            // 获得每月分类统计数据
            dataClassStatMonthDOS = dataClassStatMonthMapper.selectList(reqVO);
        }

        Map<String, List<DataClassStatMonthDO>> dataClassStatMonthDOSMap = CollectionUtils.convertMultiMap(dataClassStatMonthDOS, DataClassStatMonthDO::getStatType);

        List<DataClassStatMonth> orderSrcSumList = new ArrayList<>();
        List<DataClassStatMonth> rtCodeSumList = new ArrayList<>();
        List<DataClassStatMonth> statChannelSumList = new ArrayList<>();
        List<DataClassStatMonth> gsrcSumList = new ArrayList<>();
        List<DataClassStatMonth> inTypeSumList = new ArrayList<>();

        if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.ORDER_SOURCE.getCode())){
            managerMonthReportRespVO.setOrderSrcList(configureClassificationData(dataClassStatMonthDOSMap.getOrDefault(ORDER_SOURCE.getCode(), new ArrayList<>()), orderSrcSumList));
            managerMonthReportRespVO.setOrderSrcSumList(orderSrcSumList);
        }
        if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.ROOM_TYPE.getCode())){
            managerMonthReportRespVO.setRtCodeList(configureClassificationData(dataClassStatMonthDOSMap.getOrDefault(ROOM_TYPE.getCode(), new ArrayList<>()), rtCodeSumList));
            managerMonthReportRespVO.setRtCodeSumList(rtCodeSumList);
        }
        if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.CHANNEL.getCode())){
            managerMonthReportRespVO.setStatChannelList(configureClassificationData(dataClassStatMonthDOSMap.getOrDefault(CHANNEL.getCode(), new ArrayList<>()), statChannelSumList));
            managerMonthReportRespVO.setStatChannelSumList(statChannelSumList);
        }
        if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.GUEST_SRC.getCode())){
            managerMonthReportRespVO.setGsrcList(configureClassificationData(dataClassStatMonthDOSMap.getOrDefault(GUEST_SRC.getCode(), new ArrayList<>()), gsrcSumList));
            managerMonthReportRespVO.setGsrcSumList(gsrcSumList);
        }
        if (CollUtil.isEmpty(reqVO.getStatTypeList()) || reqVO.getStatTypeList().contains(StatTypeEnum.CHECK_IN_TYPE.getCode())){
            managerMonthReportRespVO.setInTypeList(configureClassificationData(dataClassStatMonthDOSMap.getOrDefault(CHECK_IN_TYPE.getCode(), new ArrayList<>()), inTypeSumList));
            managerMonthReportRespVO.setInTypeSumList(inTypeSumList);
        }
        managerMonthReportRespVO.setRoomRevenueIndexList(BeanUtils.toBean(managerMonthDOS, ManagerMonthRespVO.class));

        return managerMonthReportRespVO;
    }

    @Override
    public  List<ManagerMonthDO> getManagerMonthDataList(ManagerMonthReqVO reqVO) {
        List<ManagerMonthDO> managerMonthDOS = managerMonthMapper.selectComparisonList(reqVO);

        return managerMonthDOS;
    }

    private List<DataClassStatMonthVO> configureClassificationData(List<DataClassStatMonthDO> dataClassStatMonthDOS, List<DataClassStatMonth> sumList) {
        Map<LocalDate, List<DataClassStatMonthDO>> dataClassStatMonthDOSMap = CollectionUtils.convertMultiMap(dataClassStatMonthDOS, DataClassStatMonthDO::getMonth);
        Set<String> statCodes = CollectionUtils.convertSet(dataClassStatMonthDOS, DataClassStatMonthDO::getStatCode);
        Map<String, String> statNameMap = CollectionUtils.convertMap(dataClassStatMonthDOS, DataClassStatMonthDO::getStatCode, DataClassStatMonthDO::getStatName);

        statCodes.forEach(statCode -> {
            sumList.add(new DataClassStatMonth().setStatCode(statCode).setAvgRoomFee(0L).setRoomFee(0L).setNightNum(BigDecimal.ZERO));
        });

        List<DataClassStatMonthVO> list = new ArrayList<>();
        for (Map.Entry<LocalDate, List<DataClassStatMonthDO>> entry : dataClassStatMonthDOSMap.entrySet()) {
            LocalDate month = entry.getKey();
            List<DataClassStatMonthDO> value = entry.getValue();
            // 分类数据
            Map<String, DataClassStatMonthDO> dataClassStatMonthDOMap = CollectionUtils.convertMap(value, DataClassStatMonthDO::getStatCode);

            DataClassStatMonthVO dataClassStatMonthVO = new DataClassStatMonthVO();
            dataClassStatMonthVO.setMonth(month);

            List<DataClassStatMonth> dataClassStatMonthList = new ArrayList<>();
            BigDecimal nightNum = BigDecimal.ZERO;
            Long roomFee = 0L;
            Long avgRoomFee = 0L;
            for (DataClassStatMonth statMonthSum : sumList) {
                String statCode = statMonthSum.getStatCode();

                DataClassStatMonthDO dataClassStatMonthDO = dataClassStatMonthDOMap.get(statCode);
                if(dataClassStatMonthDO == null){
                    DataClassStatMonth dataClassStatMonth = new DataClassStatMonth();
                    dataClassStatMonth.setStatCode(statCode);
                    dataClassStatMonth.setStatName(statNameMap.get(statCode));
                    dataClassStatMonth.setOcc(BigDecimal.ZERO);
                    dataClassStatMonth.setNightNum(BigDecimal.ZERO);
                    dataClassStatMonth.setAvgRoomFee(0L);
                    dataClassStatMonth.setRoomFee(0L);
                    //dataClassStatMonthList.add(dataClassStatMonth);

                    statMonthSum.setNightNum(statMonthSum.getNightNum().add(dataClassStatMonth.getNightNum()));
                    statMonthSum.setRoomFee(statMonthSum.getRoomFee() + dataClassStatMonth.getRoomFee());
                    statMonthSum.setAvgRoomFee(NumberUtils.div(statMonthSum.getRoomFee(), statMonthSum.getNightNum(), 0).longValue());
                }else {
                    DataClassStatMonth bean = BeanUtils.toBean(dataClassStatMonthDO, DataClassStatMonth.class);
                    dataClassStatMonthList.add(bean);

                    statMonthSum.setNightNum(statMonthSum.getNightNum().add(bean.getNightNum()));
                    statMonthSum.setRoomFee(statMonthSum.getRoomFee() + bean.getRoomFee());
                    statMonthSum.setAvgRoomFee(NumberUtils.div(statMonthSum.getRoomFee(), statMonthSum.getNightNum(), 0).longValue());
                }
            }

            dataClassStatMonthVO.setDataClassStatMonthList(dataClassStatMonthList);
            dataClassStatMonthVO.setNightNum(nightNum);
            dataClassStatMonthVO.setRoomFee(roomFee);
            dataClassStatMonthVO.setAvgRoomFee(avgRoomFee);

            list.add(dataClassStatMonthVO);
        }
        return list;
    }

}