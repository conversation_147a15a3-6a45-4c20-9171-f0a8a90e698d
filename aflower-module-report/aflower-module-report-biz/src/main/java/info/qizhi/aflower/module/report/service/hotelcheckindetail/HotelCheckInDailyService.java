package info.qizhi.aflower.module.report.service.hotelcheckindetail;


import info.qizhi.aflower.module.report.controller.admin.hotelcheckindetail.vo.HotelCheckInContinueReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.hotelcheckindetail.vo.HotelCheckInReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.hotelcheckindetail.vo.HotelCheckInReqVO;
import info.qizhi.aflower.module.report.controller.admin.hotelcheckindetail.vo.HotelCheckInSaveReqVO;
import jakarta.validation.Valid;

import java.util.List;

public interface HotelCheckInDailyService {
   /**
    * 创建开房记录明细报表
    * @param reqDTO
    */
   void createDailyReport(HotelCheckInSaveReqVO reqDTO);

   /**
    * 批量创建开房记录明细报表
    * @param reqDTOs
    */
   void createBatchDailyReport(List<HotelCheckInSaveReqVO> reqDTOs);

   /**
    * 获得开房记录明细报表集合
    * @param reqVO
    * @return
    */
   HotelCheckInReportRespVO getReportList(HotelCheckInReqVO reqVO);

   /**
    *  获得客人续住明细报表
    * @param reqVO
    * @return
    */
   HotelCheckInContinueReportRespVO getContinueInReportList(@Valid HotelCheckInReqVO reqVO);
}