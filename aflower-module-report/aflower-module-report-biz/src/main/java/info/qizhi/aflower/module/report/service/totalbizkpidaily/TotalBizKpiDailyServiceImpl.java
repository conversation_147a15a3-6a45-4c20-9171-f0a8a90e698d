package info.qizhi.aflower.module.report.service.totalbizkpidaily;

import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import info.qizhi.aflower.module.report.controller.admin.totalbizkpidaily.vo.*;
import info.qizhi.aflower.module.report.dal.dataobject.totalbizkpidaily.TotalBizKpiDailyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.report.dal.mysql.totalbizkpidaily.TotalBizKpiDailyMapper;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.report.enums.ErrorCodeConstants.*;

/**
 * 每日总营业指标报 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TotalBizKpiDailyServiceImpl implements TotalBizKpiDailyService {

    @Resource
    private TotalBizKpiDailyMapper totalBizKpiDailyMapper;

    @Override
    public Long createTotalBizKpiDaily(TotalBizKpiDailySaveReqVO createReqVO) {
        // 插入
        TotalBizKpiDailyDO totalBizKpiDaily = BeanUtils.toBean(createReqVO, TotalBizKpiDailyDO.class);
        totalBizKpiDailyMapper.insert(totalBizKpiDaily);
        // 返回
        return totalBizKpiDaily.getId();
    }

    @Override
    public void updateTotalBizKpiDaily(TotalBizKpiDailySaveReqVO updateReqVO) {
        // 校验存在
        validateTotalBizKpiDailyExists(updateReqVO.getId());
        // 更新
        TotalBizKpiDailyDO updateObj = BeanUtils.toBean(updateReqVO, TotalBizKpiDailyDO.class);
        totalBizKpiDailyMapper.updateById(updateObj);
    }

    @Override
    public void deleteTotalBizKpiDaily(Long id) {
        // 校验存在
        validateTotalBizKpiDailyExists(id);
        // 删除
        totalBizKpiDailyMapper.deleteById(id);
    }

    private void validateTotalBizKpiDailyExists(Long id) {
        if (totalBizKpiDailyMapper.selectById(id) == null) {
            throw exception(TOTAL_BIZ_KPI_DAILY_NOT_EXISTS);
        }
    }

    @Override
    public TotalBizKpiDailyDO getTotalBizKpiDaily(Long id) {
        return totalBizKpiDailyMapper.selectById(id);
    }

    @Override
    public PageResult<TotalBizKpiDailyDO> getTotalBizKpiDailyPage(TotalBizKpiDailyPageReqVO pageReqVO) {
        return totalBizKpiDailyMapper.selectPage(pageReqVO);
    }

    @Override
    public List<TotalBizKpiDailyDO> getStoreTotalBizKpiDaily(TotalBizKpiDailyRespVO reqVO) {
        return totalBizKpiDailyMapper.selectList(reqVO);
    }


}