package info.qizhi.aflower.module.report.dal.dataobject.hotelcheckindetail;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 宾客明细报表[固化] DO
 *
 * <AUTHOR>
 */
@TableName("rp_hotel_check_in_detail")
@KeySequence("rp_hotel_check_in_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelCheckInDetailDO extends TenantBaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 入住时间
     */
    private LocalDateTime checkinTime;
    /**
     * 原离店时间
     */
    private LocalDateTime planCheckoutTime;
    /**
     * 新预离时间
     */
    private LocalDateTime nowPlanCheckoutTime;
    /**
     * 操作类型
     */
    private String operateType;
    /**
     * 班次
     */
    private String shiftNo;
    /**
     * 营业日
     */
    private LocalDate bizDate;

}