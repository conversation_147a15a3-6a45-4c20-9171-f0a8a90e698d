package info.qizhi.aflower.module.report.controller.admin.guestdetail.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 宾客明细报表[固化] Response VO")
@Data
@ExcelIgnoreUnannotated
public class GuestDetailRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32651")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "订单号")
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "联系人")
    @ExcelProperty("联系人")
    private String contact;

    @Schema(description = "客人姓名", example = "赵六")
    @ExcelProperty("客人姓名")
    private String name;

    @Schema(description = "房间代码")
    @ExcelProperty("房间代码")
    @JsonProperty(value = "rCode")
    private String rCode;

    @Schema(description = "房号")
    @ExcelProperty("房号")
    @JsonProperty(value = "rNo")
    private String rNo;

    @Schema(description = "团队代码")
    @ExcelProperty("团队代码")
    private String teamCode;

    @Schema(description = "团队名称", example = "王五")
    @ExcelProperty("团队名称")
    private String teamName;

    @Schema(description = "入住时间")
    @ExcelProperty("入住时间")
    private LocalDateTime checkinTime;

    @Schema(description = "离店时间")
    @ExcelProperty("离店时间")
    private LocalDateTime checkoutTime;

    @Schema(description = "今日发生消费", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("今日发生消费")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayConsume;

    @Schema(description = "今日发生付款", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("今日发生付款")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long todayPay;

    @Schema(description = "宾客账", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("宾客账")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long customerBill;

    @Schema(description = "订单状态")
    @ExcelProperty("订单状态")
    private String state;

    @Schema(description = "订单状态名称")
    @ExcelProperty("订单状态名称")
    private String stateName;

    @Schema(description = "营业日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("营业日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "是否现付账;0:否,1:是")
    @ExcelProperty("是否现付账")
    private String isCash;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime createTime;

}