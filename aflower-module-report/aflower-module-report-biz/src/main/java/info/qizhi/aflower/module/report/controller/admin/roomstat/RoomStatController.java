package info.qizhi.aflower.module.report.controller.admin.roomstat;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatReqVO;
import info.qizhi.aflower.module.report.controller.admin.roomstat.vo.RoomStatSaveReqVO;
import info.qizhi.aflower.module.report.service.roomstat.RoomStatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 客房营收统计报表(固化)")
@RestController
@RequestMapping("report/room/stat")
@Validated
public class RoomStatController {

    @Resource
    private RoomStatService roomStatService;

    @PostMapping("/create")
    @Operation(summary = "创建客房营收统计报表(固化)")
    @PreAuthorize("@ss.hasPermission('report:room:rate-daily:get')")
    public CommonResult<Boolean> createRoomStat(@Valid RoomStatSaveReqVO reqVO) {
        roomStatService.createRoomStat(reqVO);
        return success(true);
    }

    @GetMapping("/list")
    @Operation(summary = "获得客房营收统计报表(固化)")
    @PreAuthorize("@ss.hasPermission('report:room:rate-daily:get')")
    public CommonResult<RoomStatReportRespVO> getRoomStatList(@Valid RoomStatReqVO reqVO) {
        RoomStatReportRespVO reportRespVO = roomStatService.getRoomStatList(reqVO);
        return success(reportRespVO);
    }
}
