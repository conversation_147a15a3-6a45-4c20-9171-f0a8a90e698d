package info.qizhi.aflower.module.report.controller.admin.manager.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;
import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 经理日报(固化) Response VO")
@Data
public class ManagerDailyReportRespVO {

    @Schema(description = "门店名称", example = "门店代码示例")
    private String hname;

    @Schema(description = "门店代码", example = "门店代码示例")
    private String hcode;

    @Schema(description = "营业日", example = "2024-07-04")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY)
    private LocalDate bizDate;

    @Schema(description = "最后查询时间", example = "2024-07-04 17:35:53")
    @JsonFormat(pattern =  FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime lastSelectTime;

    @Schema(description = "操作员", example = "报表操作员")
    private String operator;

    @Schema(description = "客房营业收入指标")
    private ManagerDailyRespVO roomRevenueIndex;

    @Schema(description = "收入统计")
    private RevenueCount revenueCount;

    @Schema(description = "经营数据分类统计")
    private List<BusinessData> businessData;

}