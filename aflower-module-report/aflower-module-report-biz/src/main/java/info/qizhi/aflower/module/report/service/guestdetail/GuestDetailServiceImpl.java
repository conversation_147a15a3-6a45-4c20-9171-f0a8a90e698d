package info.qizhi.aflower.module.report.service.guestdetail;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.ConsumeAccountEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.OrderStateEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportReqVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailReportRespVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailRespVO;
import info.qizhi.aflower.module.report.controller.admin.guestdetail.vo.GuestDetailSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.guestdetail.GuestDetailDO;
import info.qizhi.aflower.module.report.dal.mysql.guestdetail.GuestDetailMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 宾客明细报表[固化] Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class GuestDetailServiceImpl implements GuestDetailService {

    public static final String CASH = "现付账";
    @Resource
    private GuestDetailMapper guestDetailMapper;

    @Resource
    private MerchantApi merchantApi;

    @Override
    public Long createGuestDetail(GuestDetailSaveReqVO createReqVO) {
        // 插入
        GuestDetailDO guestDetail = BeanUtils.toBean(createReqVO, GuestDetailDO.class);
        guestDetailMapper.insert(guestDetail);
        // 返回
        return guestDetail.getId();
    }

    @Override
    public Boolean createGuestDetails(List<GuestDetailSaveReqVO> guestDetailSaveReqVOList) {
        List<GuestDetailDO> guestDetailDOList = BeanUtils.toBean(guestDetailSaveReqVOList, GuestDetailDO.class);
        if (CollUtil.isNotEmpty(guestDetailDOList)) {
            return guestDetailMapper.insertBatch(guestDetailDOList);
        }
        return true;
    }


    @Override
    public GuestDetailReportRespVO getGuestDetailReport(GuestDetailReportReqVO reqVO) {
        GuestDetailReportRespVO guestDetailReportRespVO = new GuestDetailReportRespVO();
        // 门店信息
        MerchantRespDTO merchant = merchantApi.getMerchant(reqVO.getHcode()).getData();
        guestDetailReportRespVO.setHname(merchant.getHname())
                               .setBizDate(reqVO.getBizDate())
                               .setOperator(reqVO.getOperator())
                               .setLastSelectTime(LocalDateTime.now());
        List<GuestDetailDO> guestDetailDOList = guestDetailMapper.selectList(reqVO);
        List<GuestDetailRespVO> list = BeanUtils.toBean(guestDetailDOList, GuestDetailRespVO.class);
        list.forEach(guestDetailRespVO -> {
            if (NumberEnum.ZERO.getNumber().equals(guestDetailRespVO.getIsCash())) {
                guestDetailRespVO.setStateName(OrderStateEnum.getNameByCode(guestDetailRespVO.getState()));
            }
            if (NumberEnum.ONE.getNumber().equals(guestDetailRespVO.getIsCash())) {
                guestDetailRespVO.setStateName(CASH);
                guestDetailRespVO.setName(ConsumeAccountEnum.getLabelByCode(guestDetailRespVO.getName()));
            }
        });
        guestDetailReportRespVO.setList(list);
        return guestDetailReportRespVO;
    }

}