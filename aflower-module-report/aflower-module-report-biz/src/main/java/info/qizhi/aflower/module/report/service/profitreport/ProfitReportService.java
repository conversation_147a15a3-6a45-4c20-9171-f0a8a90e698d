package info.qizhi.aflower.module.report.service.profitreport;

import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyReqVO;
import info.qizhi.aflower.module.report.controller.admin.manager.vo.ManagerDailyRespVO;
import info.qizhi.aflower.module.report.controller.admin.profitreport.vo.DayOnDayCountVo;
import info.qizhi.aflower.module.report.controller.admin.profitreport.vo.RevenueCountVo;
import jakarta.validation.Valid;

public interface ProfitReportService {

    /**
     * 获得每日客房指标报
     *
     * @param reqVO
     * @return
     */
    ManagerDailyRespVO getGuestRoomKpiDaily(@Valid ManagerDailyReqVO reqVO);


    /**
     * 获得每日经营指标报
     *
     * @param reqVO
     * @return
     */
    RevenueCountVo getManagementKpiDaily(@Valid ManagerDailyReqVO reqVO);

    /**
     * 获得环比数据
     *
     * @param reqVO
     * @return
     */
    DayOnDayCountVo getDayOnDay(@Valid ManagerDailyReqVO reqVO);
}
