package info.qizhi.aflower.module.report.service.handoverreport;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportPageReqVO;
import info.qizhi.aflower.module.report.controller.admin.handoverreport.vo.HandoverReportSaveReqVO;
import info.qizhi.aflower.module.report.dal.dataobject.handoverreport.HandoverReportDO;
import jakarta.validation.Valid;

import java.time.LocalDate;
import java.util.List;

/**
 * 交班报表[固化] Service 接口
 *
 * <AUTHOR>
 */
public interface HandoverReportService {

    /**
     * 创建交班报表[固化]
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHandoverReport(@Valid HandoverReportSaveReqVO createReqVO);

    /**
     * 更新交班报表[固化]
     *
     * @param updateReqVO 更新信息
     */
    void updateHandoverReport(@Valid HandoverReportSaveReqVO updateReqVO);

    /**
     * 删除交班报表[固化]
     *
     * @param id 编号
     */
    void deleteHandoverReport(Long id);

    /**
     * 获得交班报表[固化]
     *
     * @param id 编号
     * @return 交班报表[固化]
     */
    HandoverReportDO getHandoverReport(Long id);

    /**
     * 获得交班报表[固化]分页
     *
     * @param pageReqVO 分页查询
     * @return 交班报表[固化]分页
     */
    PageResult<HandoverReportDO> getHandoverReportPage(HandoverReportPageReqVO pageReqVO);

    /**
     * 获得交班报表[固化]列表
     *
     * @param reqVO 查询条件
     * @return 交班报表[固化]列表
     */
    List<HandoverReportDO> getHandoverReportList(HandoverReportPageReqVO reqVO);

    /**
     * 根据集团代码、门店代码和营业日期获取交班报表列表
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param bizDate 营业日期
     * @return 交班报表列表
     */
    List<HandoverReportDO> getHandoverReportListByBizDate(String gcode, String hcode, LocalDate bizDate);

    /**
     * 根据班次号获取交班报表列表
     *
     * @param gcode 集团代码
     * @param hcode 门店代码
     * @param shiftNo 班次号
     * @param bizDate 营业日期
     * @return 交班报表列表
     */
    List<HandoverReportDO> getHandoverReportListByShiftNo(String gcode, String hcode, String shiftNo, LocalDate bizDate);

    /**
     * 批量创建交班报表[固化]
     *
     * @param createReqVOs 创建信息列表
     */
    void createBatchHandoverReport(@Valid List<HandoverReportSaveReqVO> createReqVOs);

}
