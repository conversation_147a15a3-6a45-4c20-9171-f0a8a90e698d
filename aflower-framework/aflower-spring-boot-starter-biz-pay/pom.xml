<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>info.qizhi</groupId>
        <artifactId>aflower-framework</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aflower-spring-boot-starter-biz-pay</artifactId>
    <name>${project.artifactId}</name>
    <description>支付拓展，接入国内多个支付渠道
        1. 支付宝，基于官方 SDK 接入
        2. 微信支付，基于 weixin-java-pay 接入
    </description>

    <dependencies>

        <!-- Spring 核心 -->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>-->

        <!-- Web 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-web</artifactId>
        </dependency>

        <!--<dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-common</artifactId>
        </dependency>-->

        <!-- 工具类相关 -->
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>-->

        <!--<dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>-->

        <!--<dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>-->

        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>2.1.3</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <!--<dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>-->
    </dependencies>

</project>
