package info.qizhi.aflower.framework.pay.core.client.dto.refund;

import info.qizhi.aflower.framework.pay.core.utils.FuiouUtils;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.URL;

import java.util.Map;

/**
 * 统一 退款 Request DTO
 *
 * <AUTHOR>
 */
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class PayRefundUnifiedReqDTO {

    /**
     * 钱包类型
     */
    private String accountType;

    /**
     * 外部订单号
     * 对应 PayOrderExtensionDO 的 no 字段
     */
    @NotEmpty(message = "外部订单编号不能为空")
    private String outTradeNo;

    /**
     * 渠道支付订单号
     */
    private String channelOrderNo;

    /**
     * 操作类型
     * {@link FuiouUtils}
     */
    private String operateType;

    /**
     * 支付金额，单位：分
     *
     * 目前微信支付在退款的时候，必须传递该字段
     */
    private Long payPrice;
    /**
     * 退款金额，单位：分
     */
    @NotNull(message = "退款金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "支付金额必须大于零")
    private Long refundPrice;

    /**
     * 退款结果的 notify 回调地址
     */
    @URL(message = "支付结果的 notify 回调地址必须是 URL 格式")
    private String notifyUrl;

    /**
     * 支付渠道的额外参数
     *
     * 例如说，会员卡支付退款需要传vipId
     */
    private Map<String, String> channelExtras;

}
