package info.qizhi.aflower.framework.pay.core.client.impl.lakala;

import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import jakarta.validation.Validator;
import lombok.Data;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/12 10:23
 */
@Data
public class LakalaPayClientConfig implements PayClientConfig {

    private String serverUrl;

    private String version;

    private String orgNo;

    private String merchantNo;

    private String termNo;

    /**
     * 这里存的是privateKey的 serial_no
     */
    private String privateKey;

    private String appid;




    @Override
    public void validate(Validator validator) {

    }
}
