package info.qizhi.aflower.framework.pay.core.client.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ReflectUtil;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import info.qizhi.aflower.framework.pay.core.client.PayClientFactory;
import info.qizhi.aflower.framework.pay.core.client.impl.fuiou.*;
import info.qizhi.aflower.framework.pay.core.client.impl.lakala.LakalaActiveScanPayClient;
import info.qizhi.aflower.framework.pay.core.client.impl.lakala.LakalaPreScanPayClient;
import info.qizhi.aflower.framework.pay.core.client.impl.lakala.LakalaScanPayClient;
import info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou.XiaoyouScanPayClient;
import info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou.XiaoyouVipPayClient;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 支付客户端的工厂实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class PayClientFactoryImpl implements PayClientFactory {

    /**
     * 支付客户端 Map
     * <p>
     * key：渠道编号
     */
    private final ConcurrentMap<String, AbstractPayClient<?>> clients = new ConcurrentHashMap<>();

    /**
     * 支付客户端 Class Map
     */
    private final Map<PayChannelEnum, Class<?>> clientClass = new ConcurrentHashMap<>();

    public PayClientFactoryImpl() {
        // 富友支付客户端
        clientClass.put(PayChannelEnum.FUIOU_SCAN, FuiouScanPayClient.class);
        clientClass.put(PayChannelEnum.FUIOU_PRE_SCAN, FuiouPreScanPayClient.class);
        clientClass.put(PayChannelEnum.FUIOU_WX_LITE, FuiouWxLitePayClient.class);

        // 小游支付客户端
        clientClass.put(PayChannelEnum.XIAOYOU_SCAN, XiaoyouScanPayClient.class);
        clientClass.put(PayChannelEnum.XIAOYOU_VIP, XiaoyouVipPayClient.class);

        // 拉卡拉支付客户端
        clientClass.put(PayChannelEnum.LAKALA_SCAN, LakalaScanPayClient.class);
        clientClass.put(PayChannelEnum.LAKALA_ACTIVE_SCAN, LakalaActiveScanPayClient.class);
        clientClass.put(PayChannelEnum.LAKALA_PRE_SCAN, LakalaPreScanPayClient.class);
    }

    @Override
    public void registerPayClientClass(PayChannelEnum channel, Class<?> payClientClass) {
        Assert.notNull(channel, "支付渠道不能为空");
        Assert.notNull(payClientClass, "支付客户端类不能为空");
        clientClass.put(channel, payClientClass);
    }

    @Override
    public PayClient getPayClient(String channelId) {
        Assert.notBlank(channelId, "渠道编号不能为空");
        AbstractPayClient<?> client = clients.get(channelId);
        if (client == null) {
            log.error("[getPayClient][渠道编号({}) 找不到客户端]", channelId);
        }
        return client;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <Config extends PayClientConfig> void createOrUpdatePayClient(
            String channelId, PayChannelEnum channelEnum, Config config
    ) {
        Assert.notBlank(channelId, "渠道编号不能为空");
        Assert.notNull(channelEnum, "支付渠道不能为空");
        Assert.notNull(config, "支付客户端配置不能为空");

        AbstractPayClient<Config> client = (AbstractPayClient<Config>) clients.get(channelId);
        if (client == null) {
            synchronized (this) {
                client = (AbstractPayClient<Config>) clients.get(channelId);
                if (client == null) {
                    client = this.createPayClient(channelId, channelEnum, config);
                    client.init();
                    clients.put(client.getId(), client);
                }
            }
        } else {
            client.refresh(config);
        }
    }

    @SuppressWarnings("unchecked")
    private <Config extends PayClientConfig> AbstractPayClient<Config> createPayClient(
            String channelId, PayChannelEnum channelEnum, Config config
    ) {
        Class<?> payClientClass = clientClass.get(channelEnum);
        Assert.notNull(payClientClass, String.format("支付渠道(%s) Class 为空", channelEnum.getCode()));
        return (AbstractPayClient<Config>) ReflectUtil.newInstance(payClientClass, channelId, config);
    }

}
