package info.qizhi.aflower.framework.pay.config;

import info.qizhi.aflower.framework.pay.core.client.PayClientFactory;
import info.qizhi.aflower.framework.pay.core.client.impl.PayClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;

/**
 * 支付配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class AflowerPayAutoConfiguration {

    @Bean
    public PayClientFactory payClientFactory() {
        return new PayClientFactoryImpl();
    }


}
