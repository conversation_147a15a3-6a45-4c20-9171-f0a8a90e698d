package info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.common.util.spring.SpringUtils;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.impl.AbstractPayClient;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.HttpUtils;
import info.qizhi.aflower.framework.pay.core.utils.XiaoyouUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;


/**
 * 小游抽象类，实现小游统一的接口、以及部分实现（退款）
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractXiaoyouPayClient extends AbstractPayClient<XiaoyouPayClientConfig> {

    private static final String BASE_URL = SpringUtils.getProperty("aflower.pay.xiaoyou.url");

    final String SUCCESS_CODE = "200";

    final String PAY_SUCCESS = "1";

    Map<String, Map<String, Object>> reqParams = MapUtil.newHashMap();


    public AbstractXiaoyouPayClient(String channelId, String channelCode, XiaoyouPayClientConfig config) {
        super(channelId, channelCode, config);
    }

    @Override
    @SneakyThrows
    protected void doInit() {
        config.setServerUrl(BASE_URL);

        Map<String, Object> header = new HashMap<>();
        header.put("ispid", config.getOrgNo());
        header.put("clientid", config.getMerchantNo());
        reqParams.put("header", header);
    }

    @Override
    protected PayOrderRespDTO doParseOrderNotify(String reqStr) throws Throwable {
        return null;
    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        Map<String, Object> body = new HashMap<>();
        body.put("billno", reqDTO.getOutTradeNo());
        body.put("billrefno", reqDTO.getChannelOrderNo());
        reqParams.put("body", body);

        String sign = XiaoyouUtils.sign(body, timestamp, nonceStr, config.getPrivateKey());
        refreshHeader("VIPPayState", timestamp, nonceStr, sign);

        log.info("============ AbstractXiaoyouPayClient doGetOrder reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(config.getServerUrl(), reqParams);
        log.info("============ AbstractXiaoyouPayClient doGetOrder respJson: {}", respJson);

        PayOrderRespDTO orderRespDTO;
        String resultCode = respJson.getString("code");
        if (SUCCESS_CODE.equals(resultCode)) {
            JSONObject resultJson = respJson.getJSONObject("data");
            String channelOrderNo = resultJson.getString("billrefno");
            String thirdOrderNo = resultJson.getString("payno");
            LocalDateTime successTime = LocalDateTime.now();

            String status = resultJson.getString("status");

            Integer orderStatus = switch (status) {
                case "0" -> PayOrderStatusRespEnum.WAITING.getStatus();
                case "1" -> PayOrderStatusRespEnum.SUCCESS.getStatus();
                case "2" -> PayOrderStatusRespEnum.ERROR.getStatus();
                case "3" -> PayOrderStatusRespEnum.PAYING.getStatus();
                default -> PayOrderStatusRespEnum.UNKNOWN.getStatus();
            };
            orderRespDTO = PayOrderRespDTO.of(orderStatus, channelOrderNo, successTime, reqDTO.getOutTradeNo(), channelOrderNo, thirdOrderNo, respJson);
        } else {
            // 请求接口失败
            orderRespDTO = PayOrderRespDTO.errorOf(resultCode, respJson.getString("msg"), reqDTO.getOutTradeNo(), "", "", respJson);
        }
        orderRespDTO.setPlatform("xiaoyou");
        orderRespDTO.setAccountType(reqDTO.getAccountType());
        return orderRespDTO;

    }

    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Throwable {

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        Map<String, Object> body = new HashMap<>();
        Double refundPrice = BigDecimal.valueOf(reqDTO.getRefundPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        body.put("amount", String.valueOf(refundPrice));
        body.put("billno", reqDTO.getOutTradeNo());
        body.put("billrefno", reqDTO.getChannelOrderNo());
        if (reqDTO.getChannelExtras() != null && reqDTO.getChannelExtras().get("vipId") != null) {
            body.put("vipid", reqDTO.getChannelExtras().get("vipId"));
        }
        reqParams.put("body", body);

        String sign = XiaoyouUtils.sign(body, timestamp, nonceStr, config.getPrivateKey());
        refreshHeader("VIPRefund", timestamp, nonceStr, sign);

        log.info("============ AbstractXiaoyouPayClient doUnifiedRefund reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(config.getServerUrl(), reqParams);
        log.info("============ AbstractXiaoyouPayClient doUnifiedRefund respJson: {}", respJson);

        String resultCode = respJson.getString("code");
        PayRefundRespDTO refundResp;
        if (SUCCESS_CODE.equals(resultCode)) {
            refundResp = PayRefundRespDTO.successOf("", LocalDateTime.now(), reqDTO.getOutTradeNo(), "", respJson);
        } else {
            // 失败
            refundResp = PayRefundRespDTO.errorOf(resultCode, respJson.getString("msg"), reqDTO.getOutTradeNo(), respJson);
        }
        refundResp.setMerchantNo(config.getMerchantNo());
        return refundResp;
    }

    protected void refreshHeader(String serviceName, long timestamp, String nonceStr, String sign) {

        Map<String, Object> header = reqParams.get("header");
        header.put("serviceName", serviceName);
        header.put("timestamp", String.valueOf(timestamp));
        header.put("nonstr", nonceStr);
        header.put("sign", sign);

        reqParams.put("header", header);
    }

    protected JSONObject sendPost(String url, Map<String, Map<String, Object>> reqParams) {

        // 请求头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");

        HttpEntity<String> request = new HttpEntity<String>(JSONObject.toJSONString(reqParams), headers);

        ResponseEntity<String> responseEntity = HttpUtils.sendPost(url, request, String.class);

        if (responseEntity.getStatusCode().value() != 200) {
            throw new RuntimeException("调用第三方支付接口失败！");
        }
        return JSONObject.parseObject(responseEntity.getBody());
    }





}
