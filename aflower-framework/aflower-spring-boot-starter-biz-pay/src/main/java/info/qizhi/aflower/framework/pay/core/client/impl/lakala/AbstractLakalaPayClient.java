package info.qizhi.aflower.framework.pay.core.client.impl.lakala;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.common.util.spring.SpringUtils;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.framework.pay.core.client.impl.AbstractPayClient;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.HttpUtils;
import info.qizhi.aflower.framework.pay.core.utils.LakalaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/12 16:14
 */
@Slf4j
public abstract class AbstractLakalaPayClient extends AbstractPayClient<LakalaPayClientConfig> {

    static final String SUCCESS_CODE = "BBS00000";
    static final String UNKNOWN_CODE = "BBS11112";
    static final String[] USER_PAYING_CODES = {"BBS11105", "BBS10000"};

    private static final String BASE_URL_KEY = "aflower.pay.lakala.url";
    static final Map<String, String> URL_MAP = new ConcurrentHashMap<>();
    static {
        String baseUrl = SpringUtils.getProperty(BASE_URL_KEY);
        URL_MAP.put("scan", baseUrl + "/v3/labs/trans/micropay");
        URL_MAP.put("query", baseUrl + "/v3/labs/query/tradequery");
        URL_MAP.put("refund", baseUrl + "/v3/labs/relation/refund");
        URL_MAP.put("activeScan", baseUrl + "/v3/labs/trans/preorder");
        URL_MAP.put("preScan", baseUrl + "/v2/labs/txn/preAuth/preAuth");
        URL_MAP.put("preFinish", baseUrl + "/v2/labs/txn/preAuth/preAuthComplete");
        URL_MAP.put("preCancel", baseUrl + "/v2/labs/txn/preAuth/preAuthCancel");
        URL_MAP.put("preFinishCancel", baseUrl + "/v2/labs/txn/preAuth/refundAfterPreAuthCompleted");
        URL_MAP.put("preQuery", baseUrl + "/v2/labs/txn/query");
    }

    Map<String, Object> reqParams = MapUtil.newHashMap();
    public AbstractLakalaPayClient(String channelId, String channelCode, LakalaPayClientConfig config) {
        super(channelId, channelCode, config);
    }

    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Throwable {
        config.setServerUrl(URL_MAP.get("refund"));

        JSONObject reqData = new JSONObject();
        reqData.put("merchant_no", config.getMerchantNo());
        reqData.put("term_no", config.getTermNo());
        String refundNo = LakalaUtils.generatorOrderNo();
        reqData.put("out_trade_no", refundNo);
        reqData.put("refund_amount", reqDTO.getRefundPrice());
        reqData.put("origin_out_trade_no", reqDTO.getOutTradeNo());
        reqData.put("origin_trade_no", reqDTO.getChannelOrderNo());
        JSONObject locationInfoJson = new JSONObject();
        locationInfoJson.put("request_ip", getHostAddress());
        reqData.put("location_info", locationInfoJson);

        reqParams.put("req_data", reqData);
        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("============ AbstractLakalaPayClient doUnifiedRefund reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("============ AbstractLakalaPayClient doUnifiedRefund respJson: {}", respJson);

        String resultCode = respJson.getString("code");
        PayRefundRespDTO refundResp;
        if (SUCCESS_CODE.equals(resultCode)) {
            JSONObject resultJson = respJson.getJSONObject("resp_data");
            String channelRefundNo = resultJson.getString("trade_no");
            String accRefundNo = resultJson.getString("acc_trade_no");
            LocalDateTime successTime = getSuccessTime(resultJson.getString("trade_time"));
            refundResp = PayRefundRespDTO.successOf(channelRefundNo, successTime, refundNo, accRefundNo, respJson);
        } else {
            // 失败
            refundResp = PayRefundRespDTO.errorOf(resultCode, respJson.getString("msg"), refundNo, respJson);
        }
        refundResp.setMerchantNo(config.getMerchantNo());
        return refundResp;
    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {

        JSONObject reqData = new JSONObject();
        reqData.put("merchant_no", config.getMerchantNo());
        reqData.put("term_no", config.getTermNo());
        reqData.put("out_trade_no", reqDTO.getOutTradeNo());

        reqParams.put("req_data", reqData);

        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        config.setServerUrl(URL_MAP.get("query"));
        log.info("============ AbstractLakalaPayClient doGetOrder reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("============ AbstractLakalaPayClient doGetOrder respJson: {}", respJson);

        String resultCode = respJson.getString("code");
        // 成功
        JSONObject resultJson = respJson.getJSONObject("resp_data");
        PayOrderRespDTO orderRespDTO;
        if (SUCCESS_CODE.equals(resultCode)) {

            String channelOrderNo = resultJson.getString("trade_no");
            String accTradeNo = resultJson.getString("acc_trade_no");

            String tradeState = resultJson.getString("trade_state");
            Integer orderStatus = getOrderStatus(tradeState);
            LocalDateTime successTime = getSuccessTime(resultJson.getString("trade_time"));
            orderRespDTO = PayOrderRespDTO.of(orderStatus, channelOrderNo, successTime,  reqDTO.getOutTradeNo(), accTradeNo, channelOrderNo, respJson);
            orderRespDTO.setPayPrice(resultJson.getLong("total_amount"));
        } else if (UNKNOWN_CODE.equals(resultCode)) {
            // 交易未知
            String channelOrderNo = resultJson.getString("trade_no");
            String accTradeNo = resultJson.getString("acc_trade_no");
            orderRespDTO = PayOrderRespDTO.of(PayOrderStatusRespEnum.UNKNOWN.getStatus(), channelOrderNo, null, reqDTO.getOutTradeNo(), accTradeNo, channelOrderNo, respJson);
            orderRespDTO.setPayPrice(resultJson.getLong("total_amount"));
            orderRespDTO.setAccountType(reqDTO.getAccountType());
        } else if (Arrays.asList(USER_PAYING_CODES).contains(resultCode)) {
            // 支付中
            String channelOrderNo = resultJson.getString("trade_no");
            String accTradeNo = resultJson.getString("acc_trade_no");
            orderRespDTO = PayOrderRespDTO.of(PayOrderStatusRespEnum.PAYING.getStatus(), channelOrderNo, null, reqDTO.getOutTradeNo(), accTradeNo, channelOrderNo, respJson);
            orderRespDTO.setPayPrice(resultJson.getLong("total_amount"));
        } else {
            // 请求接口失败
            String channelOrderNo = resultJson == null ? null : resultJson.getString("trade_no");
            return PayOrderRespDTO.errorOf(resultCode, respJson.getString("msg"), reqDTO.getOutTradeNo(), channelOrderNo, channelOrderNo, respJson);
        }
        return orderRespDTO;

    }

    @Override
    protected VipInfoRespDTO doGetVipInfo(VipInfoReqDTO reqDTO) throws Throwable {
        return null;
    }

    protected Integer getOrderStatus (String tradeState) {
        return switch (tradeState) {
            case "INIT", "CREATE" -> PayOrderStatusRespEnum.WAITING.getStatus();
            case "SUCCESS" -> PayOrderStatusRespEnum.SUCCESS.getStatus();
            case "FAIL" -> PayOrderStatusRespEnum.ERROR.getStatus();
            case "DEAL" -> PayOrderStatusRespEnum.PAYING.getStatus();
            case "CLOSE" -> PayOrderStatusRespEnum.CLOSED.getStatus();
            case "PART_REFUND", "REFUND" -> PayOrderStatusRespEnum.REFUND.getStatus();
            case "REVOKED" -> PayOrderStatusRespEnum.REVOKED.getStatus();
            default -> PayOrderStatusRespEnum.UNKNOWN.getStatus();
        };
    }

    protected LocalDateTime getSuccessTime(String tradeTime) {
        if (StringUtils.isBlank(tradeTime)) {
            return LocalDateTime.now();
        }
        return LocalDateTime.parse(tradeTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
    }

    protected String getHostAddress() {
        InetAddress inetAddress = null;
        try {
            inetAddress = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            log.error("获取本地IP失败，错误：", e);
        }
        if (inetAddress == null) {
            return null;
        }
        return inetAddress.getHostAddress();
    }

    protected JSONObject sendPost(String authorization, Map<String, Object> params) throws Exception {

        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        headers.add("accept", "application/json");
        headers.add("Authorization", "LKLAPI-SHA256withRSA " + authorization);

        HttpEntity<String> request = new HttpEntity<String>(JSONObject.toJSONString(params), headers);

        log.info("============ AbstractLakalaPayClient sendPost ==========: {}", JSONObject.toJSONString(request));

        // 发起网络请求
        ResponseEntity<String> response = HttpUtils.sendPost(config.getServerUrl(), request, String.class);

        if (response.getStatusCode().value() != 200) {
            throw new RuntimeException("请求失败");
        }

        return LakalaUtils.getResponseMap(response);
    }
}
