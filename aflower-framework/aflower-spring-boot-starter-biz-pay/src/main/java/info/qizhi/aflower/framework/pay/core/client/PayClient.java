package info.qizhi.aflower.framework.pay.core.client;

import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;

/**
 * 支付客户端，用于对接各支付渠道的 SDK，实现发起支付、退款等功能
 *
 * <AUTHOR>
 */
public interface PayClient {

    /**
     * 获得渠道编号
     *
     * @return 渠道编号
     */
    String getId();

    // ============ 支付相关 ==========

    /**
     * 调用支付渠道，统一下单
     *
     * @param reqDTO 下单信息
     * @return 支付订单信息
     */
    PayOrderRespDTO unifiedOrder(PayOrderUnifiedReqDTO reqDTO);

    /**
     * 解析 order 回调数据
     *
     * @param reqStr 请求信息字符串
     * @return 支付订单信息
     */
    PayOrderRespDTO parseOrderNotify(String reqStr);

    /**
     * 获得支付订单信息
     * @param reqDTO 请求参数
     * @return 支付订单信息
     */
    PayOrderRespDTO getOrder(PayOrderGetReqDTO reqDTO);

    // ============ 退款相关 ==========

    /**
     * 调用支付渠道，进行退款
     *
     * @param reqDTO  统一退款请求信息
     * @return 退款信息
     */
    PayRefundRespDTO unifiedRefund(PayRefundUnifiedReqDTO reqDTO);

    /**
     * 获取会员信息
     * @return 会员信息
     */
    VipInfoRespDTO getVipInfo(VipInfoReqDTO reqDTO);


}
