package info.qizhi.aflower.framework.pay.core.client.impl.lakala;

import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.common.util.spring.SpringUtils;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.exception.PayException;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.PayAccountTypeEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.lakala.LakalaTransTypeEnum;
import info.qizhi.aflower.framework.pay.core.utils.LakalaUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * 
 * 拉卡拉聚合主扫
 *
 * 支持 支付宝、微信、银联、数字人民币、翼支付
 *
 * <AUTHOR>
 * @date 2024/12/12 16:18
 */
@Slf4j
public class LakalaActiveScanPayClient extends AbstractLakalaPayClient {

    private static final String NOTIFY_URL = SpringUtils.getProperty("aflower.pay.lakala.notify-url");
    private static final String DEFAULT_VERSION = "3.0";


    public LakalaActiveScanPayClient(String channelId, LakalaPayClientConfig config) {
        super(channelId, PayChannelEnum.LAKALA_ACTIVE_SCAN.getCode(), config);
    }

    @Override
    @SneakyThrows
    protected void doInit() {
        JSONObject reqParams = new JSONObject();
        reqParams.put("version", DEFAULT_VERSION);
        reqParams.put("req_time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

        this.reqParams = reqParams;

    }


    @Override
    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {
        if (!validateUnifiedReq(reqDTO)) {
            throw new PayException(-1, "请求参数不正确！");
        }
        // 验证权限
        config.setServerUrl(URL_MAP.get("activeScan"));

        JSONObject reqData = buildOrderReqData(reqDTO);
        reqParams.put("req_data", reqData);

        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("==================== LakalaActiveScanPayClient doUnifiedOrder reqParams =============== : {}", reqParams);
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("==================== LakalaActiveScanPayClient doUnifiedOrder respJson =============== : {}", respJson);

        PayOrderRespDTO orderResp = parseOrderResponse(respJson, reqDTO, reqData.getString("out_trade_no"));

        orderResp.setMerchantNo(config.getMerchantNo());
        return orderResp;
    }

    private JSONObject buildOrderReqData(PayOrderUnifiedReqDTO reqDTO) {
        JSONObject reqData = new JSONObject();
        reqData.put("merchant_no", config.getMerchantNo());
        reqData.put("term_no", config.getTermNo());
        String orderNo = LakalaUtils.generatorOrderNo();
        reqData.put("out_trade_no", orderNo);
        reqData.put("account_type", reqDTO.getAccountType());
        LakalaTransTypeEnum transTypeEnum = LakalaTransTypeEnum.getByName(reqDTO.getChannelExtras().get("transType"));
        reqData.put("trans_type", transTypeEnum.getCode());
        reqData.put("total_amount", reqDTO.getPrice() + "");
        JSONObject locationInfoJson = new JSONObject();
        locationInfoJson.put("request_ip", getHostAddress());
        reqData.put("location_info", locationInfoJson);
        reqData.put("subject", reqDTO.getSubject());
        reqData.put("notify_url", NOTIFY_URL);

        JSONObject accBusiFields = new JSONObject();
        fillAccBusiFields(accBusiFields, reqDTO);
        reqData.put("acc_busi_fields", accBusiFields);

        return reqData;
    }

    private void fillAccBusiFields(JSONObject accBusiFields, PayOrderUnifiedReqDTO reqDTO) {
        String accountType = reqDTO.getAccountType();
        String transType = reqDTO.getChannelExtras().get("transType");
        if (PayAccountTypeEnum.WECHAT.getCode().equals(accountType) && !LakalaTransTypeEnum.NATIVE.getCode().equals(transType))
        {
            accBusiFields.put("sub_appid", config.getAppid());
            accBusiFields.put("user_id", reqDTO.getChannelExtras().get("openid"));
        }
        else if (PayAccountTypeEnum.ALIPAY.getCode().equals(accountType) && LakalaTransTypeEnum.JSAPI.getCode().equals(transType))
        {
            accBusiFields.put("user_id", reqDTO.getChannelExtras().get("buyer_user_id"));
        }
    }

    private PayOrderRespDTO parseOrderResponse(JSONObject respJson, PayOrderUnifiedReqDTO reqDTO, String orderNo) {
        String resultCode = respJson.getString("code");
        PayOrderRespDTO orderResp;
        if (SUCCESS_CODE.equals(resultCode)) {
            JSONObject resultJson = respJson.getJSONObject("resp_data");
            String channelOrderNo = resultJson.getString("trade_no");
            LocalDateTime successTime = LocalDateTime.now();
            orderResp = PayOrderRespDTO.of(PayOrderStatusRespEnum.WAITING.getStatus(), channelOrderNo, successTime, orderNo, "", orderNo, resultJson);

            JSONObject accRespFields = resultJson.getJSONObject("acc_resp_fields");
            Map<String, String> extraParams = extractExtraParams(accRespFields, reqDTO);
            orderResp.setExtraParams(extraParams);
        } else {
            orderResp = PayOrderRespDTO.errorOf(resultCode, respJson.getString("msg"), orderNo, null, null, respJson);
        }
        orderResp.setMerchantNo(config.getMerchantNo());
        return orderResp;
    }

    private Map<String, String> extractExtraParams(JSONObject accRespFields, PayOrderUnifiedReqDTO reqDTO) {
        Map<String, String> extraParams = new HashMap<>();
        String transType = reqDTO.getChannelExtras().get("transType");
        PayAccountTypeEnum accountTypeEnum = PayAccountTypeEnum.getByCode(reqDTO.getAccountType());

        if (LakalaTransTypeEnum.NATIVE.getName().equals(transType)) {
            extraParams.put("code", accRespFields.getString("code"));
        } else if (LakalaTransTypeEnum.JSAPI.getName().equals(transType)) {
            switch (accountTypeEnum) {
                case ALIPAY:
                    extraParams.put("prepayId", accRespFields.getString("prepay_id"));
                    extraParams.put("subMchId", accRespFields.getString("sub_mch_id"));
                    break;
                case WECHAT:
                    extraParams.put("prepayId", accRespFields.getString("prepay_id"));
                    extraParams.put("paySign", accRespFields.getString("pay_sign"));
                    extraParams.put("appid", accRespFields.getString("app_id"));
                    extraParams.put("timestamp", accRespFields.getString("time_stamp"));
                    extraParams.put("nonceStr", accRespFields.getString("nonce_str"));
                    extraParams.put("package", accRespFields.getString("package"));
                    extraParams.put("signType", accRespFields.getString("sign_type"));
                    extraParams.put("subMchId", accRespFields.getString("sub_mch_id"));
                    break;
                case UQRCODEPAY:
                    extraParams.put("redirectUrl", accRespFields.getString("redirect_url"));
                    extraParams.put("subMchId", accRespFields.getString("sub_mch_id"));
                    break;
                default:
                    break;
            }
        }
        else if (LakalaTransTypeEnum.APP.getName().equals(transType) || LakalaTransTypeEnum.MINI.getName().equals(transType))
        {
            if (Objects.requireNonNull(accountTypeEnum) == PayAccountTypeEnum.WECHAT) {
                extraParams.put("prepayId", accRespFields.getString("prepay_id"));
                extraParams.put("paySign", accRespFields.getString("pay_sign"));
                extraParams.put("appId", accRespFields.getString("app_id"));
                extraParams.put("timestamp", accRespFields.getString("time_stamp"));
                extraParams.put("nonce_str", accRespFields.getString("nonce_str"));
                extraParams.put("package", accRespFields.getString("package"));
                extraParams.put("signType", accRespFields.getString("sign_type"));
                if (LakalaTransTypeEnum.APP.getName().equals(transType)) {
                    extraParams.put("partnerId", accRespFields.getString("partner_id"));
                }
                extraParams.put("subMchId", accRespFields.getString("sub_mch_id"));
            }
        }
        return extraParams;
    }


    @Override
    protected PayOrderRespDTO doParseOrderNotify(String reqStr) throws Throwable {
        if (StringUtils.isBlank(reqStr)) {
            return null;
        }
        JSONObject reqJson = JSONObject.parseObject(reqStr);

        String merchantNo = reqJson.getString("merchant_no");
        String channelOrderNo = reqJson.getString("trade_no");
        String orderNo = reqJson.getString("out_trade_no");
        String accOrderNo = reqJson.getString("acc_order_no");
        String tradeStatus = reqJson.getString("trade_status");
        String successTime = reqJson.getString("trade_time");

        Integer payStatus = getOrderStatus(tradeStatus);
        return new PayOrderRespDTO() {{
            setMerchantNo(merchantNo);
            setStatus(payStatus);
            setOutTradeNo(orderNo);
            setChannelOrderNo(channelOrderNo);
            setAccTradeNo(accOrderNo);
            setThirdOrderNo(channelOrderNo);
            if (StringUtils.isNotBlank(successTime) && PayOrderStatusRespEnum.isSuccess(payStatus)) {
                setSuccessTime(LocalDateTime.parse(successTime, DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
            }
        }};
    }

    private boolean validateUnifiedReq(PayOrderUnifiedReqDTO reqDTO) {
        String accountType = reqDTO.getAccountType();
        if (StringUtils.isBlank(accountType)) {
            throw new PayException(-1, "钱包类型不能为空！");
        }
        PayAccountTypeEnum accountTypeEnum = PayAccountTypeEnum.getByCode(accountType);
        if (accountTypeEnum == null) {
            throw new PayException(-1, "无效的钱包类型！");
        }
        String transType = reqDTO.getChannelExtras() == null ? null : reqDTO.getChannelExtras().get("transType");
        if (StringUtils.isBlank(transType)) {
            throw new PayException(-1, "接入方式不能为空！");
        }
        LakalaTransTypeEnum transTypeEnum = LakalaTransTypeEnum.getByName(transType);
        if (transTypeEnum == null) {
            throw new PayException(-1, "无效的接入方式！");
        }
        if (PayAccountTypeEnum.WECHAT.getCode().equals(accountType)) {
            String appId = reqDTO.getChannelExtras() == null ? null : reqDTO.getChannelExtras().get("appId");
            String openId = reqDTO.getChannelExtras() == null ? null : reqDTO.getChannelExtras().get("openId");
            switch (transTypeEnum) {
                case NATIVE:
                    break;
                case JSAPI, APP:
                    // 验证 appid、openid
                    if (StringUtils.isBlank(appId) || StringUtils.isBlank(openId)) {
                        throw new PayException(-1, "appid和openid不能为空！");
                    }
                    break;
                case MINI:
                    // 验证 appid
                    if (StringUtils.isBlank(appId)) {
                        throw new PayException(-1, "appid不能为空！");
                    }
                    break;
                default:
                    throw new PayException(-1, "接入方式不存在！");
            }

        } else if (PayAccountTypeEnum.ALIPAY.getCode().equals(accountType)) {
            String buyerUserId = reqDTO.getChannelExtras() == null ? null : reqDTO.getChannelExtras().get("buyerUserId");
            switch (transTypeEnum) {
                case NATIVE:
                    break;
                case JSAPI:
                    if (StringUtils.isBlank(buyerUserId)) {
                        throw new PayException(-1, "买家在支付宝的用户ID不能为空！");
                    }
                    break;
                default:
                    throw new PayException(-1, "接入方式不存在！");
            }
        }
        return true;
    }


}
