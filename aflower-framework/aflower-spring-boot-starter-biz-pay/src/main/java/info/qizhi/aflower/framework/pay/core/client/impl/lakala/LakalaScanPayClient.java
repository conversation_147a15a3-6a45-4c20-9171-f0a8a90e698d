package info.qizhi.aflower.framework.pay.core.client.impl.lakala;

import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.LakalaUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;


/**
 * 
 * 拉卡拉聚合被扫
 *
 * 支持 支付宝、微信、银联、数字人民币、翼支付
 *
 * <AUTHOR>
 * @date 2024/12/12 16:18
 */
@Slf4j
public class LakalaScanPayClient extends AbstractLakalaPayClient {


    public LakalaScanPayClient(String channelId, LakalaPayClientConfig config) {
        super(channelId, PayChannelEnum.LAKALA_SCAN.getCode(), config);
    }

    @Override
    @SneakyThrows
    protected void doInit() {

        reqParams.put("version", StringUtils.isBlank(config.getVersion()) ? "3.0" : config.getVersion());
        reqParams.put("req_time", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));

    }


    @Override
    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {
        config.setServerUrl(URL_MAP.get("scan"));

        JSONObject reqData = new JSONObject();
        reqData.put("merchant_no", config.getMerchantNo());
        reqData.put("term_no", config.getTermNo());
        String orderNo = LakalaUtils.generatorOrderNo();
        reqData.put("out_trade_no", orderNo);
        reqData.put("auth_code", reqDTO.getChannelExtras().get("authCode"));
        reqData.put("total_amount", reqDTO.getPrice() + "");
        JSONObject locationInfoJson = new JSONObject();
        locationInfoJson.put("request_ip", getHostAddress());
        reqData.put("location_info", locationInfoJson);
        reqData.put("subject", reqDTO.getSubject());

        reqParams.put("req_data", reqData);

        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("==================== LakalaScanPayClient doUnifiedOrder reqParams =============== : {}", reqParams);
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("==================== LakalaScanPayClient doUnifiedOrder respJson =============== : {}", respJson);

        PayOrderRespDTO orderResp;
        String resultCode = respJson.getString("code");
        JSONObject resultJson = respJson.getJSONObject("resp_data");
        if (SUCCESS_CODE.equals(resultCode)) {
            String channelOrderNo = resultJson.getString("trade_no");
            String accTradeNo = resultJson.getString("acc_trade_no");
            LocalDateTime successTime = LocalDateTime.parse(resultJson.getString("trade_time"), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            orderResp = PayOrderRespDTO.successOf(channelOrderNo, successTime, orderNo, accTradeNo, orderNo, resultJson);
        } else if (UNKNOWN_CODE.equals(resultCode) || Arrays.asList(USER_PAYING_CODES).contains(resultCode)) {
            // 交易未知、支付中轮询查
            // 轮询查询
            int loopCount = 4;
            int loopTime = 5000;
            long loopTotalTime = 21000L;
            long startTime = System.currentTimeMillis();
            orderResp = new PayOrderRespDTO();
            for (int i = 0; i < loopCount; i++) {
                long endTime = System.currentTimeMillis();
                if (endTime - startTime > loopTotalTime) {
                    break;
                }
                Thread.sleep(loopTime);

                orderResp = doGetOrder(new PayOrderGetReqDTO(){{
                    setOutTradeNo(orderNo);
                }});
                // 未知，未支付，支付中，要再次轮询查
                if (!PayOrderStatusRespEnum.isLoopQuery(orderResp.getStatus())) {
                    break;
                }
            }
        } else {
            // lakala的 thirdOrderNo = channelOrderNo
            String channelOrderNo = resultJson == null ? null : resultJson.getString("trade_no");
            orderResp = PayOrderRespDTO.errorOf(resultCode, respJson.getString("msg"), orderNo, channelOrderNo, channelOrderNo, respJson);
        }
        orderResp.setMerchantNo(config.getMerchantNo());
        return orderResp;
    }

    @Override
    protected PayOrderRespDTO doParseOrderNotify(String reqStr) throws Throwable {
        return null;
    }


}
