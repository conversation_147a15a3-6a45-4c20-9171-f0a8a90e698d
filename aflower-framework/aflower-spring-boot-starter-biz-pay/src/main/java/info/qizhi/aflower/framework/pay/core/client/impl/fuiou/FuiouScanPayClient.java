package info.qizhi.aflower.framework.pay.core.client.impl.fuiou;

import cn.hutool.core.map.MapUtil;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.fuiou.FuiouBusinessEnum;
import info.qizhi.aflower.framework.pay.core.utils.FuiouUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 富友【条码支付】的 PayClient 实现类
 * 文档：https://fundwx.fuiou.com/doc
 *
 * <AUTHOR>
 */
@Slf4j
public class FuiouScanPayClient extends AbstractFuiouPayClient {

    private static final String BUSINESS_PAY_CODE = "SCAN_";
    private static final String BUSINESS_REFUND_CODE = "REFUND_";

    public FuiouScanPayClient(String channelId, FuiouPayClientConfig config) {
        super(channelId, PayChannelEnum.FUIOU_SCAN.getCode(), config);
    }


    @Override
    public PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Exception {
        config.setServerUrl(URL_MAP.get("micropay"));
        return super.doUnifiedOrder(reqDTO, BUSINESS_PAY_CODE + reqDTO.getAccountType());

    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {
        return super.doGetOrder(reqDTO.getOutTradeNo(), BUSINESS_PAY_CODE + reqDTO.getAccountType());
    }

    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Exception {
        config.setServerUrl(URL_MAP.get("refund"));


        Map<String, String> params = MapUtil.newHashMap();
        params.putAll(baseParams);
        FuiouBusinessEnum businessEnum = FuiouBusinessEnum.getByCode(BUSINESS_REFUND_CODE + reqDTO.getAccountType());
        params.put("order_type", businessEnum.getOrderType());
        params.put("mchnt_order_no", reqDTO.getOutTradeNo());
        String refundOrderNo = FuiouUtils.generatorOrderNo(BUSINESS_REFUND_CODE + reqDTO.getAccountType(), config.getOrderPrefix());
        params.put("refund_order_no", refundOrderNo);
        params.put("refund_amt", reqDTO.getRefundPrice().toString());
        params.put("total_amt", reqDTO.getPayPrice().toString());
        params.put("operator_id", "");
        int orderPrefix = StringUtils.isBlank(config.getOrderPrefix()) ? 0 : config.getOrderPrefix().length();
        params.put("reserved_origi_dt", reqDTO.getOutTradeNo().substring(orderPrefix, orderPrefix + 8));

        Map<String, String> resposeMap = sendPost(params, config);

        String returnCode = resposeMap.get("result_code");
        PayRefundRespDTO refundRespDTO;
        if (SUCCESS_CODE.equals(returnCode)) {
            String accRefundNo = resposeMap.get("refund_id");
            String channelRefundNo = resposeMap.get("reserved_fy_trace_no");
            refundRespDTO = PayRefundRespDTO.successOf(channelRefundNo, LocalDateTime.now(), refundOrderNo, accRefundNo, resposeMap);
        } else {
            refundRespDTO = PayRefundRespDTO.errorOf(resposeMap.get("result_code"), resposeMap.get("result_msg"), resposeMap.get("refund_order_no"), resposeMap);
        }
        refundRespDTO.setMerchantNo(config.getMerchantNo());
        return refundRespDTO;
    }

}
