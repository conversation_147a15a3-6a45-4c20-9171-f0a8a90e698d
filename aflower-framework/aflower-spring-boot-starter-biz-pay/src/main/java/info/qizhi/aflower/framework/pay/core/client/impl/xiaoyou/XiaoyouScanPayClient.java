package info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou;

import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.XiaoyouUtils;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 *
 * 小游 B2C（被扫）
 * <AUTHOR>
 * @date 2024/12/13 11:19
 */
@Slf4j
public class XiaoyouScanPayClient extends AbstractXiaoyouPayClient {

    public XiaoyouScanPayClient(String channelId,  XiaoyouPayClientConfig config) {
        super(channelId, PayChannelEnum.XIAOYOU_SCAN.getCode(), config);
    }

    @Override
    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        Map<String, Object> body = new HashMap<>();
        body.put("qrcode", reqDTO.getChannelExtras().get("authCode"));
        Double price = BigDecimal.valueOf(reqDTO.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        body.put("amount", String.valueOf(price));
        String billno = XiaoyouUtils.generatorOrderNo();
        body.put("billno", billno);
        reqParams.put("body", body);

        String sign = XiaoyouUtils.sign(body, timestamp, nonceStr, config.getPrivateKey());
        refreshHeader("VIPBtCQrPay", timestamp, nonceStr, sign);


        log.info("=========== XiaoyouScanPayClient doUnifiedOrder reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(config.getServerUrl(), reqParams);
        log.info("=========== XiaoyouScanPayClient doUnifiedOrder respJson: {}", respJson);

        String resultCode = respJson.getString("code");
        if (!SUCCESS_CODE.equals(resultCode)) {
            String errorMsg = respJson.getString("msg");
            PayOrderRespDTO queryResp = PayOrderRespDTO.errorOf(respJson.getString("code"), errorMsg, billno, "", "", respJson);
            queryResp.setMerchantNo(config.getMerchantNo());
            return queryResp;
        }

        // 不管状态是什么，都最少查询一次
        JSONObject resultJson = respJson.getJSONObject("data");
        String channelOrderNo = resultJson.getString("billrefno");
        int loopCount = 10;
        int loopTime = 2000;
        long loopTotalTime = 20000L;
        long startTime = System.currentTimeMillis();
        PayOrderRespDTO queryResp = new PayOrderRespDTO();
        for (int i = 0; i < loopCount; i++) {
            long endTime = System.currentTimeMillis();
            if (endTime - startTime > loopTotalTime) {
                break;
            }
            Thread.sleep(loopTime);

            queryResp = doGetOrder(new PayOrderGetReqDTO(){{
                setOutTradeNo(billno);
                setChannelOrderNo(channelOrderNo);
            }});
            // 未知，未支付，支付中，要再次轮询查
            if (!PayOrderStatusRespEnum.isLoopQuery(queryResp.getStatus())) {
                break;
            }
        }
        queryResp.setMerchantNo(config.getMerchantNo());
        queryResp.setChannelOrderNo(channelOrderNo);

        return queryResp;
    }

    @Override
    protected VipInfoRespDTO doGetVipInfo(VipInfoReqDTO reqDTO) throws Throwable {
        return null;
    }

}
