package info.qizhi.aflower.framework.pay.core.client.impl.fuiou;

import info.qizhi.aflower.framework.common.util.validation.ValidationUtils;
import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotBlank;
import lombok.*;

/**
 * 富友的 PayClientConfig 实现类
 * 属性主要是一些必要属性
 *
 * <AUTHOR>
 */
@Data
public class FuiouPayClientConfig implements PayClientConfig {

    /**
     * 网关地址
     *
     * 1. <a href="https://spay-mc.fuioupay.com">生产环境</a>
     * 2. <a href="https://fundwx.fuiou.com">沙箱环境</a>
     */
    @NotBlank(message = "网关地址不能为空", groups = {ModePublicKey.class})
    private String serverUrl;

    /**
     * 接口版本
     */
    @NotBlank(message = "版本不能为空", groups = {ModePublicKey.class})
    private String version;

    /**
     * 机构号,接入机构在富友的唯一代码
     */
    @NotBlank(message = "机构号不能为空", groups = {ModePublicKey.class})
    private String orgNo;

    /**
     * 商户号, 富友分配给二级商户的商户号
     */
    @NotBlank(message = "商户号不能为空", groups = {ModePublicKey.class})
    private String merchantNo;

    /**
     * 终端号(没有真实终端号统一填88888888)
     */
    @NotBlank(message = "终端号不能为空", groups = {ModePublicKey.class})
    private String termNo;

    /**
     * 商户私钥--用于请求报文签名原文加密
     */
    @NotBlank(message = "商户私钥不能为空", groups = {ModePublicKey.class})
    private String privateKey;

    /**
     * 公钥字符串--用于响应报文签名原文解密
     */
    @NotBlank(message = "公钥字符串不能为空", groups = {ModePublicKey.class})
    private String publicKey;

    @NotBlank(message = "订单前缀不能为空", groups = {ModePublicKey.class})
    private String orderPrefix;


    public interface ModePublicKey {
    }

    @Override
    public void validate(Validator validator) {
        ValidationUtils.validate(validator, this,  ModePublicKey.class);
    }

}
