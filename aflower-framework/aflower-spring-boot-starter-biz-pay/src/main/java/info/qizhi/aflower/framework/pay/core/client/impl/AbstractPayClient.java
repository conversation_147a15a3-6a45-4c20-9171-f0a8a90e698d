package info.qizhi.aflower.framework.pay.core.client.impl;

import info.qizhi.aflower.framework.common.exception.ServiceException;
import info.qizhi.aflower.framework.common.util.validation.ValidationUtils;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.framework.pay.core.client.exception.PayException;
import lombok.extern.slf4j.Slf4j;

import static info.qizhi.aflower.framework.common.util.json.JsonUtils.toJsonString;

/**
 * 支付客户端的抽象类，提供模板方法，减少子类的冗余代码
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractPayClient<Config extends PayClientConfig> implements PayClient {

    /**
     * 渠道编号 hcode + "_" + channelCode
     */
    private final String channelId;
    /**
     * 渠道编码
     */
    @SuppressWarnings("FieldCanBeLocal")
    private final String channelCode;
    /**
     * 支付配置
     */
    protected Config config;

    public AbstractPayClient(String channelId, String channelCode, Config config) {
        this.channelId = channelId;
        this.channelCode = channelCode;
        this.config = config;
    }

    /**
     * 初始化
     */
    public final void init() {
        doInit();
        log.debug("[init][客户端({}) 初始化完成]", getId());
    }

    /**
     * 自定义初始化
     */
    protected abstract void doInit();

    public final void refresh(Config config) {
        // 判断是否更新
        if (config.equals(this.config)) {
            return;
        }
        log.info("[refresh][客户端({})发生变化，重新初始化]", getId());
        this.config = config;
        // 初始化
        this.init();
    }

    @Override
    public String getId() {
        return channelId;
    }

    // ============ 支付相关 ==========

    @Override
    public final PayOrderRespDTO unifiedOrder(PayOrderUnifiedReqDTO reqDTO) {
        ValidationUtils.validate(reqDTO);
        // 执行统一下单
        try {
            return doUnifiedOrder(reqDTO);
        } catch (ServiceException ex) {
            log.warn("[unifiedOrder][客户端({}) request({}) 发起支付失败，原因: {}]",
                    getId(), toJsonString(reqDTO), ex.getMessage());
            throw ex;
        } catch (Throwable ex) {
            log.error("[unifiedOrder][客户端({}) request({}) 发起支付异常]",
                    getId(), toJsonString(reqDTO), ex);
            throw buildPayException(ex);
        }
    }

    protected abstract PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO)
            throws Throwable;

    @Override
    public final PayOrderRespDTO parseOrderNotify(String reqStr) {
        try {
            return doParseOrderNotify(reqStr);
        } catch (ServiceException ex) {
            log.warn("[parseOrderNotify][客户端({}) reqStr({}) 解析失败，原因: {}]",
                    getId(), reqStr, ex.getMessage());
            throw ex;
        } catch (Throwable ex) {
            log.error("[parseOrderNotify][客户端({}) reqStr({}) 解析失败]",
                    getId(), reqStr, ex);
            throw buildPayException(ex);
        }
    }

    protected abstract PayOrderRespDTO doParseOrderNotify(String reqStr)
            throws Throwable;

    @Override
    public PayOrderRespDTO getOrder(PayOrderGetReqDTO reqDTO) {
        try {
            return doGetOrder(reqDTO);
        } catch (ServiceException ex) {
            log.warn("[getOrder][客户端({}) outTradeNo({}) 查询支付单失败，原因: {}]",
                    getId(), reqDTO.getOutTradeNo(), ex.getMessage());
            throw ex;
        } catch (Throwable ex) {
            log.error("[getOrder][客户端({}) outTradeNo({}) 查询支付单异常]",
                    getId(), reqDTO.getOutTradeNo(), ex);
            throw buildPayException(ex);
        }
    }

    protected abstract PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable;

    // ============ 退款相关 ==========

    @Override
    public final PayRefundRespDTO unifiedRefund(PayRefundUnifiedReqDTO reqDTO) {
        ValidationUtils.validate(reqDTO);
        // 执行统一退款
        try {
            return doUnifiedRefund(reqDTO);
        } catch (ServiceException ex) {
            log.warn("[unifiedRefund][客户端({}) request({}) 发起退款失败，原因: {}]",
                    getId(), toJsonString(reqDTO), ex.getMessage());
            throw ex;
        } catch (Throwable ex) {
            log.error("[unifiedRefund][客户端({}) request({}) 发起退款异常]",
                    getId(), toJsonString(reqDTO), ex);
            throw buildPayException(ex);
        }
    }

    protected abstract PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Throwable;

    @Override
    public VipInfoRespDTO getVipInfo(VipInfoReqDTO reqDTO) {
        try {
            return doGetVipInfo(reqDTO);
        } catch (ServiceException ex) {
            log.warn("[getVipInfo][客户端({}) 查询会员信息失败，原因: {}]", getId(), ex.getMessage());
            throw ex;
        } catch (Throwable ex) {
            log.error("[getVipInfo][客户端({}) 查询会员信息异常]", getId(), ex);
            throw buildPayException(ex);
        }
    }

    protected abstract VipInfoRespDTO doGetVipInfo(VipInfoReqDTO reqDTO) throws Throwable;

    // ========== 各种工具方法 ==========

    private PayException buildPayException(Throwable ex) {
        if (ex instanceof PayException) {
            return (PayException) ex;
        }
        return new PayException(ex);
    }



}
