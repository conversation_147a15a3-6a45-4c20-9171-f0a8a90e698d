package info.qizhi.aflower.framework.pay.core.client.dto.order;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.URL;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 统一下单 Request DTO
 *
 * <AUTHOR>
 */
@Data
public class PayOrderUnifiedReqDTO {

    /**
     * 钱包类型
     */
    private String accountType;

    /**
     * 商品标题
     */
    @NotEmpty(message = "商品标题不能为空")
    @Length(max = 32, message = "商品标题不能超过 32")
    private String subject;
    /**
     * 商品描述信息
     */
    @Length(max = 128, message = "商品描述信息长度不能超过128")
    private String body;
    /**
     * 支付结果的 notify 回调地址
     */
    @URL(message = "支付结果的 notify 回调地址必须是 URL 格式")
    private String notifyUrl;

    /**
     * 支付金额，单位：分
     */
    @NotNull(message = "支付金额不能为空")
    @DecimalMin(value = "0", inclusive = false, message = "支付金额必须大于零")
    private Long price;

    /**
     * 支付过期时间，单位分钟
     */
    private Long expireTime;

    // ========== 拓展参数 ==========
    /**
     * 支付渠道的额外参数
     *
     * 例如：
     * 1、微信公众号需要传递 openid，appid 参数
     * 2、lakala主扫需要传递 trans_type（接入方式） 参数
     *
     */
    private Map<String, String> channelExtras;

}
