package info.qizhi.aflower.framework.pay.core.client.impl.fuiou;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.util.spring.SpringUtils;
import info.qizhi.aflower.framework.pay.core.client.PayClient;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.framework.pay.core.client.impl.AbstractPayClient;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.FuiouUtils;
import info.qizhi.aflower.framework.common.util.http.HttpUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

import static cn.hutool.core.date.DatePattern.PURE_DATETIME_FORMATTER;
import static cn.hutool.core.date.DatePattern.PURE_DATE_FORMATTER;
import static info.qizhi.aflower.framework.common.exception.enums.GlobalErrorCodeConstants.BAD_REQUEST;
import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception0;


/**
 * 富友抽象类，实现富友统一的接口、以及部分实现（退款）
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractFuiouPayClient extends AbstractPayClient<FuiouPayClientConfig> {

    private static final String DEFAULT_VERSION = "1.0";
    private static final String CHARSET = "GBK";
    private static final String[] NEED_LOOP_QUERY_CODE = {"030010", "010002", "9999", "010001", "2001", "2002"};
    private static final String UNKNOWN_CODE = "9999";
    private static final int LOOP_COUNT = 4;
    private static final int LOOP_TIME = 5000;
    private static final long LOOP_TOTAL_TIME = 21000L;
    protected static final String SUCCESS_CODE = "000000";

    private static final String BASE_URL = SpringUtils.getProperty("aflower.pay.fuiou.url");
    protected static final Map<String, String> URL_MAP = new ConcurrentHashMap<>();
    static {
        URL_MAP.put("micropay", BASE_URL + "/micropay");
        URL_MAP.put("query", BASE_URL + "/commonQuery");
        URL_MAP.put("refund", BASE_URL + "/commonRefund");
        URL_MAP.put("preAuthFinish", BASE_URL + "/preAuthFinish");
        URL_MAP.put("preAuthCancel", BASE_URL + "/preAuthCancel");
        URL_MAP.put("preAuthFinishCancel", BASE_URL + "/preAuthFinishCancel");
        URL_MAP.put("wxPreCreCreate", BASE_URL + "/wxPreCreate");
    }

    protected Map<String, String> baseParams = new ConcurrentHashMap<>();

    public AbstractFuiouPayClient(String channelId, String channelCode, FuiouPayClientConfig config) {
        super(channelId, channelCode, config);
    }

    @Override
    @SneakyThrows
    protected void doInit() {
        baseParams.put("version", DEFAULT_VERSION);
        // 机构号
        baseParams.put("ins_cd", config.getOrgNo());
        // 商户号
        baseParams.put("mchnt_cd", config.getMerchantNo());
        // 终端号
        baseParams.put("term_id", config.getTermNo());
        baseParams.put("random_str", UUID.randomUUID().toString().replace("-", ""));
        baseParams.put("sign", "");
    }

    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO, String businessCode) throws Exception {
        String authCode = MapUtil.getStr(reqDTO.getChannelExtras(), "authCode");
        if (StrUtil.isEmpty(authCode)) {
            throw exception0(BAD_REQUEST.getCode(), "条形码不能为空");
        }

        Map<String, String> params = new ConcurrentHashMap<>(baseParams);

        params.put("order_type", FuiouUtils.getOrderType(businessCode));
        params.put("goods_des", reqDTO.getSubject());
        params.put("goods_detail", StrUtil.blankToDefault(reqDTO.getBody(), ""));
        params.put("addn_inf", "");
        String orderNo = FuiouUtils.generatorOrderNo(businessCode, config.getOrderPrefix());
        params.put("mchnt_order_no", orderNo);
        params.put("curr_type", "");
        params.put("order_amt", String.valueOf(reqDTO.getPrice()));
        params.put("term_ip", getHostAddress());
        params.put("txn_begin_ts", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")));
        params.put("goods_tag", "");
        params.put("auth_code", authCode);
        params.put("sence", "1");
        params.put("reserved_sub_appid", "");
        params.put("reserved_limit_pay", "");
        params.put("reserved_terminal_info", "{\"serial_num\":\"12345678901SN012\"}");

        Map<String, String> resposeMap = sendPost(params, config);

        String returnCode = resposeMap.get("result_code");
        PayOrderRespDTO queryResp;
        String channelOrderNo = resposeMap.get("reserved_fy_order_no");
        if (SUCCESS_CODE.equals(returnCode)) {
            String accTradeNo = resposeMap.get("transaction_id");
            LocalDateTime successTime = LocalDateTime.parse(resposeMap.get("reserved_txn_fin_ts"), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            queryResp = PayOrderRespDTO.successOf(channelOrderNo, successTime, orderNo, accTradeNo, orderNo, resposeMap);
        } else if (Arrays.asList(NEED_LOOP_QUERY_CODE).contains(returnCode)) {
            // 轮询查询
            long startTime = System.currentTimeMillis();
            queryResp = new PayOrderRespDTO();
            for (int i = 0; i < LOOP_COUNT; i++) {
                long endTime = System.currentTimeMillis();
                if (endTime - startTime > LOOP_TOTAL_TIME) {
                    break;
                }
                Thread.sleep(LOOP_TIME);
                queryResp = doGetOrder(orderNo, businessCode);
                // 未知，未支付，支付中，要再次轮询查
                if (!PayOrderStatusRespEnum.isLoopQuery(queryResp.getStatus())) {
                    break;
                }
            }
        } else {
            String errorMsg = resposeMap.get("result_msg");
            queryResp = PayOrderRespDTO.errorOf(returnCode, errorMsg, orderNo, channelOrderNo, orderNo, resposeMap);
        }
        queryResp.setMerchantNo(config.getMerchantNo());
        return queryResp;
    }

    @Override
    protected PayOrderRespDTO doParseOrderNotify(String reqStr) {
        throw new UnsupportedOperationException("富友暂不支持异步回调");
    }


    protected PayOrderRespDTO doGetOrder(String outTradeNo, String businessCode) throws Exception {
        config.setServerUrl(URL_MAP.get("query"));
        Map<String, String> params = MapUtil.newHashMap();
        params.putAll(baseParams);
        params.put("order_type", FuiouUtils.getOrderType(businessCode));
        params.put("mchnt_order_no", outTradeNo);

        Map<String, String> resposeMap = sendPost(params, config);

        String resultCode = resposeMap.get("result_code");
        String channelOrderNo = resposeMap.get("reserved_fy_order_no");
        String accTradeNo = resposeMap.get("transaction_id");
        Long payPrice = Long.valueOf(resposeMap.get("order_amt"));
        PayOrderRespDTO orderRespDTO;
        if (SUCCESS_CODE.equals(resultCode)) {
            LocalDateTime successTime = parseDateTime(resposeMap.get("reserved_txn_fin_ts"));
            String transStat = resposeMap.get("trans_stat");
            Integer orderStatus = this.getOrderStatus(transStat);
            orderRespDTO = PayOrderRespDTO.of(orderStatus, channelOrderNo, successTime, outTradeNo, accTradeNo, outTradeNo, resposeMap);

        } else if (UNKNOWN_CODE.equals(resultCode)) {
            // 状态未知
            orderRespDTO = PayOrderRespDTO.of(PayOrderStatusRespEnum.UNKNOWN.getStatus(), channelOrderNo, null, outTradeNo, accTradeNo, outTradeNo, resposeMap);
        } else {
            String errorMsg = resposeMap.get("result_msg");
            orderRespDTO = PayOrderRespDTO.errorOf(resultCode, errorMsg, outTradeNo, channelOrderNo, outTradeNo, resposeMap);
        }
        orderRespDTO.setPayPrice(payPrice);
        orderRespDTO.setMerchantNo(config.getMerchantNo());
        return orderRespDTO;

    }

    private Integer getOrderStatus(String transStat) {
        return switch (transStat) {
            case "SUCCESS" -> PayOrderStatusRespEnum.SUCCESS.getStatus();
            case "USERPAYING" -> PayOrderStatusRespEnum.PAYING.getStatus();
            case "NOTPAY" -> PayOrderStatusRespEnum.WAITING.getStatus();
            case "CLOSED" -> PayOrderStatusRespEnum.CLOSED.getStatus();
            case "REVOKED" -> PayOrderStatusRespEnum.REVOKED.getStatus();
            case "REFUND" -> PayOrderStatusRespEnum.REFUND.getStatus();
            default -> PayOrderStatusRespEnum.UNKNOWN.getStatus();
        };
    }

    @Override
    protected VipInfoRespDTO doGetVipInfo(VipInfoReqDTO reqDTO) throws Throwable {
        throw new UnsupportedOperationException("富友暂不支持会员信息查询");
    }


    protected String formatTime(LocalDateTime time) {
        return LocalDateTimeUtil.format(time, PURE_DATETIME_FORMATTER);
    }

    protected LocalDateTime parseDateTime(String str) {
        return LocalDateTimeUtil.parse(str, PURE_DATETIME_FORMATTER);
    }

    protected LocalDateTime parseDate(String str) {
        return LocalDateTimeUtil.parse(str, PURE_DATE_FORMATTER);
    }

    protected String getHostAddress() {
        InetAddress inetAddress;
        try {
            inetAddress = InetAddress.getLocalHost();
            return inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("获取本地IP失败，错误：", e);
            return "127.0.0.1";
        }
    }

    protected Map<String, String> sendPost(Map<String, String> params, FuiouPayClientConfig config) throws Exception {
        // 请求参数
        Map<String, String> requestMap = FuiouUtils.getRequestMap(params, config.getPrivateKey());

        // 发起网络请求
        String result = HttpUtils.sendPost(config.getServerUrl(), requestMap, CHARSET);

        return FuiouUtils.getResposeMap(result, config.getPublicKey());
    }




}
