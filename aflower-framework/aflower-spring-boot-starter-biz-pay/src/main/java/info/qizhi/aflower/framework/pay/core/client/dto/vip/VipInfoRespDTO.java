package info.qizhi.aflower.framework.pay.core.client.dto.vip;

import lombok.Data;


/**
 * 
 * 
 * <AUTHOR>
 * @date 2024/12/18 10:11
 */
@Data
public class VipInfoRespDTO {
    /**
     * 会员ID
     */
    private String vipId;
    /**
     * 会员名称
     */
    private String vipName;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 余额
     */
    private Long balance;
    /**
     * 赠送余额
     */
    private Long giveBalance;
    /**
     * 查询状态：1-成功；2-失败
     */
    private Integer status;
    /**
     * 错误代码
     */
    private Integer errorCode;
    /**
     * 错误信息
     */
    private String errorMsg;


}
