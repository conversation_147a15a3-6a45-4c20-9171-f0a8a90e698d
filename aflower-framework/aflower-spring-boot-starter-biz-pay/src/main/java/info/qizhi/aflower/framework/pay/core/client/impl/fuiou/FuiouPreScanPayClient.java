package info.qizhi.aflower.framework.pay.core.client.impl.fuiou;

import cn.hutool.core.map.MapUtil;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.fuiou.FuiouBusinessEnum;
import info.qizhi.aflower.framework.pay.core.enums.refund.PayRefundOperateTypeEnum;
import info.qizhi.aflower.framework.pay.core.utils.FuiouUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 富友【预授权扫码支付】的 PayClient 实现类
 * 文档：https://fundwx.fuiou.com/doc
 *
 * <AUTHOR>
 */
@Slf4j
public class FuiouPreScanPayClient extends AbstractFuiouPayClient {

    private static final String BUSINESS_PAY_CODE = "PRE_SCAN_";
    private static final String BUSINESS_FINISH_CODE = "PRE_FINISH_";
    private static final String BUSINESS_CANCEL_CODE = "PRE_CANCEL_";
    private static final String BUSINESS_FINISH_CANCEL_CODE = "PRE_FINISH_CANCEL_";


    public FuiouPreScanPayClient(String channelId, FuiouPayClientConfig config) {
        super(channelId, PayChannelEnum.FUIOU_PRE_SCAN.getCode(), config);
    }


    @Override
    public PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Exception {
        config.setServerUrl(URL_MAP.get("micropay"));
        return super.doUnifiedOrder(reqDTO, BUSINESS_PAY_CODE + reqDTO.getAccountType());
    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {
        return super.doGetOrder(reqDTO.getOutTradeNo(), BUSINESS_PAY_CODE + reqDTO.getAccountType());
    }

    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Exception {
        if (StringUtils.isBlank(reqDTO.getOperateType())) {
            throw new ServerException("操作类型不能为空！");
        }
        if (StringUtils.isBlank(reqDTO.getOutTradeNo())) {
            throw new ServerException("商户订单号不能为空！");
        }
        if (StringUtils.isBlank(reqDTO.getAccountType())) {
            throw new ServerException("钱包类型不能未空！");
        }

        PayRefundOperateTypeEnum operateTypeEnum = PayRefundOperateTypeEnum.getByCode(reqDTO.getOperateType());
        if (operateTypeEnum == null) {
            throw new ServerException("操作类型不正确！");
        }

        Map<String, String> params = MapUtil.newHashMap();
        params.putAll(baseParams);

        FuiouBusinessEnum businessEnum;
        switch (operateTypeEnum) {
            case PRE_FINISH:
                businessEnum = FuiouBusinessEnum.getByCode(BUSINESS_FINISH_CODE + reqDTO.getAccountType());
                params.put("order_amt", reqDTO.getPayPrice().toString());
                params.put("finish_amt", reqDTO.getRefundPrice().toString());
                config.setServerUrl(URL_MAP.get("preAuthFinish"));
                break;
            case PRE_CANCEL:
                businessEnum = FuiouBusinessEnum.getByCode(BUSINESS_CANCEL_CODE + reqDTO.getAccountType());
                params.put("order_amt", reqDTO.getPayPrice().toString());
                config.setServerUrl(URL_MAP.get("preAuthCancel"));
                break;
            case PRE_FINISH_CANCEL:
                businessEnum = FuiouBusinessEnum.getByCode(BUSINESS_FINISH_CANCEL_CODE + reqDTO.getAccountType());
                params.put("reserved_refund_amt", reqDTO.getRefundPrice().toString());
                config.setServerUrl(URL_MAP.get("preAuthFinishCancel"));
                break;
            default:
                throw new ServerException("操作类型不正确！");
        }
        params.put("order_type", businessEnum.getOrderType());
        String orderNo = FuiouUtils.generatorOrderNo(businessEnum.getCode(), config.getOrderPrefix());
        params.put("mchnt_order_no", orderNo);
        params.put("orig_order_no", reqDTO.getOutTradeNo());

        Map<String, String> resposeMap = sendPost(params, config);

        String returnCode = resposeMap.get("result_code");
        PayRefundRespDTO refundRespDTO;
        if (SUCCESS_CODE.equals(returnCode)) {
            String accRefundNo = resposeMap.get("refund_id");
            String channelRefundNo = resposeMap.get("reserved_fy_trace_no");
            refundRespDTO = PayRefundRespDTO.successOf(channelRefundNo, LocalDateTime.now(), orderNo, accRefundNo, resposeMap);
        } else {
            refundRespDTO = PayRefundRespDTO.errorOf(resposeMap.get("refund_order_no"), resposeMap.get("result_msg"), orderNo, resposeMap);
        }
        refundRespDTO.setMerchantNo(config.getMerchantNo());
        return refundRespDTO;
    }

}
