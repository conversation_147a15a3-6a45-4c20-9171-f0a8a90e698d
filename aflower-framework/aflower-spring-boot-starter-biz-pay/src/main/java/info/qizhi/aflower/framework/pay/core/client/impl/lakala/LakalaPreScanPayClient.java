package info.qizhi.aflower.framework.pay.core.client.impl.lakala;

import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import info.qizhi.aflower.framework.pay.core.utils.LakalaUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.rmi.ServerException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 *
 * 拉卡拉扫码预授权
 * <AUTHOR>
 * @date 2025/01/02 16:16
 */
@Slf4j
public class LakalaPreScanPayClient extends AbstractLakalaPayClient {

    private static final String DEFAULT_VERSION = "1.0.0";
    private static final String SUCCESS_TRADE_STATE = "000000";
    private static final String NEED_QUERY = "1";
    private static final Integer LOOP_COUNT = 4;
    private static final Integer LOOP_TIME = 5000;
    private static final Integer LOOP_TOTAL_TIME = 21000;

    public LakalaPreScanPayClient(String channelId, LakalaPayClientConfig config) {
        super(channelId, PayChannelEnum.LAKALA_PRE_SCAN.getCode(), config);
    }

    @Override
    @SneakyThrows
    protected void doInit() {

        JSONObject reqParams = new JSONObject();
        reqParams.put("ver", DEFAULT_VERSION);
        reqParams.put("timestamp", System.currentTimeMillis() / 1000);
        reqParams.put("rnd", UUID.randomUUID().toString());
        JSONObject locationInfoJson = new JSONObject();
        locationInfoJson.put("request_ip", getHostAddress());
        reqParams.put("location_info", locationInfoJson);
        reqParams.put("termExtInfo", new JSONObject());

        this.reqParams = reqParams;
    }

    @Override
    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {

        config.setServerUrl(URL_MAP.get("preScan"));

        JSONObject reqDataJson = new JSONObject();
        reqDataJson.put("merId", config.getMerchantNo());
        reqDataJson.put("termId", config.getTermNo());
        reqDataJson.put("authCode", reqDTO.getChannelExtras().get("authCode"));
        reqDataJson.put("amount", reqDTO.getPrice());
        String orderNo = LakalaUtils.generatorOrderNo();
        reqDataJson.put("merReqSno", orderNo);
        reqDataJson.put("subject", reqDTO.getSubject());

        reqParams.put("reqData", reqDataJson);

        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("==================== LakalaPreScanPayClient doUnifiedOrder reqParams =============== : {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("==================== LakalaPreScanPayClient doUnifiedOrder respJson =============== : {}", respJson);

        JSONObject resultJson = respJson.getJSONObject("respData");
        if (resultJson == null) {
            return PayOrderRespDTO.errorOf(UNKNOWN_CODE, "拉卡拉返回结果为空", orderNo, "", "", respJson);
        }

        String channelOrderNo = resultJson.getString("lklOrderNo");
        String accOrderNo = resultJson.getString("accountChannelOrderNo");

        String resultCode = respJson.getString("retCode");
        if (SUCCESS_TRADE_STATE.equals(resultCode)) {
            return PayOrderRespDTO.successOf(channelOrderNo, LocalDateTime.now(), orderNo, accOrderNo, channelOrderNo, resultJson);
        }

        PayOrderRespDTO orderResp;
        String needQuery = resultJson.getString("needQuery");
        if (NEED_QUERY.equals(needQuery)) {
            // 轮询查询
            long startTime = System.currentTimeMillis();
            orderResp = new PayOrderRespDTO();
            for (int i = 0; i < LOOP_COUNT; i++) {
                long endTime = System.currentTimeMillis();
                if (endTime - startTime > LOOP_TOTAL_TIME) {
                    break;
                }
                Thread.sleep(LOOP_TIME);

                orderResp = doGetOrder(new PayOrderGetReqDTO(){{
                    setOutTradeNo(orderNo);
                }});
                // 未知，未支付，支付中，要再次轮询查
                if (!PayOrderStatusRespEnum.isLoopQuery(orderResp.getStatus())) {
                    break;
                }
            }

        } else {
            orderResp = PayOrderRespDTO.errorOf(resultCode, respJson.getString("retMsg"), orderNo, channelOrderNo, channelOrderNo, resultJson);
        }
        orderResp.setMerchantNo(config.getMerchantNo());
        return orderResp;
    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {

        config.setServerUrl(URL_MAP.get("preQuery"));

        JSONObject reqData = new JSONObject();
        reqData.put("mercId", config.getMerchantNo());
        reqData.put("termNo", config.getTermNo());
        reqData.put("ornOrderId", reqDTO.getOutTradeNo());

        reqParams.put("reqData", reqData);

        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("============ LakalaPreScanPayClient doGetOrder reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("============ LakalaPreScanPayClient doGetOrder respJson: {}", respJson);

        String resultCode = respJson.getString("retCode");
        // 成功
        JSONObject resultJson = respJson.getJSONObject("respData");
        if (SUCCESS_TRADE_STATE.equals(resultCode)) {
            String channelOrderNo = resultJson.getString("lklOrderNo");
            String accTradeNo = resultJson.getString("weOrderNo");

            String tradeState = resultJson.getString("tradeState");
            Integer orderStatus = getOrderStatus(tradeState);
            LocalDateTime successTime = null;
            if (StringUtils.isNotBlank(resultJson.getString("payTime"))) {
                successTime = LocalDateTime.parse(resultJson.getString("payTime"), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            }
            return PayOrderRespDTO.of(orderStatus, channelOrderNo, successTime,  reqDTO.getOutTradeNo(), accTradeNo, channelOrderNo, respJson);
        } else {
            // 请求接口失败
            String channelOrderNo = resultJson == null ? null : resultJson.getString("lklOrderNo");
            return PayOrderRespDTO.errorOf(resultCode, respJson.getString("msg"), reqDTO.getOutTradeNo(), channelOrderNo, channelOrderNo, respJson);
        }

    }


    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Throwable {
        String operateType = reqDTO.getOperateType();
        if (StringUtils.isBlank(operateType)) {
            throw new ServerException("操作类型不能为空！");
        }

        JSONObject reqData = new JSONObject();
        reqData.put("merId", config.getMerchantNo());
        reqData.put("termId", config.getTermNo());

        String orderNo = LakalaUtils.generatorOrderNo();
        switch (operateType) {
            case "PRE_FINISH":
                reqData.put("merReqSno", orderNo);
                reqData.put("originLklOrderNo", reqDTO.getChannelOrderNo());
                reqData.put("authAmt", reqDTO.getRefundPrice());
                config.setServerUrl(URL_MAP.get("preFinish"));
                break;
            case "PRE_CANCEL":
                reqData.put("merReqSno", orderNo);
                reqData.put("originLklOrderNo", reqDTO.getChannelOrderNo());
                config.setServerUrl(URL_MAP.get("preCancel"));
                break;
            case "PRE_FINISH_CANCEL":
                reqData.put("merReqSno", orderNo);
                reqData.put("originLklOrderNo", reqDTO.getChannelOrderNo());
                reqData.put("refundAmt", reqDTO.getRefundPrice());
                config.setServerUrl(URL_MAP.get("preFinishCancel"));
                break;
            default:
                throw new ServerException("操作类型不正确！");
        }
        reqParams.put("reqData", reqData);


        String authorization = LakalaUtils.getAuthorization(config.getOrgNo(), config.getPrivateKey(), JSONObject.toJSONString(reqParams));

        log.info("============ LakalaPreScanPayClient doUnifiedRefund reqParams: {}", JSONObject.toJSONString(reqParams));
        JSONObject respJson = sendPost(authorization, reqParams);
        log.info("============ LakalaPreScanPayClient doUnifiedRefund respJson: {}", respJson);

        String returnCode = respJson.getString("retCode");
        PayRefundRespDTO refundRespDTO;
        if (SUCCESS_TRADE_STATE.equals(returnCode)) {
            JSONObject resultJson = respJson.getJSONObject("respData");
            String successTimeStr = StringUtils.isBlank(resultJson.getString("finishTime")) ? resultJson.getString("tradeTime") : resultJson.getString("finishTime");
            LocalDateTime successTime = getSuccessTime(successTimeStr);
            String channelRefundNo = resultJson.getString("lklOrderNo");
            refundRespDTO = PayRefundRespDTO.successOf(channelRefundNo, successTime, orderNo, "", respJson);
        } else {
            refundRespDTO = PayRefundRespDTO.errorOf(returnCode, respJson.getString("retMsg"), orderNo, respJson);
        }
        refundRespDTO.setMerchantNo(config.getMerchantNo());
        return refundRespDTO;
    }

    @Override
    protected PayOrderRespDTO doParseOrderNotify(String reqStr) throws Throwable {
        throw new ServerException("暂不支持该功能！");
    }
}
