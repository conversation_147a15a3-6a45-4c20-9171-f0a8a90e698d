package info.qizhi.aflower.framework.pay.core.client.exception;

import info.qizhi.aflower.framework.common.exception.ErrorCode;
import info.qizhi.aflower.framework.common.exception.enums.ServiceErrorCodeRange;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付系统异常 Exception
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PayException extends RuntimeException {

    /**
     * 业务错误码
     *
     * @see ServiceErrorCodeRange
     */
    private Integer code;
    /**
     * 错误提示
     */
    private String message;

    /**
     * 空构造方法，避免反序列化问题
     */
    public PayException() {
    }

    public PayException(Throwable e) {
        this.code = 400;
        this.message = e.getMessage();
    }

    public PayException(ErrorCode errorCode) {
        this.code = errorCode.getCode();
        this.message = errorCode.getMsg();
    }

    public PayException(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public PayException setCode(Integer code) {
        this.code = code;
        return this;
    }

    public PayException setMessage(String message) {
        this.message = message;
        return this;
    }

}
