package info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.vip.VipInfoRespDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.utils.XiaoyouUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 小游支付会员卡支付
 * 
 * <AUTHOR>
 * @date 2024/12/13 15:27
 */
public class XiaoyouVipPayClient extends AbstractXiaoyouPayClient {

    public XiaoyouVipPayClient(String channelId, XiaoyouPayClientConfig config) {
        super(channelId, PayChannelEnum.XIAOYOU_VIP.getCode(), config);
    }

    @Override
    protected PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Throwable {

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        Map<String, Object> body = MapUtil.newHashMap();
        body.put("vipid", reqDTO.getChannelExtras().get("vipId"));
        Double price = BigDecimal.valueOf(reqDTO.getPrice()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP).doubleValue();
        body.put("amount", String.valueOf(price));
        String billno = XiaoyouUtils.generatorOrderNo();
        body.put("billno", billno);
        reqParams.put("body", body);

        String sign = XiaoyouUtils.sign(body, timestamp, nonceStr, config.getPrivateKey());
        refreshHeader("VIPPayment", timestamp, nonceStr, sign);

        JSONObject respJson = sendPost(config.getServerUrl(), reqParams);
        String resultCode = respJson.getString("code");

        PayOrderRespDTO payResp;

        if (SUCCESS_CODE.equals(resultCode)) {
            JSONObject resultDataJson = respJson.getJSONObject("data");
            String status = resultDataJson.getString("status");
            if (PAY_SUCCESS.equals(status)) {
                payResp = PayOrderRespDTO.successOf(resultDataJson.getString("billrefno"), LocalDateTime.now(), billno,  resultDataJson.getString("billrefno"), "", respJson);
            } else {
                payResp = PayOrderRespDTO.errorOf(status, resultDataJson.getString("statusmsg"), billno, "", "", respJson);
            }
        } else {
            String errorMsg = respJson.getString("msg");
            payResp = PayOrderRespDTO.errorOf(respJson.getString("code"), errorMsg, billno, "", "", respJson);

        }
        payResp.setMerchantNo(config.getMerchantNo());
        return payResp;

    }

    @Override
    protected VipInfoRespDTO doGetVipInfo(VipInfoReqDTO reqDTO) throws Throwable {

        long timestamp = System.currentTimeMillis() / 1000;
        String nonceStr = UUID.randomUUID().toString().replace("-", "");

        Map<String, Object> body = new HashMap<>();
        body.put("keyword", reqDTO.getKeyword());
        body.put("itype", String.valueOf(reqDTO.getType()));

        reqParams.put("body", body);

        String sign = XiaoyouUtils.sign(body, timestamp, nonceStr, config.getPrivateKey());
        refreshHeader("VIPInfo", timestamp, nonceStr, sign);

        JSONObject respJson = sendPost(config.getServerUrl(), reqParams);
        String resultCode = respJson.getString("code");
        if (SUCCESS_CODE.equals(resultCode)) {
            JSONObject resultDataJson = respJson.getJSONObject("data");
            VipInfoRespDTO respDTO = new VipInfoRespDTO();
            respDTO.setStatus(1);
            respDTO.setVipId(resultDataJson.getString("vipid"));
            respDTO.setVipName(resultDataJson.getString("uname"));
            respDTO.setMobile(resultDataJson.getString("mobile"));
            respDTO.setIdCard(resultDataJson.getString("idcard"));
            respDTO.setBalance(BigDecimal.valueOf(resultDataJson.getDouble("balance")).multiply(BigDecimal.valueOf(100)).longValue());
            respDTO.setGiveBalance(BigDecimal.valueOf(resultDataJson.getDouble("balance2")).multiply(BigDecimal.valueOf(100)).longValue());

            return respDTO;
        } else {
            String errorMsg = respJson.getString("msg");
            return new VipInfoRespDTO(){{
                setStatus(2);
                setErrorCode(Integer.valueOf(resultCode));
                setErrorMsg(errorMsg);
            }};
        }
    }
}
