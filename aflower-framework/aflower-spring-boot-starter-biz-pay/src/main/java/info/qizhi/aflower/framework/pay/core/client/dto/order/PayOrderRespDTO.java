package info.qizhi.aflower.framework.pay.core.client.dto.order;

import info.qizhi.aflower.framework.pay.core.client.exception.PayException;
import info.qizhi.aflower.framework.pay.core.enums.order.PayOrderStatusRespEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 渠道支付订单 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class PayOrderRespDTO {
    /**
     * 支付状态
     *
     * 枚举：{@link PayOrderStatusRespEnum}
     */
    private Integer status;

    /**
     * 渠道平台
     */
    private String platform;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 外部订单号
     *
     * 对应 PayOrderExtensionDO 的 no 字段
     */
    private String outTradeNo;

    /**
     * 支付渠道订单号
     */
    private String channelOrderNo;

    /**
     * 账户端交易单号
     */
    private String accTradeNo;

    /**
     * 第三方单号
     */
    private String thirdOrderNo;

    /**
     * 支付总金额，单位：分
     */
    private Long payPrice;

    /**
     * 退款金额，单位：分
     */
    private Long refundPrice;

    /**
     * 钱包类型
     */
    private String accountType;

    /**
     * 支付成功时间
     */
    private LocalDateTime successTime;

    /**
     * 额外的返回参数：
     * 1、主扫时返回的二维码信息
     * 2、小程序、公众号支付时返回的 prepay_id等信息
     */
    private Map<String, String> extraParams;

    /**
     * 原始的同步/异步通知结果
     */
    private Object rawData;

    /**
     * 调用渠道的错误码
     *
     * 注意：这里返回的是业务异常，而是不系统异常。
     * 如果是系统异常，则会抛出 {@link PayException}
     */
    private String channelErrorCode;
    /**
     * 调用渠道报错时，错误信息
     */
    private String channelErrorMsg;

    public PayOrderRespDTO() {
    }

    /**
     * 创建【SUCCESS】状态的订单返回
     */
    public static PayOrderRespDTO successOf(
            String channelOrderNo, LocalDateTime successTime, String outTradeNo, String tradeNo, String thirdOrderNo, Object rawData
    ) {
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        respDTO.status = PayOrderStatusRespEnum.SUCCESS.getStatus();
        respDTO.channelOrderNo = channelOrderNo;
        respDTO.successTime = successTime;
        // 相对通用的字段
        respDTO.outTradeNo = outTradeNo;
        respDTO.accTradeNo = tradeNo;
        respDTO.thirdOrderNo = thirdOrderNo;
        respDTO.rawData = rawData;
        return respDTO;
    }

    /**
     * 创建【ERROR】状态的订单返回，适合调用支付渠道失败时
     */
    public static PayOrderRespDTO errorOf(
            String channelErrorCode, String channelErrorMsg, String outTradeNo,
            String channelOrderNo, String thirdOrderNo, Object rawData
    ) {
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        respDTO.status = PayOrderStatusRespEnum.ERROR.getStatus();
        respDTO.channelErrorCode = channelErrorCode;
        respDTO.channelErrorMsg = channelErrorMsg;
        // 相对通用的字段
        respDTO.outTradeNo = outTradeNo;
        respDTO.channelOrderNo = channelOrderNo;
        respDTO.thirdOrderNo = thirdOrderNo;
        respDTO.rawData = rawData;
        return respDTO;
    }

    /**
     * 创建指定状态的订单返回
     */
    public static PayOrderRespDTO of(
            Integer status, String channelOrderNo, LocalDateTime successTime,
            String outTradeNo, String tradeNo, String thirdOrderNo, Object rawData
    ) {
        PayOrderRespDTO respDTO = new PayOrderRespDTO();
        respDTO.status = status;
        respDTO.channelOrderNo = channelOrderNo;
        respDTO.successTime = successTime;
        // 相对通用的字段
        respDTO.outTradeNo = outTradeNo;
        respDTO.accTradeNo = tradeNo;
        respDTO.thirdOrderNo = thirdOrderNo;
        respDTO.rawData = rawData;
        return respDTO;
    }



}
