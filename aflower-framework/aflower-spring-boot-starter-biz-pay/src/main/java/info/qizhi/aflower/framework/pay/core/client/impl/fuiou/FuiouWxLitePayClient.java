package info.qizhi.aflower.framework.pay.core.client.impl.fuiou;

import cn.hutool.core.map.MapUtil;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderGetReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.order.PayOrderUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundRespDTO;
import info.qizhi.aflower.framework.pay.core.client.dto.refund.PayRefundUnifiedReqDTO;
import info.qizhi.aflower.framework.pay.core.enums.channel.PayChannelEnum;
import info.qizhi.aflower.framework.pay.core.enums.order.fuiou.FuiouBusinessEnum;
import info.qizhi.aflower.framework.pay.core.utils.FuiouUtils;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Map;


/**
 * 富友【微信小程序支付】的 PayClient 实现类
 *
 * <AUTHOR>
 */
@Slf4j
public class FuiouWxLitePayClient extends AbstractFuiouPayClient {

    private static final String BUSINESS_CODE = "LITE_WX_";
    private static final String REFUND_BUSINESS_CODE = "REFUND_wx";

    public FuiouWxLitePayClient(String channelId, FuiouPayClientConfig config) {
        super(channelId, PayChannelEnum.FUIOU_WX_LITE.getCode(), config);
    }


    @Override
    public PayOrderRespDTO doUnifiedOrder(PayOrderUnifiedReqDTO reqDTO) throws Exception {

        config.setServerUrl(URL_MAP.get("wxPreCreate"));

        Map<String, String> params = MapUtil.newHashMap();
        params.putAll(baseParams);

        // 商户生成的订单号
        String orderNo = FuiouUtils.generatorOrderNo(BUSINESS_CODE, config.getOrderPrefix());
        params.put("mchnt_order_no", orderNo);
        params.put("txn_begin_ts", formatTime(LocalDateTime.now()));
        // 终端IP是什么意思，上面代码都是用的测试的终端ip
        params.put("term_ip", getHostAddress());

        params.put("order_amt", reqDTO.getPrice().toString());
        params.put("notify_url", reqDTO.getNotifyUrl());
        params.put("trade_type", "LETPAY");

        params.put("sub_openid", reqDTO.getChannelExtras().get("openid"));
        params.put("sub_appid", reqDTO.getChannelExtras().get("appid"));
        params.put("addn_inf", reqDTO.getChannelExtras().get("attch"));
        params.put("goods_des", reqDTO.getSubject());
        params.put("goods_detail", reqDTO.getBody().isBlank() ? "" : reqDTO.getBody());
        params.put("goods_tag", "");
        params.put("limit_pay", "");
        params.put("product_id", "");


        Map<String, String> resposeMap = sendPost(params, config);
        String resultCode = resposeMap.get("result_code");
        String accTradeNo = resposeMap.get("transaction_id");
        String channelOrderNo = resposeMap.get("reserved_fy_order_no");
        if (SUCCESS_CODE.equals(resultCode)) {

            LocalDateTime successTime = Instant.ofEpochMilli(Long.valueOf(resposeMap.get("sdk_timestamp"))).atZone(ZoneId.systemDefault()).toLocalDateTime();
            return PayOrderRespDTO.successOf(channelOrderNo, successTime, orderNo, orderNo, accTradeNo, resposeMap);
        } else {
            String errorMsg = resposeMap.get("result_msg");
            return PayOrderRespDTO.errorOf(resultCode, errorMsg, orderNo, channelOrderNo, orderNo, resposeMap);
        }
    }

    @Override
    protected PayOrderRespDTO doGetOrder(PayOrderGetReqDTO reqDTO) throws Throwable {
        return super.doGetOrder(reqDTO.getOutTradeNo(), BUSINESS_CODE + reqDTO.getAccountType());
    }

    @Override
    protected PayRefundRespDTO doUnifiedRefund(PayRefundUnifiedReqDTO reqDTO) throws Exception {

        config.setServerUrl(URL_MAP.get("refund"));
        Map<String, String> params = MapUtil.newHashMap();
        params.putAll(baseParams);

        params.put("order_type", FuiouUtils.getOrderTypeByOrderNo(reqDTO.getOutTradeNo()));
        params.put("refund_order_no", reqDTO.getOutTradeNo());
        String orderNo = FuiouUtils.generatorOrderNo(BUSINESS_CODE, config.getOrderPrefix());
        params.put("mchnt_order_no", orderNo);
        params.put("refund_amt", reqDTO.getRefundPrice().toString());
        params.put("total_amt", reqDTO.getPayPrice().toString());

        String url = FuiouBusinessEnum.getByCode(REFUND_BUSINESS_CODE).getUrl();
        Map<String, String> resposeMap = sendPost(params, config);

        String returnCode = resposeMap.get("result_code");
        if (SUCCESS_CODE.equals(returnCode)) {
            String accRefundNo = resposeMap.get("refund_id");
            String channelRefundNo = resposeMap.get("reserved_fy_trace_no");
            return PayRefundRespDTO.successOf(channelRefundNo, parseDate(resposeMap.get("reserved_fy_settle_dt")), reqDTO.getOutTradeNo(), accRefundNo, resposeMap);
        } else {
            return PayRefundRespDTO.errorOf(resposeMap.get("refund_order_no"), resposeMap.get("result_msg"), reqDTO.getOutTradeNo(), resposeMap);
        }
    }
}
