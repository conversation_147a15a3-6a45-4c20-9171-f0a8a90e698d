package info.qizhi.aflower.framework.pay.core.client.impl.xiaoyou;

import info.qizhi.aflower.framework.common.util.validation.ValidationUtils;
import info.qizhi.aflower.framework.pay.core.client.PayClientConfig;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 小游的 PayClientConfig 实现类
 * 属性主要是一些必要属性
 *
 * <AUTHOR>
 */
@Data
public class XiaoyouPayClientConfig implements PayClientConfig {

    /**
     * 网关地址
     *
     * 1. https://ttest.et0731.com/ 测试环境
     *
     */
    @NotBlank(message = "网关地址不能为空")
    private String serverUrl;

    @NotBlank(message = "三方ID不能为空（相当于渠道）")
    private String orgNo;

    @NotBlank(message = "服务ID不能为空（相当与商户）")
    private String merchantNo;

    @NotBlank(message = "服务密钥不能为空")
    private String privateKey;


    @Override
    public void validate(Validator validator) {
        ValidationUtils.validate(validator, this);
    }

}
