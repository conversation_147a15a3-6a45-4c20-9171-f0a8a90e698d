<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>info.qizhi</groupId>
        <artifactId>aflower-framework</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>aflower-spring-boot-starter-sms</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
            短信发送配置模块,短信发送前签名
    </description>
    <dependencies>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-common</artifactId>
        </dependency>

        <!-- Spring 核心 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-web</artifactId>
        </dependency>
        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <scope>provided</scope> <!-- 设置为 provided，只有工具类需要使用到 -->
        </dependency>

        <!-- api sign client -->
        <dependency>
            <groupId>com.icoolkj</groupId>
            <artifactId>icoolkj-api-wrap-client</artifactId>
        </dependency>
    </dependencies>

</project>