package info.qizhi.aflower.framework.core.client;

import info.qizhi.aflower.framework.core.client.dto.BatchSendReqDTO;
import info.qizhi.aflower.framework.core.client.dto.SendReqDTO;
import jakarta.validation.Valid;

/**
 * 短信客户端，用于对接各短信平台的 SDK，实现短信发送等功能
 *
 * <AUTHOR>
 * @since 2021/1/25 14:14
 */
public interface SmsClient {
    /**
     * 单条短信发送
     * @param sendReqDTO
     * @return
     */
    Boolean send(@Valid SendReqDTO sendReqDTO);

    /**
     * 批量发送短信，最多100个手机号码
     * @param batchSendReqDTO
     * @return
     */
    Boolean batchSend(@Valid BatchSendReqDTO batchSendReqDTO);
}
