package info.qizhi.aflower.framework.config;


import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 短信配置类
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@EnableConfigurationProperties(SmsProperties.class)
public class SmsConfiguration {
    private final SmsProperties properties;
    public SmsConfiguration(SmsProperties properties) {
        this.properties = properties;
    }

    public SmsProperties getProperties() {
        return properties;
    }

}
