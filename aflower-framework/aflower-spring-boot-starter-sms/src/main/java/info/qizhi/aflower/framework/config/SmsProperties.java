package info.qizhi.aflower.framework.config;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * @Author: TY
 * @CreateTime: 2024-07-13
 * @Description: 短信配置
 * @Version: 1.0
 */
@ConfigurationProperties(prefix = "aflower.sms")
@Validated
@Data
public class SmsProperties {

    /**
     * appKey: 短信平台分配的 appKey 用于api sign
     */
    @NotEmpty(message = "appKey不能为空")
    private String appKey;

    /**
     * appSecret: 短信平台分配的 appSecret 用于api sign
     */
    @NotEmpty(message = "appSecret不能为空")
    private String appSecret;

    /**
     * url: 短信平台分配的 url
     */
    @NotEmpty(message = "url不能为空")
    private String url;
}
