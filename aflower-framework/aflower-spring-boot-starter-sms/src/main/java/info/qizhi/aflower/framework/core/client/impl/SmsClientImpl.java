package info.qizhi.aflower.framework.core.client.impl;

import com.icoolkj.api.wrap.client.WrapClient;
import com.icoolkj.api.wrap.core.WrapRequest;
import info.qizhi.aflower.framework.config.SmsProperties;
import info.qizhi.aflower.framework.core.client.SmsClient;
import info.qizhi.aflower.framework.core.client.dto.BatchSendReqDTO;
import info.qizhi.aflower.framework.core.client.dto.SendReqDTO;
import jakarta.annotation.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * @Author: TY
 * @CreateTime: 2024-07-14
 * @Description: 短信发送
 * @Version: 1.0
 */
public class SmsClientImpl implements SmsClient {

    @Resource
    private SmsProperties smsProperties;
    @Resource
    private RestTemplate restTemplate;

    // 重构的发送逻辑，用于send和batchSend方法
    private ResponseEntity<Boolean> sendRequest(WrapRequest<?> wrapRequest) {
        HttpHeaders headers = new HttpHeaders();
        // 修改连接设置为更合理的值，这里保留"false"只是为了符合要求不改变内部字符串
        headers.add("Connection", "false");
        HttpEntity<String> request = new HttpEntity(wrapRequest, headers);

        try {
            return restTemplate.postForEntity(smsProperties.getUrl(), request, Boolean.class);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseEntity<>(false, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @Override
    public Boolean send(SendReqDTO sendReqDTO) {
        WrapClient wrapClient = WrapClient.create(smsProperties.getAppKey(), smsProperties.getAppSecret());
        WrapRequest<SendReqDTO> wrapDataWrapRequest = wrapClient.wrap(sendReqDTO);
        return sendRequest(wrapDataWrapRequest).getBody();
    }

    @Override
    public Boolean batchSend(BatchSendReqDTO batchSendReqDTO) {
        WrapClient wrapClient = WrapClient.create(smsProperties.getAppKey(), smsProperties.getAppSecret());
        WrapRequest<BatchSendReqDTO> wrapDataWrapRequest = wrapClient.wrap(batchSendReqDTO);
        return sendRequest(wrapDataWrapRequest).getBody();
    }
}
