package info.qizhi.aflower.framework.core.client.dto;

import com.icoolkj.api.wrap.core.WrapData;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class BatchSendReqDTO extends WrapData {

    /**
     * 发送模版id
     */
    @NotNull(message = "发送模板id不能为空！")
    private Integer tplId;
    /**
     * 如需指定发送的签名，需要传签名id
     */
    private Integer signId;

    @NotNull(message = "发送参数错误！")
    private List<SendParams> paramList;
}
