package info.qizhi.aflower.framework.idempotent.config;

import info.qizhi.aflower.framework.idempotent.core.aop.IdempotentAspect;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.IdempotentKeyResolver;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.DefaultIdempotentKeyResolver;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import info.qizhi.aflower.framework.idempotent.core.keyresolver.impl.UserTokenempotentKeyResolver;
import info.qizhi.aflower.framework.idempotent.core.redis.IdempotentRedisDAO;
import info.qizhi.aflower.framework.redis.config.AflowerRedisAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;

@AutoConfiguration(after = AflowerRedisAutoConfiguration.class)
public class AflowerIdempotentConfiguration {

    @Bean
    public IdempotentAspect idempotentAspect(List<IdempotentKeyResolver> keyResolvers, IdempotentRedisDAO idempotentRedisDAO) {
        return new IdempotentAspect(keyResolvers, idempotentRedisDAO);
    }

    @Bean
    public IdempotentRedisDAO idempotentRedisDAO(StringRedisTemplate stringRedisTemplate) {
        return new IdempotentRedisDAO(stringRedisTemplate);
    }

    // ========== 各种 IdempotentKeyResolver Bean ==========

    @Bean
    public DefaultIdempotentKeyResolver defaultIdempotentKeyResolver() {
        return new DefaultIdempotentKeyResolver();
    }

    @Bean
    public UserIdempotentKeyResolver userIdempotentKeyResolver() {
        return new UserIdempotentKeyResolver();
    }

    @Bean
    public ExpressionIdempotentKeyResolver expressionIdempotentKeyResolver() {
        return new ExpressionIdempotentKeyResolver();
    }

    @Bean
    public UserTokenempotentKeyResolver userTokenempotentKeyResolver() {
        return new UserTokenempotentKeyResolver();
    }

}
