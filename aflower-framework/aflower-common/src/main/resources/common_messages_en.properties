channel.mini_app=Mini App
channel.ctrip=Ctrip
channel.jd=JD
channel.elong=Elong
channel.lobby=Store
channel.meituan=Meituan
channel.fliggy=Fliggy
channel.tiktok=TikTok
channel.ly=Tongcheng
channel.hiii=Hi
channel.qunar=qunar
channel.zx=ZX
channel.xiaohongshu=XiaoHoongShu

# AccountStatusEnum
accountStatus.open=Open
accountStatus.closed=Closed
accountStatus.red=Redeemed
accountStatus.transfer_out=Transferred
accountStatus.cancel=Canceled

# AccountTagEnum
accountTag.split=Split Account
accountTag.red=Redemption
accountTag.transfer_out=Transfer Out
accountTag.transfer_in=Transfer In
accountTag.refund=Refund

# AccountTypeEnum
accountType.general=General Account
accountType.group=Group Main Account
accountType.book=Pre-booked Account
accountType.cash=Cash Account

# ArrearStateEnum
arrearState.normal=Normal
arrearState.urgent=Urgent
arrearState.arrear=Arrear


# ArSetHandleTypeEnum
arSetHandleType.collection=Collection
arSetHandleType.verify=Verify
arSetHandleType.credit=Credit
arSetHandleType.cancel_verify=Cancel Verify
arSetHandleType.red=Red


# BookingOrderActionTypeEnum
bookingOrderActionType.booking_creation=Booking Creation
bookingOrderActionType.booking_modification=Booking Modification
bookingOrderActionType.local_booking_cancellation=Local Booking Cancellation
bookingOrderActionType.booking_noshow=Booking No Show
bookingOrderActionType.check_in=Check In
bookingOrderActionType.room_change=Room Change
bookingOrderActionType.extend_stay=Extend Stay
bookingOrderActionType.check_out=Check Out
bookingOrderActionType.order_info_modification=Order Info Modification
bookingOrderActionType.guest_info_operation=Guest Info Operation

# BooleanEnum
boolean.true=Yes
boolean.false=No

# BusinessDetailsEnum
businessDetails.couponCode=Coupon Code
businessDetails.couponType=Discount Type
businessDetails.discountValue=Discount Value

# CheckInTypeEnum
checkInType.hourRoom=Hourly Room
checkInType.allDay=All Day
checkInType.longStay=Long Stay
checkInType.selfUse=Self Use
checkInType.free=Free
checkInType.travelGroup=Travel Group
checkInType.meetingGroup=Meeting Group

# CommonStatusEnum
commonStatus.customerNormal=Normal
commonStatus.customerBlack=Blacklisted
commonStatus.enable=Enabled
commonStatus.disable=Disabled
commonStatus.leave=Leave
commonStatus.yes=Yes
commonStatus.no=No

# ConsumeAccountEnum
consumeAccount.goods=Goods
consumeAccount.indemnityFee=Indemnity Fee
consumeAccount.bkFee=Breakfast Fee
consumeAccount.memberCard=Member Card
consumeAccount.memberRecharge=Member Recharge
consumeAccount.roomFee=Room Fee
consumeAccount.adjustRoomFee=Adjusted Room Fee
consumeAccount.handInputRoomFee=Manual Room Fee
consumeAccount.roomFeeAdd=Additional Room Fee
consumeAccount.addHalfDay=Additional Half Day
consumeAccount.addAllDay=Additional Full Day
consumeAccount.catering=Catering
consumeAccount.goodsType=Goods Type
consumeAccount.meetingFee=Meeting Fee
consumeAccount.roomFeeDedit=Room Fee Penalty


# ConsumeSceneEnum
consumeScene.room=Room
consumeScene.mall=Mall
consumeScene.catering=Catering
consumeScene.memberUp=Upgrade
consumeScene.member=Member
consumeScene.red=Upgrade Adjustment

# ContinuePriceEnum
continuePrice.last=Last Day
continuePrice.first=First Day
continuePrice.highest=Highest Price Day
continuePrice.lowest=Lowest Price Day
continuePrice.average=Average Price
continuePrice.baseprice=Rack Rate
continuePrice.custom=Custom Price

# CouponActivityTypeEnum
couponActivity.upgrade=Membership Upgrade Coupon
couponActivity.birthday=Birthday Gift Coupon
couponActivity.autoUpgrade=Auto Upgrade Coupon
couponActivity.register=Registration Coupon
couponActivity.checkout=Checkout Coupon
couponActivity.recharge=Recharge Coupon

# CouponEnum
coupon.voucher=Voucher
coupon.discount=Discount Coupon
coupon.free=Free Room Coupon
coupon.breakfast=Breakfast Coupon

# CouponOrderTypeEnum
couponOrderType.room=Room Order
couponOrderType.cash=Cash Order

# CreditAccTypeEnum
creditAccType.cashPay=Credit Account
creditAccType.general=Prepaid Account

# CreditSrcTypeEnum
creditSrcType.general=General Order Credit
creditSrcType.cashPay=Cash Credit
creditSrcType.third=Third-Party Credit

# CreditTargetTypeEnum
creditTargetType.agent=Agent
creditTargetType.protocol=Organization
creditTargetType.replacePay=Substitute Payment
creditTargetType.group=Group/Travel Agency

# DateIntervalEnum
dateInterval.day=Day
dateInterval.week=Week
dateInterval.month=Month
dateInterval.quarter=Quarter
dateInterval.year=Year

# DeviceTypeEnum
deviceType.door_lock=Door Lock
deviceType.printer=Printer
deviceType.switch=Switch
deviceType.router=Router
deviceType.id_card_reader=ID Card Reader
deviceType.member_card_reader=Member Card Reader
deviceType.network_door_lock=Network Door Lock
deviceType.customer_control=Customer Control
deviceType.call_in=Call In
deviceType.smart_tv=Smart TV
deviceType.smart_door_lock=Smart Door Lock

# DictDataEnum translations
dictData.shift.morning=Morning
dictData.shift.middle=Middle Shift
dictData.shift.night=Night Shift
dictData.shift.early=Early Morning

dictData.member.rule=Member Points Rule
dictData.member.update=Member Upgrade
dictData.member.reg=Register Member

dictData.memberconfig.sms_notice=SMS & Public Message Reminder
dictData.memberconfig.register_change_fee=Allow manual change of amount during member registration
dictData.memberconfig.cancel_num=Daily Order Cancellation Limit
dictData.memberconfig.book_max_num=Daily Booking Room Limit
dictData.memberconfig.book_ahead_days=Max Days in Advance for Member Booking
dictData.memberconfig.member_info_search=Member Info Search Control
dictData.memberconfig.points_with_non_owner_card=Points for Non-Owner Stays
dictData.memberconfig.pts_coupons_together=Allow Using Points and Coupons Together

dictData.hotel.level.1star=1 Star
dictData.hotel.level.2star=2 Stars
dictData.hotel.level.3star=3 Stars
dictData.hotel.level.4star=4 Stars
dictData.hotel.level.5star=5 Stars

dictData.discount_type.room_discount=Room Discount
dictData.discount_type.room_reduce=Room Price Reduction
dictData.discount_type.price_handle_round=Round to Integer
dictData.discount_type.price_handle_one_decimal=One Decimal Place
dictData.discount_type.price_handle_two_decimal=Two Decimal Places

dictData.team.tour_group=Tour Group
dictData.team.meeting_group=Meeting Group

dictData.channel.mini_app=Mini App
dictData.channel.lobby=Store

dictData.charge_rule.all_day=All-Day Room
dictData.charge_rule.daytime=Daytime Room
dictData.charge_rule.hour=Hourly Room
dictData.charge_rule.midnight=Midnight Room

dictData.room_fee=Room Fee
dictData.consume_scene=Consumption Scene
dictData.point_scene=Points Scene

# DictTypeEnum translations
dictType.id_type=ID Type
dictType.shift=Shift
dictType.post=Post
dictType.pay_mode=Payment Mode
dictType.protocol_level=Protocol Level
dictType.brokerage_level=Brokerage Level
dictType.agent_level=Agent Level
dictType.room_status=Room Status
dictType.memberconfig=Member Configuration
dictType.memberrule=Member Points Rule
dictType.sms_component=SMS Component
dictType.sms_template=SMS Template
dictType.discount_type=Discount Type
dictType.rights_type=Rights Type
dictType.price_handle=Price Handling
dictType.room_feature=Room Feature
dictType.bed_type=Bed Type
dictType.bed_size=Bed Size
dictType.delay=Late Checkout
dictType.multi_point=Multi-Point
dictType.hotel_level=Hotel Star Level
dictType.hotel_status=Hotel Status
dictType.service=Service Project
dictType.channel=Channel
dictType.hotel_type=Hotel Type
dictType.consume_account=Consumption Account
dictType.consume_account_room_fee=Room Fee Consumption Account
dictType.pay_account=Payment Account
dictType.bank_type=Bank
dictType.up_mode=Upgrade Mode
dictType.member_pathway=Member Pathway
dictType.nation=Nation
dictType.checkin_type=Check-In Type
dictType.guest_src_type=Guest Source Type
dictType.guarantee_type=Guarantee Type
dictType.order_source=Order Source
dictType.device_type=Device Type

# DocumentEnum translations
document.redis_install=Redis Installation Document
document.tenant=SaaS Multi-Tenant Document

# GeneralConfigTypeEnum translations
generalConfig.cancel_reason=Cancel Reason
generalConfig.change_reason=Change Reason
generalConfig.offset_reason=Offset Reason
generalConfig.lock_reason=Lock Reason
generalConfig.repair_reason=Repair Reason
generalConfig.agent_level=Agent Level
generalConfig.protocol_level=Protocol Level
generalConfig.brokerage_level=Brokerage Level
generalConfig.pay_account_type=Payment Account Type
generalConfig.consume_account_type=Consume Account Type
generalConfig.pay_account=Payment Account
generalConfig.consume_account=Consume Account
generalConfig.all_credit_account=All Credit Accounts
generalConfig.record_credit_account=Recordable Credit Accounts
generalConfig.record_cash_credit_account=Recordable Cash Credit Accounts
generalConfig.refund_cash_credit_account=Refundable Cash Credit Accounts
generalConfig.deposit_credit_account=Deposit Credit Accounts
generalConfig.security_credit_account=Secured Credit Accounts
generalConfig.ali_credit_account=Ali Payable Credit Accounts
generalConfig.pay_credit_account=Payable Credit Accounts
generalConfig.member_credit_account=Member Related Credit Accounts
generalConfig.up_credit_account=Member Upgrade Related Credit Accounts
generalConfig.cancel_ar_credit_account=AR Cancellable Credit Accounts
generalConfig.cancel_ar_refund_credit_account=AR Refundable Credit Accounts
generalConfig.all_debit_account=All Debit Accounts
generalConfig.adjust_debit_account=Adjustable Debit Accounts
generalConfig.cash_debit_account=Cash Debit Accounts
generalConfig.hand_debit_account=Manual Debit Accounts
generalConfig.service=Service Items
generalConfig.biz_date=Business Date
generalConfig.latest_check_out_time=Latest Checkout Time
generalConfig.how_minute=Minimum Minutes for One Hour
generalConfig.anonymous=Anonymous
generalConfig.default_plan_checkin_time=Pre offset default time
generalConfig.warehouse_config=Warehouse configuration
generalConfig.pending_payment_time=Waiting time for payment

# GenerateFeeTypeEnum translations
generateFeeType.add_all_day=Full Day Surcharge
generateFeeType.add_half_day=Half Day Surcharge
generateFeeType.room_fee=System Auto Billing
generateFeeType.no_add=No Room Charge
generateFeeType.hand_input_room_fee=Manual Room Fee

# GuaranteeTypeEnum translations
guaranteeType.no=None
guaranteeType.first=First Night Guarantee
guaranteeType.full=Full Guarantee
guaranteeType.ota=OTA Guarantee
guaranteeType.point=Points Guarantee
guaranteeType.guarantee=Guarantee

# GuestSrcTypeEnum translations
guestSrcType.all=All
guestSrcType.walk_in=Walk-in Guest
guestSrcType.member=Member
guestSrcType.agent=Agent
guestSrcType.protocol=Protocol Unit

# HotelMessageTypeEnum translations
hotelMessageType.connect_success=Connection Successful
hotelMessageType.room_order=Room Status
hotelMessageType.order=Order
hotelMessageType.refresh=Refresh Room Status Board
hotelMessageType.notification=Notification
hotelMessageType.wakeup=WakeUp
hotelMessageType.hourOrderAlert=Hour Order Alert
hotelMessageType.nightAuditOver=Night Audit Over
hotelMessageType.nightAuditBegin=Night Audit Begin
hotelMessageType.otaOrder=OTA Order Alert
hotelMessageType.otaOrder.cancel=OTA Order Cancel Alert
hotelMessageType.otaOrder.fail=OTA Order Fail Alert

# HotelStatusEnum translations
hotelStatus.prep=In Preparation
hotelStatus.in_construction=Under Construction
hotelStatus.trial_open=Trial Opening
hotelStatus.open=Open
hotelStatus.temp_close=Temporarily Closed
hotelStatus.stop=Permanently Closed

# HourRoomEnum translations
hourRoom.zero=0 hours
hourRoom.one=1 hour
hourRoom.two=2 hours
hourRoom.three=3 hours
hourRoom.four=4 hours
hourRoom.five=5 hours
hourRoom.six=6 hours
hourRoom.seven=7 hours
hourRoom.eight=8 hours
hourRoom.nine=9 hours
hourRoom.ten=10 hours
hourRoom.eleven=11 hours
hourRoom.twelve=12 hours

# IdTypeEnum translations
idType.id_cert=Resident ID Card
idType.hongkong=Hong Kong and Macau Pass
idType.officer_cert=Military Officer Certificate
idType.soldier_cert=Soldier Certificate
idType.police_cert=Police Certificate
idType.household_register=Household Register
idType.driving_licence=Driving License
idType.passport=Passport
idType.other=Other

# JobNameEnum translations
jobName.enterRepairState=Repair Task Start
jobName.exitRepairState=Repair Task End
jobName.bookNoShow=Set No Show for Expired Booking
jobName.forceNightAudi=Force Night Audit
jobName.hourOrderAlert=Hour Order Alert
jobName.wakeUp=Wake Up

# ManageTypeEnum translations
manageType.join=Join
manageType.deposit=Managed
manageType.direct=Direct Operation

# OrderSrcEnum translations
orderSrc.lobby=Store
orderSrc.book_center=Booking Center
orderSrc.web=Website
orderSrc.app=Mobile App
orderSrc.agent=Agent Direct
orderSrc.other=Other

# OrderStateEnum translations
orderState.no_check_in=Booking
orderState.check_in=In House
orderState.check_out=Checked Out
orderState.cancel=Cancelled
orderState.noshow=No Show
orderState.be_confirm=Pending Confirmation
orderState.refuse=Refused
orderState.over=Completed
orderState.credit=On Credit

# ParamConfigTypeEnum translations
paramConfig.cash_flow=Cash Flow Mode
paramConfig.paid_in=Paid In Mode
paramConfig.receivable=Receivable Mode
paramConfig.shift_mode=Shift Mode
paramConfig.breakfast_ticket=Breakfast Ticket
paramConfig.subject=Cashier Subject
paramConfig.roomcolor=Room Status Color
paramConfig.finance=Finance
paramConfig.memberconfig=Member Configuration
paramConfig.memberrule=Member Points Rule Configuration
paramConfig.nightnum=Night Number Settings
paramConfig.order=Order Settings
paramConfig.front=Front Desk
paramConfig.cash_modify=Cash & Compensation & Product Modification Permission
paramConfig.deposit=Deposit Configuration
paramConfig.room_panel=Room Status Panel Customization

# PayAccountClassEnum translations
payAccountClass.cash_total=Cash Total
payAccountClass.bank_card_total=Bank Card Total
payAccountClass.stored_value_card_total=Stored Value Card Total
payAccountClass.check_total=Check Total
payAccountClass.ar_total=AR Total
payAccountClass.free_total=Free Total
payAccountClass.coupon_total=Coupon Total
payAccountClass.qr_code_total=QR Code Total
payAccountClass.bill_total=Bill Total
payAccountClass.hkd_total=Hong Kong Dollar Total
payAccountClass.discount_total=Discount Total
payAccountClass.cloud_flash_total=Cloud Flash Total
payAccountClass.points_exchange_total=Points Exchange Total
payAccountClass.ctrip_total=Ctrip Total
payAccountClass.meituan_total=Meituan Total
payAccountClass.ly_total=Lvmama Total
payAccountClass.alibaba_total=Alibaba Total
payAccountClass.tiktok_total=Tiktok Total
payAccountClass.commission_total=Commission Total
payAccountClass.other_receipt_total=Other Receipt Total

# PayAccountEnum translations
payAccount.bank_pre_auth=Bank Pre-authorization
payAccount.bank_card=Bank Card
payAccount.bank_card_refund=Bank Card Refund (Financial Receipt)
payAccount.bank_transfer=Bank Transfer
payAccount.bank_transfer_refund=Bank Transfer Refund
payAccount.scan_gun_pre_auth=QR Code Payment - Pre-authorization
payAccount.scan_gun_wx=QR Code Payment - WeChat
payAccount.scan_gun_wx_refund=QR Code Payment - WeChat Refund
payAccount.scan_gun_alipay=QR Code Payment - Alipay
payAccount.scan_gun_alipay_refund=QR Code Payment - Alipay Refund
payAccount.scan_gun=QR Code Payment - Collection
payAccount.coupon=Coupon
payAccount.discount_coupon=Discount Coupon
payAccount.voucher=Voucher
payAccount.pre_ticket=Pre-sale Ticket
payAccount.free_voucher=Free Room Voucher
payAccount.coupon_refund=Coupon Refund
payAccount.pre_ticket_refund=Pre-sale Ticket Refund
payAccount.free_voucher_refund=Free Room Voucher Refund
payAccount.discount_coupon_refund=Discount Coupon Refund
payAccount.credit_s_account=AR Account
payAccount.ar_refund=AR Refund
payAccount.draft=Draft
payAccount.draft_refund=Draft Refund
payAccount.hk_dollar=Hong Kong Dollar Collection
payAccount.hk_dollar_refund=Hong Kong Dollar Refund
payAccount.discount_refund=Discount Refund
payAccount.discount=Discount
payAccount.cloud_quick_pay=Cloud Quick Pay
payAccount.cloud_quick_pay_refund=Cloud Quick Pay Refund
payAccount.point_change=Points Redemption
payAccount.point_change_refund=Points Redemption Refund
payAccount.fuyou_receipt=Fuyou Payment - Collection
payAccount.fuyou_refund=Fuyou Payment - Refund
payAccount.alipay=Alipay
payAccount.alipay_refund=Alipay Refund
payAccount.wx=WeChat Payment
payAccount.wx_refund=WeChat Refund
payAccount.store_card=Stored Value Card
payAccount.store_card_refund=Stored Value Card Refund
payAccount.cheque=Cheque
payAccount.cheque_refund=Cheque Refund
payAccount.rmb_deposit=RMB Deposit
payAccount.rmb_receipt=RMB Cash Collection
payAccount.cash_refund=RMB Cash Refund
payAccount.merge_account=Combined Billing
payAccount.alitrap=Ali Credit Stay
payAccount.free=Free
payAccount.room_account=Room Account
payAccount.cash=Cash

# PointSceneEnum translations
pointScene.increase=Manual Point Increase/Decrease
pointScene.change_room=Room Change with Points
pointScene.consume=Earn Points from Consumption
pointScene.member_up=Points Earned on Upgrade
pointScene.point_exchange=Point Exchange
pointScene.reset=Points Reset
pointScene.cash=Points to Cash
pointScene.recharge=Points Earned from Recharge
pointScene.mall_exchange=Mall Exchange Points Adjustment

# RechargeChannelEnum translations
rechargeChannel.lobby=Store
rechargeChannel.mini_app=Mini App

# RoomPriceTypeEnum translations
roomPriceType.normal=Listed Price
roomPriceType.manual=Manual Price

# RoomStatusEnum translations
roomStatus.VC=VC
roomStatus.VD=VD
roomStatus.OC=OC
roomStatus.OD=OD
roomStatus.OO=OO
roomStatus.LR=LR

# SexEnum translations
sex.male=Male
sex.female=Female
sex.unknown=Unknown

# ShiftTypeEnum translations
shiftType.morning=Morning Shift
shiftType.middle=Middle Shift
shiftType.night=Night Shift
shiftType.early=Early Shift

# SmsConfigParamEnum translations
smsConfigParam.gname=Group Name
smsConfigParam.hname=Hotel Name
smsConfigParam.address=Hotel Address
smsConfigParam.frontPhone=Front Desk Phone
smsConfigParam.teamName=Team Name
smsConfigParam.code=Verification Code
smsConfigParam.rechargeFee=Recharge Amount
smsConfigParam.consumeFee=Consume Amount
smsConfigParam.giftAmount=Gift Amount
smsConfigParam.checkinTime=Check-in Time
smsConfigParam.checkoutTime=Check-out Time
smsConfigParam.retainTime=Retain Time
smsConfigParam.rtName=Room Type
smsConfigParam.rtNumber=Room Count
smsConfigParam.rtSize=Room Size
smsConfigParam.rNo=Room Number
smsConfigParam.roomFee=Room Fee
smsConfigParam.totalFee=Total Consumption
smsConfigParam.phone=Phone Number
smsConfigParam.receiver=Receiver Phone Number
smsConfigParam.orderNo=Order Number
smsConfigParam.preOrderNo=Pre-order Number
smsConfigParam.remark=Remark
smsConfigParam.pwd=Door Lock Password
smsConfigParam.cardNo=Member Card Number
smsConfigParam.birthday=Member Birthday
smsConfigParam.operateTime=Occurrence Time
smsConfigParam.mname=Member Name
smsConfigParam.couponName=Coupon Name
smsConfigParam.consumePoint=Consume Points
smsConfigParam.mtCode=Member Level
smsConfigParam.point=Available Points
smsConfigParam.addPoint=Add Points
smsConfigParam.balance=Stored Balance
smsConfigParam.expPoint=Expired Points
smsConfigParam.bkNum=Breakfast Ticket Count
smsConfigParam.memberPwd=Initial Member Password
smsConfigParam.expTime=Expiration Time
smsConfigParam.couponCode=Coupon Code
smsConfigParam.link=Electronic Document

# StatTypeEnum translations
statType.order_source=Order Source
statType.room_type=Room Type
statType.channel=Channel
statType.guest_src=Guest Source
statType.check_in_type=Check-in Type

# TicketGainModeEnum translations
ticketGainMode.buy=Purchase
ticketGainMode.room=Room with Breakfast
ticketGainMode.free=Free Gift

# VerifyEnum translations
verifyEnum.verify=Verification
verifyEnum.undoVerify=Undo Verification

# VerifyStatusEnum translations
verifyStatusEnum.noVerify=Not Verified
verifyStatusEnum.verify=Verified

# WeekEnum translations
week.monday=Monday
week.tuesday=Tuesday
week.wednesday=Wednesday
week.thursday=Thursday
week.friday=Friday
week.saturday=Saturday
week.sunday=Sunday

# CurrencyUnitEnum
CurrencyUnitEnum.CNY=Chinese Yuan
CurrencyUnitEnum.USD=US Dollar
CurrencyUnitEnum.VND=Vietnamese Dong

# CountryEnum
countryEnum.cn=China
countryEnum.us=United States
countryEnum.vn=Vietnam
