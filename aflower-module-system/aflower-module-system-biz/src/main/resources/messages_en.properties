# messages_zh_EN.properties
gcode.notempty=Group code cannot be empty
gname.notempty=Group name cannot be empty
rtCode.notempty=Room type code cannot be empty
rtName.notempty=Room type cannot be empty
rNo.notempty=Room number cannot be empty
rtType.notrmpty=Application range - Room type; 0: Store room type, 1: Group room type cannot be empty
orderNo.notblank=Order number cannot be blank
hcode.notempty=Store code cannot be empty
name.notempty=Name cannot be empty
ofname.notempty=Owner name cannot be empty
idCard.notempty=ID card number cannot be empty
idType.notempty=ID card type cannot be empty
userType.notempty=Visitor type cannot be empty
failTime.notnull=Expiration time cannot be null
telephone.notempty=Phone number cannot be empty
mobile.notempty=Mobile number cannot be empty
scene.notempty=The send scene cannot be empty
templateName.notempty=Template name cannot be empty
templateWebsite.notempty=Template URL cannot be empty
layoutType.notempty=Layout type; 0: Horizontal, 1: Vertical cannot be empty

checkInTime.notnull=Check-in time cannot be null
checkOutTime.notnull=Check-out time cannot be null
price.notnull=Room price cannot be null
feeSub.notblank=Room fee subject cannot be blank
nightNum.notnull=Number of nights cannot be null
fee.notnull=Room fee cannot be null
roomFee.notnull=Total room fee cannot be null
todayNightNum.notnull=Today's nights cannot be null
curMonthNightNum.notnull=Current month's nights cannot be null
curYearNightNum.notnull=Current year's nights cannot be null
sort.notnull=Sort order cannot be null
phone.size=Phone number cannot exceed 11 characters
email.size=Email length cannot exceed 50 characters
status.notnull=Status cannot be null
status.inenum=The status must be 0 or 1
isMerchant.notnull=Merchant identifier cannot be null
isMerchant.instringenum=Merchant identifier must be 0 or 1
name.notblank=Position name cannot be empty
name.size=Position name cannot exceed 50 characters
code.notblank=Position code cannot be empty
code.size=Position code cannot exceed 64 characters
applicationName.notnull=Application name cannot be null
code.notnull=Error code cannot be null
message.notnull=Error message cannot be null
brandName.notempty=Brand name cannot be empty
brandName.size=Brand name cannot exceed 30 characters
intro.size=Introduction cannot exceed 1024 characters
isEnable.instringenum=Status; 0: Disabled, 1: Enabled
brandCode.notempty=Code cannot be empty
isEnable.notempty=Status; 0: Disabled, 1: Enabled cannot be empty
mail.notnull=Email cannot be null
mail.email=Must be in Email format
username.notnull=Username cannot be null
password.notnull=Password is required
host.notnull=SMTP server domain cannot be null
port.notnull=SMTP server port cannot be null
sslEnable.notnull=SSL enablement is required
name.notnull=Name cannot be null
templateCode.notnull=Template code cannot be null
accountId.notnull=Email account ID cannot be null
title.notempty=Title cannot be empty
content.notempty=Content cannot be empty
title.notblank=Announcement title cannot be blank
title.size=Announcement title cannot exceed 50 characters
type.notnull=Announcement type cannot be null
templateType.notnull=Template type cannot be null
nickname.notempty=Sender name cannot be empty
userId.notnull=User ID cannot be null
userType.notnull=User type cannot be null
id.notnull=ID cannot be null
num.notnull=Number of copies cannot be null
num.min=Number of copies must be at least 1
layout.notblank=Print format (A4, A4-1/2, A4-1/3, POS(76mm)) cannot be blank
remark.size=Remark length cannot exceed 255 characters
nickname.size=Nickname length cannot exceed 30 characters
username.size=Username length must be between 4 and 30 characters
password.notempty=Password cannot be empty
password.length=Password length must be between 4 and 16 characters
oldPassword.notempty=Old password cannot be empty
newPassword.notempty=New password cannot be empty



# ========== AUTH \u6A21\u5757 ==========
AUTH_LOGIN_BAD_CREDENTIALS=Invalid credentials
AUTH_LOGIN_USER_DISABLED=Login failed, the account is disabled
AUTH_LOGIN_CAPTCHA_CODE_ERROR=Captcha code is incorrect, reason: {0}
AUTH_THIRD_LOGIN_NOT_BIND=Account not bound, please bind it
AUTH_TOKEN_EXPIRED=Token has expired
AUTH_MOBILE_NOT_EXISTS=Mobile number does not exist
AUTH_LOGIN_GCODE_NOT_EXISTS=Group not found
AUTH_LOGIN_USER_INVALIDATION=login user invalidation
SERVICE_EXPIRED=Your service has expired on {0}, please renew it as soon as possible!

# ========== \u83DC\u5355\u6A21\u5757 ==========
MENU_NAME_DUPLICATE=Menu name already exists
MENU_PARENT_NOT_EXISTS=Parent menu does not exist
MENU_PARENT_ERROR=Cannot set yourself as the parent menu
MENU_NOT_EXISTS=Menu does not exist
MENU_EXISTS_CHILDREN=Menu has child menus, cannot be deleted
MENU_PARENT_NOT_DIR_OR_MENU=Parent menu must be of type directory or menu

# ========== \u89D2\u8272\u6A21\u5757 ==========
ROLE_NOT_EXISTS=Role does not exist
ROLE_NAME_DUPLICATE=Role name \u3010{0}\u3011 already exists
ROLE_CODE_DUPLICATE=Role code \u3010{0}\u3011 already exists
ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE=System built-in roles cannot be modified
ROLE_IS_DISABLE=Role \u3010{0}\u3011 is disabled
ROLE_ADMIN_CODE_ERROR=Role code \u3010{0}\u3011 is invalid

# ========== \u7528\u6237\u6A21\u5757 ==========
USER_USERNAME_EXISTS=Username already exists
USER_MOBILE_EXISTS=Mobile number already exists
USER_EMAIL_EXISTS=Email already exists
USER_NOT_EXISTS=User does not exist
USER_IMPORT_LIST_IS_EMPTY=Import data cannot be empty
USER_PASSWORD_FAILED=Password validation failed
USER_IS_DISABLE=User \u3010{0}\u3011 is disabled
USER_COUNT_MAX=Cannot create user, reason: exceeded tenant quota ({0})
USER_DEFAULT_MERCHANT_NOT_IN_HCODES=Default store \u3010{0}\u3011 is not in the list of accessible stores for the current user
USER_BELONG_MERCHANT_NOT_IN_HCODES=Belonged store \u3010{0}\u3011 is not in the list of accessible stores for the current user
USER_NOT_LOGIN=User must log in to access the available stores
USER_MOBILE_NOT_EMPTY=Mobile number cannot be empty

# ========== \u90E8\u95E8\u6A21\u5757 ==========
DEPT_NAME_DUPLICATE=Department name already exists
DEPT_PARENT_NOT_EXITS=Parent department does not exist
DEPT_NOT_FOUND=Department not found
DEPT_EXITS_CHILDREN=Department has child departments, cannot be deleted
DEPT_PARENT_ERROR=Cannot set yourself as the parent department
DEPT_EXISTS_USER=Department has users, cannot be deleted
DEPT_NOT_ENABLE=Department \u3010{0}\u3011 is not enabled, cannot be selected
DEPT_PARENT_IS_CHILD=Cannot set a child department as the parent department
USER_DEPT_NOT_EXISTS=User department association does not exist
DEPT_EXITS_MERCHANT=Department has stores, cannot be deleted
DEPT_ROOT_CAN_NOT_DELETE=Root department cannot be deleted
# ========== \u5C97\u4F4D\u6A21\u5757 ==========
POST_NOT_FOUND=Position does not exist
POST_NOT_ENABLE=Position ({0}) is not enabled, cannot be selected
POST_NAME_DUPLICATE=Position name already exists
POST_CODE_DUPLICATE=Position code already exists

# ========== \u5B57\u5178\u7C7B\u578B\u6A21\u5757 ==========
DICT_TYPE_NOT_EXISTS=Dictionary type does not exist
DICT_TYPE_NOT_ENABLE=Dictionary type is not enabled, cannot be selected
DICT_TYPE_NAME_DUPLICATE=Dictionary type name already exists
DICT_TYPE_TYPE_DUPLICATE=Dictionary type already exists with this type
DICT_TYPE_HAS_CHILDREN=This dictionary type has child data, cannot be deleted

# ========== \u5B57\u5178\u6570\u636E\u6A21\u5757 ==========
DICT_DATA_NOT_EXISTS=Dictionary data does not exist
DICT_DATA_NOT_ENABLE=Dictionary data \u3010{0}\u3011 is not enabled, cannot be selected
DICT_DATA_VALUE_DUPLICATE=Dictionary data value already exists

# ========== \u901A\u77E5\u516C\u544A\u6A21\u5757 ==========
NOTICE_NOT_FOUND=Notice not found

# ========== \u77ED\u4FE1\u6E20\u9053\u6A21\u5757 ==========
SMS_CHANNEL_NOT_EXISTS=SMS channel does not exist
SMS_CHANNEL_DISABLE=SMS channel is not enabled, cannot be selected
SMS_CHANNEL_HAS_CHILDREN=This SMS channel has SMS templates, cannot be deleted

# ========== \u77ED\u4FE1\u6A21\u677F\u6A21\u5757 ==========
SMS_TEMPLATE_NOT_EXISTS=SMS template does not exist
SMS_TEMPLATE_CODE_DUPLICATE=SMS template code \u3010{0}\u3011 already exists
SMS_TEMPLATE_API_ERROR=SMS API template call failed, reason: {0}
SMS_TEMPLATE_API_AUDIT_CHECKING=SMS API template cannot be used, reason: under review
SMS_TEMPLATE_API_AUDIT_FAIL=SMS API template cannot be used, reason: review failed, {0}
SMS_TEMPLATE_API_NOT_FOUND=SMS API template cannot be used, reason: template not found

# ========== \u77ED\u4FE1\u53D1\u9001\u6A21\u5757 ==========
SMS_SEND_MOBILE_NOT_EXISTS=Mobile number does not exist
SMS_SEND_MOBILE_TEMPLATE_PARAM_MISS=Template parameter ({0}) missing
SMS_SEND_TEMPLATE_NOT_EXISTS=SMS template does not exist
SMS_SIGN_NO_HAVE=SMS Sign Not Find

# ========== \u77ED\u4FE1\u9A8C\u8BC1\u7801\u6A21\u5757 ==========
SMS_CODE_NOT_FOUND=Verification code does not exist
SMS_CODE_EXPIRED=Verification code has expired
SMS_CODE_USED=Verification code has been used
SMS_CODE_NOT_CORRECT=Verification code is incorrect
SMS_CODE_EXCEED_SEND_MAXIMUM_QUANTITY_PER_DAY=Exceeded daily SMS send limit
SMS_CODE_SEND_TOO_FAST=SMS sending is too frequent
SMS_CODE_IS_EXISTS=Mobile number is already in use
SMS_CODE_IS_UNUSED=Verification code is unused
SMS_CODE_SEND_ERROR=SMS sending error:\u3010{0}\u3011

# ========== \u79DF\u6237\u6A21\u5757 ==========
TENANT_NOT_EXISTS=Tenant does not exist
TENANT_DISABLE=Tenant \u3010{0}\u3011 is disabled
TENANT_EXPIRE=Tenant \u3010{0}\u3011 has expired
TENANT_CAN_NOT_UPDATE_SYSTEM=System tenant cannot be modified or deleted
TENANT_NAME_DUPLICATE=Tenant \u3010{0}\u3011 already exists
TENANT_WEBSITE_DUPLICATE=Tenant with domain \u3010{0}\u3011 already exists

# ========== \u79DF\u6237\u5957\u9910\u6A21\u5757 ==========
TENANT_PACKAGE_NOT_EXISTS=Tenant package does not exist
TENANT_PACKAGE_USED=Tenant is using this package, please reassign a package before deleting
TENANT_PACKAGE_DISABLE=Tenant package \u3010{0}\u3011 is disabled

# ========== \u9519\u8BEF\u7801\u6A21\u5757 ==========
ERROR_CODE_NOT_EXISTS=Error code does not exist
ERROR_CODE_DUPLICATE=Error code \u3010{0}\u3011 already exists

# ========== \u793E\u4EA4\u7528\u6237\u6A21\u5757 ==========
SOCIAL_USER_AUTH_FAILURE=Social authorization failed, reason: {0}
SOCIAL_USER_NOT_FOUND=Social authorization failed, corresponding user not found

SOCIAL_CLIENT_WEIXIN_MINI_APP_PHONE_CODE_ERROR=Failed to obtain mobile number
SOCIAL_CLIENT_NOT_EXISTS=Social client does not exist
SOCIAL_CLIENT_UNIQUE=Social client configuration already exists

# ========== \u7CFB\u7EDF\u654F\u611F\u8BCD\u6A21\u5757 ==========
SENSITIVE_WORD_NOT_EXISTS=Sensitive word does not exist in any tag
SENSITIVE_WORD_EXISTS=Sensitive word already exists in tag

# ========== OAuth2 \u5BA2\u6237\u7AEF\u6A21\u5757 ==========
OAUTH2_CLIENT_NOT_EXISTS=OAuth2 client does not exist
OAUTH2_CLIENT_EXISTS=OAuth2 client ID already exists
OAUTH2_CLIENT_DISABLE=OAuth2 client is disabled
OAUTH2_CLIENT_AUTHORIZED_GRANT_TYPE_NOT_EXISTS=Authorized grant type not supported
OAUTH2_CLIENT_SCOPE_OVER=Authorized scope is too broad
OAUTH2_CLIENT_REDIRECT_URI_NOT_MATCH=Invalid redirect_uri: {0}
OAUTH2_CLIENT_CLIENT_SECRET_ERROR=Invalid client_secret: {0}

# ========== OAuth2 \u6388\u6743\u6A21\u5757 ==========
OAUTH2_GRANT_CLIENT_ID_MISMATCH=client_id mismatch
OAUTH2_GRANT_REDIRECT_URI_MISMATCH=redirect_uri mismatch
OAUTH2_GRANT_STATE_MISMATCH=state mismatch
OAUTH2_GRANT_CODE_NOT_EXISTS=code does not exist

# ========== OAuth2 \u6388\u6743\u7801\u6A21\u5757 ==========
OAUTH2_CODE_NOT_EXISTS=Authorization code does not exist
OAUTH2_CODE_EXPIRE=Authorization code has expired

# ========== \u90AE\u7BB1\u8D26\u53F7\u6A21\u5757 ==========
MAIL_ACCOUNT_NOT_EXISTS=Email account does not exist
MAIL_ACCOUNT_RELATE_TEMPLATE_EXISTS=Cannot delete, email account has related templates

# ========== \u90AE\u4EF6\u6A21\u7248\u6A21\u5757 ==========
MAIL_TEMPLATE_NOT_EXISTS=Email template does not exist
MAIL_TEMPLATE_CODE_EXISTS=Email template code \u3010{0}\u3011 already exists

# ========== \u90AE\u4EF6\u53D1\u9001\u6A21\u5757 ==========
MAIL_SEND_TEMPLATE_PARAM_MISS=Template parameter ({0}) missing
MAIL_SEND_MAIL_NOT_EXISTS=Email address does not exist

# ========== \u7AD9\u5185\u4FE1\u6A21\u7248\u6A21\u5757 ==========
NOTIFY_TEMPLATE_NOT_EXISTS=In-app message template does not exist
NOTIFY_TEMPLATE_CODE_DUPLICATE=In-app message template code \u3010{0}\u3011 already exists

# ========== \u7AD9\u5185\u4FE1\u53D1\u9001\u6A21\u5757 ==========
NOTIFY_SEND_TEMPLATE_NOT_EXISTS=In-app message template does not exist

# ========== \u7AD9\u5185\u4FE1\u53D1\u9001\u6A21\u5757 (\u624B\u673A\u53F7) ==========
NOTIFY_SEND_MOBILE_NOT_EXISTS=Mobile number does not exist

# ========== \u7AD9\u5185\u4FE1\u53D1\u9001\u6A21\u5757 (\u6A21\u677F\u53C2\u6570) ==========
NOTIFY_SEND_TEMPLATE_PARAM_MISS=Template parameter ({0}) missing

# ========== \u96C6\u56E2\u6A21\u5757 ==========
GROUP_NOT_EXISTS=Group does not exist

# ========== \u96C6\u56E2\u54C1\u724C\u6A21\u5757 ==========
GROUP_BRAND_NOT_EXISTS=Group brand does not exist
GROUP_BRAND_REFERENCED=This group brand is referenced and cannot be deleted

# ========== \u90E8\u95E8\u7EC4\u7EC7\u67B6\u6784\u6A21\u5757 ==========
GROUP_DEPT_NOT_EXISTS=Group department structure does not exist
GROUP_DEPT_EXITS_CHILDREN=There are child departments, cannot be deleted
GROUP_DEPT_PARENT_NOT_EXITS=Parent group department structure does not exist
GROUP_DEPT_PARENT_ERROR=Cannot set yourself as the parent group department structure
GROUP_DEPT_DEPT_NAME_DUPLICATE=Department name already exists in group structure
GROUP_DEPT_PARENT_IS_CHILD=Cannot set your child group department as the parent

# ========== \u95E8\u5E97\u6A21\u5757 ==========
MERCHANT_NOT_EXISTS=Store does not exist
MERCHANT_NAME_REPEAT=Store name already exists
MERCHANT_NAME_REPEAT_ERROR=Failed to initialize the store, reason: {0}


# ========== \u95E8\u5E97\u6269\u5C55\u4FE1\u606F\u6A21\u5757 ==========
EXT_NOT_EXISTS=Store extension information does not exist

# ========== \u95E8\u5E97\u8D44\u8D28\u8BA4\u8BC1\u6A21\u5757 ==========
AUTH_NOT_EXISTS=Store qualification certification does not exist

# ========== \u56FE\u7247\u5B58\u50A8\u6A21\u5757 ==========
PIC_NOT_EXISTS=Picture storage does not exist

# ========== \u4EA7\u54C1\u6A21\u5757 ==========
PRODUCT_NOT_EXISTS=Product does not exist

# ========== \u6253\u5370\u683C\u5F0F\u6A21\u5757 ==========
PRINT_LAYOUT_NOT_EXISTS=Print layout does not exist

# ============= Coze\u6A21\u5757 ==========
COZE_OAUTH_ERROR=Coze authorization failed, reason: {0}

#============ APIKey ===============
API_ALREADY_EXISTS=APIKey already exists

