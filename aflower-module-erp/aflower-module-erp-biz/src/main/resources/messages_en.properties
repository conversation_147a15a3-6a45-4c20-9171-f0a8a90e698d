
pmsAccountSale=Pms Account Sale
customerDefault=Default Customer
stockDefaultName=Hotel Warehouse
supplierDefault=Default Seller

# ========== ERP ä¾åºåï¼1-030-100-000ï¼ ==========
SUPPLIER_NOT_EXISTS=Supplier does not exist
SUPPLIER_NOT_ENABLE=Supplier ({0}) is not enabled

# ========== ERP éè´­è®¢åï¼1-030-101-000ï¼ ==========
PURCHASE_ORDER_NOT_EXISTS=Purchase order does not exist
PURCHASE_ORDER_DELETE_FAIL_APPROVE=Purchase order ({0}) has been approved, cannot be deleted
PURCHASE_ORDER_PROCESS_FAIL=Unreview failure, only approved purchase orders can be unreviewed
PURCHASE_ORDER_APPROVE_FAIL=Approval failure, only unapproved purchase orders can be approved
PURCHASE_ORDER_NO_EXISTS=Failed to generate purchase order number, please resubmit
PURCHASE_ORDER_UPDATE_FAIL_APPROVE=Purchase order ({0}) has been approved, cannot be modified
PURCHASE_ORDER_NOT_APPROVE=Purchase order is not approved, cannot be operated
PURCHASE_ORDER_ITEM_IN_FAIL_PRODUCT_EXCEED=Purchase order item ({0}) exceeds the maximum allowed inbound quantity ({1})
PURCHASE_ORDER_PROCESS_FAIL_EXISTS_IN=Unreview failure, corresponding purchase inbound order already exists
PURCHASE_ORDER_ITEM_RETURN_FAIL_IN_EXCEED=Purchase order item ({0}) exceeds the maximum allowed return quantity ({1})
PURCHASE_ORDER_PROCESS_FAIL_EXISTS_RETURN=Unreview failure, corresponding purchase return order already exists

# ========== ERP éè´­å¥åºï¼1-030-102-000ï¼ ==========
PURCHASE_IN_NOT_EXISTS=Purchase inbound order does not exist
PURCHASE_IN_DELETE_FAIL_APPROVE=Purchase inbound order ({0}) has been approved, cannot be deleted
PURCHASE_IN_PROCESS_FAIL=Unreview failure, only approved inbound orders can be unreviewed
PURCHASE_IN_APPROVE_FAIL=Approval failure, only unapproved inbound orders can be approved
PURCHASE_IN_NO_EXISTS=Failed to generate inbound order, please resubmit
PURCHASE_IN_UPDATE_FAIL_APPROVE=Purchase inbound order ({0}) has been approved, cannot be modified
PURCHASE_IN_NOT_APPROVE=Purchase inbound order is not approved, cannot be operated
PURCHASE_IN_FAIL_PAYMENT_PRICE_EXCEED=Payment amount ({0}) exceeds total amount of purchase inbound order ({1})
PURCHASE_IN_PROCESS_FAIL_EXISTS_PAYMENT=Unreview failure, corresponding payment order already exists

# ========== ERP éè´­éè´§ï¼1-030-103-000ï¼ ==========
PURCHASE_RETURN_NOT_EXISTS=Purchase return order does not exist
PURCHASE_RETURN_DELETE_FAIL_APPROVE=Purchase return order ({0}) has been approved, cannot be deleted
PURCHASE_RETURN_PROCESS_FAIL=Unreview failure, only approved return orders can be unreviewed
PURCHASE_RETURN_APPROVE_FAIL=Approval failure, only unapproved return orders can be approved
PURCHASE_RETURN_NO_EXISTS=Failed to generate return order, please resubmit
PURCHASE_RETURN_UPDATE_FAIL_APPROVE=Purchase return order ({0}) has been approved, cannot be modified
PURCHASE_RETURN_NOT_APPROVE=Purchase return order is not approved, cannot be operated
PURCHASE_RETURN_FAIL_REFUND_PRICE_EXCEED=Refund amount ({0}) exceeds total amount of purchase return order ({1})
PURCHASE_RETURN_PROCESS_FAIL_EXISTS_REFUND=Unreview failure, corresponding refund order already exists

# ========== ERP å®¢æ·ï¼1-030-200-000ï¼==========
CUSTOMER_NOT_EXISTS=Customer does not exist
CUSTOMER_NOT_ENABLE=Customer ({0}) is not enabled

# ========== ERP éå®è®¢åï¼1-030-201-000ï¼ ==========
SALE_ORDER_NOT_EXISTS=Sales order does not exist
SALE_ORDER_DELETE_FAIL_APPROVE=Sales order ({0}) has been approved, cannot be deleted
SALE_ORDER_PROCESS_FAIL=Unreview failure, only approved sales orders can be unreviewed
SALE_ORDER_APPROVE_FAIL=Approval failure, only unapproved sales orders can be approved
SALE_ORDER_NO_EXISTS=Failed to generate sales order number, please resubmit
SALE_ORDER_UPDATE_FAIL_APPROVE=Sales order ({0}) has been approved, cannot be modified
SALE_ORDER_NOT_APPROVE=Sales order is not approved, cannot be operated
SALE_ORDER_ITEM_OUT_FAIL_PRODUCT_EXCEED=Sales order item ({0}) exceeds the maximum allowed outbound quantity ({1})
SALE_ORDER_PROCESS_FAIL_EXISTS_OUT=Unreview failure, corresponding sales outbound order already exists
SALE_ORDER_ITEM_RETURN_FAIL_OUT_EXCEED=Sales order item ({0}) exceeds the maximum allowed return quantity ({1})
SALE_ORDER_PROCESS_FAIL_EXISTS_RETURN=Unreview failure, corresponding sales return order already exists

# ========== ERP éå®åºåºï¼1-030-202-000ï¼ ==========
SALE_OUT_NOT_EXISTS=Sales outbound order does not exist
SALE_OUT_DELETE_FAIL_APPROVE=Sales outbound order ({0}) has been approved, cannot be deleted
SALE_OUT_PROCESS_FAIL=Unreview failure, only approved outbound orders can be unreviewed
SALE_OUT_APPROVE_FAIL=Approval failure, only unapproved outbound orders can be approved
SALE_OUT_NO_EXISTS=Failed to generate outbound order, please resubmit
SALE_OUT_UPDATE_FAIL_APPROVE=Sales outbound order ({0}) has been approved, cannot be modified
SALE_OUT_NOT_APPROVE=Sales outbound order is not approved, cannot be operated
SALE_OUT_FAIL_RECEIPT_PRICE_EXCEED=Receipt amount ({0}) exceeds total amount of sales outbound order ({1})
SALE_OUT_PROCESS_FAIL_EXISTS_RECEIPT=Unreview failure, corresponding receipt order already exists

# ========== ERP éå®éè´§ï¼1-030-203-000ï¼ ==========
SALE_RETURN_NOT_EXISTS=Sales return order does not exist
SALE_RETURN_DELETE_FAIL_APPROVE=Sales return order ({0}) has been approved, cannot be deleted
SALE_RETURN_PROCESS_FAIL=Unreview failure, only approved return orders can be unreviewed
SALE_RETURN_APPROVE_FAIL=Approval failure, only unapproved return orders can be approved
SALE_RETURN_NO_EXISTS=Failed to generate return order, please resubmit
SALE_RETURN_UPDATE_FAIL_APPROVE=Sales return order ({0}) has been approved, cannot be modified
SALE_RETURN_NOT_APPROVE=Sales return order is not approved, cannot be operated
SALE_RETURN_FAIL_REFUND_PRICE_EXCEED=Refund amount ({0}) exceeds total amount of sales return order ({1})
SALE_RETURN_PROCESS_FAIL_EXISTS_REFUND=Unreview failure, corresponding refund order already exists

# ========== ERP ä»åº 1-030-400-000 ==========
WAREHOUSE_NOT_EXISTS=Warehouse does not exist
WAREHOUSE_IS_GROUP=The warehouse is a group warehouse and cannot be update/deleted
WAREHOUSE_NOT_ENABLE=Warehouse ({0}) is not enabled
WAREHOUSE_DELETE_HAVE_STOCK=Warehouse can't delete, it have stock

# ========== ERP å¶å®å¥åºå 1-030-401-000 ==========
STOCK_IN_NOT_EXISTS=Other inbound order does not exist
STOCK_IN_DELETE_FAIL_APPROVE=Other inbound order ({0}) has been approved, cannot be deleted
STOCK_IN_PROCESS_FAIL=Unreview failure, only approved inbound orders can be unreviewed
STOCK_IN_APPROVE_FAIL=Approval failure, only unapproved inbound orders can be approved
STOCK_IN_NO_EXISTS=Failed to generate inbound order, please resubmit
STOCK_IN_UPDATE_FAIL_APPROVE=Other inbound order ({0}) has been approved, cannot be modified

# ========== ERP å¶å®åºåºå 1-030-402-000 ==========
STOCK_OUT_NOT_EXISTS=Other outbound order does not exist
STOCK_OUT_DELETE_FAIL_APPROVE=Other outbound order ({0}) has been approved, cannot be deleted
STOCK_OUT_PROCESS_FAIL=Unreview failure, only approved outbound orders can be unreviewed
STOCK_OUT_APPROVE_FAIL=Approval failure, only unapproved outbound orders can be approved
STOCK_OUT_APPROVE_FAIL_BREWING = Reversal failed, accounting: ã({0})ã has been reversed
STOCK_OUT_BREWING_STATUS_ERROR=The adjustment failed, and the status modification failed
STOCK_OUT_NO_EXISTS=Failed to generate outbound order, please resubmit
STOCK_OUT_UPDATE_FAIL_APPROVE=Other outbound order ({0}) has been approved, cannot be modified
STOCK_OUT_ACCOUNT_NO_CANT_EMPTY = There is an error in the bill number
STOCK_OUT_BREWING_CANT_UPDATE=Preparations cannot be updated

# ========== ERP åºå­è°æ¨å 1-030-403-000 ==========
STOCK_MOVE_NOT_EXISTS=Inventory transfer order does not exist
STOCK_MOVE_DELETE_FAIL_APPROVE=Inventory transfer order ({0}) has been approved, cannot be deleted
STOCK_MOVE_PROCESS_FAIL=Unreview failure, only approved transfer orders can be unreviewed
STOCK_MOVE_APPROVE_FAIL=Approval failure, only unapproved transfer orders can be approved
STOCK_MOVE_NO_EXISTS=Failed to generate transfer number, please resubmit
STOCK_MOVE_UPDATE_FAIL_APPROVE=Inventory transfer order ({0}) has been approved, cannot be modified

# ========== ERP åºå­çç¹å 1-030-403-000 ==========
STOCK_CHECK_NOT_EXISTS=Inventory check order does not exist
STOCK_CHECK_DELETE_FAIL_APPROVE=Inventory check order ({0}) has been approved, cannot be deleted
STOCK_CHECK_PROCESS_FAIL=Unreview failure, only approved check orders can be unreviewed
STOCK_CHECK_APPROVE_FAIL=Approval failure, only unapproved check orders can be approved
STOCK_CHECK_NO_EXISTS=Failed to generate check number, please resubmit
STOCK_CHECK_UPDATE_FAIL_APPROVE=Inventory check order ({0}) has been approved, cannot be modified

# ========== ERP äº§ååºå­ 1-030-404-000 ==========
STOCK_COUNT_NEGATIVE=Operation failed, the inventory of product ({0}) in warehouse ({1}): {2}, is less than the change quantity: {3}
STOCK_COUNT_NEGATIVE2=Operation failed, insufficient inventory of product ({0}) in warehouse ({1})

# ========== ERP äº§å 1-030-500-000 ==========
PRODUCT_NOT_EXISTS=Product does not exist
PRODUCT_NOT_ENABLE=Product ({0}) is not enabled
PRODUCT_PAY_NOT_STATUS=When the product is sold, the status cannot be empty

# ========== ERP äº§ååç±» 1-030-501-000 ==========
PRODUCT_CATEGORY_NOT_EXISTS=Product category does not exist
PRODUCT_CATEGORY_EXITS_CHILDREN=There are child product categories, cannot delete
PRODUCT_CATEGORY_PARENT_NOT_EXITS=Parent product category does not exist
PRODUCT_CATEGORY_PARENT_ERROR=Cannot set itself as the parent product category
PRODUCT_CATEGORY_NAME_DUPLICATE=A product category with this name already exists
PRODUCT_CATEGORY_PARENT_IS_CHILD=Cannot set its own child category as the parent category
PRODUCT_CATEGORY_EXITS_PRODUCT=Products using this category exist, cannot delete

# ========== ERP äº§ååä½ 1-030-502-000 ==========
PRODUCT_UNIT_NOT_EXISTS=Product unit does not exist
PRODUCT_UNIT_NAME_DUPLICATE=A product unit with this name already exists
PRODUCT_UNIT_EXITS_PRODUCT=Products using this unit exist, cannot delete

# ========== ERP ç»ç®è´¦æ· 1-030-600-000 ==========
ACCOUNT_NOT_EXISTS=Settlement account does not exist
ACCOUNT_NOT_ENABLE=Settlement account ({0}) is not enabled

FINANCE_PAYMENT_NOT_EXISTS=Payment order does not exist
FINANCE_PAYMENT_DELETE_FAIL_APPROVE=Payment order ({0}) has been approved, cannot be deleted
FINANCE_PAYMENT_PROCESS_FAIL=Unreview failure, only approved payment orders can be unreviewed
FINANCE_PAYMENT_APPROVE_FAIL=Approval failure, only unapproved payment orders can be approved
FINANCE_PAYMENT_NO_EXISTS=Failed to generate payment order number, please resubmit
FINANCE_PAYMENT_UPDATE_FAIL_APPROVE=Payment order ({0}) has been approved, cannot be modified

FINANCE_RECEIPT_NOT_EXISTS=Receipt order does not exist
FINANCE_RECEIPT_DELETE_FAIL_APPROVE=Receipt order ({0}) has been approved, cannot be deleted
FINANCE_RECEIPT_PROCESS_FAIL=Unreview failure, only approved receipt orders can be unreviewed
FINANCE_RECEIPT_APPROVE_FAIL=Approval failure, only unapproved receipt orders can be approved
FINANCE_RECEIPT_NO_EXISTS=Failed to generate receipt order number, please resubmit
FINANCE_RECEIPT_UPDATE_FAIL_APPROVE=Receipt order ({0}) has been approved, cannot be modified
