# messages_zh_CN.properties
gcode.notempty=éå¢ä»£ç ä¸è½ä¸ºç©º
gname.notempty=éå¢åç§°ä¸è½ä¸ºç©º
hcode.notempty=é¨åºä»£ç ä¸è½ä¸ºç©º
rtCode.notempty=æ¿é´ä»£ç ä¸è½ä¸ºç©º
rtName.notempty=æ¿åä¸è½ä¸ºç©º
rNo.notempty=æ¿å·ä¸è½ä¸ºç©º
channelCode.notempty=æ¸ éä»£ç ä¸è½ä¸ºç©º
orderSource.notempty=è®¢åæ¥æºä¸è½ä¸ºç©º
guestSrcType.notempty=å®¢æºç±»åä¸è½ä¸ºç©º
bookType.notempty=é¢è®¢ç±»åä¸è½ä¸ºç©º
contact.notempty=é¢è®¢äºº(èç³»äºº);å¦ææ¯å¢éé¢è®¢ï¼è¿éä¿å­å¢éçèç³»äººå§åä¸è½ä¸ºç©º

isSendSms.notempty=æ¯å¦åç­ä¿¡;0ï¼å¦ 1ï¼æ¯ä¸è½ä¸ºç©º
batches.notempty=æ¹æ¬¡åè¡¨ä¸è½ä¸ºç©º
batchNo.notempty=æ¹æ¬¡å·ä¸è½ä¸ºç©º
days.notempty=å¤©æ°ä¸è½ä¸ºç©º
planCheckinTime.notnull=é¢ä½æ¶é´ä¸è½ä¸ºç©º
planCheckoutTime.notnull=é¢ç¦»æ¶é´ä¸è½ä¸ºç©º
bookRoomTypes.notnull=é¢è®¢æ¿ååè¡¨ä¸è½ä¸ºç©º
bkNum.notnull=èµ æ©é¤ä»½æ°ä¸è½ä¸ºç©º
dayPrices.notempty=æ¯æ¥ä»·æ ¼ä¸è½ä¸ºç©º
preOccupied.notempty=æ¯å¦é¢å æ¿;0:å¦ 1:ä¸è½ä¸ºç©º
state.notempty=ç¶æä¸è½ä¸ºç©º
priceDate.notnull=æ¥æä¸è½ä¸ºç©º
price.notnull=é¢è®¢ä»·æ ¼ä¸è½ä¸ºç©º
vipPrice.notnull=ä¼æ ä»·ä¸è½ä¸ºç©º

no.notblank=åå·ä¸è½ä¸ºç©º
accType.instringenum=è´¦å¡ç±»åå¿é¡»æ¯ generalãbookãgroupãcash ä¸­çä¸ä¸ª
accType.notblank=è´¦å¡ç±»åä¸è½ä¸ºç©º
fee.notnull=éé¢ä¸è½ä¸ºç©º
fee.min=éé¢å¿é¡»å¤§äºæç­äº 1
subCode.notblank=ä»æ¬¾ç§ç®ä»£ç ä¸è½ä¸ºç©º
payCode.size=ä»æ¬¾ç é¿åº¦ä¸è½è¶è¿ 32 ä¸ªå­ç¬¦
bankCardNo.size=é¶è¡å¡å·é¿åº¦ä¸è½è¶è¿ 20 ä¸ªå­ç¬¦
accDetail.size=ä¸å¡è¯¦æé¿åº¦ä¸è½è¶è¿ 128 ä¸ªå­ç¬¦
guestSrcType.instringenum=å®¢æºç±»åä¸å¨é¢è®¾å¼å

bookType.instringenum=é¢è®¢ç±»åä¸å¨é¢è®¾å¼å
hourCode.instringenum=å°æ¶æ¿ä¸å¨é¢è®¾å¼å
guestName.size=å®¢äººåç§°é¿åº¦ä¸è½è¶è¿ 32
teamName.size=å¢éåç§°é¿åº¦ä¸è½è¶è¿ 32
contractNo.size=ååå·é¿åº¦ä¸è½è¶è¿ 50
contact.size=é¢è®¢äºº(èç³»äºº)é¿åº¦ä¸è½è¶è¿ 32
checkinPerson.size=å¥ä½äººå§åé¿åº¦ä¸è½è¶è¿ 32
isSendSms.instringenum=æ¯å¦åç­ä¿¡å¼ä¸å¨é¢è®¾å¼å
remark.size=è®¢åå¤æ³¨é¿åº¦ä¸è½è¶è¿ 255
outOrderRemark.size=å¤é¨è®¢åå¤æ³¨é¿åº¦ä¸è½è¶è¿ 2555
bkNum.min=èµ æ©é¤ä»½æ°ä¸è½å°äº 0
roomNum.min=é¢è®¢æ¿é´æ°ä¸è½å°äº 1
state.instringenum=ç¶æä¸å¨é¢è®¾å¼å
priceType.instringenum=ä»·æ ¼ç±»åä¸å¨é¢è®¾å¼å
outOrderNo.size=å¤é¨è®¢åå·é¿åº¦ä¸è½è¶è¿ 32
orderSource.instringenum=è®¢åæ¥æºä¸å¨é¢è®¾å¼å



HIII_ERROR=éè¯¯åå ï¼{0}