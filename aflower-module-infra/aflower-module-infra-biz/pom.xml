<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>info.qizhi</groupId>
        <artifactId>aflower-module-infra</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aflower-module-infra-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        infra 模块，主要提供两块能力：
            1. 我们放基础设施的运维与管理，支撑上层的通用与核心业务。 例如说：定时任务的管理、服务器的信息等等
            2. 研发工具，提升研发效率与质量。 例如说：代码生成器、接口文档等等
    </description>

    <dependencies>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>

        <!-- 依赖服务 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-biz-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-seata</artifactId>
        </dependency>
        <!-- Web 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-security</artifactId>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-rpc</artifactId>
        </dependency>

        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- Job 定时任务相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-job-plus</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 消息队列相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-mq</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId> <!-- 实现代码生成 -->
        </dependency>

        <!-- 监控相关 -->
        <dependency>
            <groupId>info.qizhi</groupId>
            <artifactId>aflower-spring-boot-starter-monitor</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-server</artifactId> <!-- 实现 Spring Boot Admin Server 服务端 -->
        </dependency>

        <!-- 三方云服务相关 -->
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId> <!-- 文件客户端：解决 ftp 连接 -->
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId> <!-- 文件客户端：解决 sftp 连接 -->
        </dependency>
        <dependency>
            <groupId>io.minio</groupId>
            <artifactId>minio</artifactId> <!-- 文件客户端：解决阿里云、腾讯云、minio 等 S3 连接 -->
        </dependency>

        <dependency>
            <groupId>org.apache.tika</groupId>
            <artifactId>tika-core</artifactId> <!-- 文件客户端：文件类型的识别 -->
        </dependency>
    </dependencies>
    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
