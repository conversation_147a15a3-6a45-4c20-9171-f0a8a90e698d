package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.http.HttpUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.job.ordersync.parser.JdOrderParser;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.jd.*;
import info.qizhi.aflower.module.order.sync.mq.producer.OTAOrderFailAlertProducer;
import info.qizhi.aflower.module.order.sync.service.ota.OtaOrderSynchronizedService;
import info.qizhi.aflower.module.order.sync.service.ota.RoomTypeRefService;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookCancelOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenRespDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils.buildJdOrderListRequest;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_ROOM_TYPE_REF_NOT_FOUND;

@Service
@Slf4j
public class JdSyncStrategy implements OtaSyncStrategy {
    @Resource
    private RoomTypeRefService roomTypeRefService;
    @Resource
    private BookingApi bookingApi;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private OtaOrderSynchronizedService otaOrderSynchronizedService;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private OTAOrderFailAlertProducer otaOrderFailAlertProducer;

    @Override
    public void syncOrders(ServiceIntegrationRespDTO service,
                           List<OtaApiDO> otaApis,
                           String gcode,
                           String hcode,
                           String lastCheckOutTime) {
        try {
            log.info("开始同步京东订单,酒店代码: {}", hcode);

            // 1. 准备同步上下文
            SyncContext context = prepareSyncContext(gcode, hcode, otaApis, lastCheckOutTime);
            if (!context.isValid()) {
                log.error("无效的同步上下文，酒店: {}", hcode);
                return;
            }

            // 2. 抓取订单
            List<JdOrder> orders = fetchOrders(context.orderListApi());
            orders.forEach(order -> log.info("京东订单号>>>>>>>>>>: {}", order.getErpOrderId()));

            // 2.1 排除已同步的订单
            orders = excludeSyncedOrders(orders, context.syncedOrders());
            if (orders != null) {
                orders.forEach(order -> log.info("待处理的京东订单号------------>: {}", order.getErpOrderId()));
            } else {
                log.warn("经过过滤后，没有需要处理的京东订单");
            }

            if (orders != null) {
                log.info("需要同步的京东订单数量: {}", orders.size());
            }
            if (CollUtil.isEmpty(orders)) {
                log.info("没有京东订单需要同步,酒店代码: {}", hcode);
                return;
            }

            // 3. 处理订单
            processOrdersIndependently(orders, context, service);
            log.info("京东订单同步完成,酒店代码: {}", hcode);
        } catch (Exception e) {
            log.error("京东订单同步失败,酒店代码: {}", hcode, e);
            throw e;
        }
    }

    @Override
    public void processUploadedJson(String gcode, String hcode, String acceptedJson, String cancelJson, List<String> jsonList) {
        if (CollUtil.isEmpty(jsonList)) {
            return;
        }

        List<JdOrderDetail> jdOrderDetails = new ArrayList<>();
        for (String json : jsonList) {
            try {
                JdOrderDetail jdOrderDetail = JsonUtils.parseObject(json, JdOrderDetail.class);
                if (jdOrderDetail != null && jdOrderDetail.getCode() == 200) {
                    jdOrderDetails.add(jdOrderDetail);
                } else {
                    if (jdOrderDetail != null) {
                        log.error("京东订单详情同步失败，响应码：{}，消息：{}", jdOrderDetail.getCode(), jdOrderDetail.getMsg());
                    } else {
                        log.error("京东订单详情同步失败，响应为空");
                    }
                }
            } catch (Exception e) {
                log.error("解析京东订单详情失败", e);
            }
        }

        if (CollUtil.isEmpty(jdOrderDetails)) {
            log.info("没有有效的京东订单详情需要处理");
            return;
        }

        ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi
                .getProtocolAgentSimple(hcode, ProtocolAgentEnum.JD_PREPAY.getName())
                .getData();
        if (protocolAgentSimpleRespDTO == null) {
            log.error("缺少京东协议代理配置，酒店: {}", hcode);
            return;
        }

        try {
            SyncContext context = new SyncContext(
                    gcode,
                    hcode,
                    null,
                    null,
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO()
                            .setGcode(gcode)
                            .setHcode(hcode)
                            .setChannel(ChannelEnum.JD.getCode())),
                    getLastCheckOutTime(gcode),
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.JD.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)),
                    protocolAgentSimpleRespDTO.getPaCode()
            );

            // 排除已同步的订单
            jdOrderDetails = excludeSyncedOrderDetails(jdOrderDetails, context.syncedOrders());

            if (CollUtil.isEmpty(jdOrderDetails)) {
                log.info("过滤后没有需要处理的京东订单");
                return;
            }

            // 处理订单详情
            List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();
            ServiceIntegrationRespDTO service = new ServiceIntegrationRespDTO().setGcode(gcode).setHcode(hcode);
            for (JdOrderDetail detail : jdOrderDetails) {
                try {
                    OtaOrderSynchronizedSaveVO record = processOrder(detail, context, service);
                    if (record != null) {
                        syncRecords.add(record);
                    }
                } catch (Exception e) {
                    log.error("处理京东订单失败，订单ID: {}",
                            detail != null && detail.getData() != null ? detail.getData().getErpOrderId() : "unknown", e);
                }
            }

            // 批量保存同步记录
            if (CollUtil.isNotEmpty(syncRecords)) {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
                log.info("成功处理并保存{}条京东订单同步记录", syncRecords.size());
            }
        } catch (Exception e) {
            log.error("处理上传的京东订单JSON失败", e);
            throw e;
        }
    }

    private List<JdOrderDetail> excludeSyncedOrderDetails(List<JdOrderDetail> orderDetails,
                                                          List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(orderDetails)) {
            return Collections.emptyList();
        }

        return orderDetails.stream()
                .filter(detail -> {
                    if (detail == null || detail.getData() == null) {
                        return false;
                    }

                    String orderId = detail.getData().getErpOrderId();
                    Integer orderStatus = detail.getData().getOrderStatus();

                    // 排除已过离店日期的订单
                    if ("已过离店日期".equals(detail.getData().getOrderStatusDesc())) {
                        return false;
                    }

                    // 只处理已确认、已入住、已取消状态的订单
                    if (!List.of(
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode(),
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CHECKED_IN.getCode(),
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
                    ).contains(orderStatus)) {
                        return false;
                    }

                    // 检查是否已同步
                    boolean existsInSynced = syncedOrders.stream()
                            .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId));

                    // 已确认或已入住订单：未同步过的才处理
                    if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode()) ||
                            orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CHECKED_IN.getCode())) {
                        return !existsInSynced;
                    }

                    // 已取消订单：已同步且状态为已确认的才处理
                    if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode())) {
                        if (!existsInSynced) {
                            return false;
                        }
                        boolean shouldExclude = syncedOrders.stream()
                                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId)
                                        && OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
                                        .equals(synced.getOtaOrderState()));
                        return !shouldExclude;
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    private String getLastCheckOutTime(String gcode) {
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO()
                .setGcode(gcode)
                .setHcode(NumberEnum.ZERO.getNumber())
                .setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())).getData();
        if (generalConfig == null) {
            throw new RuntimeException("[订单同步] 获取最晚离店时间失败");
        }
        String lastCheckOutTime = generalConfig.getValue();
        if (StrUtil.isBlank(lastCheckOutTime) || !lastCheckOutTime.matches("\\d{2}:\\d{2}")) {
            throw new RuntimeException("[订单同步] 最晚离店时间格式错误,集团代码:" + gcode);
        }
        return lastCheckOutTime;
    }

    private List<JdOrder> excludeSyncedOrders(List<JdOrder> orders, List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(orders)) {
            return null;
        }
        if (CollUtil.isEmpty(syncedOrders)) {
            return orders.stream()
                    .filter(order -> List.of(
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode(),
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
                    ).contains(order.getOrderStatus().toString()))
                    .collect(Collectors.toList());
        }

        return orders.stream()
                .filter(order -> {
                    String orderId = order.getErpOrderId();
                    Integer orderStatus = order.getOrderStatus();

                    // 排除已过离店日期的订单
                    if ("已过离店日期".equals(order.getOrderStatusDesc())) {
                        return false;
                    }

                    // 只处理特定状态的订单
                    if (!List.of(
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode(),
                            OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
                    ).contains(orderStatus.toString())) {
                        return false;
                    }

                    // 检查是否已同步
                    boolean existsInSynced = syncedOrders.stream()
                            .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId));

                    // 已确认订单：未同步过的才处理
                    if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode())) {
                        return !existsInSynced;
                    }

                    // 已取消订单：已同步且状态为已确认的才处理
                    if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode())) {
                        if (!existsInSynced) {
                            return false;
                        }
                        boolean shouldExclude = syncedOrders.stream()
                                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId)
                                        && OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
                                        .equals(synced.getOtaOrderState()));
                        return !shouldExclude;
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    public static LocalDateTime getJdPlanCheckOutTime(JdOrderDetail jdOrderDetail, String lastCheckOutTime) {
    if (jdOrderDetail == null || jdOrderDetail.getData() == null || lastCheckOutTime == null) {
        throw new IllegalArgumentException("参数不能为空");
    }

    try {
        Long checkOutDate = jdOrderDetail.getData().getCheckOutDate();

        // 将时间戳转换为 LocalDateTime
        LocalDateTime checkOutDateTime = Instant.ofEpochMilli(checkOutDate)
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();

        // 使用 lastCheckOutTime 构建最终的离店时间
        String dateTimeStr = checkOutDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + lastCheckOutTime;
        return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
    } catch (Exception e) {
        throw new RuntimeException("解析京东离店时间失败: " + e.getMessage(), e);
    }
}


    private SyncContext prepareSyncContext(String gcode, String hcode, List<OtaApiDO> otaApis, String lastCheckOutTime) {
        OtaApiDO orderListApi = OtaApiUtils.findApi(otaApis, OtaMethodTypeEnum.ORDER_LIST, ChannelEnum.JD);
        OtaApiDO orderDetailApi = OtaApiUtils.findApi(otaApis, OtaMethodTypeEnum.ORDER_DETAIL, ChannelEnum.JD);

        if (orderListApi == null || orderDetailApi == null) {
            log.error("缺少京东API配置，酒店: {}", hcode);
            return SyncContext.invalid();
        }

        ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi
                .getProtocolAgentSimple(hcode, ProtocolAgentEnum.JD_PREPAY.getName())
                .getData();
        if (protocolAgentSimpleRespDTO == null) {
            log.error("缺少京东协议代理配置，酒店: {}", hcode);
            return SyncContext.invalid();
        }

        return new SyncContext(
                gcode,
                hcode,
                orderListApi,
                orderDetailApi,
                roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO()
                        .setGcode(gcode)
                        .setHcode(hcode)
                        .setChannel(ChannelEnum.JD.getCode())),
                lastCheckOutTime,
                System.currentTimeMillis(),
                otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                        .setPlatform(ChannelEnum.JD.getCode())
                        .setGcode(gcode)
                        .setHcode(hcode)),
                protocolAgentSimpleRespDTO.getPaCode()
        );
    }

    private List<JdOrder> fetchOrders(OtaApiDO orderListApi) {
        try {
            String response = HttpUtils.post(
                    orderListApi.getUrl(),
                    OtaApiUtils.parseHeaders(orderListApi.getHeaders()),
                    buildJdOrderListRequest(orderListApi.getBody())
            );
            return JdOrderParser.parseOrderList(orderListApi.getHcode(), response);
        } catch (Exception e) {
            log.error("获取京东订单失败", e);
            return Collections.emptyList();
        }
    }

    private void processOrdersIndependently(List<JdOrder> orders, SyncContext context, ServiceIntegrationRespDTO service) {
        List<JdOrderDetail> orderDetails = fetchJdOrderDetailsConcurrently(
                context.orderDetailApi(),
                orders
        );
        log.info("京东需要同步的订单详情数:{}", orderDetails.size());

        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();

        for (JdOrderDetail detail : orderDetails) {
            try {
                OtaOrderSynchronizedSaveVO record = processOrderInTransaction(detail, context, service);
                if (record != null) {
                    syncRecords.add(record);
                }
            } catch (Exception e) {
                log.error("处理京东订单失败，订单ID: {}, 错误: {}",
                        detail != null && detail.getData() != null ? detail.getData().getErpOrderId() : "unknown",
                        e.getMessage());
            }
        }

        if (CollUtil.isNotEmpty(syncRecords)) {
            try {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
            } catch (Exception e) {
                log.error("批量创建同步记录失败", e);
            }
        }
    }

    private OtaOrderSynchronizedSaveVO processOrderInTransaction(JdOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        return processOrder(detail, context, service);
    }

    private OtaOrderSynchronizedSaveVO processOrder(JdOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        if (detail == null || detail.getData() == null || detail.getData().getOrderStatus() == null) {
            log.warn("无效的京东订单详情");
            return null;
        }

        Integer orderStatus = detail.getData().getOrderStatus();
        String orderId = detail.getData().getErpOrderId();

        if (StrUtil.isBlank(orderId)) {
            log.warn("订单号为空，无法处理订单");
            return null;
        }

        log.info("处理订单时的订单状态：{}, 订单号：{}", orderStatus, orderId);

        // 处理已确认订单
        if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode())) {
            return handleNewOrder(detail, context, service, orderStatus, orderId);
        }

        // 处理已取消订单
        if (orderStatus.toString().equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode())) {
            String bookNo = cancelOrder(orderId, context.syncedOrders());
            if (bookNo != null) {
                return buildSyncRecord(context, orderId, bookNo, String.valueOf(orderStatus));
            }
        } else {
            log.warn("未知的订单状态：{}, 订单号：{}", orderStatus, orderId);
        }

        return null;
    }

    private OtaOrderSynchronizedSaveVO handleNewOrder(JdOrderDetail detail,
                                                      SyncContext context,
                                                      ServiceIntegrationRespDTO service,
                                                      Integer orderStatus,
                                                      String orderId) {
        String bookNo = createJdBook(detail, context, service);
        log.info("创建京东订单成功，酒店代码：{} 订单号：{}", service.getHcode(), orderId);
        if (bookNo != null) {
            return buildSyncRecord(context, orderId, bookNo, String.valueOf(orderStatus));
        }
        return null;
    }

    private String createJdBook(JdOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        List<OtaOrderSynchronizedDO> syncedOrders = context.syncedOrders();
        String orderId = detail.getData().getErpOrderId();

        if (syncedOrders != null && syncedOrders.stream()
                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId))) {
            log.info("订单已同步，跳过创建，酒店代码：{} 订单号：{}", service.getHcode(), orderId);
            return null;
        }

        OtaBookOpenReqDTO req = buildJdBookSaveReqVO(detail, service, context, context.lastCheckOutTime());
        CommonResult<OtaBookOpenRespDTO> result = bookingApi.createBook(req);

        if (!CommonResult.isSuccess(result.getCode())) {
            log.error("创建京东预订单失败，酒店代码：{}, 订单号：{}，错误信息：{}",
                    service.getHcode(), orderId, result.getMsg());
            otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                    .setGcode(context.gcode())
                    .setHcode(context.hcode())
                    .setPlatform(ChannelEnum.JD.getCode())
                    .setSyncTime(context.syncTime())
                    .setOtaOrderNo(orderId)
                    .setOtaOrderState("fail")
                    .setPmsBookNo("0")
                    .setRemark(result.getMsg()));

            if (result.getCode() == 1009001027) { // 无房错误码
                OTAOrderFailAlertProducer.OTAOrderFailAlertBO otaOrderFailAlertBO =
                        new OTAOrderFailAlertProducer.OTAOrderFailAlertBO();
                otaOrderFailAlertBO.setHcode(service.getHcode());
                otaOrderFailAlertBO.setChannelName("京东");
                otaOrderFailAlertBO.setOutOrderNo(orderId);
                otaOrderFailAlertBO.setOtaRtName(detail.getData().getRoom().getRatePlanName());
                otaOrderFailAlertBO.setPlanCheckinTime(req.getPlanCheckinTime());
                otaOrderFailAlertBO.setPlanCheckoutTime(req.getPlanCheckoutTime());
                otaOrderFailAlertBO.setGuestName(req.getGuestName());
                otaOrderFailAlertBO.setReason(result.getMsg());
                otaOrderFailAlertProducer.sendOTAOrderFailAlertMessage(otaOrderFailAlertBO);
            }
            return null;
        }

        return result.getData().getPmsResNo();
    }

    private OtaBookOpenReqDTO buildJdBookSaveReqVO(JdOrderDetail jdOrderDetail,
                                                   ServiceIntegrationRespDTO service,
                                                   SyncContext context,
                                                   String lastCheckOutTime) {
        if (jdOrderDetail == null || jdOrderDetail.getData() == null) {
            throw new IllegalArgumentException("京东订单信息不能为空");
        }

        JdOrderDetail.OrderData orderData = jdOrderDetail.getData();
        OtaBookOpenReqDTO bookSaveReqVO = new OtaBookOpenReqDTO()
                .setGcode(service.getGcode())
                .setHcode(service.getHcode())
                .setChannelCode(ChannelEnum.JD.getCode())
                .setOrderSource(OrderSrcEnum.AGENT.getCode())
                .setGuestSrcType(GuestSrcTypeEnum.AGENT.getCode())
                .setBookType(OrderTypeEnum.GENERAL.getCode());

        // 设置计划入住和退房时间 TODO 这里还需要判断是否为钟点房
        LocalDateTime planCheckInTime = getJdPlanCheckInTime(jdOrderDetail);
        LocalDateTime planCheckoutTime = getJdPlanCheckOutTime(jdOrderDetail, lastCheckOutTime);

        bookSaveReqVO.setPlanCheckinTime(planCheckInTime)
                .setPlanCheckoutTime(planCheckoutTime)
                .setCheckinType(CheckInTypeEnum.ALL_DAY.getCode());

        // 设置客人信息
        bookSaveReqVO.setGuestCode(context.paCode())
                .setGuestName(ProtocolAgentEnum.JD_PREPAY.getName())
                .setContact(orderData.getCustomers().getFirst()) // 取第一个住客作为联系人
                .setCheckinPerson(orderData.getCustomers().getFirst())
                .setIsSendSms(BooleanEnum.FALSE.getValue())
                .setOutOrderNo(orderData.getErpOrderId());

        // 设置订单备注
        StringBuilder outOrderRemark = new StringBuilder(String.format("|订单ID[%s]|", orderData.getErpOrderId()));
        JdOrderDetail.NightDetail nightDetail =jdOrderDetail.getData().getNightDetail();
        List<JdOrderDetail.NightDetailItem> nightDetailItems = nightDetail.getNightDetailItems();
        if (nightDetailItems != null && !nightDetailItems.isEmpty()) {
            for (JdOrderDetail.NightDetailItem orderRoomPrice : nightDetailItems) {
                outOrderRemark.append("|").append(orderRoomPrice.getDate()).append("房价").append(orderRoomPrice.getSellPriceBeforeDiscount())
                        .append("结算价").append(orderRoomPrice.getPreRevenue()).append("/")
                        .append(orderRoomPrice.getMealInfo());
            }
        }
        bookSaveReqVO.setOutOrderRemark(outOrderRemark.toString());

        // 处理预订批次信息
        List<OtaBookOpenReqDTO.Batch> batchList = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch batch = new OtaBookOpenReqDTO.Batch();
        String batchNo = DateUtil.format(planCheckInTime, "yyyy-MM-dd") + "/" +
                DateUtil.format(planCheckoutTime, "yyyy-MM-dd");
        batch.setBatchNo(batchNo)
                .setDays(orderData.getRoomNights())
                .setPlanCheckinTime(planCheckInTime)
                .setPlanCheckoutTime(planCheckoutTime);

        // 处理预订房型信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch.BookRoomType bookRoomType = new OtaBookOpenReqDTO.Batch.BookRoomType();
        String roomTypeId = orderData.getRoom().getRatePlanId();
        RoomTypeRefDO roomTypeRefDO = context.roomTypeRefs().stream()
                .filter(roomTypeRef ->
                        roomTypeRef.getOtaProducts() != null &&
                                roomTypeRef.getOtaProducts().stream()
                                        .anyMatch(product ->
                                                product.getProductCode() != null &&
                                                        product.getProductCode().equals(roomTypeId) &&
                                                        ChannelEnum.JD.getCode().equals(roomTypeRef.getChannel())
                                        ))
                .findFirst()
                .orElse(null);

        if (roomTypeRefDO != null) {
            bookRoomType.setRtCode(roomTypeRefDO.getRoomTypeCode());
        } else {
            log.error("京东订单房型代码不存在，酒店代码：{} 房型代码：{} 订单ID：{}",
                    service.getHcode(), roomTypeId, orderData.getErpOrderId());
            throw exception(OTA_ROOM_TYPE_REF_NOT_FOUND);
        }

        bookRoomType.setBkNum(0)
                .setRoomNum(orderData.getRoom().getRoomNum());

        // 处理每日价格信息（京东订单可能没有每日价格明细，使用统一价格）
        List<OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice> dayPrices = CollUtil.newArrayList();
        for (int i = 0; i < orderData.getRoomNights(); i++) {
            LocalDate priceDate = planCheckInTime.toLocalDate().plusDays(i);
            OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice dayPrice =
                    new OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice();
            dayPrice.setPriceDate(priceDate)
                    .setRoomBkNum(0) // 默认无早
                    .setWeek(priceDate.getDayOfWeek().getValue())
                    .setPrice(convertYuanToFen(orderData.getOrderPrice().getSellPriceAfterDiscount()))
                    .setVipPrice(convertYuanToFen(orderData.getOrderPrice().getPreRevenue()));
            dayPrices.add(dayPrice);
        }

        bookRoomType.setDayPrices(dayPrices);
        bookRoomTypes.add(bookRoomType);
        batch.setBookRoomTypes(bookRoomTypes);
        batchList.add(batch);
        bookSaveReqVO.setBatches(batchList);

        return bookSaveReqVO;
    }

private LocalDateTime getJdPlanCheckInTime(JdOrderDetail jdOrderDetail) {
    Long checkInDate = jdOrderDetail.getData().getCheckInDate();

    // 将时间戳转换为 LocalDateTime
    return Instant.ofEpochMilli(checkInDate)
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime()
            .with(LocalTime.of(12, 0)); // 设置时间为 12:00
}


    private OtaOrderSynchronizedSaveVO buildSyncRecord(SyncContext context, String orderId, String bookNo, String orderStatus) {
        return new OtaOrderSynchronizedSaveVO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setPmsBookNo(bookNo)
                .setOtaOrderNo(orderId)
                .setOtaOrderState(orderStatus)
                .setSyncTime(context.syncTime())
                .setPlatform(ChannelEnum.JD.getCode());
    }

    private String cancelOrder(String orderId, List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(syncedOrders)) {
            log.info("[取消订单] 无可取消订单：无同步记录");
            return null;
        }

        List<OtaOrderSynchronizedDO> matchedOrders = syncedOrders.stream()
                .filter(order -> order.getOtaOrderNo().equals(orderId))
                .sorted(Comparator.comparing(OtaOrderSynchronizedDO::getSyncTime).reversed())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(matchedOrders)) {
            log.info("[取消订单] 无可取消订单：未找到匹配的订单记录，orderId={}", orderId);
            return null;
        }

        OtaOrderSynchronizedDO latestOrder = matchedOrders.getFirst();

        if (!Objects.equals(OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode(), latestOrder.getOtaOrderState())) {
            log.debug("[取消订单] 订单不可取消：当前状态={}, orderId={}", latestOrder.getOtaOrderState(), orderId);
            return null;
        }

        try {
            OtaBookCancelOpenReqDTO cancelReq = new OtaBookCancelOpenReqDTO()
                    .setGcode(latestOrder.getGcode())
                    .setHcode(latestOrder.getHcode())
                    .setBookNo(latestOrder.getPmsBookNo());

            CommonResult<?> result = bookingApi.cancleBook(cancelReq);

            if (!result.isSuccess()) {
                log.error("[取消订单] 取消失败 orderId={}, msg={}", orderId, result.getMsg());
                otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                        .setGcode(latestOrder.getGcode())
                        .setHcode(latestOrder.getHcode())
                        .setPlatform(ChannelEnum.JD.getCode())
                        .setSyncTime(System.currentTimeMillis())
                        .setOtaOrderNo(orderId)
                        .setOtaOrderState("fail")
                        .setRemark(result.getMsg())
                        .setPmsBookNo(latestOrder.getPmsBookNo()));
                return null;
            }

            log.info("[取消订单] 取消成功 orderId={}, pmsBookNo={}", orderId, latestOrder.getPmsBookNo());
            return latestOrder.getPmsBookNo();
        } catch (Exception e) {
            log.error("[取消订单] 取消异常 orderId={}", orderId, e);
            return null;
        }
    }

    private List<JdOrderDetail> fetchJdOrderDetailsConcurrently(OtaApiDO orderDetailApi, List<JdOrder> needSyncJdOrderList) {
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(5, needSyncJdOrderList.size()));
        List<Future<JdOrderDetail>> futures = new ArrayList<>();
        List<JdOrderDetail> resultList = Collections.synchronizedList(new ArrayList<>());

        for (JdOrder jdOrder : needSyncJdOrderList) {
            futures.add(executor.submit(() -> getJdOrderDetail(orderDetailApi, jdOrder)));
        }

        for (Future<JdOrderDetail> future : futures) {
            try {
                JdOrderDetail detail = future.get(10, TimeUnit.SECONDS);
                if (detail != null) {
                    resultList.add(detail);
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("获取京东订单详情异常", e);
            }
        }

        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        return resultList;
    }

    private JdOrderDetail getJdOrderDetail(OtaApiDO otaApiDO, JdOrder jdOrder) {
        if (!OtaMethodTypeEnum.ORDER_DETAIL.getCode().equals(otaApiDO.getMethod()) ||
                !ChannelEnum.JD.getCode().equals(otaApiDO.getChannel())) {
            return null;
        }
        try {
            Map<String, String> headers = OtaApiUtils.parseHeaders(otaApiDO.getHeaders());
            JSONObject requestBody = JSONUtil.parseObj(otaApiDO.getBody());
            requestBody.getJSONObject("orderDetailParam").set("erpOrderId", jdOrder.getErpOrderId());

            String response = HttpUtils.post(otaApiDO.getUrl(), headers, requestBody.toString());
            JdOrderDetail orderDetail = JsonUtils.parseObject(response, JdOrderDetail.class);

            if (orderDetail != null && orderDetail.getCode() == 200) {
                return orderDetail;
            } else {
                if (orderDetail != null) {
                    log.error("京东订单详情同步失败，响应码：{}，消息：{}",
                            orderDetail.getCode(),
                            orderDetail.getMsg());
                } else {
                    log.error("京东订单详情同步失败，响应为空");
                }
            }
        } catch (Exception e) {
            log.error("京东订单详情同步异常", e);
            return null;
        }
        return null;
    }

    private long convertYuanToFen(String yuan) {
        BigDecimal yuanValue = new BigDecimal(yuan);
        return yuanValue.multiply(BigDecimal.valueOf(100)).longValue();
    }

    private record SyncContext(
            String gcode,
            String hcode,
            OtaApiDO orderListApi,
            OtaApiDO orderDetailApi,
            List<RoomTypeRefDO> roomTypeRefs,
            String lastCheckOutTime,
            Long syncTime,
            List<OtaOrderSynchronizedDO> syncedOrders,
            String paCode
    ) {
        public static SyncContext invalid() {
            return new SyncContext(null, null, null, null, null, null, null, null, null);
        }

        public boolean isValid() {
            return (orderListApi != null && orderDetailApi != null) || CollUtil.isEmpty(roomTypeRefs);
        }
    }
}