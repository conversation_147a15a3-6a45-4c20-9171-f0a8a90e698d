package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "按钮显示信息")
public class ButtonDisplay {
    @Schema(description = "按钮类型")
    private String buttonType;

    @Schema(description = "按钮显示文本")
    private String buttonDisplay;

    @Schema(description = "按钮显示类型")
    private Integer buttonDisplayType;

    @Schema(description = "按钮是否禁用")
    private Boolean buttonDisabled;

    @Schema(description = "订单动作类型")
    private String OrderActionType;

    @Schema(description = "帮助描述")
    private Object HelpDesc;
}
