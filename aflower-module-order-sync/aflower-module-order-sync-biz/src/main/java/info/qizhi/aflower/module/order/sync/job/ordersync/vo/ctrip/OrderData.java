package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "订单数据对象")
public class OrderData {
    @Schema(description = "商户房间名称")
    private String merchantRoomName;

    @Schema(description = "是否存在订单备注")
    private Boolean existOrderRemark;

    @Schema(description = "订单备注")
    private String orderRemark;

    @Schema(description = "原始到达最早和最晚时间")
    private String oriArrivalEarlyAndLatestTime;

    @Schema(description = "特殊备注多行")
    private String spRemarksMulti;

    @Schema(description = "携程代收费信息")
    private String ctripTakeTotalFeehtml;

    @Schema(description = "房间价格文本")
    private String roomPriceText;

    @Schema(description = "是否大陆订单")
    private Boolean mainLandOrder;

    @Schema(description = "审核状态")
    private Integer auditStatus;

    @Schema(description = "是否已过期")
    private Boolean isExpired;

    @Schema(description = "审核列表")
    private List<Object> auditList;

    @Schema(description = "发送邮件地址")
    private String didEmail;

    @Schema(description = "本地化信息")
    private String locale;

    @Schema(description = "自动确认表单状态")
    private Integer autoconfirmformstatus;

    @Schema(description = "服务评分")
    private String serviceScore;

    @Schema(description = "服务评分扣除文本")
    private String serviceScoreDeductText;

    @Schema(description = "限制关闭日期列表")
    private List<Object> limitColseDates;

    @Schema(description = "当前订单状态")
    private Integer currentOrderStatus;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "产品描述")
    private String productDescription;

    @Schema(description = "产品图片")
    private Object productImages;

    @Schema(description = "是否是房间票订单")
    private Boolean isRoomTicketOrder;

    @Schema(description = "官方支付状态")
    private Object officialPaymentStatus;

    @Schema(description = "官方退款状态文本")
    private Object officialRefundStatusTxt;

    @Schema(description = "包含证书")
    private Object containCertificate;

    @Schema(description = "包含生日")
    private Object containBirthDays;

    @Schema(description = "证书提示")
    private Object certificateTips;

    @Schema(description = "是否是询价订单")
    private Boolean isEnquiryOrder;

    @Schema(description = "客户端电话")
    private String clientPhone;

    @Schema(description = "是否显示禁用")
    private Boolean showDisabled;

    @Schema(description = "费用取消描述")
    private Object feeCancelDesc;

    @Schema(description = "费用是否已取消")
    private Boolean feeCanceled;

    @Schema(description = "主订单ID")
    private Object masterOrderId;

    @Schema(description = "部分酒店数量")
    private Object partHotelCount;

    @Schema(description = "竞争状态")
    private Object competeState;

    @Schema(description = "剩余秒数")
    private Object leftSeconds;

    @Schema(description = "携程备注列表")
    private List<CtripRemark> ctripRemarkList;

    @Schema(description = "发送渠道文本")
    private String sendChannelText;

    @Schema(description = "OToken")
    private String oToken;

    @Schema(description = "翻转日志")
    private Object overturnLogs;

    @Schema(description = "订单旅行成本")
    private String orderTripCost;

    @Schema(description = "额外表单类型")
    private Object extFormType;

    @Schema(description = "交付信息")
    private String deliverInfo;

    @Schema(description = "引用超时修改阶段")
    private Integer refOvertimeModifyStage;

    @Schema(description = "超时修改费用提示")
    private Object overtimeModifyFeeTip;

    @Schema(description = "税前金额")
    private String preTaxAmount;

    @Schema(description = "税前成本")
    private String preTaxCost;

    @Schema(description = "税前价格")
    private String preTaxPrice;

    @Schema(description = "其他税金额")
    private String otherTaxAmount;

    @Schema(description = "订单任务奖励消息")
    private String orderMissionRewardMessage;

    @Schema(description = "COT策略提示")
    private Object cotStrategyTips;

    @Schema(description = "是否自动开房")
    private Boolean autoOpenRoom;

    @Schema(description = "是否翻转订单")
    private Boolean overturnOrder;

    @Schema(description = "是否今日取消订单")
    private Boolean todayCancelOrder;

    @Schema(description = "是否已过期")
    private Boolean expired;

    @Schema(description = "客人手机号")
    private Object guestMobilePhone;

    @Schema(description = "电话")
    private Object telPhone;

    @Schema(description = "是否国内")
    private Object domestic;

    @Schema(description = "是否可以修改预订号")
    private Boolean canModifyBookingNO;

    @Schema(description = "是否VCCTO PCC")
    private Boolean vcctoPCC;

    @Schema(description = "是否存在销售差价")
    private Boolean existSellGapPrice;

    @Schema(description = "是否存在成本差价")
    private Boolean existCostGapPrice;

    @Schema(description = "管理邮箱")
    private String managementEmail;

    @Schema(description = "引用订单备注")
    private Object refOrderRemarks;

    @Schema(description = "是否显示儿童价格")
    private Boolean showChildPirce;

    @Schema(description = "儿童信息")
    private Object childInfos;

    @Schema(description = "儿童总价")
    private Object childTotalPrice;

    @Schema(description = "是否显示邮件发送")
    private Boolean showMailTo;

    @Schema(description = "延迟住宿销售价格")
    private Object delayStaySalePrice;

    @Schema(description = "延迟住宿成本价格")
    private Object delayStayCostPrice;

    @Schema(description = "房间价格")
    private Object sroomPice;

    @Schema(description = "其他费用")
    private Object sotherFee;

    @Schema(description = "费用类型")
    private Object sfeeType;

    @Schema(description = "按钮列表显示")
    private List<ButtonDisplay> buttonListDisplay;

    @Schema(description = "是否可以发送国内短信")
    private Boolean canSendSMSDomestic;

    @Schema(description = "是否预修改订单")
    private Boolean preModifyOrder;

    @Schema(description = "酒店发票")
    private Boolean hotelInvoice;

    @Schema(description = "是否长期")
    private Boolean longTerm;

    @Schema(description = "询价订单")
    private Boolean enquiryOrder;

    @Schema(description = "月租订单")
    private Boolean monthlyRentOrder;

    @Schema(description = "转为线下")
    private Boolean conToOffline;

    @Schema(description = "学生促销订单")
    private Boolean studentPromoteOrder;

    @Schema(description = "多点订单")
    private Boolean multiplePointsOrder;

    @Schema(description = "竞争订单")
    private Boolean competitiveOrder;

    @Schema(description = "精英俱乐部订单")
    private Boolean eliteClubOrder;

    @Schema(description = "是否LRA")
    private Boolean lra;

    @Schema(description = "是否XC SMZ")
    private Boolean xcsmz;

    @Schema(description = "是否FG")
    private Boolean fg;

    @Schema(description = "是否重新发送")
    private Boolean resend;

    @Schema(description = "担保类型")
    private String guaranteeType;

    @Schema(description = "备注")
    private Object remarks;

    @Schema(description = "到达最早和最晚时间")
    private String arrivalEarlyAndLatestTime;

    @Schema(description = "携程来源类型显示")
    private String ctripSourceTypeDisplay;

    @Schema(description = "是否显示阶梯扣除政策")
    private Boolean showLadderDeductPolicy;

    @Schema(description = "取消是否超时")
    private Boolean cancelTimeOut;

    @Schema(description = "房间票订单")
    private Boolean roomTicketOrder;

    @Schema(description = "公司名称")
    private Object companyName;

    @Schema(description = "是否拒绝取消")
    private Boolean rejectedCancel;

    @Schema(description = "确认名称")
    private String confirmName;

    @Schema(description = "发票键")
    private Integer invoiceKey;

    @Schema(description = "是否显示权益信息")
    private Boolean showRigthsInfo;

    @Schema(description = "变更摘要")
    private Object changeSummary;

    @Schema(description = "总费用")
    private String totalFee;

    @Schema(description = "订单房间价格列表")
    private List<OrderRoomPrice> orderRoomPrices;

    @Schema(description = "是否显示权益旅行")
    private Boolean showRightsTrip;

    @Schema(description = "携程备注")
    private String ctripRemarks;

    @Schema(description = "变更")
    private Object changes;

    @Schema(description = "特殊备注")
    private Object spRemarks;

    @Schema(description = "部分订单接受")
    private Boolean partialOrderAccept;

    @Schema(description = "结算周期")
    private String balancePeriod;

    @Schema(description = "额外可选")
    private Object additionalOptional;

    @Schema(description = "支付信息")
    private PaymentInfo paymentInfo;

    @Schema(description = "礼物信息")
    private String giftInfo;

    @Schema(description = "支付条款")
    private String paymentTerm;

    @Schema(description = "订单状态显示")
    private String orderStatusDisplay;

    @Schema(description = "是否可以发送短信")
    private Boolean canSendSMS;

    @Schema(description = "到达和离开时间")
    private String arrivalAndDeparture;

    @Schema(description = "金额描述")
    private String amountDes;

    @Schema(description = "是否显示PCC")
    private Boolean showPCC;

    @Schema(description = "是否显示VCC")
    private Boolean showVCC;

    @Schema(description = "通用备注")
    private Object commonRemark;

    @Schema(description = "确认其他方式类型")
    private String confirmOtherWayType;

    @Schema(description = "确认备注")
    private String confirmRemarks;

    @Schema(description = "预订号")
    private String bookingNO;

    @Schema(description = "确认状态")
    private String confirmStatus;

    @Schema(description = "历史记录")
    private List<History> historys;

    @Schema(description = "历史记录数量")
    private Integer historyCount;

    @Schema(description = "国内支付信息")
    private DomesticPaymentInfo domesticPaymentInfo;

    @Schema(description = "合同号")
    private Object contractNO;

    @Schema(description = "是否启用确认")
    private Boolean enableConfirm;

    @Schema(description = "是否隐藏预订号")
    private Boolean hideBookingNO;

    @Schema(description = "联系信息")
    private ContactInfo contactInfo;

    @Schema(description = "押金")
    private String cashPledge;

    @Schema(description = "是否国内订单")
    private Boolean domesticOrder;

    @Schema(description = "引用订单ID")
    private Object refOrderID;

    @Schema(description = "是否显示酒店名称")
    private Boolean showHotelName;

    @Schema(description = "原始支付信息")
    private Object orgPaymentInfo;

    @Schema(description = "价格模式")
    private Integer priceMode;

    @Schema(description = "发票信息")
    private Invoice invoice;

    @Schema(description = "促销信息")
    private String promotionInfo;

    @Schema(description = "预变更")
    private Object preChanges;

    @Schema(description = "官方印章路径")
    private Object officalSealPath;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "预订描述")
    private Object bookDescription;

    @Schema(description = "担保类型提示")
    private String guaranteeTypeTips;

    @Schema(description = "预订发票")
    private Boolean bookingInvoice;

    @Schema(description = "发票申请类型")
    private Integer invoiceApplyType;

    @Schema(description = "发票Token")
    private String invoiceToken;

    @Schema(description = "取消政策标题")
    private String cancelPolicyTitle;

    @Schema(description = "取消政策文本")
    private Object cancelPolicyText;

    @Schema(description = "采购代码")
    private Object purchaseCodes;

    @Schema(description = "预修改表单状态")
    private Integer preModifyFormStatus;

    @Schema(description = "是否PCCTO VCC")
    private Boolean pcctoVCC;

    @Schema(description = "阶梯扣除政策")
    private Object ladderDeductPolicy;

    @Schema(description = "原始订单ID")
    private Integer originalOrderid;

    @Schema(description = "权益VM信息")
    private List<RightsVMInfo> rightsVMInfo;

    @Schema(description = "权益信息取消政策")
    private Object rightInfoCancelPolicy;

    @Schema(description = "是否满房订单")
    private Boolean fullyBookedOrder;

    @Schema(description = "团购订单类型")
    private Integer groupOrderType;

//    @Schema(description = "限制取消HTML")
//    private String limitCancelHtml;

    @Schema(description = "TH订单ID信息")
    private String thOrderIdInfo;

    @Schema(description = "TH订单ID")
    private String thOrderId;

    @Schema(description = "是否买断订单")
    private Boolean buyoutOrder;

    @Schema(description = "是否酒店商城订单")
    private Boolean hotelMallOrder;

    @Schema(description = "是否权益订单")
    private Boolean rightOrder;

    @Schema(description = "是否联盟会员")
    private Boolean unionMember;

    @Schema(description = "床型")
    private String bedType;

    @Schema(description = "是否限制取消")
    private Boolean limitCancel;

    @Schema(description = "是否套餐订单")
    private Boolean setMealOrder;

    @Schema(description = "是否确认其他方式")
    private Boolean confirmOtherWay;

    @Schema(description = "是否显示订单日期")
    private Boolean showOrderDate;

    @Schema(description = "酒店商城信息列表")
    private Object hotelMallInfoList;

    @Schema(description = "官方订单来源")
    private Object officialOrderSource;

    @Schema(description = "通知类型")
    private Integer notifyType;

    @Schema(description = "是否免费房间订单")
    private Boolean freeRoomOrder;

    @Schema(description = "是否联盟订单")
    private Boolean allicanceOrder;

    @Schema(description = "是否显示报告客人不良行为")
    private Boolean showReportGuestBadBehavior;

    @Schema(description = "是否竞争订单")
    private Boolean competeOrder;

    @Schema(description = "是否OTA CT订单")
    private Boolean otaCtOrder;

    @Schema(description = "是否免费取消政策")
    private Boolean freeCancelPolicy;

    @Schema(description = "是否竞价订单")
    private Boolean bidOrder;

    @Schema(description = "是否满房订单")
    private Boolean fullRoomOrder;

    @Schema(description = "是否即时确认订单")
    private Boolean imComfirmOrder;

    @Schema(description = "是否外部订单")
    private Boolean outsideOrder;

    @Schema(description = "是否儿童价格订单")
    private Boolean childPriceOrder;

    @Schema(description = "酒店商城成本价格")
    private Object hotelMallCostPirce;

    @Schema(description = "酒店商城销售价格")
    private Object hotelMallSalePirce;

    @Schema(description = "儿童数量")
    private Integer childCount;

    @Schema(description = "来源类型")
    private String sourceType;

    @Schema(description = "是否PP")
    private Boolean pp;

    @Schema(description = "是否紧急")
    private Boolean urgent;

    @Schema(description = "Token")
    private String token;

    @Schema(description = "是否CON")
    private Boolean con;

    @Schema(description = "订单ID")
    private String orderID;

    @Schema(description = "金额")
    private String amount;

    @Schema(description = "房间ID")
    private Long roomID;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "显示价格类型")
    private Object showPriceType;

    @Schema(description = "酒店名称")
    private String hotelName;

    @Schema(description = "酒店英文名称")
    private String hotelEName;

    @Schema(description = "数量")
    private Integer quantity;

    @Schema(description = "是否保留房间")
    private Boolean holdRoom;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "表单ID")
    private Long formID;

    @Schema(description = "携程订单来源类型")
    private String ctripOrderSourceType;

    @Schema(description = "客户名称")
    private String clientName;

    @Schema(description = "订单键")
    private Object orderKey;

    @Schema(description = "促销列表")
    private List<Promotion> promotionList;

    @Schema(description = "总成本价格")
    private String totalCostPrice;

    @Schema(description = "客人数量")
    private Integer guests;

    @Schema(description = "到达日期")
    private String arrival;

    @Schema(description = "离开日期")
    private String departure;

    @Schema(description = "房间英文名称")
    private String roomEnName;

    @Schema(description = "表单日期")
    private String formDate;

    @Schema(description = "住宿天数")
    private Integer liveDays;

    @Schema(description = "订单状态类型")
    private String orderStatusType;

    @Schema(description = "是否信用订单")
    private Boolean creditOrder;

    @Schema(description = "是否担保")
    private Boolean guaranteed;

    @Schema(description = "是否自由销售")
    private Boolean freeSale;

    @Schema(description = "是否风险订单")
    private Boolean riskyOrder;

    @Schema(description = "支付类型")
    private String paymentType;

    @Schema(description = "是否显示销售价格")
    private Boolean showSalePrice;

    @Schema(description = "是否显示成本价格")
    private Boolean showCostPrice;

    @Schema(description = "是否自动确认")
    private Object autoConfirmed;

    @Schema(description = "是否显示指导价格")
    private Boolean showGuidedPrice;

    @Schema(description = "联盟名称")
    private String allinanceName;

    @Schema(description = "是否大陆")
    private Boolean mainLand;

    @Schema(description = "总销售价格")
    private String totalSalePrice;

    @Schema(description = "风险类型")
    private Object riskyType;

    @Schema(description = "是否钟点房")
    private Boolean hourRom;

    @Schema(description = "酒店ID")
    private Long hotel;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "货币")
    private String currency;
}
