package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;

import java.util.List;

/**
 * 策略接口
 */
public interface OtaSyncStrategy {
    void syncOrders(ServiceIntegrationRespDTO service,
                    List<OtaApiDO> otaApiList,
                    String gcode,
                    String hcode,
                    String lastCheckOutTime);

    /**
     * 处理上传的json
     * @param gcode
     * @param hcode
     * @param acceptedJson 接受订单的json
     * @param cancelJson 取消订单的json
     * @param jsonList 携程订单详情json列表
     */
    void processUploadedJson(String gcode, String hcode, String acceptedJson, String cancelJson, List<String> jsonList);
}
