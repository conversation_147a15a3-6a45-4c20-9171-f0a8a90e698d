package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "商品信息")
public class GoodsInfo {
    @Schema(description = "服务描述")
    private String serviceDes;

    @Schema(description = "服务名称")
    private String serviceName;

    @Schema(description = "服务价格")
    private String servicePrice;
}
