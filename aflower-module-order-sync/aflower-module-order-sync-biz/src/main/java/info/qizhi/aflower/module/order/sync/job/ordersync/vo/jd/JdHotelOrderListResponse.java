package info.qizhi.aflower.module.order.sync.job.ordersync.vo.jd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "京东酒店订单列表响应")
public class JdHotelOrderListResponse {
    @Schema(description = "消息")
    private String msg;

    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "订单列表数据")
    private OrderListData data;

    @Schema(description = "跟踪ID")
    private String dsmTraceId;

    @Data
    @Schema(description = "订单列表数据")
    public static class OrderListData {
        @Schema(description = "总记录数")
        private Integer total;

        @Schema(description = "总页数")
        private Integer pages;

        @Schema(description = "每页大小")
        private Integer pageSize;

        @Schema(description = "当前页码")
        private Integer pageNum;

        @Schema(description = "订单列表")
        private List<OrderListItem> list;
    }

    @Data
    @Schema(description = "订单列表项")
    public static class OrderListItem {
        @Schema(description = "支付时间(yyyy-MM-dd HH:mm:ss)")
        private String payTime;

        @Schema(description = "预订结束时间(yyyy-MM-dd HH:mm:ss)")
        private String bookEndTime;

        @Schema(description = "取消类型 0-未取消 2-已取消")
        private Integer cancelType;

        @Schema(description = "支付方式 1-预付")
        private Integer payMode;

        @Schema(description = "订单状态描述")
        private String orderStatusDesc;

        @Schema(description = "入住时间(yyyy-MM-dd HH:mm:ss)")
        private String checkInTime;

        @Schema(description = "预订开始时间(yyyy-MM-dd HH:mm:ss)")
        private String bookBeginTime;

        @Schema(description = "订单标签列表")
        private List<OrderLabel> orderLabel;

        @Schema(description = "房型名称")
        private String roomTypeName;

        @Schema(description = "订单状态 3-已接单 4-已入住 5-已过离店日期 6-已取消")
        private Integer orderStatus;

        @Schema(description = "供应商ID")
        private Long vendorId;

        @Schema(description = "确认时间(yyyy-MM-dd HH:mm:ss)")
        private String confirmTime;

        @Schema(description = "拒绝时间(yyyy-MM-dd HH:mm:ss)")
        private String rejectTime;

        @Schema(description = "间夜数")
        private Integer nightNum;

        @Schema(description = "房型ID")
        private Long roomTypeId;

        @Schema(description = "住客姓名列表")
        private List<String> hotelGuest;

        @Schema(description = "支付类型编码")
        private Integer payType;

        @Schema(description = "退房时间(yyyy-MM-dd HH:mm:ss)")
        private String checkOutTime;

        @Schema(description = "取消时间(yyyy-MM-dd HH:mm:ss)")
        private String cancelTime;

        @Schema(description = "ERP订单ID")
        private String erpOrderId;

        @Schema(description = "房间数量")
        private Integer roomAmount;

        @Schema(description = "订单ID")
        private String id;

        @Schema(description = "京东酒店ID")
        private Long jdHotelId;
    }

    @Data
    @Schema(description = "订单标签")
    public static class OrderLabel {
        @Schema(description = "排序号")
        private Integer sortNo;

        @Schema(description = "标签类型 success/gray等")
        private String tagType;

        @Schema(description = "文本")
        private String text;

        @Schema(description = "类型 1")
        private Integer type;
    }
}