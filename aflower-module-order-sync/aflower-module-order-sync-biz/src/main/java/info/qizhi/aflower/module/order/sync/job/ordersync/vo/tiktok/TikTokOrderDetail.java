package info.qizhi.aflower.module.order.sync.job.ordersync.vo.tiktok;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "抖音订单详情DTO")
public class TikTokOrderDetail {

    @Schema(description = "预计接受时间(毫秒时间戳)", example = "1746719104000")
    private Long acceptExpectTimeMs;

    @Schema(description = "实际接受时间(毫秒时间戳)", example = "1746715507000")
    private Long acceptTimeMs;

    @Schema(description = "接受类型", example = "0")
    private Integer acceptType;

    @Schema(description = "金额信息")
    private AmountInfo amountInfo;

    @Schema(description = "预订申请时间(毫秒时间戳)", example = "1746715498000")
    private Long bookApplyTimeMs;

    @Schema(description = "预订结束时间(毫秒时间戳)", example = "1746720000000")
    private Long bookEndTimeMs;

    @Schema(description = "预订ID", example = "")
    private String bookId;

    @Schema(description = "预订订单ID", example = "1085087814428825197")
    private String bookOrderId;

    @Schema(description = "预订房间列表")
    private List<BookRoom> bookRoomList;

    @Schema(description = "预订开始时间(毫秒时间戳)", example = "1746633600000")
    private Long bookStartTimeMs;

    @Schema(description = "预订状态", example = "3")
    private Integer bookStatus;

    @Schema(description = "确认号", example = "df29118d672d494d83b86264bdf81cd3")
    private String confirmNumber;

    @Schema(description = "订单ID", example = "1085043243687705197")
    private String orderId;

    @Schema(description = "订单标签列表")
    private List<Integer> orderTagList;

    @Schema(description = "原始夜间数", example = "1")
    private Integer originNightCount;

    @Schema(description = "原始房间数", example = "1")
    private Integer originRoomCount;

    @Schema(description = "支付时间(毫秒时间戳)", example = "1746715504000")
    private Long payTimeMs;

    @Schema(description = "物理房间名称", example = "沉浸式观影大床房")
    private String physicalRoomName;

    @Schema(description = "POI名称", example = "Hiii花祺酒店(那大镇店)")
    private String poiName;

    @Schema(description = "销售产品名称", example = "【五一畅享】投影大床房+视频会员+免费投屏")
    private String saleProductName;

    @Schema(description = "用户列表")
    private List<UserInfo> userList;

    @Data
    @Schema(description = "金额信息")
    public static class AmountInfo {
        @Schema(description = "货币类型", example = "CNY")
        private String currency;

        @Schema(description = "商家优惠金额(分)", example = "0")
        private Long merchantDiscountAmount;

        @Schema(description = "平台优惠金额(分)", example = "0")
        private Long platformDiscountAmount;

        @Schema(description = "实收金额(分)", example = "10800")
        private Long receivedAmount;

        @Schema(description = "销售金额")
        private SaleAmount saleAmount;

        @Schema(description = "总账单金额(分)", example = "10800")
        private Long totalBillAmount;
    }

    @Data
    @Schema(description = "销售金额")
    public static class SaleAmount {
        @Schema(description = "每日金额列表")
        private List<DailyAmount> amountByDay;

        @Schema(description = "总金额(分)", example = "10800")
        private Long totalAmount;
    }

    @Data
    @Schema(description = "每日金额")
    public static class DailyAmount {
        @Schema(description = "金额(分)", example = "10800")
        private Long amount;
    }

    @Data
    @Schema(description = "预订房间信息")
    public static class BookRoom {
        @Schema(description = "日期范围")
        private DateScope dateScope;

        @Schema(description = "夜间数", example = "1")
        private Integer nightCount;

        @Schema(description = "房间数", example = "1")
        private Integer roomCount;
    }

    @Data
    @Schema(description = "日期范围")
    public static class DateScope {
        @Schema(description = "结束日期(yyyy-MM-dd)", example = "2025-05-09")
        private String endDate;

        @Schema(description = "开始日期(yyyy-MM-dd)", example = "2025-05-08")
        private String startDate;
    }

    @Data
    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "证件号码", example = "")
        private String cardNumber;

        @Schema(description = "姓氏", example = "")
        private String firstName;

        @Schema(description = "名字", example = "")
        private String lastName;

        @Schema(description = "姓名", example = "李水英")
        private String name;

        @Schema(description = "手机号", example = "18898956532")
        private String phone;
    }
}
