package info.qizhi.aflower.module.order.sync.dal.mysql.ota;

import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaRoomTypeReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaRoomTypeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OTA房型表
 *
 * <AUTHOR>
 */
@Mapper
public interface OtaRoomTypeMapper extends BaseMapperX<OtaRoomTypeDO> {

    default List<OtaRoomTypeDO> selectList(OtaRoomTypeReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OtaRoomTypeDO>()
                .eq(OtaRoomTypeDO::getGcode, reqVO.getGcode())
                .eq(OtaRoomTypeDO::getHcode, reqVO.getHcode())
                .eqIfPresent(OtaRoomTypeDO::getChannel, reqVO.getChannel()));
    }

}