package info.qizhi.aflower.module.order.sync.dal.mysql.ota;

import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaOrderSynchronizedReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OTA已同步订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OtaOrderSynchronizedMapper extends BaseMapperX<OtaOrderSynchronizedDO> {

    default List<OtaOrderSynchronizedDO> selectList(OtaOrderSynchronizedReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OtaOrderSynchronizedDO>()
                .eq(OtaOrderSynchronizedDO::getGcode, reqVO.getGcode())
                .eq(OtaOrderSynchronizedDO::getHcode, reqVO.getHcode())
                .eqIfPresent(OtaOrderSynchronizedDO::getPlatform, reqVO.getPlatform())
                .eqIfPresent(OtaOrderSynchronizedDO::getSyncTime, reqVO.getSyncTime())
                .eqIfPresent(OtaOrderSynchronizedDO::getOtaOrderNo, reqVO.getOtaOrderNo())
                .eqIfPresent(OtaOrderSynchronizedDO::getOtaOrderState, reqVO.getOtaOrderState())
        );
    }

    @Delete("DELETE a FROM ota_order_synchronized a INNER JOIN ota_order_synchronized b ON a.id = b.id and a.gcode = b.gcode WHERE a.hcode = #{hcode} and a.gcode = #{gcode}")
    void deleteOtaOrderSync(String gcode, String hcode);

}