package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "权益VM信息")
public class RightsVMInfo {
    @Schema(description = "权益ID")
    private String rId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "是否启用")
    private Boolean enabled;

    @Schema(description = "组名称")
    private String gName;

    @Schema(description = "是否可拒绝")
    private Boolean refusable;

    @Schema(description = "组ID")
    private Integer gId;

    @Schema(description = "是否组权益")
    private Boolean isGroupRights;

    @Schema(description = "数量")
    private Integer count;

    @Schema(description = "日期时间信息")
    private String dateTimeInfo;

    @Schema(description = "退款原因")
    private String refundReason;
}
