package info.qizhi.aflower.module.order.sync.service.ota;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiRespVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiSaveReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaRoomTypeSaveReqVO;
import info.qizhi.aflower.module.order.sync.controller.open.vo.AllOtaApiRespVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.mysql.ota.OtaApiMapper;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import info.qizhi.aflower.module.pms.api.serviceintegration.ServiceIntegrationApi;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_API_NOT_EXISTS;

/**
 * OTA_api Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OtaApiServiceImpl implements OtaApiService {

    @Resource
    private OtaApiMapper otaApiMapper;
    @Resource
    private ServiceIntegrationApi serviceIntegrationApi;
    @Resource
    private OtaRoomTypeService otaRoomTypeService;

    @Override
    public Long createOtaApi(OtaApiSaveReqVO createReqVO) {
        // 插入
        OtaApiDO otaApi = BeanUtils.toBean(createReqVO, OtaApiDO.class);
        otaApiMapper.insert(otaApi);
        // 返回
        return otaApi.getId();
    }

    @Override
    public void updateOtaApi(OtaApiSaveReqVO updateReqVO) {
        // 校验存在
        validateOtaApiExists(updateReqVO.getId());
        // 更新
        OtaApiDO updateObj = BeanUtils.toBean(updateReqVO, OtaApiDO.class);
        otaApiMapper.updateById(updateObj);
    }

    @Override
    public void deleteOtaApi(Long id) {
        // 校验存在
        validateOtaApiExists(id);
        // 删除
        otaApiMapper.deleteById(id);
    }

    private void validateOtaApiExists(Long id) {
        if (otaApiMapper.selectById(id) == null) {
            throw exception(OTA_API_NOT_EXISTS);
        }
    }

    @Override
    public OtaApiDO getOtaApi(OtaApiReqVO reqVO) {

        return otaApiMapper.selectOne(reqVO);
    }

    @Override
    public List<OtaApiDO> getOtaApiList(OtaApiReqVO reqVO) {
        return otaApiMapper.selectList(reqVO);
    }

    public List<OtaApiDO> getDetailAndOrderListOtaApi() {
        return otaApiMapper.getDetailAndOrderListOtaApi();
    }


    /**
     * 获取房型相关的OTA API列表
     * <p>
     * 该方法执行以下操作：
     * 1. 获取当前服务的酒店编码集合(hcodes)
     * 2. 查询所有房型相关的OTA API列表
     * 3. 过滤出当前服务酒店可用的OTA API
     * 4. 将DO对象转换为VO对象返回
     *
     * @return List<OtaApiRespVO> 返回当前服务可用的房型OTA API列表，如果没有则返回空列表
     */
    @Override
    public List<OtaApiRespVO> getRoomTypeOtaApiList() {
        // 获取当前服务的酒店编码集合
        Set<String> hcodes = getServiceHcodes();
        if (CollUtil.isEmpty(hcodes)) {
            return Collections.emptyList();
        }

        // 查询所有房型相关的OTA API列表
        List<OtaApiDO> otaApiList = getOtaApiList(new OtaApiReqVO().setMethod(OtaMethodTypeEnum.ROOM_TYPE.getCode()));
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 过滤出当前服务酒店可用的OTA API
        otaApiList = CollectionUtils.filterList(otaApiList, otaApiDO -> hcodes.contains(otaApiDO.getHcode()));
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 转换DO对象为VO对象并返回
        return BeanUtils.toBean(otaApiList, OtaApiRespVO.class);
    }

    /**
     * 获取美团渠道的API列表
     * <p>
     * 1. 首先获取服务对应的酒店编码集合(hcodes)
     * 2. 根据渠道(美团)和方法类型(订单列表)查询OTA API基础数据
     * 3. 过滤出与当前服务相关的API数据(hcode匹配)
     * 4. 对每个API数据进行美团特有的包装处理
     * 5. 将DO对象转换为VO对象返回
     *
     * @return 美团渠道的API响应VO列表，如果没有数据则返回空集合
     */
    @Override
    public List<OtaApiRespVO> getMtApiList() {
        // 获取当前服务关联的酒店编码集合
        Set<String> hcodes = getServiceHcodes();
        if (CollUtil.isEmpty(hcodes)) {
            return Collections.emptyList();
        }

        // 查询美团渠道的订单列表API基础数据
        List<OtaApiDO> otaApiList = getOtaApiList(new OtaApiReqVO()
                .setChannel(ChannelEnum.MEITUAN.getCode())
                .setMethod(OtaMethodTypeEnum.ORDER_LIST.getCode()));
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 过滤出当前服务可访问的API数据
        otaApiList = CollectionUtils.filterList(otaApiList, otaApiDO -> hcodes.contains(otaApiDO.getHcode()));
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 对每个API进行美团特有的数据包装
        for (OtaApiDO otaApi : otaApiList) {
            warpMeituan(otaApi);
        }

        // 转换数据对象并返回
        return BeanUtils.toBean(otaApiList, OtaApiRespVO.class);
    }

    /**
     * 获取所有OTA API列表，并按hcode和channel分组返回
     * <p>
     * 1. 首先获取有效的hcode集合，若为空则直接返回空列表
     * 2. 获取详细的OTA API列表数据，若为空则直接返回空列表
     * 3. 过滤掉不包含有效hcode的API数据
     * 4. 将过滤后的数据按hcode和channel进行分组
     * 5. 构造返回结果，为每个分组创建响应对象并填充订单列表和详情URL
     *
     * @return List<AllOtaApiRespVO> 分组后的OTA API响应列表，包含订单列表和详情URL信息
     */
    @Override
    public List<AllOtaApiRespVO> getAllApiList() {
        // 获取当前服务有效的hcode集合
        Set<String> hcodes = getServiceHcodes();
        if (CollUtil.isEmpty(hcodes)) {
            return Collections.emptyList();
        }

        // 获取所有OTA API详细数据（包含订单列表和详情）
        List<OtaApiDO> otaApiList = getDetailAndOrderListOtaApi();
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 过滤掉hcode不在有效集合中的API数据
        otaApiList = CollectionUtils.filterList(otaApiList, otaApiDO -> hcodes.contains(otaApiDO.getHcode()));
        if (CollUtil.isEmpty(otaApiList)) {
            return Collections.emptyList();
        }

        // 按hcode和channel进行两级分组
        Map<String, Map<String, List<OtaApiDO>>> groupedMap = otaApiList.stream()
                .collect(Collectors.groupingBy(OtaApiDO::getHcode,
                        Collectors.groupingBy(OtaApiDO::getChannel)));

        // 构造最终返回结果
        List<AllOtaApiRespVO> result = new ArrayList<>();
        for (Map.Entry<String, Map<String, List<OtaApiDO>>> hcodeEntry : groupedMap.entrySet()) {
            String hcode = hcodeEntry.getKey();
            Map<String, List<OtaApiDO>> channelMap = hcodeEntry.getValue();

            for (Map.Entry<String, List<OtaApiDO>> channelEntry : channelMap.entrySet()) {
                String channel = channelEntry.getKey();
                List<OtaApiDO> apiList = channelEntry.getValue();

                if (CollUtil.isEmpty(apiList)) {
                    continue;
                }

                // 创建响应对象并设置基础信息
                AllOtaApiRespVO vo = new AllOtaApiRespVO();
                vo.setGcode(apiList.getFirst().getGcode());
                vo.setHcode(hcode);
                vo.setChannel(channel);

                // 合并查找订单列表API和订单详情API的操作
                OtaApiDO orderListApi = null;
                OtaApiDO orderDetailApi = null;

                for (OtaApiDO api : apiList) {
                    if (OtaMethodTypeEnum.ORDER_LIST.getCode().equals(api.getMethod())) {
                        orderListApi = api;
                    } else if (OtaMethodTypeEnum.ORDER_DETAIL.getCode().equals(api.getMethod())) {
                        orderDetailApi = api;
                    }

                    // 如果两个都找到了，可以提前终止循环
                    if (orderListApi != null && orderDetailApi != null) {
                        break;
                    }
                }

                // 设置找到的API信息到响应对象
                if (orderListApi != null) {
                    vo.setOrderListUrl(BeanUtils.toBean(orderListApi, OtaApiRespVO.class));
                }

                if (orderDetailApi != null) {
                    vo.setOrderDetailUrl(BeanUtils.toBean(orderDetailApi, OtaApiRespVO.class));
                }

                result.add(vo);
            }
        }

        return result;
    }

    /**
     * 保存OTA房型信息
     * <p>
     * 该函数用于处理OTA房型信息的保存请求，将请求数据通过服务层进行保存操作。
     *
     * @param reqVO OTA房型保存请求对象，包含需要保存的房型信息
     * @return Boolean 固定返回true，表示操作成功完成（注：实际业务中可能需要更详细的成功/失败状态）
     */
    @Override
    public Boolean saveOtaRoomType(OtaRoomTypeSaveReqVO reqVO) {
        // 调用服务层方法保存OTA房型信息（JSON格式）
        otaRoomTypeService.saveOtaRoomTypesByJson(reqVO);
        return true;
    }


    /**
     * 获取服务集成列表中特定解决方案类型的酒店编码集合
     * <p>
     * 本函数通过调用服务集成API获取OTA类型的服务集成列表，并筛选出解决方案类型为美团OTA订单同步的条目，
     * 最后提取这些条目的酒店编码(hcode)组成集合返回
     *
     * @return Set<String> 包含符合条件的酒店编码集合，如果API调用失败或没有符合条件的条目则返回空集合
     */
    private Set<String> getServiceHcodes() {
        // 调用API获取OTA类型的服务集成列表
        CommonResult<List<ServiceIntegrationRespDTO>> serviceIntegrationList =
                serviceIntegrationApi.getServiceIntegrationList(null, null,
                        ServiceTypeEnum.OTA.getCode(), BooleanEnum.TRUE.getValue());

        // API调用失败时返回空集合
        if (!serviceIntegrationList.isSuccess()) {
            return new HashSet<>();
        }

        // 筛选解决方案类型为美团OTA订单同步的条目
        List<ServiceIntegrationRespDTO> serviceIntegrationRespDTOS = CollectionUtils.filterList(
                serviceIntegrationList.getData(),
                serviceIntegrationRespDTO -> SolutionTypeEnum.OTA_ORDER_SYNC_MEITUAN.getCode()
                        .equals(serviceIntegrationRespDTO.getSolutionType()));

        // 没有符合条件的条目时返回空集合
        if (CollUtil.isEmpty(serviceIntegrationRespDTOS)) {
            return new HashSet<>();
        }

        // 提取并返回酒店编码集合
        return CollectionUtils.convertSet(serviceIntegrationRespDTOS, ServiceIntegrationRespDTO::getHcode);
    }

    private void warpMeituan(OtaApiDO otaApi) {
        String url = OtaApiUtils.buildMeituanOrderListUrl(otaApi, OtaOrderStatusEnum.MEITUAN_ACCEPTED.getCode());
        if (StrUtil.isNotBlank(otaApi.getUrl())) {
            otaApi.setUrl(url);
        }
    }
}
