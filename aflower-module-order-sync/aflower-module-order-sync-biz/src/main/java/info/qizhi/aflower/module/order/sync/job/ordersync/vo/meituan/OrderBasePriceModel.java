package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "订单基础价格模型")
public class OrderBasePriceModel {
    @Schema(description = "业务促销详情列表")
    private List<BizPromotionDetailModel> bizPromotionDetails;

    @Schema(description = "佣金信息")
    private PriceDetail commission;

    @Schema(description = "底价奖励价格")
    private Object floorAwardPrice;

    @Schema(description = "底价信息")
    private PriceDetail floorPrice;

    @Schema(description = "踢出价格信息")
    private PriceDetail kickOutPrice;

    @Schema(description = "促销价格信息")
    private PriceDetail promotionPrice;

    @Schema(description = "销售价格信息")
    private PriceDetail salePrice;

    @Schema(description = "子比例信息")
    private PriceDetail subRatio;
}
