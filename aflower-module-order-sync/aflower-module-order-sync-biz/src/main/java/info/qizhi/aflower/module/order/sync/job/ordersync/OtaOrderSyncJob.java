package info.qizhi.aflower.module.order.sync.job.ordersync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.tenant.core.aop.TenantIgnore;
import info.qizhi.aflower.framework.tenant.core.util.TenantUtils;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.service.ota.OtaApiService;
import info.qizhi.aflower.module.pms.api.serviceintegration.ServiceIntegrationApi;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiReqVO;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class OtaOrderSyncJob {
    @Resource
    private ServiceIntegrationApi serviceIntegrationApi;
    @Resource
    private OtaApiService otaApiService;
    @Resource
    private OrderSyncProcessor orderSyncProcessor;

    /**
     * 同步订单xxl任务，设置一般是10分钟一次
     */
    @XxlJob("otaOrderSync")
    @TenantIgnore
    public void otaOrderSync() {
        try {
            ExecutorParam params = parseJobParams();
            if (params == null) {
                return;
            }

            log.info("开始同步OTA订单，酒店代码: {}", params.getHcode());
            TenantUtils.execute(Long.parseLong(params.getGcode()), () -> {
                List<ServiceIntegrationRespDTO> activeServices = getActiveServices(params);
                if (CollUtil.isEmpty(activeServices)) {
                    return;
                }

                List<OtaApiDO> otaApis = getOtaApis(params);
                if (CollUtil.isEmpty(otaApis)) {
                    return;
                }

                orderSyncProcessor.processOrderSync(activeServices, otaApis, params.getGcode(), params.getHcode());
            });

            log.info("同步OTA订单完成, 酒店代码: {}", params.getHcode());
        } catch (Exception e) {
            log.error("OTA order sync failed", e);
        }
    }

    private ExecutorParam parseJobParams() {
        String param = XxlJobHelper.getJobParam();
        if (StrUtil.isBlank(param)) {
            log.warn("Empty job parameters");
            return null;
        }
        return JSONUtil.toBean(param, ExecutorParam.class);
    }

    private List<ServiceIntegrationRespDTO> getActiveServices(ExecutorParam params) {
        List<ServiceIntegrationRespDTO> services = serviceIntegrationApi
                .getServiceIntegrationList(params.getGcode(), params.getHcode(), ServiceTypeEnum.OTA.getCode(), BooleanEnum.TRUE.getValue())
                .getData();
        if (CollUtil.isEmpty(services)) {
            return services;
        }
        return services.stream()
                .filter(s ->
                        List.of(
                                SolutionTypeEnum.OTA_ORDER_SYNC_CTRIP.getCode(),
                                SolutionTypeEnum.OTA_ORDER_SYNC_TIKTOK.getCode(),
                                SolutionTypeEnum.OTA_ORDER_SYNC_JD.getCode(),
                                SolutionTypeEnum.OTA_ORDER_SYNC_XIAOHONGSHU.getCode()).contains(s.getSolutionType()))
                .collect(Collectors.toList());
    }

    private List<OtaApiDO> getOtaApis(ExecutorParam params) {
        return otaApiService.getOtaApiList(
                new OtaApiReqVO()
                        .setGcode(params.getGcode())
                        .setHcode(params.getHcode())
        );
    }

    @Data
    private static class ExecutorParam {
        private String gcode;
        private String hcode;
    }
}
