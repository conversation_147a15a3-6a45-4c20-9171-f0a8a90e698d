package info.qizhi.aflower.module.order.sync.dal.dataobject.ota;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;

/**
 * OTA房型关联表;ota房型可以关联多个酒店房型 DO
 *
 * <AUTHOR>
 */
@Slf4j
@TableName(value = "ota_room_type_ref", autoResultMap = true)
@KeySequence("ota_room_type_ref_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoomTypeRefDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 酒店房型代码
     */
    private String roomTypeCode;
    /**
     * ota的房型代码
     */
    private String otaRoomTypeCode;
    /**
     * ota的房型名称
     */
    private String otaRoomTypeName;

    /**
     * ota房型对应产品
     */
    @TableField(typeHandler = ProductTypeHandler.class)
    private List<OtaProduct> otaProducts;
    /**
     * 渠道
     */
    private String channel;


    @Data
    public static class OtaProduct implements Serializable {

        @Schema(description = "产品编码")
        private String productCode;

        @Schema(description = "产品名称")
        private String productName;
    }


    public static class ProductTypeHandler extends AbstractJsonTypeHandler<List<OtaProduct>> {

        @Override
        protected List<OtaProduct> parse(String json) {
            return JsonUtils.parseArray(json, OtaProduct.class);
        }

        @Override
        protected String toJson(List<OtaProduct> product) {
            return JsonUtils.toJsonString(product);
        }
    }
}