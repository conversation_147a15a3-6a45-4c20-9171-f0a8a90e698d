package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "支付信息")
public class PaymentInfo {
    @Schema(description = "支付方法")
    private String paymentMethod;

    @Schema(description = "结算方法")
    private Object settlingMethod;

    @Schema(description = "总金额")
    private String totalAmount;

    @Schema(description = "额外可选金额")
    private Object addOptionalAmount;

    @Schema(description = "总原始金额")
    private String totalOrginAmount;

    @Schema(description = "总儿童金额")
    private Object totalChildAmount;

    @Schema(description = "总原始销售价格")
    private String totalOrginSellPrice;

    @Schema(description = "总原始成本价格")
    private String totalOrginCostPrice;

    @Schema(description = "总销售价格")
    private String totalSellPrice;

    @Schema(description = "总成本价格")
    private String totalCostPrice;

    @Schema(description = "总销售价格金额")
    private String totalSellPriceAmount;

    @Schema(description = "总成本价格金额")
    private String totalCostPriceAmount;
}
