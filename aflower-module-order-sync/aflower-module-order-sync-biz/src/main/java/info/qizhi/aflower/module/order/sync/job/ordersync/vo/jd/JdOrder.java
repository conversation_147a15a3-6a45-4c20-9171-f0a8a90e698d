package info.qizhi.aflower.module.order.sync.job.ordersync.vo.jd;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class JdOrder {
    private String erpOrderId;
    private Integer orderStatus;
    private String orderStatusDesc;
    private String payTime;
    private String bookBeginTime;
    private String bookEndTime;
    private String checkInTime;
    private String checkOutTime;
    private String cancelTime;
    private String roomTypeName;
    private Long roomTypeId;
    private Integer nightNum;
    private Integer roomAmount;
    private Long jdHotelId;
    private String id;
    private List<String> hotelGuest;
    private List<Map<String, Object>> orderLabel;
}