package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "订单主响应对象")
public class CtripOrderDetail {
    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "消息")
    private String message;

    @Schema(description = "订单类型, N:新订、C:取消、M:修改、D:续住")
    private String orderType;

    @Schema(description = "订单数据")
    private OrderData data;
}

