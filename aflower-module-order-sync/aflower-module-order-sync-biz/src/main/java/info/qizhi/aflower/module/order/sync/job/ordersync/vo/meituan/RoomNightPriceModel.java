package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间夜价格模型")
public class RoomNightPriceModel {
    @Schema(description = "日期(时间戳)")
    private Long date;

    @Schema(description = "日期字符串")
    private String dateStr;

    @Schema(description = "订单基础价格模型")
    private OrderBasePriceModel orderBasePriceModel;

    @Schema(description = "支付单元ID")
    private String paymentUnitId;
}
