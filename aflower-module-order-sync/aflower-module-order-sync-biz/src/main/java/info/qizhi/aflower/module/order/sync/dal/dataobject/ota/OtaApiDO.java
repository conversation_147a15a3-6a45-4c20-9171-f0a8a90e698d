package info.qizhi.aflower.module.order.sync.dal.dataobject.ota;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

import java.time.LocalDateTime;

/**
 * OTA_api DO
 *
 * <AUTHOR>
 */
@TableName("ota_api")
@KeySequence("ota_api_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtaApiDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * 链接
     */
    private String url;
    /**
     * 请求方式;get、post
     */
    private String requestMethod;
    /**
     * 请求头;直接存储api的请求头文本，多个换行
     */
    private String headers;

    /**
     * 请求体body
     */
    private String body;
    /**
     * 渠道代码;ctrip:携程 meituan:美团
     */
    private String channel;
    /**
     * api标题
     */
    private String apiTitle;
    /**
     * api类型;获取房型、获取待入住订单等
     */
    private String method;
    /**
     * 到期时间;记录cookie或者token的过期时间
     */
    private LocalDateTime expired;

    private String remark;

}