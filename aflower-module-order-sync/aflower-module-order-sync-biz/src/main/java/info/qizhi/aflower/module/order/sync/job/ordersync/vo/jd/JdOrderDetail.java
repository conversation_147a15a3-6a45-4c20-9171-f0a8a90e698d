package info.qizhi.aflower.module.order.sync.job.ordersync.vo.jd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "京东酒店订单响应")
public class JdOrderDetail {
    @Schema(description = "消息")
    private String msg;

    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "订单数据")
    private OrderData data;

    @Schema(description = "跟踪ID")
    private String dsmTraceId;

    @Data
    @Schema(description = "订单数据")
    public static class OrderData {
        @Schema(description = "订单类型 1-有效")
        private Integer orderType;

        @Schema(description = "支付方式描述")
        private String payModeDesc;

        @Schema(description = "间夜明细")
        private NightDetail nightDetail;

        @Schema(description = "支付时间(毫秒时间戳)")
        private Long payTime;

        @Schema(description = "是否显示入住通知")
        private Boolean showCheckInNotice;

        @Schema(description = "订单状态时间描述")
        private String orderStatusTimeDesc;

        @Schema(description = "订单标签列表")
        private List<OrderLabel> orderLabels;

        @Schema(description = "订单状态描述")
        private String orderStatusDesc;

        @Schema(description = "订单状态 4-已入住")
        private Integer orderStatus;

        @Schema(description = "剩余完整小时数")
        private Integer remainingFullHours;

        @Schema(description = "是否显示入住按钮")
        private Boolean showCheckInButton;

        @Schema(description = "房间晚数")
        private Integer roomNights;

        @Schema(description = "是否立即确认 0-否")
        private Integer immediately;

        @Schema(description = "立即确认描述")
        private String immediatelyDesc;

        @Schema(description = "入住日期(毫秒时间戳)")
        private Long checkInDate;

        @Schema(description = "订单状态时间(毫秒时间戳)")
        private Long orderStatusTime;

        @Schema(description = "退房日期(毫秒时间戳)")
        private Long checkOutDate;

        @Schema(description = "ERP订单ID")
        private String erpOrderId;

        @Schema(description = "订单价格信息")
        private OrderPrice orderPrice;

        @Schema(description = "客户姓名列表")
        private List<String> customers;

        @Schema(description = "订单跟踪列表")
        private List<Object> orderTrackList;

        @Schema(description = "客户请求信息")
        private CustomerRequest customerRequest;

        @Schema(description = "发票类型 1-酒店开票")
        private Integer receiptType;

        @Schema(description = "支付方式 1-预付")
        private Integer payMode;

        @Schema(description = "倒计时毫秒数")
        private Long countdownMillis;

        @Schema(description = "发票类型描述")
        private String receiptTypeDesc;

        @Schema(description = "订单类型描述")
        private String orderTypeDesc;

        @Schema(description = "房间信息")
        private Room room;

        @Schema(description = "退款订单ID")
        private String refundOrderId;

        @Schema(description = "确认订单信息")
        private ConfirmOrder confirmOrder;

        @Schema(description = "订单操作类型")
        private Integer orderOperateType;

        @Schema(description = "创建时间(毫秒时间戳)")
        private Long createTime;

        @Schema(description = "天数")
        private Integer days;
    }

    @Data
    @Schema(description = "间夜明细")
    public static class NightDetail {
        @Schema(description = "复制内容")
        private String copyContent;

        @Schema(description = "间夜明细项列表")
        private List<NightDetailItem> nightDetailItems;

        @Schema(description = "标题")
        private String title;
    }

    @Data
    @Schema(description = "间夜明细项")
    public static class NightDetailItem {
        @Schema(description = "日期")
        private String date;

        @Schema(description = "预估收入")
        private String preRevenue;

        @Schema(description = "餐食信息")
        private String mealInfo;

        @Schema(description = "折扣前销售价格")
        private String sellPriceBeforeDiscount;
    }

    @Data
    @Schema(description = "订单标签")
    public static class OrderLabel {
        @Schema(description = "排序号")
        private Integer sortNo;

        @Schema(description = "标签类型 success/yellow/info等")
        private String tagType;

        @Schema(description = "文本")
        private String text;

        @Schema(description = "ID")
        private String id;

        @Schema(description = "类型 0/1")
        private Integer type;

        @Schema(description = "浮动文本")
        private String floatText;
    }

    @Data
    @Schema(description = "订单价格信息")
    public static class OrderPrice {
        @Schema(description = "预估收入")
        private String preRevenue;

        @Schema(description = "折扣前基础价格")
        private String basePriceBeforeDiscount;

        @Schema(description = "折扣后销售价格")
        private String sellPriceAfterDiscount;

        @Schema(description = "标题")
        private String title;
    }

    @Data
    @Schema(description = "客户请求信息")
    public static class CustomerRequest {
        @Schema(description = "未入住通知")
        private NotCheckInNotice notCheckInNotice;

        @Schema(description = "取消规则")
        private CancelRule cancelRule;

        @Schema(description = "京东提示")
        private JdNotice jdNotice;

        @Schema(description = "发票信息")
        private Invoice invoice;

        @Data
        @Schema(description = "未入住通知")
        public static class NotCheckInNotice {
            @Schema(description = "标题")
            private String title;

            @Schema(description = "内容")
            private String content;
        }
    }

    @Data
    @Schema(description = "取消规则")
    public static class CancelRule {
        @Schema(description = "取消描述")
        private String cancelDesc;

        @Schema(description = "取消类型")
        private String cancelType;

        @Schema(description = "取消规则项列表")
        private List<CancelRuleItem> cancelRuleItems;
    }

    @Data
    @Schema(description = "取消规则项")
    public static class CancelRuleItem {
        @Schema(description = "日期")
        private String date;

        @Schema(description = "描述")
        private String desc;
    }

    @Data
    @Schema(description = "京东提示")
    public static class JdNotice {
        @Schema(description = "标题")
        private String title;

        @Schema(description = "通知内容")
        private String notice;
    }

    @Data
    @Schema(description = "发票信息")
    public static class Invoice {
        @Schema(description = "发票描述")
        private String invoiceDesc;

        @Schema(description = "标题")
        private String title;
    }

    @Data
    @Schema(description = "房间信息")
    public static class Room {
        @Schema(description = "床型信息")
        private String bedInfos;

        @Schema(description = "房间数量")
        private Integer roomNum;

        @Schema(description = "房型名称")
        private String ratePlanName;

        @Schema(description = "房型ID")
        private String ratePlanId;

        @Schema(description = "最大成人入住数")
        private Integer maxAdultOccupancy;

        @Schema(description = "最大儿童入住数")
        private Integer maxChildOccupancy;
    }

    @Data
    @Schema(description = "确认订单信息")
    public static class ConfirmOrder {
        @Schema(description = "确认操作者")
        private String confirmOperator;

        @Schema(description = "确认结果")
        private String confirmResult;

        @Schema(description = "确认类型")
        private String confirmType;
    }
}
