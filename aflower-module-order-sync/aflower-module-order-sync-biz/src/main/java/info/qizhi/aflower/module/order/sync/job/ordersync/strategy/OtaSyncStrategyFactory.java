package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import info.qizhi.aflower.framework.common.enums.SolutionTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class OtaSyncStrategyFactory {
    private final Map<String, OtaSyncStrategy> strategyMap;

    /**
     * 工厂
     * @param ctripSyncStrategy 携程同步策略
     * @param meituanSyncStrategy 美团同步策略
     * @param tikTokSyncStrategy 抖音同步策略
     */
    public OtaSyncStrategyFactory(
            @Autowired
            CtripSyncStrategy ctripSyncStrategy,
            @Autowired
            MeituanSyncStrategy meituanSyncStrategy,
            @Autowired
            TiktokSyncStrategy tikTokSyncStrategy,
            @Autowired
            JdSyncStrategy jdSyncStrategy
    ) {
        strategyMap = Map.of(
                SolutionTypeEnum.OTA_ORDER_SYNC_CTRIP.getCode(), ctripSyncStrategy,
                SolutionTypeEnum.OTA_ORDER_SYNC_MEITUAN.getCode(), meituanSyncStrategy,
                SolutionTypeEnum.OTA_ORDER_SYNC_TIKTOK.getCode(), tikTokSyncStrategy,
                SolutionTypeEnum.OTA_ORDER_SYNC_JD.getCode(), jdSyncStrategy
        );
    }

    /**
     * 根据平台类型获取对应的策略
     * @param solutionType 平台类型
     * @return
     */
    public OtaSyncStrategy getStrategy(String solutionType) {
        OtaSyncStrategy strategy = strategyMap.get(solutionType);
        if (strategy == null) {
            throw new IllegalArgumentException("Unsupported OTA platform: " + solutionType);
        }
        return strategy;
    }
}
