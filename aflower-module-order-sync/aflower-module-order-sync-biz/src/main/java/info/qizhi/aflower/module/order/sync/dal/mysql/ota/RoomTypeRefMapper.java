package info.qizhi.aflower.module.order.sync.dal.mysql.ota;

import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.RoomTypeRefReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OTA房型关联表;ota房型可以关联多个酒店房型 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface RoomTypeRefMapper extends BaseMapperX<RoomTypeRefDO> {

    default List<RoomTypeRefDO> selectList(RoomTypeRefReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<RoomTypeRefDO>()
                .eq(RoomTypeRefDO::getGcode, reqVO.getGcode())
                .eq(RoomTypeRefDO::getHcode, reqVO.getHcode())
                .eqIfPresent(RoomTypeRefDO::getRoomTypeCode, reqVO.getRoomTypeCode())
                .eqIfPresent(RoomTypeRefDO::getChannel, reqVO.getChannel()));
    }

}