package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "促销信息")
public class Promotion {
    @Schema(description = "规则名称")
    private String ruleName;

    @Schema(description = "成本差价")
    private String costGapPrice;

    @Schema(description = "销售差价")
    private String sellGapPrice;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "货币")
    private String currency;

    @Schema(description = "名称")
    private String name;
}
