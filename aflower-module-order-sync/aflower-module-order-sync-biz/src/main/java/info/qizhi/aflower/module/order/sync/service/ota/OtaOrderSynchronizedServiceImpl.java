package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaOrderSynchronizedReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaOrderSynchronizedSaveVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import info.qizhi.aflower.module.order.sync.dal.mysql.ota.OtaOrderSynchronizedMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_API_NOT_EXISTS;

/**
 * OTA已同步订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OtaOrderSynchronizedServiceImpl implements OtaOrderSynchronizedService {

    @Resource
    private OtaOrderSynchronizedMapper orderSynchronizedMapper;

    @Override
    public void batchCreateOtaOrderSynchronized(List<OtaOrderSynchronizedSaveVO> createReqVO) {
        List<OtaOrderSynchronizedDO> orderSynchronizedDOS = BeanUtils.toBean(createReqVO, OtaOrderSynchronizedDO.class);
        orderSynchronizedMapper.insertBatch(orderSynchronizedDOS);
    }

    // 加上定时任务
    public void sysnchronizedOrder(OtaOrderSynchronizedSaveVO createReqVO) {
        // 插入
        OtaOrderSynchronizedDO otaOrderSynchronizedDO = BeanUtils.toBean(createReqVO, OtaOrderSynchronizedDO.class);
        orderSynchronizedMapper.insert(otaOrderSynchronizedDO);
    }


    @Override
    public Long createOtaOrderSynchronized(OtaOrderSynchronizedSaveVO createReqVO) {
        // 插入
        OtaOrderSynchronizedDO otaOrderSynchronizedDO = BeanUtils.toBean(createReqVO, OtaOrderSynchronizedDO.class);
        orderSynchronizedMapper.insert(otaOrderSynchronizedDO);
        // 返回
        return otaOrderSynchronizedDO.getId();
    }

    @Override
    public void updateOtaOrderSynchronized(OtaOrderSynchronizedSaveVO updateReqVO) {
        // 校验存在
        validateOtaApiExists(updateReqVO.getId());
        // 更新
        OtaOrderSynchronizedDO updateObj = BeanUtils.toBean(updateReqVO, OtaOrderSynchronizedDO.class);
        orderSynchronizedMapper.updateById(updateObj);
    }

    private void validateOtaApiExists(Long id) {
        if (orderSynchronizedMapper.selectById(id) == null) {
            throw exception(OTA_API_NOT_EXISTS);
        }
    }

    @Override
    public List<OtaOrderSynchronizedDO> getOtaOrderSynchronizedList(OtaOrderSynchronizedReqVO reqVO) {
        return orderSynchronizedMapper.selectList(reqVO);
    }

    @Override
    public void deleteOtaOrderSynchronized(String gcode, String hcode) {
        orderSynchronizedMapper.deleteOtaOrderSync(gcode, hcode);
    }

}