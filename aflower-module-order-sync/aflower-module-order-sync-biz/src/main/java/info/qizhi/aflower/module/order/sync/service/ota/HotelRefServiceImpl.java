package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.HotelRefReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.HotelRefSaveReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.HotelRefDO;
import info.qizhi.aflower.module.order.sync.dal.mysql.ota.HotelRefMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.HOTEL_REF_ALREADY_EXISTS;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.HOTEL_REF_NOT_EXISTS;

/**
 * OTA酒店关联 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class HotelRefServiceImpl implements HotelRefService {

    @Resource
    private HotelRefMapper hotelRefMapper;

    @Override
    public Long createHotelRef(HotelRefSaveReqVO createReqVO) {
        // 校验酒店代码和渠道代码是否已存在
        validateHotelRefNotExists(createReqVO.getHcode(), createReqVO.getChannel());

        // 插入
        HotelRefDO hotelRef = BeanUtils.toBean(createReqVO, HotelRefDO.class);
        hotelRefMapper.insert(hotelRef);

        // 返回
        return hotelRef.getId();
    }

    @Override
    public void updateHotelRef(HotelRefSaveReqVO updateReqVO) {
        // 校验存在
        validateHotelRefExists(updateReqVO.getId());

        // 校验酒店代码和渠道代码是否已存在（排除自身）
        HotelRefDO existingHotelRef = hotelRefMapper.selectOne(HotelRefDO::getHcode, updateReqVO.getHcode(), HotelRefDO::getChannel, updateReqVO.getChannel());
        if (existingHotelRef != null && !existingHotelRef.getId().equals(updateReqVO.getId())) {
            throw exception(HOTEL_REF_ALREADY_EXISTS);
        }

        // 更新
        HotelRefDO updateObj = BeanUtils.toBean(updateReqVO, HotelRefDO.class);
        hotelRefMapper.updateById(updateObj);
    }

    @Override
    public void deleteHotelRef(Long id) {
        // 校验存在
        validateHotelRefExists(id);

        // 删除
        hotelRefMapper.deleteById(id);
    }

    /**
     * 校验酒店代码和渠道代码是否已存在
     *
     * @param hcode    酒店代码
     * @param channel  渠道代码
     */
    private void validateHotelRefNotExists(String hcode, String channel) {
        HotelRefDO hotelRefDO = hotelRefMapper.selectOne(HotelRefDO::getHcode, hcode, HotelRefDO::getChannel, channel);
        if (hotelRefDO != null) {
            throw exception(HOTEL_REF_ALREADY_EXISTS);
        }
    }

    /**
     * 校验酒店关联是否存在
     *
     * @param id 主键
     */
    private void validateHotelRefExists(Long id) {
        if (hotelRefMapper.selectById(id) == null) {
            throw exception(HOTEL_REF_NOT_EXISTS);
        }
    }

    @Override
    public HotelRefDO getHotelRef(Long id) {
        return hotelRefMapper.selectById(id);
    }

    @Override
    public List<HotelRefDO> getHotelRefs(HotelRefReqVO reqVO) {
        return hotelRefMapper.selectList(reqVO);
    }
}