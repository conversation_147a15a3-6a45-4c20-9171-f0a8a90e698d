package info.qizhi.aflower.module.order.sync.job.ordersync.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan.MeituanOrderDetail;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MeituanOrderParser {

    /**
     * 解析美团订单列表API响应
     */
    public static List<MeituanOrderDetail> parseOrderList(String jsonResponse) {
        List<MeituanOrderDetail> orders = new ArrayList<>();

        try {
            JSONObject response = JSONUtil.parseObj(jsonResponse);
            if (response.getInt("status", -1) != 0) {
                log.error("美团订单列表API错误: {}", response.getStr("message"));
                String alertMsg = String.format(
                        "**OTA同步异常报警**\n" +
                                "> 异常类型: `%s`\n" +
                                "> 异常信息: `%s`\n" +
                                "> 发生时间: `%s`\n" +
                                "请及时处理！",
                        "美团订单列表API错误",
                        response.getStr("message"),
                        LocalDateTime.now()
                );
                WeChatBotNotifier.sendAlert(alertMsg);
                return orders;
            }

            JSONObject data = response.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                log.info("美团订单列表数据为空");
                return orders;
            }

            JSONArray results = data.getJSONArray("results");
            if (CollUtil.isEmpty(results)) {
                return orders;
            }

            for (int i = 0; i < results.size(); i++) {
                JSONObject orderJson = results.getJSONObject(i);
                MeituanOrderDetail order = parseMeituanOrder(orderJson);
                if (order != null) {
                    orders.add(order);
                }
            }

            // 【新增】去重逻辑：使用 orderId 作为唯一标识
            Map<String, MeituanOrderDetail> uniqueOrders = new LinkedHashMap<>();
            for (MeituanOrderDetail order : orders) {
                if (order != null && StrUtil.isNotBlank(order.getOrderId())) {
                    uniqueOrders.putIfAbsent(order.getOrderId(), order);
                }
            }
            orders = new ArrayList<>(uniqueOrders.values());
        } catch (Exception e) {
            log.error("解析美团订单列表异常", e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析美团订单列表异常",
                    e.getMessage(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            return orders;
        }

        return orders;
    }

    /**
     * 解析美团订单的详细信息
     * 此方法旨在将从美团获取到的订单JSON对象转换为MeituanOrderDetail对象
     * 主要目的是为了方便后续处理和访问订单信息
     *
     * @param orderJson 美团订单的JSON对象如果为null，则返回null
     * @return 解析后的MeituanOrderDetail对象如果解析失败或输入为null，则返回null
     */
    private static MeituanOrderDetail parseMeituanOrder(JSONObject orderJson) {
        // 检查输入的JSON对象是否为null
        if (orderJson == null) {
            return null;
        }

        MeituanOrderDetail order = null;
        try {
            // 解析基本订单信息
            // 使用JsonUtils工具类将订单JSON字符串转换为MeituanOrderDetail对象
            order = JsonUtils.parseObject(orderJson.toString(), MeituanOrderDetail.class);
            log.info("解析订单成功: {} {}", order.getOrderId(), order.getStatus());
        } catch (Exception e) {
            // 记录解析订单时的错误信息
            log.error("解析美团订单异常，订单JSON: {}", orderJson, e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 订单信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析美团订单异常",
                    orderJson,
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            return null;
        }

        // 返回解析后的订单对象
        return order;
    }

}
