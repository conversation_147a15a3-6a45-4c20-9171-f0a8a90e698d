package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "风险订单信息")
public class RiskOrderInfo {
    @Schema(description = "假期描述")
    private String holidayDesc;

    @Schema(description = "是否超过几天")
    private Boolean moreThanAFewDays;

    @Schema(description = "是否风险订单")
    private Boolean riskOrder;
}
