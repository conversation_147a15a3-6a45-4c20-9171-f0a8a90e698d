package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "业务促销详情模型")
public class BizPromotionDetailModel {
    @Schema(description = "附加文本")
    private String additionalText;

    @Schema(description = "业务金额详情")
    private PriceDetail bizMoney;

    @Schema(description = "业务促销类型")
    private Integer bizPromotionType;

    @Schema(description = "悬停文本")
    private String hoverText;

    @Schema(description = "悬停类型")
    private Integer hoverType;

    @Schema(description = "促销ID")
    private Long promotionId;

    @Schema(description = "促销类型")
    private Integer promotionType;

    @Schema(description = "显示模式")
    private Integer showPattern;

    @Schema(description = "标签名称")
    private TagName tagName;
}
