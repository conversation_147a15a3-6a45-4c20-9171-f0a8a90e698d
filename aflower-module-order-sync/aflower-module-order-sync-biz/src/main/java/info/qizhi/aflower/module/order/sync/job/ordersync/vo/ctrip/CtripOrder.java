package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class CtripOrder {

    @Schema(description = "订单号")
    private String orderID;

    @Schema(description = "表单ID")
    private String formID;

    @Schema(description = "令牌")
    private String token;

    @Schema(description = "订单类型, N:新订、C:取消、M:修改、D:续住")
    private String orderType;

    @Schema(description = "订单状态, Receipt:已接单、CheckIn:已入住、Canceled:已取消")
    private String orderStatusType;

    @Schema(description = "订单状态描述")
    private String orderStatusDisplay;
}
