package info.qizhi.aflower.module.order.sync.job.ordersync;

import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.GeneralConfigTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.job.ordersync.strategy.OtaSyncStrategy;
import info.qizhi.aflower.module.order.sync.job.ordersync.strategy.OtaSyncStrategyFactory;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Component
@Slf4j
public class OrderSyncProcessor {
    @Resource
    private OtaSyncStrategyFactory strategyFactory;
    @Resource
    private GeneralConfigApi generalConfigApi;

    /**
     * 统一订单同步入口
     * @param services 酒店开通的OTA 服务列表
     * @param otaApis 酒店OTA接口列表
     * @param gcode 集团代码
     * @param hcode 酒店代码
     */
    public void processOrderSync(List<ServiceIntegrationRespDTO> services,
                                 List<OtaApiDO> otaApis,
                                 String gcode,
                                 String hcode) {
        // 获取最晚离店时间
        String lastCheckOutTime = getLastCheckOutTime(gcode);
        services.forEach(service -> {
            try {
                // 获取对应的策略
                OtaSyncStrategy strategy = strategyFactory.getStrategy(service.getSolutionType());
                strategy.syncOrders(service, otaApis, gcode, hcode, lastCheckOutTime);
            } catch (Exception e) {
                log.error("[订单同步] 平台处理异常 solutionType={}", service.getSolutionType(), e);
            }
        });
    }

    /**
     * 获取最晚离店时间
     * @param gcode 集团代码
     * @return 最晚离店时间
     */
    private String getLastCheckOutTime(String gcode) {
        // 获取最晚离店时间
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO()
                .setGcode(gcode)
                .setHcode(NumberEnum.ZERO.getNumber())
                .setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())).getData();
        if (generalConfig == null) {
            throw new RuntimeException("[订单同步] 获取最晚离店时间失败");
        }
        String lastCheckOutTime = generalConfig.getValue();
        // 检查是否为HH:mm格式的时间
        if (StrUtil.isBlank(lastCheckOutTime) || !lastCheckOutTime.matches("\\d{2}:\\d{2}")) {
            throw new RuntimeException("[订单同步] 最晚离店时间格式错误,集团代码:" + gcode);
        }
        return lastCheckOutTime;
    }
}
