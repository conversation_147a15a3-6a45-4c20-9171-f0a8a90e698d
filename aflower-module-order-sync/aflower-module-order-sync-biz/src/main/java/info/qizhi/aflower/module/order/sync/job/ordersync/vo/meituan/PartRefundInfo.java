package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "部分退款信息")
public class PartRefundInfo {
    @Schema(description = "是否部分退款")
    private Boolean partRefund;

    @Schema(description = "是否按金额部分退款")
    private Boolean partRefundByMoney;

    @Schema(description = "按金额部分退款描述")
    private String partRefundByMoneyDesc;

    @Schema(description = "房间信息列表")
    private List<RoomInfo> roomInfo;

    @Schema(description = "总退款房间权益数量")
    private Integer totalRefundRoomRightCount;

    @Schema(description = "总房间夜数")
    private Integer totalRoomNightCount;
}
