package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "入住日期模型")
public class CheckInDateModel {
    @Schema(description = "入住日期(时间戳)")
    private Long checkInDate;

    @Schema(description = "入住日期字符串")
    private String checkInDateString;

    @Schema(description = "每间房价格")
    private Integer pricePerRoom;

    @Schema(description = "退款房间夜数")
    private Integer refundedRoomNightsCnt;

    @Schema(description = "房间夜信息列表")
    private List<RoomNightsInfo> roomNightsInfo;
}
