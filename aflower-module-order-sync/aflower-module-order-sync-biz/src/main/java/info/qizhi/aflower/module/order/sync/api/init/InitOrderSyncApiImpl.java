package info.qizhi.aflower.module.order.sync.api.init;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.order.sync.service.ota.OtaOrderSynchronizedService;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@RestController // 提供 RESTful API 接口，给 Feign 调用
@Validated
public class InitOrderSyncApiImpl implements  InitOrderSyncApi{

    @Resource
    private OtaOrderSynchronizedService otaOrderSynchronizedService;

    @Override
    public CommonResult<Boolean> initOrderSyncBizData(String gcode, String hcode) {
        otaOrderSynchronizedService.deleteOtaOrderSynchronized( gcode, hcode);
        return success(true);
    }
}
