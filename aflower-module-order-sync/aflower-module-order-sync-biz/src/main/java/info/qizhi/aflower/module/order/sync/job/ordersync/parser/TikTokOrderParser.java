package info.qizhi.aflower.module.order.sync.job.ordersync.parser;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.tiktok.TikTokOrderDetail;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class TikTokOrderParser {

    /**
     * 解析抖音订单列表
     * @param json
     * @return
     */
    public static List<TikTokOrderDetail> parseOrderList(String hcode, String json) {
        List<TikTokOrderDetail> orderList = CollUtil.newArrayList();

        try {
            JSONObject response = JSONUtil.parseObj(json);
            if (response.getInt("status_code", -1) != 0) {
                log.error("抖音订单列表API错误: {}", response.getStr("status_msg"));
                String alertMsg = String.format(
                        "**OTA同步异常报警**\n" +
                                "> 异常类型: `%s`\n" +
                                "> 异常信息: `%s`\n" +
                                "> 发生时间: `%s`\n" +
                                "请及时处理！",
                        "抖音订单列表API错误 hcode:" + hcode,
                        response.getStr("status_msg"),
                        LocalDateTime.now()
                );
                WeChatBotNotifier.sendAlert(alertMsg);
                return orderList;
            }

            JSONObject data = response.getJSONObject("data");
            if (data == null || data.isEmpty()) {
                log.info("抖音订单列表数据为空");
                return orderList;
            }

            JSONArray orderListJson = data.getJSONArray("book_order_list");
            if (orderListJson == null || orderListJson.isEmpty()) {
                log.info("抖音订单列表数据为空");
                return orderList;
            }

            for (int i = 0; i < orderListJson.size(); i++) {
                TikTokOrderDetail orderDetail = JSONUtil.toBean(orderListJson.getJSONObject(i), TikTokOrderDetail.class);
                if (orderDetail != null) {
                    orderList.add(orderDetail);
                }
            }
        } catch (Exception e) {
            log.error("解析抖音订单列表失败", e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析抖音订单列表失败 hcode:" + hcode,
                    e.getMessage(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
        }

        return orderList;
    }

    /**
     * 解析抖音订单详情
     * @param orderJson
     * @return
     */
    public static TikTokOrderDetail parseOrderDetail(JSONObject orderJson) {
        if (orderJson == null) {
            return null;
        }

        TikTokOrderDetail orderDetail = null;
        try {
            orderDetail = JsonUtils.parseObject(orderJson.toString(), TikTokOrderDetail.class);
        } catch (Exception e) {
            log.error("解析抖音订单详情失败", e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析抖音订单列表失败",
                    e.getMessage(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
        }
        return orderDetail;
    }
}
