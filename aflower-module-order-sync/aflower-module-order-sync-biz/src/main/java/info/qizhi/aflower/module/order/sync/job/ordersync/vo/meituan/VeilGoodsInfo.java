package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "商品信息")
public class VeilGoodsInfo {
    @Schema(description = "是否定向显示")
    private Boolean directionalDisplay;

    @Schema(description = "显示规则列表")
    private List<Object> displayRuleList;

    @Schema(description = "显示标题")
    private String displayTitle;

    @Schema(description = "子描述")
    private String subDesc;
}
