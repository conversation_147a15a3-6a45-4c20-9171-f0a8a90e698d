package info.qizhi.aflower.module.order.sync.dal.mysql.ota;

import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OTA_api Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OtaApiMapper extends BaseMapperX<OtaApiDO> {

    default List<OtaApiDO> selectList(OtaApiReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<OtaApiDO>()
                .eqIfPresent(OtaApiDO::getGcode, reqVO.getGcode())
                .eqIfPresent(OtaApiDO::getHcode, reqVO.getHcode())
                .eqIfPresent(OtaApiDO::getChannel, reqVO.getChannel())
                .inIfPresent(OtaApiDO::getMethod, reqVO.getMethods())
                .eqIfPresent(OtaApiDO::getMethod, reqVO.getMethod()));
    }

    default OtaApiDO selectOne(OtaApiReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<OtaApiDO>()
                .eq(OtaApiDO::getGcode, reqVO.getGcode())
                .eq(OtaApiDO::getHcode, reqVO.getHcode())
                .eq(OtaApiDO::getChannel, reqVO.getChannel())
                .eq(OtaApiDO::getMethod, reqVO.getMethod()));
    }

    default List<OtaApiDO> getDetailAndOrderListOtaApi() {
        return selectList(new LambdaQueryWrapperX<OtaApiDO>()
                .in(OtaApiDO::getMethod, "order_list", "order_detail"));
    }

}