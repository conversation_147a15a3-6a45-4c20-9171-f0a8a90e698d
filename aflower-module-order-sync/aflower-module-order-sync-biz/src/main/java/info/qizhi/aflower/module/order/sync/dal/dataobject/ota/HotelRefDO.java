package info.qizhi.aflower.module.order.sync.dal.dataobject.ota;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * OTA酒店关联 DO
 *
 * <AUTHOR>
 */
@TableName("ota_hotel_ref")
@KeySequence("ota_hotel_ref_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HotelRefDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * OTA酒店代码
     */
    private String otaHcode;

    /**
     * OTA酒店名称
     */
    private String otaHname;
    /**
     * 渠道
     */
    private String channel;

}