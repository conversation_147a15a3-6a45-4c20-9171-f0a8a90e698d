package info.qizhi.aflower.module.order.sync.job.ordersync.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.OtaOrderStatusEnum;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip.CtripOrder;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class CtripOrderParser {
    public static List<CtripOrder> parseOrderList(String hcode, String json) {

        List<String> orderStatusTypes = List.of(OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode(), OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode());

        List<CtripOrder> ctripOrderList = CollUtil.newArrayList();
            // 解析响应
            if (StrUtil.isNotBlank(json)) {
                JSONObject jsonResponse = JSONUtil.parseObj(json);
                if (jsonResponse.getInt("code", -1) == 200) {
                    JSONObject data = jsonResponse.getJSONObject("data");
                    if (data != null) {
                        // 解析订单列表
                        JSONArray orderArray = data.getJSONArray("orders");
                        if (CollUtil.isNotEmpty(orderArray)) {
                            for (int i = 0; i < orderArray.size(); i++) {
                                JSONObject orderJson = orderArray.getJSONObject(i);
                                CtripOrder order = parseCtripOrder(orderJson);
                                if (order != null && orderStatusTypes.contains(order.getOrderStatusType())) { // 暂时只处理已接待、已取消订单
                                    ctripOrderList.add(order);
                                }
                            }
                        }
                        log.info("携程订单列表URL请求成功~~~~~~~~~~~，hcode: {}, 订单数：{}", hcode, ctripOrderList.size());
                    }
                } else {
                    log.error("携程订单同步失败，hcode: {}, 响应码：{}，消息：{}, JSON:{}",
                            hcode,
                            jsonResponse.getInt("code"),
                            jsonResponse.getStr("message"),
                            json);
                    String alertMsg = String.format(
                            "**OTA同步异常报警**\n" +
                                    "> 异常类型: `%s`\n" +
                                    "> 异常信息: `%s`\n" +
                                    "> 发生时间: `%s`\n" +
                                    "请及时处理！",
                            "携程订单同步失败 hcode:" + hcode,
                            json,
                            LocalDateTime.now()
                    );
                    WeChatBotNotifier.sendAlert(alertMsg);
                }
            }
        return ctripOrderList;
    }

    /**
     * 解析携程订单的JSON对象为CtripOrder对象
     * 此方法主要用于从给定的JSON对象中提取订单信息，并将其封装到CtripOrder对象中
     * 如果提供的JSON对象为null或无法解析，则方法返回null
     *
     * @param orderJson 包含携程订单信息的JSON对象
     * @return 解析后的CtripOrder对象，如果无法解析或输入为null，则返回null
     */
    private static CtripOrder parseCtripOrder(JSONObject orderJson) {
        // 检查输入的JSON对象是否为null
        if (orderJson == null) {
            return null;
        }

        CtripOrder order = new CtripOrder();
        try {
            // 解析基本订单信息
            order.setOrderID(orderJson.getStr("orderID"));
            order.setOrderType(orderJson.getStr("orderType"));
            order.setOrderStatusType(orderJson.getStr("orderStatusType"));
            order.setOrderStatusDisplay(orderJson.getStr("orderStatusDisplay"));
            order.setFormID(orderJson.getStr("formID"));
            order.setToken(orderJson.getStr("token"));
        } catch (Exception e) {
            // 记录解析异常信息
            log.error("解析携程订单异常，订单JSON: {}", orderJson, e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> JSON: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析携程订单异常",
                    orderJson,
                    e.getMessage(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            return null;
        }

        return order;
    }
}
