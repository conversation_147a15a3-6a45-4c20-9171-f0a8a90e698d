package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间夜信息")
public class RoomNightsInfo {
    @Schema(description = "入住时间戳")
    private Long checkInTimeStamp;

    @Schema(description = "入住时间字符串")
    private String checkInTimeStampString;

    @Schema(description = "佣金")
    private Integer commission;

    @Schema(description = "ID")
    private String id;

    @Schema(description = "美团销售价格")
    private Integer mtSoldPrice;

    @Schema(description = "退款金额")
    private Integer refundMoney;

    @Schema(description = "退款原因")
    private Object refundReason;

    @Schema(description = "是否已退款")
    private Boolean refunded;

    @Schema(description = "房间类型")
    private String roomType;

    @Schema(description = "房间用户")
    private String roomUser;
}
