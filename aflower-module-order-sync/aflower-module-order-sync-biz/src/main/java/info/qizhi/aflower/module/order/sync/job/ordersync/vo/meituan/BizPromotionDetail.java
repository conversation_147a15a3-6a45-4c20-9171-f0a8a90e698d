package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "业务促销详情")
public class BizPromotionDetail {
    @Schema(description = "附加文本")
    private String additionalText;

    @Schema(description = "业务金额")
    private Integer bizMoney;

    @Schema(description = "本地业务金额")
    private Integer bizMoneyLocal;

    @Schema(description = "业务促销类型")
    private Integer bizPromotionType;

    @Schema(description = "悬停文本")
    private String hoverText;

    @Schema(description = "悬停类型")
    private Integer hoverType;

    @Schema(description = "标签信息")
    private String tagInfo;

    @Schema(description = "英文标签信息")
    private String tagInfo4En;
}
