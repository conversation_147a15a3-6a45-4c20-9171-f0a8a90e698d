package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.http.HttpUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.parser.MeituanOrderParser;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan.MeituanOrderDetail;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan.PriceInfo;
import info.qizhi.aflower.module.order.sync.mq.producer.OTAOrderFailAlertProducer;
import info.qizhi.aflower.module.order.sync.service.ota.OtaOrderSynchronizedService;
import info.qizhi.aflower.module.order.sync.service.ota.RoomTypeRefService;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookCancelOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenRespDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_ROOM_TYPE_REF_NOT_FOUND;

@Slf4j
@Service
public class MeituanSyncStrategy implements OtaSyncStrategy{

    @Resource
    private RoomTypeRefService roomTypeRefService;
    @Resource
    private BookingApi bookingApi;
    @Resource
    private OtaOrderSynchronizedService otaOrderSynchronizedService;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private OTAOrderFailAlertProducer otaOrderFailAlertProducer;
    @Override
    @Deprecated
    public void syncOrders(ServiceIntegrationRespDTO service,
                           List<OtaApiDO> otaApis,
                           String gcode,
                           String hcode,
                           String lastCheckOutTime) {
        try {
            log.info("Starting Meituan order sync for hotel: {}", hcode);
            // 1. 获取 Meituan 订单列表接口
            OtaApiDO orderListApi = OtaApiUtils.findApi(otaApis, OtaMethodTypeEnum.ORDER_LIST, ChannelEnum.MEITUAN);
            if (orderListApi == null) {
                log.error("Meituan order list API not configured for hotel: {}", hcode);
                return;
            }
            ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.MEITUAN_PREPAY.getName()).getData();
            if (protocolAgentSimpleRespDTO == null) {
                log.error("Missing required Meituan protocol agent for hotel: {}", hcode);
                return;
            }
            // 2. 构建请求体
            SyncContext context = new SyncContext(
                    gcode,
                    hcode,
                    orderListApi,
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(gcode).setHcode(hcode).setChannel(ChannelEnum.MEITUAN.getCode())),
                    lastCheckOutTime,
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.MEITUAN.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)),
                    protocolAgentSimpleRespDTO.getPaCode()
            );
            // 3. 获取订单
            List<MeituanOrderDetail> orders = fetchOrders(context);
            if (CollUtil.isEmpty(orders)) {
                log.info("No meituan orders found for hotel: {}", hcode);
                return;
            }
            // 4. 处理订单
            processOrders(orders, context);

            log.info("Meituan order sync completed for hotel: {}", hcode);
        } catch (Exception e) {
            log.error("Meituan order sync failed for hotel: " + hcode, e);
            throw e;
        }
    }

    @Override
    public void processUploadedJson(String gcode, String hcode, String acceptedJson, String cancelJson, List<String> jsonList)  {
        try {
            log.info("开始处理美团上传订单数据，酒店: {}", hcode);

            // 1. 解析上传的接受订单的JSON数据
            List<MeituanOrderDetail> acceptedOrders = MeituanOrderParser.parseOrderList(acceptedJson);
            List<MeituanOrderDetail> cancelOrders = MeituanOrderParser.parseOrderList(cancelJson);

            // 合并接受订单和取消订单，并根据 orderId + status 去重
            List<MeituanOrderDetail> orders = CollUtil.newArrayList();
            orders.addAll(acceptedOrders);
            orders.addAll(cancelOrders);
            if (CollUtil.isEmpty(orders)) {
                log.info("上传的美团订单数据为空，酒店: {}", hcode);
                return;
            }
            ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.MEITUAN_PREPAY.getName()).getData();
            if (protocolAgentSimpleRespDTO == null) {
                log.error("Missing required Meituan protocol agent for hotel: {}", hcode);
                return;
            }
            // 2. 构建同步上下文
            SyncContext context = new SyncContext(
                    gcode,
                    hcode,
                    null, // 不再需要API配置
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO()
                            .setGcode(gcode)
                            .setHcode(hcode)
                            .setChannel(ChannelEnum.MEITUAN.getCode())),
                    getLastCheckOutTime(gcode),
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.MEITUAN.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)),
                    protocolAgentSimpleRespDTO.getPaCode()
            );

            // 3. 处理订单
            processOrdersIndependently(orders, context);

            log.info("美团上传订单数据处理完成，酒店: {}", hcode);
        } catch (Exception e) {
            log.error("处理美团上传订单数据失败，酒店: " + hcode, e);
            throw e;
        }
    }

    /**
     * Process each order independently with its own transaction
     */
    private void processOrdersIndependently(List<MeituanOrderDetail> orders, SyncContext context) {
        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();

        for (MeituanOrderDetail order : orders) {
            try {
                OtaOrderSynchronizedSaveVO record = processOrderInTransaction(order, context);
                if (record != null) {
                    syncRecords.add(record);
                }
            } catch (Exception e) {
                log.error("处理美团订单失败，订单ID: {}, 错误: {}",
                        order != null ? order.getOrderId() : "unknown",
                        e.getMessage());
                // Continue with next order
            }
        }

        if (CollUtil.isNotEmpty(syncRecords)) {
            try {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
            } catch (Exception e) {
                log.error("批量创建同步记录失败", e);
                // Optionally retry individual records here
            }
        }
    }

    /**
     * Process a single order within its own transaction
     */
//    @GlobalTransactional(rollbackFor = Exception.class)
    private OtaOrderSynchronizedSaveVO processOrderInTransaction(MeituanOrderDetail order, SyncContext context) {
        // 1. Check if order is already synced
        if (isOrderSynced(order, context.syncedOrders())) {
            log.debug("订单已同步，跳过处理 orderId={}", order.getOrderId());
            return null;
        }

        // 2. Process order by status
        String pmsBookNo = processOrderByStatus(order, context);
        if (pmsBookNo == null) {
            return null;
        }

        // 3. Build sync record
        return buildSyncRecord(order, pmsBookNo, context.syncTime(), context);
    }


    /**
     * 获取最晚离店时间
     * @param gcode 集团代码
     * @return 最晚离店时间
     */
    private String getLastCheckOutTime(String gcode) {
        // 获取最晚离店时间
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO()
                .setGcode(gcode)
                .setHcode(NumberEnum.ZERO.getNumber())
                .setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())).getData();
        if (generalConfig == null) {
            throw new RuntimeException("[订单同步] 获取最晚离店时间失败");
        }
        String lastCheckOutTime = generalConfig.getValue();
        // 检查是否为HH:mm格式的时间
        if (StrUtil.isBlank(lastCheckOutTime) || !lastCheckOutTime.matches("\\d{2}:\\d{2}")) {
            throw new RuntimeException("[订单同步] 最晚离店时间格式错误,集团代码:" + gcode);
        }
        return lastCheckOutTime;
    }


    /**
     * 处理订单列表，同步未处理的订单
     * 此方法首先检查订单是否已同步，然后根据订单状态进行处理，并记录同步结果
     *
     * @param orders 订单详情列表，包含待处理的订单
     * @param context 同步上下文，包含已同步订单和同步时间等信息
     */
    private void processOrders(List<MeituanOrderDetail> orders, SyncContext context) {
        // 初始化同步记录列表，用于存储本次同步成功的订单记录
        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();

        // 遍历订单列表，逐个处理每个订单
        for (MeituanOrderDetail order : orders) {
            try {
                // 1. 检查订单是否已同步
                if (isOrderSynced(order, context.syncedOrders())) {
                    log.debug("订单已同步，跳过处理 orderId={}", order.getOrderId());
                    continue;
                }

                // 2. 根据订单状态处理
                // 处理订单并获取PMS订单号，如果返回null则跳过当前订单
                String pmsBookNo = processOrderByStatus(order, context);
                if (pmsBookNo == null) continue;

                // 3. 记录同步结果
                // 构建同步记录并添加到同步记录列表中
                syncRecords.add(buildSyncRecord(order, pmsBookNo, context.syncTime(), context));
            } catch (Exception e) {
                // 异常处理：记录日志并继续处理下一个订单
                log.error("处理美团订单异常 orderId={}", order.getOrderId(), e);
            }
        }

        // 批量创建同步记录，仅当有成功同步的订单时执行
        if (CollUtil.isNotEmpty(syncRecords)) {
            otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
        }
    }

    /**
     * 根据订单状态处理不同业务逻辑
     */
    private String processOrderByStatus(MeituanOrderDetail order, SyncContext context) {
        return switch (OtaOrderStatusEnum.getEnumByCode(order.getStatus())) {
            case OtaOrderStatusEnum.MEITUAN_ACCEPTED -> // 已接单（新订单）
                    createMeituanBook(order, context);
            case OtaOrderStatusEnum.MEITUAN_CANCELED -> // 已取消
                    cancelOrder(order, context);
            case OtaOrderStatusEnum.MEITUAN_RESCHEDULED -> // 已改签
                    modifyOrder(order, context);
            case null, default -> {
                log.warn("未知订单状态 orderId={}, status={}", order.getOrderId(), order.getStatus());
                yield null;
            }
        };
    }

    /**
     * 创建美团预订订单
     */
    private String createMeituanBook(MeituanOrderDetail order, SyncContext context) {
        // 验证，如果存在与已同步订单，则忽略
        if (isNewOrderSynced(order, context.syncedOrders())) {
            log.debug("订单已同步，跳过处理 hcode={} orderId={}", context.hcode(), order.getOrderId());
            return null;
        }
        try {
            OtaBookOpenReqDTO req = buildBookRequest(order, context);
            CommonResult<OtaBookOpenRespDTO> result = bookingApi.createBook(req);
            if (!result.isSuccess()) {
                log.error("创建美团订单失败 hcode={} orderId={}, msg={}", context.hcode(), order.getOrderId(), result.getMsg());
                otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                        .setGcode(context.gcode())
                        .setHcode(context.hcode())
                        .setPlatform(ChannelEnum.MEITUAN.getCode())
                        .setSyncTime(context.syncTime())
                        .setOtaOrderNo(order.getOrderId())
                        .setOtaOrderState("fail")
                        .setPmsBookNo("0")
                        .setRemark(result.getMsg()));
                // 没有房
                if (result.getCode() == 1009001027) {
                    OTAOrderFailAlertProducer.OTAOrderFailAlertBO otaOrderFailAlertBO = new OTAOrderFailAlertProducer.OTAOrderFailAlertBO();
                    otaOrderFailAlertBO.setHcode(context.hcode());
                    otaOrderFailAlertBO.setChannelName("美团");
                    otaOrderFailAlertBO.setOutOrderNo(order.getOrderId());
                    otaOrderFailAlertBO.setOtaRtName(order.getRoomName());
                    otaOrderFailAlertBO.setPlanCheckinTime(req.getPlanCheckinTime());
                    otaOrderFailAlertBO.setPlanCheckoutTime(req.getPlanCheckoutTime());
                    otaOrderFailAlertBO.setGuestName(req.getGuestName());
                    otaOrderFailAlertBO.setReason(result.getMsg());
                    otaOrderFailAlertProducer.sendOTAOrderFailAlertMessage(otaOrderFailAlertBO);
                }
                return null;
            }
            return result.getData().getPmsResNo();
        } catch (Exception e) {
            log.error("创建美团订单异常 orderId={}", order.getOrderId(), e);
            return null;
        }
    }

    private boolean isNewOrderSynced(MeituanOrderDetail order, List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(syncedOrders)) {
            return false;
        }
        return syncedOrders.stream()
                .anyMatch(synced ->
                        order.getOrderId().equals(synced.getOtaOrderNo()));
    }

    /**
     * 修改订单  TODO
     * @param order
     * @param context
     * @return
     */
    private String modifyOrder(MeituanOrderDetail order, SyncContext context) {
        return null;
    }

    /**
     * 取消订单
     * @param order
     * @param context
     * @return
     */
    private String cancelOrder(MeituanOrderDetail order, SyncContext context) {
        OtaOrderSynchronizedDO syncedOrder = findCanCancelSyncedOrder(order, context.syncedOrders());
        if (syncedOrder == null) {
            log.warn("未找到已同步订单，无法取消 orderId={}", order.getOrderId());
            return null;
        }

        try {
            OtaBookCancelOpenReqDTO req = new OtaBookCancelOpenReqDTO()
                    .setGcode(context.gcode())
                    .setHcode(context.hcode())
                    .setBookNo(syncedOrder.getPmsBookNo());

            CommonResult<?> result = bookingApi.cancleBook(req);
            if (!result.isSuccess()) {
                log.error("取消美团订单失败 orderId={}, msg={}", order.getOrderId(), result.getMsg());
                otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                        .setGcode(context.gcode())
                        .setHcode(context.hcode())
                        .setPlatform(ChannelEnum.MEITUAN.getCode())
                        .setSyncTime(context.syncTime())
                        .setOtaOrderNo(order.getOrderId())
                        .setOtaOrderState("fail")
                        .setRemark(result.getMsg())
                        .setPmsBookNo(syncedOrder.getPmsBookNo())
                );
                return null;
            }
            return syncedOrder.getPmsBookNo();
        } catch (Exception e) {
            log.error("取消美团订单异常 orderId={}", order.getOrderId(), e);
            return null;
        }
    }

    /**
     * 构建同步记录
     */
    private OtaOrderSynchronizedSaveVO buildSyncRecord(
            MeituanOrderDetail order,
            String pmsBookNo,
            long syncTime,
            SyncContext context
    ) {
        return new OtaOrderSynchronizedSaveVO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setPmsBookNo(pmsBookNo)
                .setOtaOrderNo(order.getOrderId())
                .setOtaOrderState(order.getStatus())
                .setSyncTime(syncTime)
                .setPlatform(ChannelEnum.MEITUAN.getCode());
    }

    /**
     * 检查订单是否已同步
     * @param order 美团订单详情
     * @param syncedOrders 已同步订单列表
     * @return 是否已同步
     */
    private boolean isOrderSynced(MeituanOrderDetail order, List<OtaOrderSynchronizedDO> syncedOrders) {
        return syncedOrders.stream()
                .anyMatch(synced ->
                        order.getOrderId().equals(synced.getOtaOrderNo()) &&
                                order.getStatus().equals(synced.getOtaOrderState())
                );
    }

    /**
     * 查找可取消的已同步订单
     * syncedOrders通过order过滤出的订单中，只能存在订单状态为ACCEPTED，不能存在订单状态为CANCELED的订单
     * @param order 美团订单详情
     * @param syncedOrders 已同步的OTA订单列表
     * @return 符合条件的可取消订单，若无则返回null
     */
private OtaOrderSynchronizedDO findCanCancelSyncedOrder(
        MeituanOrderDetail order,
        List<OtaOrderSynchronizedDO> syncedOrders) {
    // 1. 过滤出相同订单号的记录
    List<OtaOrderSynchronizedDO> sameOrderRecords = CollectionUtils.filterList(
            syncedOrders,
            synced -> synced.getOtaOrderNo().equals(order.getOrderId())
    );

    if (CollUtil.isEmpty(sameOrderRecords)) {
        log.debug("[取消订单检查] 订单无同步记录，允许首次取消 orderId={}", order.getOrderId());
        return null;
    }

    // 2. 检查是否存在任何取消相关的记录（成功或失败）
    boolean hasCancelRelatedRecord = sameOrderRecords.stream().anyMatch(synced ->
            synced.getOtaOrderState().equals(OtaOrderStatusEnum.MEITUAN_CANCELED.getCode()) ||
                    "fail".equals(synced.getOtaOrderState())
    );

    if (hasCancelRelatedRecord) {
        log.info("[取消订单检查] 订单已存在取消记录（状态: {}），跳过处理 orderId={}",
            sameOrderRecords.stream()
                .filter(s -> OtaOrderStatusEnum.MEITUAN_CANCELED.getCode().equals(s.getOtaOrderState()) || "fail".equals(s.getOtaOrderState()))
                .findFirst()
                .map(OtaOrderSynchronizedDO::getOtaOrderState)
                .orElse("unknown"),
            order.getOrderId());
        return null;
    }

    // 3. 仅当原订单状态为 ACCEPTED 时允许取消
    Optional<OtaOrderSynchronizedDO> acceptedOrder = sameOrderRecords.stream()
            .filter(synced -> synced.getOtaOrderState().equals(OtaOrderStatusEnum.MEITUAN_ACCEPTED.getCode()))
            .findFirst();

    if (acceptedOrder.isPresent()) {
        log.debug("[取消订单检查] 找到可取消的ACCEPTED订单 orderId={}, pmsBookNo={}",
            order.getOrderId(),
            acceptedOrder.get().getPmsBookNo());
        return acceptedOrder.get();
    } else {
        log.warn("[取消订单检查] 订单状态不匹配，不允许取消 orderId={}, currentStatus={}, expectedStatus=ACCEPTED",
            order.getOrderId(),
            sameOrderRecords.get(0).getOtaOrderState());
        return null;
    }
}



    /**
     * 构建预订请求
     */
    private OtaBookOpenReqDTO buildBookRequest(MeituanOrderDetail meituanOrderDetail, SyncContext context) {
        // 初始化BookSaveReqVO对象并设置基础信息
        OtaBookOpenReqDTO bookSaveReqVO = new OtaBookOpenReqDTO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setChannelCode(ChannelEnum.MEITUAN.getCode())
                .setOrderSource(OrderSrcEnum.AGENT.getCode())
                .setGuestSrcType(GuestSrcTypeEnum.AGENT.getCode())
                .setBookType(OrderTypeEnum.GENERAL.getCode());

        // 获取预计入住和退房时间
        LocalDateTime planCheckInTime = getMeituanCheckInTime(meituanOrderDetail);
        LocalDateTime planCheckoutTime = getMeituanCheckOutTime(meituanOrderDetail, context.lastCheckOutTime());

        // 根据是否存在促销判断是否为钟点房
        if (meituanOrderDetail.getHourRoomTime() != null && meituanOrderDetail.getHourRoomTime() > 0) {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.HOUR_ROOM.getCode());
            bookSaveReqVO.setHourCode(HourRoomEnum.getCodeByValue(meituanOrderDetail.getHourRoomTime()));
            planCheckoutTime = planCheckoutTime.plusHours(meituanOrderDetail.getHourRoomTime().longValue());
        } else {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.ALL_DAY.getCode());
        }

        bookSaveReqVO.setPlanCheckinTime(planCheckInTime).setPlanCheckoutTime(planCheckoutTime);

        // 设置客人信息和联系方式
        setGuestInfo(bookSaveReqVO, meituanOrderDetail, context);

        String outOrderRemark = String.format("|订单ID[%s]|", meituanOrderDetail.getOrderId());
        bookSaveReqVO.setOutOrderRemark(outOrderRemark);

        // 处理订单批次信息
        List<OtaBookOpenReqDTO.Batch> batchList = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch batch = new OtaBookOpenReqDTO.Batch();

        String batchNo = generateBatchNo(planCheckInTime, planCheckoutTime);
        long days = ChronoUnit.DAYS.between(planCheckInTime.toLocalDate(), planCheckoutTime.toLocalDate());

        batch.setBatchNo(batchNo)
             .setDays((int) days)
             .setPlanCheckinTime(planCheckInTime)
             .setPlanCheckoutTime(planCheckoutTime);

        // 处理预订房型信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = processBookRoomTypes(meituanOrderDetail, context, days);
        batch.setBookRoomTypes(bookRoomTypes);
        batchList.add(batch);
        bookSaveReqVO.setBatches(batchList);

        return bookSaveReqVO;
    }

    // 提取设置客人信息的方法
    private void setGuestInfo(OtaBookOpenReqDTO bookSaveReqVO, MeituanOrderDetail meituanOrderDetail, SyncContext context) {
        bookSaveReqVO.setGuestCode(context.paCode())
                     .setGuestName(ProtocolAgentEnum.MEITUAN_PREPAY.getName());

        if (CollUtil.isNotEmpty(meituanOrderDetail.getContacts())) {
            String contactName = meituanOrderDetail.getContacts().getFirst().getName();
            bookSaveReqVO.setContact(contactName)
                         .setCheckinPerson(contactName);
        } else {
            log.warn("美团订单无联系人信息，订单ID：{}", meituanOrderDetail.getOrderId());
            bookSaveReqVO.setContact("")
                         .setCheckinPerson("");
        }
        bookSaveReqVO.setIsSendSms(BooleanEnum.FALSE.getValue())
                     .setOutOrderNo(meituanOrderDetail.getOrderId());
    }

    // 提取生成批次编号的方法
    private String generateBatchNo(LocalDateTime checkIn, LocalDateTime checkOut) {
        return DateUtil.format(checkIn, "yyyy-MM-dd") + "/" + DateUtil.format(checkOut, "yyyy-MM-dd");
    }

    // 提取处理房型信息的方法
    private List<OtaBookOpenReqDTO.Batch.BookRoomType> processBookRoomTypes(
            MeituanOrderDetail meituanOrderDetail, SyncContext context, long days) {
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch.BookRoomType bookRoomType = new OtaBookOpenReqDTO.Batch.BookRoomType();

        Long roomID = meituanOrderDetail.getRoomId();
        RoomTypeRefDO roomTypeRefDO = context.roomTypeRefs().stream()
                .filter(ref -> Objects.equals(ref.getOtaRoomTypeCode(), roomID.toString())
                        && ChannelEnum.MEITUAN.getCode().equals(ref.getChannel()))
                .findFirst()
                .orElse(null);

        if (roomTypeRefDO != null) {
            bookRoomType.setRtCode(roomTypeRefDO.getRoomTypeCode());
        } else {
            log.error("美团订单房型代码不存在，酒店代码：{} 房型代码：{} 订单ID：{}", context.hcode(), roomID, meituanOrderDetail.getOrderId());
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "美团订单房型代码不存在，酒店代码:" + context.hcode(),
                    "房型代码: " + roomID + "订单ID：" + meituanOrderDetail.getOrderId(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            throw exception(OTA_ROOM_TYPE_REF_NOT_FOUND, meituanOrderDetail.getOrderId(), roomID);
        }

        bookRoomType.setBkNum(0);
        bookRoomType.setRoomNum(meituanOrderDetail.getRoomCount());
        List<PriceInfo> priceInfos = meituanOrderDetail.getPriceInfo().stream().distinct().toList();
        // 处理每日价格信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice> dayPrices = CollUtil.newArrayList();
        priceInfos.forEach(orderRoomPrice -> {
            OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice dayPrice = new OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice();
            String dateString = orderRoomPrice.getDateString();
            String dateOnly = dateString.split(" ")[0]; // 提取日期部分（去掉时间部分）
            LocalDate priceDate = LocalDate.parse(dateOnly, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dayPrice.setPriceDate(priceDate);
            dayPrice.setWeek(priceDate.getDayOfWeek().getValue());

            long sellPriceFen = orderRoomPrice.getPrice();
            long roomPriceFen = orderRoomPrice.getFloorPrice();

            dayPrice.setPrice(sellPriceFen);      // 售价（分）
            dayPrice.setVipPrice(roomPriceFen);   // VIP 价格（分）
            dayPrices.add(dayPrice);
        });

        bookRoomType.setDayPrices(dayPrices);
        bookRoomTypes.add(bookRoomType);
        return bookRoomTypes;
    }



    /**
     * 获取美团订单入住时间
     * @param meituanOrderDetail 美团订单详情
     * @return LocalDateTime
     */
    private LocalDateTime getMeituanCheckInTime(MeituanOrderDetail meituanOrderDetail) {
        // 1. 获取原始到达时间字符串（格式如："2025-06-07 14:00:00"）
        String arriveTimeStr = meituanOrderDetail.getCheckInDateString();

        // 2. 分割日期和时间部分
        String[] timeParts = arriveTimeStr.split(" ");
        if (timeParts.length != 2) {
            throw new IllegalArgumentException("无效的时间格式：" + arriveTimeStr);
        }

        // 3. 处理特殊时间：00:00或00:00:00替换为12:00
        String timePart = timeParts[1];
        if ("00:00".equals(timePart) || "00:00:00".equals(timePart)) {
            timePart = "12:00";
        }
        // 4. 如果时间包含秒数（如14:00:00），截取到分钟部分（14:00）
        else if (timePart.length() > 5) {
            timePart = timePart.substring(0, 5);
        }

        // 5. 组合并解析为LocalDateTime
        return LocalDateTime.parse(
                timeParts[0] + " " + timePart,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        );
    }

    /**
     * 获取美团订单退房时间
     * @param meituanOrderDetail 美团订单详情
     * @param lastCheckOutTime 默认最晚退房时间
     * @return LocalDateTime
     */
    private LocalDateTime getMeituanCheckOutTime(MeituanOrderDetail meituanOrderDetail, String lastCheckOutTime) {
        // 1. 参数校验
        if (meituanOrderDetail == null || lastCheckOutTime == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 2. 获取退房日期字符串（格式示例："2025-06-10 12:00:00"）
        String leaveTimeStr = meituanOrderDetail.getCheckOutDateString();
        if (StrUtil.isBlank(leaveTimeStr)) {
            throw new IllegalArgumentException("退房日期字符串为空");
        }

        // 3. 分割日期和时间部分
        String[] timeParts = leaveTimeStr.split(" ");
        if (timeParts.length != 2) {
            throw new IllegalArgumentException("无效的退房时间格式：" + leaveTimeStr);
        }

        // 4. 处理时间部分
        String timePart = timeParts[1];

        // 情况1：时间为00:00或00:00:00时，使用配置的lastCheckOutTime（如"14:00"）
        if ("00:00".equals(timePart) || "00:00:00".equals(timePart)) {
            timePart = lastCheckOutTime;
        }
        // 情况2：时间包含秒数时（如12:00:00），截取到分钟部分（12:00）
        else if (timePart.length() > 5) {
            timePart = timePart.substring(0, 5);
        }

        // 5. 格式化和解析
        try {
            return LocalDateTime.parse(
                    timeParts[0] + " " + timePart,
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
            );
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("退房时间解析失败：" + timeParts[0] + " " + timePart, e);
        }
    }

    private List<MeituanOrderDetail> fetchOrders(SyncContext context) {
        try {
            // 提取公共 headers，避免重复调用
            Map<String, String> headers = OtaApiUtils.parseHeaders(context.orderListApi().getHeaders());

            // 第一次获取已接受订单
            String acceptedUrl = buildMeituanOrderListUrl(context.orderListApi(), OtaOrderStatusEnum.MEITUAN_ACCEPTED.getCode());
            String acceptedResponse = HttpUtils.get(acceptedUrl, headers);
            List<MeituanOrderDetail> acceptedOrders = MeituanOrderParser.parseOrderList(acceptedResponse);

            // 第二次获取已取消订单
            String cancelledUrl = buildMeituanOrderListUrl(context.orderListApi(), OtaOrderStatusEnum.MEITUAN_CANCELED.getCode());
            String cancelledResponse = HttpUtils.get(cancelledUrl, headers);
            List<MeituanOrderDetail> cancelledOrders = MeituanOrderParser.parseOrderList(cancelledResponse);

            // 合并两个订单列表
            List<MeituanOrderDetail> orders = CollUtil.newArrayList();
            orders.addAll(acceptedOrders);
            orders.addAll(cancelledOrders);
            return orders;
        } catch (Exception e) {
            log.error("Unexpected error during fetching meituan orders", e);
            return Collections.emptyList();
        }
    }

    /**
     * 构建美团订单列表的URL
     * 该方法根据OTA API配置动态生成查询订单列表的URL，主要通过设置时间参数来实现
     *
     * @param api OTA API配置对象，包含原始URL等信息
     * @param orderStatus 订单状态参数，用于设置查询的订单状态 CANCELED:已取消 ACCEPTED：已接受
     * @return 返回构建好的带有时间参数的URL
     */
    private String buildMeituanOrderListUrl(OtaApiDO api, String orderStatus) {
        // 解析原始URL并动态设置时间参数
        String originalUrl = api.getUrl();
        Map<String, String> queryParams = parseQueryParams(originalUrl);

        // 设置动态时间范围（默认查询最近30天）
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDate = now.minusDays(30);

        // 转换为美团API需要的时间戳格式（毫秒）
        long startTime = startDate.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long endTime = now.toInstant(ZoneOffset.of("+8")).toEpochMilli();

        // 将时间参数添加到查询参数中
        queryParams.put("startTime", String.valueOf(startTime));
        queryParams.put("endTime", String.valueOf(endTime));
        queryParams.put("limit", "20");
        queryParams.put("orderStatus", orderStatus);

        // 重新构建URL并返回
        return rebuildUrlWithParams(originalUrl.split("\\?")[0], queryParams);
    }


    /**
     * 解析URL中的查询参数
     * 此方法接收一个URL字符串作为输入，提取并解析查询参数部分，将其转换为键值对映射
     * 如果URL中不包含查询参数，则返回一个空的映射
     *
     * @param url 包含查询参数的URL字符串
     * @return 包含查询参数键值对的映射
     */
    private Map<String, String> parseQueryParams(String url) {
        Map<String, String> params = new HashMap<>();
        // 检查URL是否包含查询参数
        if (url.contains("?")) {
            // 提取查询参数部分
            String query = url.split("\\?")[1];
            // 分割查询参数中的每个键值对
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                // 分割键值对
                String[] kv = pair.split("=", 2);
                // 只有当键值对完整时，才添加到映射中
                if (kv.length == 2) {
                    params.put(kv[0], kv[1]);
                }
            }
        }
        // 返回解析后的查询参数映射
        return params;
    }

    /**
     * 将基础URL和参数组合成完整的URL字符串
     * 此方法接受一个基础URL和一个参数映射，然后将这些参数附加到URL后面，用于构建HTTP请求
     * 如果存在多个参数，它们将通过"&"符号分隔参数键值对按照"key=value"的格式附加到URL后面
     * 如果参数映射为空，则返回原始的基础URL
     *
     * @param baseUrl 基础URL字符串，例如 "http://example.com/page"
     * @param params 包含要附加到URL的参数的映射如果映射为空，则不对URL进行修改
     * @return 包含参数的完整URL字符串
     */
    private static String rebuildUrlWithParams(String baseUrl, Map<String, String> params) {
        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        if (!params.isEmpty()) {
            urlBuilder.append("?");
            boolean first = true;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (!first) {
                    urlBuilder.append("&");
                }
                urlBuilder.append(entry.getKey()).append("=").append(entry.getValue());
                first = false;
            }
        }
        return urlBuilder.toString();
    }

    /**
     * 定义了一个名为 SyncContext 的私有不可变记录类（record），用于封装与OTA订单同步相关的上下文数据，包括房型编码、API配置、已同步订单列表等信息
     * @param gcode
     * @param hcode
     * @param orderListApi 获取订单列表的api
     * @param roomTypeRefs 房型关联列表
     * @param lastCheckOutTime 最晚退房时间
     * @param syncTime 同步时间
     * @param syncedOrders 已同步订单列表
     * @param paCode 中介代码
     */
    private record SyncContext(
            String gcode,
            String hcode,
            OtaApiDO orderListApi,
            List<RoomTypeRefDO> roomTypeRefs,
            String lastCheckOutTime,
            Long syncTime,
            List<OtaOrderSynchronizedDO> syncedOrders,
            String paCode
    ) {}
}
