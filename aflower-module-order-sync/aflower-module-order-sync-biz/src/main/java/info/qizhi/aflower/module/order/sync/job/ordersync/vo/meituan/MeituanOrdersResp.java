package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "美团订单同步响应")
public class MeituanOrdersResp {
    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "信息")
    private String message;

    @Schema(description = "数据")
    private Dt data;

    @Data
    public static class Dt {
        @Schema(description = "总数")
        private Integer total;

        @Schema(description = "数据")
        private List<MeituanOrderDetail> results;
    }

}
