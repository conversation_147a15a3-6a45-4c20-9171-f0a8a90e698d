package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "发票信息")
public class Invoice {
    @Schema(description = "信息")
    private String info;

    @Schema(description = "每日金额")
    private List<Object> dailyAmount;

    @Schema(description = "发票金额")
    private Object invoiceAmount;

    @Schema(description = "货币")
    private Object currency;
}
