package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import jakarta.validation.Valid;

import java.util.List;


/**
 * OTA房型关联表;ota房型可以关联多个酒店房型 Service 接口
 *
 * <AUTHOR>
 */
public interface RoomTypeRefService {

    /**
     * 获取OTA房型列表
     * @param reqVO
     * @return
     */
    List<OtaRoomTypeRespVO> getRemoteOtaRoomTypeList(RoomTypeRefReqVO reqVO);

    /**
     * 创建OTA房型关联表;ota房型可以关联多个酒店房型
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createRoomTypeRef(@Valid RoomTypeRefSaveReqVO createReqVO);

    /**
     * 更新OTA房型关联表;ota房型可以关联多个酒店房型
     *
     * @param updateReqVO 更新信息
     */
    void updateRoomTypeRef(@Valid RoomTypeRefSaveReqVO updateReqVO);

    /**
     * 批量更新OTA房型关联表;ota房型可以关联多个酒店房型
     * @param roomTypeRefDOList 
     */
    void updateRoomTypeRefList(@Valid List<RoomTypeRefDO> roomTypeRefDOList);

    /**
     * 删除OTA房型关联表;ota房型可以关联多个酒店房型
     *
     * @param id 编号
     */
    void deleteRoomTypeRef(Long id);

    /**
     * 获得OTA房型关联表;ota房型可以关联多个酒店房型
     *
     * @param id 编号
     * @return OTA房型关联表;ota房型可以关联多个酒店房型
     */
    RoomTypeRefDO getRoomTypeRef(Long id);

    /**
     * 根据产品代码获取对应的关联关系
     * @param reqVO
     * @return
     */
    RoomTypeRefDO getRoomTypeRefByProductCode(RoomTypeRefReq2VO reqVO);

    /**
     * 删除OTA房型关联表;ota房型可以关联多个酒店房型
     * @param deleteReqVO
     */
    void deleteRoomTypeRef(RoomTypeRefDeleteReqVO deleteReqVO);

    /**
     * 获得OTA房型关联表;ota房型可以关联多个酒店房型列表
     *
     * @param reqVO 查询
     * @return OTA房型关联表;ota房型可以关联多个酒店房型分列表
     */
    List<RoomTypeRefDO> getRoomTypeRefList(RoomTypeRefReqVO reqVO);

}