package info.qizhi.aflower.module.order.sync.dal.dataobject.ota;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import info.qizhi.aflower.framework.mybatis.core.dataobject.BaseDO;
import lombok.*;

/**
 * OTA_api DO
 *
 * <AUTHOR>
 */
@TableName("ota_order_synchronized")
@KeySequence("ota_order_synchronized_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtaOrderSynchronizedDO extends BaseDO {

    /**
     * id
     */
    @TableId
    private Long id;
    /**
     * 集团代码
     */
    private String gcode;
    /**
     * 门店代码
     */
    private String hcode;
    /**
     * PMS预订单号
     */
    private String pmsBookNo;
    /**
     * OTA订单号
     */
    private String otaOrderNo;
    /**
     * OTA订单状态
     */
    private String otaOrderState;

    /**
     * 平台 ctrip:携程 meituan:美团
     */
    private String platform;
    /**
     * 备注
     */
    private String remark;

    /**
     * 同步时间
     */
    private Long syncTime;

}