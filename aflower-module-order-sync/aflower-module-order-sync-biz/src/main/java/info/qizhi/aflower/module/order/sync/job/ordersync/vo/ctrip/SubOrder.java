package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "子订单")
public class SubOrder {
    @Schema(description = "携程来源类型")
    private String ctripSourceType;

    @Schema(description = "EBK订单ID")
    private Long ebkOrderID;

    @Schema(description = "表单日期时间戳")
    private Long formDate;

    @Schema(description = "表单日期字符串")
    private Object formDateStr;

    @Schema(description = "表单日期显示")
    private String formDateDisplay;

    @Schema(description = "表单ID")
    private Long formID;

    @Schema(description = "订单类型")
    private String orderType;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "携程来源类型显示")
    private String ctripSourceTypeDisplay;

    @Schema(description = "原始订单ID")
    private Long originalOrderID;

    @Schema(description = "是否无用")
    private Boolean isUseless;

    @Schema(description = "是否未处理")
    private Boolean isUnprocess;

    @Schema(description = "是否已选择")
    private Boolean isSelected;

    @Schema(description = "是否顶部")
    private Boolean isTop;

    @Schema(description = "是否底部")
    private Boolean isBottom;

    @Schema(description = "引用超时修改阶段")
    private Object refOvertimeModifyStage;

    @Schema(description = "引用订单ID")
    private Object refOrderID;

    @Schema(description = "Token")
    private String Token;

    @Schema(description = "酒店ID")
    private Integer HotelId;

    @Schema(description = "表单类型")
    private String FormType;
}
