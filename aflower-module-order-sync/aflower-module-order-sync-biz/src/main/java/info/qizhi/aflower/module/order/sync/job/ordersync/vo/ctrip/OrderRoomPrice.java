package info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "订单房间价格")
public class OrderRoomPrice {
    @Schema(description = "GUID货币")
    private Object guidCurrency;

//    @Schema(description = "国内每日价格悬停HTML")
//    private String domesticDailyPriceHoverHtml;

    @Schema(description = "原始入住日期")
    private String originLivingDate;

    @Schema(description = "餐食信息")
    private String mealInfo;

    @Schema(description = "销售价格")
    private String sellPrice;

    @Schema(description = "成本价格")
    private String costPrice;

    @Schema(description = "原始成本价格")
    private String originCostPrice;

    @Schema(description = "促销项目")
    private List<PromotionItem> promotionItems;

    @Schema(description = "早餐权益")
    private Integer rightBreakFirst;

    @Schema(description = "原始销售价格")
    private String originSellPrice;

//    @Schema(description = "每日价格悬停HTML")
//    private String dailyPriceHoverHtml;

    @Schema(description = "房间价格")
    private String roomPrice;

    @Schema(description = "入住日期")
    private String livingDate;

    @Schema(description = "早餐数量")
    private Integer breakfast;

    @Schema(description = "原始房间价格")
    private String originRoomPrice;

    @Schema(description = "订单儿童价格信息")
    private Object orderChildPriceInfos;

    @Schema(description = "查看儿童价格信息")
    private Object viewChildPriceInfos;

    @Schema(description = "总每日价格")
    private Object totalDailyPrice;

    @Schema(description = "儿童总每日价格")
    private Object childTotalDailyPrice;

    @Schema(description = "货币")
    private String currency;
}
