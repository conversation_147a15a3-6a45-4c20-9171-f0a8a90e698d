package info.qizhi.aflower.module.order.sync.service.ota;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.ChannelEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.tenant.core.util.TenantUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaRoomTypeDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.dal.mysql.ota.OtaRoomTypeMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.GET_ROOM_TYPE_FAIL;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.UNSUPPORTED_CHANNEL;

/**
 * OTA房型表;ota房型可以关联多个酒店房型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class OtaRoomTypeServiceImpl implements OtaRoomTypeService {

    @Resource
    private OtaRoomTypeMapper otaRoomTypeMapper;
    @Lazy
    @Resource
    private RoomTypeRefService roomTypeRefService;

    /**
     * 根据JSON信息保存OTA房型信息
     *
     * @param reqVO 包含OTA房型保存请求信息的对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOtaRoomTypesByJson(OtaRoomTypeSaveReqVO reqVO) {
        // 解析JSON信息，获取新的OTA房型列表
        List<OtaRoomTypeDO> newOtaRoomTypeLis = parseRoomTypeJson(reqVO.getGcode(), reqVO.getHcode(), reqVO.getJson(), reqVO.getChannel());
        // 如果解析后的房型列表不为空，则进一步处理
        if (CollUtil.isNotEmpty(newOtaRoomTypeLis)) {
            // 使用租户工具执行后续操作，确保操作在正确的租户环境下执行
            TenantUtils.execute(Long.parseLong(reqVO.getGcode()), () -> {
                // 先删除旧数据
                List<OtaRoomTypeDO> oldOtaRoomTypeList = otaRoomTypeMapper.selectList(OtaRoomTypeReqVO.builder().gcode(reqVO.getGcode()).hcode(reqVO.getHcode()).channel(reqVO.getChannel()).build());
                // 如果存在旧的房型数据，则进行删除操作
                if (CollUtil.isNotEmpty(oldOtaRoomTypeList)) {
                    otaRoomTypeMapper.deleteBatchIds(oldOtaRoomTypeList);
                }
                // 批量插入新的OTA房型数据
                otaRoomTypeMapper.insertBatch(newOtaRoomTypeLis);

                if (ChannelEnum.CTRIP.getCode().equals(reqVO.getChannel()) || ChannelEnum.JD.getCode().equals(reqVO.getChannel())) {
                    List<RoomTypeRefDO> roomTypeRefList = roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()));
                    // 关联房型
                    if (CollUtil.isNotEmpty(roomTypeRefList)) {
                        linkHotelRoomType(newOtaRoomTypeLis, roomTypeRefList, reqVO.getChannel());
                    }
                }
            });
        }
    }


    /**
     * 这段代码的功能是：根据 OTA 房型数据与已有的房型关联信息进行匹配，更新关联表中的房型名称和产品信息。
     * 具体逻辑如下：
     * 筛选渠道匹配的房型关联数据（channelRoomTypeRefList）。
     * 构建 OTA 房型编码到关联对象的映射表（roomTypeRefMap）。
     * 遍历 OTA 房型列表：
     * 若该 OTA 房型在关联表中存在，则创建或更新对应的 RoomTypeRefDO 对象。
     * 同步更新 OTA 产品信息。
     * 批量更新数据库中的房型关联信息（调用 updateRoomTypeRefList）。
     * @param newOtaRoomTypeLis OTA 房型列表
     * @param roomTypeRefList 已有的房型关联列表
     * @param channel 渠道标识
     */
    private void linkHotelRoomType(List<OtaRoomTypeDO> newOtaRoomTypeLis, List<RoomTypeRefDO> roomTypeRefList, String channel) {
        // 筛选出与当前渠道匹配的房型关联数据
        List<RoomTypeRefDO> channelRoomTypeRefList = CollectionUtils.filterList(roomTypeRefList, roomTypeRefDO -> channel.equals(roomTypeRefDO.getChannel()));
        // 如果筛选结果为空，则直接返回
        if (CollUtil.isEmpty(channelRoomTypeRefList)) {
            return;
        }
        // 构建 OTA 房型编码到关联对象的映射表
        Map<String, RoomTypeRefDO> roomTypeRefMap = CollectionUtils.convertMap(channelRoomTypeRefList, RoomTypeRefDO::getOtaRoomTypeCode);
        List<RoomTypeRefDO> newRoomTypeRefList = new ArrayList<>();
        // 遍历OTA房型列表,遍历酒店房型关联列表,如果酒店房型关联列表中存在该OTA房型，则进行关联
        for (OtaRoomTypeDO otaRoomTypeDO : newOtaRoomTypeLis) {
            List<OtaRoomTypeDO.OtaProduct> otaProducts = otaRoomTypeDO.getOtaProducts();
            // 如果 OTA 产品信息为空，则跳过当前房型
            if (CollUtil.isEmpty(otaProducts)) {
                continue;
            }
            // 如果当前 OTA 房型在关联表中已存在，则创建新的关联对象以更新信息
            if (roomTypeRefMap.containsKey(otaRoomTypeDO.getOtaRoomTypeCode())) {
                RoomTypeRefDO roomTypeRefDO = roomTypeRefMap.get(otaRoomTypeDO.getOtaRoomTypeCode());
                RoomTypeRefDO newRoomTypeRefDO = RoomTypeRefDO.builder()
                        .id(roomTypeRefDO.getId())
                        .otaRoomTypeName(otaRoomTypeDO.getOtaRoomTypeName())
                        .build();
                List<RoomTypeRefDO.OtaProduct> newOtaProducts = new ArrayList<>();
                // 同步更新 OTA 产品信息
                for (OtaRoomTypeDO.OtaProduct otaProduct : otaProducts) {
                    newOtaProducts.add(new RoomTypeRefDO.OtaProduct().setProductCode(otaProduct.getProductCode()).setProductName(otaProduct.getProductName()));
                }
                newRoomTypeRefDO.setOtaProducts(newOtaProducts);
                newRoomTypeRefList.add(newRoomTypeRefDO);
            }
        }
        // 如果有新的房型关联信息，则批量更新到数据库
        if (CollUtil.isNotEmpty(newRoomTypeRefList)) {
            roomTypeRefService.updateRoomTypeRefList(newRoomTypeRefList);
        }
    }

    private List<OtaRoomTypeDO> parseRoomTypeJson(String gcode, String hcode, String json, String channelCode) {
        ChannelEnum channelEnum = ChannelEnum.getChannelEnumByCode(channelCode);
        if (channelEnum == null) {
            throw exception(UNSUPPORTED_CHANNEL);
        }

        return switch (channelEnum) {
            case MEITUAN -> parseMeituanRoomTypeJson(gcode, hcode, json, channelCode);
            case CTRIP -> parseCtripRoomTypeJson(gcode, hcode, json, channelCode);
            case TIKTOK -> parseTitokRoomTypeJson(gcode, hcode, json, channelCode);
            default -> throw exception(UNSUPPORTED_CHANNEL);
        };
    }

    /**
     * 解析抖音房型数据
     *
     * @param gcode       宾馆代码
     * @param hcode       酒店代码
     * @param json        房型数据的JSON字符串
     * @param channelCode 渠道代码
     * @return 解析后的房型列表
     * @throws Exception 如果解析失败则抛出异常
     */
    private List<OtaRoomTypeDO> parseTitokRoomTypeJson(String gcode, String hcode, String json, String channelCode) {
        // Parse the JSON string into a JSON object
        JSONObject jsonObject = JSONUtil.parseObj(json);

        if (jsonObject.getInt("status_code") != 0) {
            throw exception(GET_ROOM_TYPE_FAIL, jsonObject.getStr("status_msg"));
        }

        if (!jsonObject.containsKey("rooms")) {
            return Collections.emptyList();
        }

        // Get the rooms array
        JSONArray roomsArray = jsonObject.getJSONArray("rooms");
        if (roomsArray == null || roomsArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeDO> roomTypeRefList = new ArrayList<>();

        // Process each room in the array
        for (int i = 0; i < roomsArray.size(); i++) {
            JSONObject roomObject = roomsArray.getJSONObject(i);
            if (roomObject == null) {
                continue;
            }

            OtaRoomTypeDO roomTypeRef = new OtaRoomTypeDO();
            roomTypeRef.setGcode(gcode).setHcode(hcode).setChannel(channelCode);
            // Set basic room type information
            roomTypeRef.setOtaRoomTypeCode(roomObject.getStr("physical_room_id"));
            roomTypeRef.setOtaRoomTypeName(roomObject.getStr("physical_room_name"));
            roomTypeRefList.add(roomTypeRef);
        }

        return roomTypeRefList;
    }

    private List<OtaRoomTypeDO> parseCtripRoomTypeJson(String gcode, String hcode, String json, String channelCode) {
        JSONObject jsonObject = JSONUtil.parseObj(json);
        if (jsonObject.getInt("code") != 200) {
            throw exception(GET_ROOM_TYPE_FAIL, jsonObject.getStr("message"));
        }
        if (!jsonObject.containsKey("data")) {
            return Collections.emptyList();
        }

        JSONArray dataArray = jsonObject.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeDO> roomTypeRefList = new ArrayList<>();

        // Process each room type in the array
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject roomTypeObject = dataArray.getJSONObject(i);
            if (roomTypeObject == null) {
                continue;
            }

            // Create a base RoomTypeRefDO with basic room type info
            OtaRoomTypeDO baseRoomType = new OtaRoomTypeDO();
            baseRoomType.setGcode(gcode).setHcode(hcode).setChannel(channelCode);
            baseRoomType.setOtaRoomTypeCode(roomTypeObject.getStr("basicRoomTypeID"));
            baseRoomType.setOtaRoomTypeName(HtmlUtil.unescape(roomTypeObject.getStr("roomName")));
            List<OtaRoomTypeDO.OtaProduct> otaProducts = new ArrayList<>();
            // Process roomInfos (rate plans)
            JSONArray roomInfos = roomTypeObject.getJSONArray("roomInfos");
            if (roomInfos != null && !roomInfos.isEmpty()) {
                for (int j = 0; j < roomInfos.size(); j++) {
                    JSONObject roomInfo = roomInfos.getJSONObject(j);
                    if (roomInfo == null) {
                        continue;
                    }

                    // Create product info for this rate plan
                    OtaRoomTypeDO.OtaProduct product = new OtaRoomTypeDO.OtaProduct();
                    product.setProductCode(roomInfo.getStr("roomTypeID"));
                    product.setProductName(roomInfo.getStr("roomNameDesc"));

                    otaProducts.add(product);
                }
            }
            baseRoomType.setOtaProducts(otaProducts);

            roomTypeRefList.add(baseRoomType);
        }

        return roomTypeRefList;
    }

    /**
     * 解析美团房型信息
     *
     * @param json
     * @return
     */
    private List<OtaRoomTypeDO> parseMeituanRoomTypeJson(String gcode, String hcode, String json, String channelCode) {
        JSONObject jsonObject = JSONUtil.parseObj(json);
        if (jsonObject.getInt("status") != 0 || !jsonObject.containsKey("data")) {
            return Collections.emptyList();
        }

        JSONArray dataArray = jsonObject.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeDO> roomTypeRefList = new ArrayList<>();

        // Process each room type in the array
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject roomTypeObject = dataArray.getJSONObject(i);
            if (roomTypeObject == null) {
                continue; // Skip inactive room types
            }

            OtaRoomTypeDO roomTypeRef = new OtaRoomTypeDO();
            roomTypeRef.setGcode(gcode).setHcode(hcode).setChannel(channelCode);
            roomTypeRef.setOtaRoomTypeCode(roomTypeObject.getStr("roomId"));
            roomTypeRef.setOtaRoomTypeName(roomTypeObject.getStr("roomName"));

            roomTypeRefList.add(roomTypeRef);
        }

        return roomTypeRefList;
    }

    @Override
    public List<OtaRoomTypeDO> getOtaRoomTypeList(OtaRoomTypeReqVO reqVO) {
        return otaRoomTypeMapper.selectList(reqVO);
    }
}