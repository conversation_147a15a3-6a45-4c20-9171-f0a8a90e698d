package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaRoomTypeDO;

import java.util.List;


/**
 * OTA房型关表Service 接口
 *
 * <AUTHOR>
 */
public interface OtaRoomTypeService {

    /**
     * 保存OTA房型
     * @param reqVO
     */
    void saveOtaRoomTypesByJson(OtaRoomTypeSaveReqVO reqVO);


    /**
     * 获得OTA房型表
     *
     * @param reqVO 查询
     * @return OTA房型表
     */
    List<OtaRoomTypeDO> getOtaRoomTypeList(OtaRoomTypeReqVO reqVO);

}