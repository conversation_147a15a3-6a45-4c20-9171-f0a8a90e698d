package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.framework.utils.HttpUtils;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.parser.TikTokOrderParser;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.tiktok.TikTokOrderDetail;
import info.qizhi.aflower.module.order.sync.mq.producer.OTAOrderFailAlertProducer;
import info.qizhi.aflower.module.order.sync.service.ota.OtaOrderSynchronizedService;
import info.qizhi.aflower.module.order.sync.service.ota.RoomTypeRefService;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookCancelOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenRespDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_ROOM_TYPE_REF_NOT_FOUND;

@Service
@Slf4j
public class TiktokSyncStrategy implements OtaSyncStrategy {

    @Resource
    private RoomTypeRefService roomTypeRefService;
    @Resource
    private BookingApi bookingApi;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private OtaOrderSynchronizedService otaOrderSynchronizedService;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private OTAOrderFailAlertProducer otaOrderFailAlertProducer;

    @Override
    public void syncOrders(ServiceIntegrationRespDTO service,
                           List<OtaApiDO> otaApiList,
                           String gcode,
                           String hcode,
                           String lastCheckOutTime) {
        try {
            log.info("开始同步抖音订单:{}", hcode);

            // 1. 获取抖音订单列表接口
            OtaApiDO douyinApi = OtaApiUtils.findApi(otaApiList, OtaMethodTypeEnum.ORDER_LIST, ChannelEnum.TIKTOK);
            if (douyinApi == null) {
                log.error("抖音订单列表接口不存在:{}", hcode);
                return;
            }
            ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.TIKTOK_PREPAY.getName()).getData();
            if (protocolAgentSimpleRespDTO == null) {
                log.error("Missing required Meituan protocol agent for hotel: {}", hcode);
                return;
            }
            // 2. 构建请求体
            SyncContext context = new SyncContext(
                    gcode,
                    hcode,
                    douyinApi,
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(gcode).setHcode(hcode).setChannel(ChannelEnum.TIKTOK.getCode())),
                    lastCheckOutTime,
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.TIKTOK.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)),
                    protocolAgentSimpleRespDTO.getPaCode()
            );
            // 3. 获取订单
            List<TikTokOrderDetail> orderList = fetchOrders(context);
            if (CollUtil.isEmpty(orderList)) {
                log.info("没有抖音订单: {}", hcode);
                return;
            }
            // 4. 处理订单
            processOrdersIndependently(orderList, context);
            log.info("同步抖音订单完成: {}", hcode);
        } catch (Exception e) {
            log.error("同步订单异常", e);
            throw e;
        }
    }

    /**
     * 处理上传的JSON数据，该数据包含抖音订单信息
     * 此方法解析接收到的JSON数据，区分接受和取消的订单，并进一步处理这些订单
     *
     * @param gcode        酒店全球代码
     * @param hcode        酒店本地代码
     * @param acceptedJson 接受的订单JSON字符串
     * @param cancelJson   取消的订单JSON字符串
     * @param jsonList     上传的JSON数据列表，此参数未在方法中使用
     */
    @Override
    public void processUploadedJson(String gcode, String hcode, String acceptedJson, String cancelJson, List<String> jsonList) {
        try {
            // 记录处理抖音上传订单数据的开始日志
            log.info("处理抖音上传订单数据: {}", hcode);

            // 解析接受的订单JSON数据
            List<TikTokOrderDetail> acceptedOrders = TikTokOrderParser.parseOrderList(hcode, acceptedJson);
            // 解析取消的订单JSON数据
            List<TikTokOrderDetail> cancelOrders = TikTokOrderParser.parseOrderList(hcode, cancelJson);

            // 创建一个列表来存储所有解析的订单，包括接受和取消的订单
            List<TikTokOrderDetail> allOrders = CollUtil.newArrayList();
            allOrders.addAll(acceptedOrders);
            allOrders.addAll(cancelOrders);

            // 如果没有订单数据，则记录日志并返回
            if (CollUtil.isEmpty(allOrders)) {
                log.info("抖音上传订单数据为空，酒店: {}", hcode);
                return;
            }
            ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.TIKTOK_PREPAY.getName()).getData();
            if (protocolAgentSimpleRespDTO == null) {
                log.error("Missing required Meituan protocol agent for hotel: {}", hcode);
                return;
            }
            // 创建同步上下文对象，包含同步所需的信息和设置
            TiktokSyncStrategy.SyncContext context = new TiktokSyncStrategy.SyncContext(
                    gcode,
                    hcode,
                    null,
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(gcode).setHcode(hcode).setChannel(ChannelEnum.TIKTOK.getCode())),
                    getLastCheckOutTime(gcode),
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.TIKTOK.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)
                    ),
                    protocolAgentSimpleRespDTO.getPaCode()
            );

            // 独立处理每个订单
            processOrdersIndependently(allOrders, context);
        } catch (Exception e) {
            // 记录处理抖音上传订单数据时发生的错误
            log.error("处理抖音上传订单数据失败，酒店: " + hcode, e);
            throw e;
        }
    }


    /**
     * 获取最晚离店时间
     *
     * @param gcode 集团代码
     * @return 最晚离店时间
     */
    private String getLastCheckOutTime(String gcode) {
        // 获取最晚离店时间
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO()
                .setGcode(gcode)
                .setHcode(NumberEnum.ZERO.getNumber())
                .setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())).getData();
        if (generalConfig == null) {
            throw new RuntimeException("[订单同步] 获取最晚离店时间失败");
        }
        String lastCheckOutTime = generalConfig.getValue();
        // 检查是否为HH:mm格式的时间
        if (StrUtil.isBlank(lastCheckOutTime) || !lastCheckOutTime.matches("\\d{2}:\\d{2}")) {
            throw new RuntimeException("[订单同步] 最晚离店时间格式错误,集团代码:" + gcode);
        }
        return lastCheckOutTime;
    }

    /**
     * Process each order independently with its own transaction
     */
    private void processOrdersIndependently(List<TikTokOrderDetail> orders, SyncContext context) {
        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();

        for (TikTokOrderDetail order : orders) {
            try {
                OtaOrderSynchronizedSaveVO record = processOrderInTransaction(order, context);
                if (record != null) {
                    syncRecords.add(record);
                }
            } catch (Exception e) {
                log.error("处理抖音订单失败，订单ID: {}, 错误: {}",
                        order != null ? order.getOrderId() : "unknown",
                        e.getMessage());
                // Continue with next order
            }
        }

        if (CollUtil.isNotEmpty(syncRecords)) {
            try {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
            } catch (Exception e) {
                log.error("批量创建同步记录失败", e);
                // Optionally retry individual records here
            }
        }
    }

    /**
     * Process a single order within its own transaction
     */
//    @GlobalTransactional(rollbackFor = Exception.class)
    private OtaOrderSynchronizedSaveVO processOrderInTransaction(TikTokOrderDetail order, SyncContext context) {
        // 1. Check if order is already synced
        if (isOrderSynced(order, context.syncedOrders())) {
            log.debug("订单已同步，跳过处理 orderId={}", order.getOrderId());
            return null;
        }

        // 2. Process order by status
        String pmsBookNo = processOrderByStatus(order, context);
        if (pmsBookNo == null) {
            return null;
        }

        // 3. Build sync record
        return buildSyncRecord(order, pmsBookNo, context.syncTime(), context);
    }

    private void processOrders(List<TikTokOrderDetail> orders, SyncContext context) {
        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();
        for (TikTokOrderDetail order : orders) {
            // 1. 检查订单是否已同步
            if (isOrderSynced(order, context.syncedOrders())) {
                log.debug("订单已同步，跳过处理 orderId={}", order.getOrderId());
                continue;
            }
            // 2. 根据订单状态处理
            String pmsBookNo = processOrderByStatus(order, context);
            if (pmsBookNo == null) continue;
            // 3. 记录同步结果
            syncRecords.add(buildSyncRecord(order, pmsBookNo, context.syncTime(), context));
        }

        if (CollUtil.isNotEmpty(syncRecords)) {
            otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
        }
    }

    /**
     * 根据订单状态处理不同业务逻辑
     */
    private String processOrderByStatus(TikTokOrderDetail order, SyncContext context) {
        return switch (OtaOrderStatusEnum.getEnumByCode(order.getBookStatus().toString())) {
            case OtaOrderStatusEnum.TIKTOK_2 -> // 已接单（新订单）
                    createTikTokBook(order, context);
            case OtaOrderStatusEnum.TIKTOK_4 -> // 已取消
                    cancelOrder(order, context);
            case null, default -> {
                log.warn("未知订单状态 orderId={}, status={}", order.getOrderId(), order.getBookStatus());
                yield null;
            }
        };
    }

    /**
     * 构建同步记录
     */
    private OtaOrderSynchronizedSaveVO buildSyncRecord(
            TikTokOrderDetail order,
            String pmsBookNo,
            long syncTime,
            SyncContext context
    ) {
        return new OtaOrderSynchronizedSaveVO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setPmsBookNo(pmsBookNo)
                .setOtaOrderNo(order.getOrderId())
                .setOtaOrderState(order.getBookStatus().toString())
                .setSyncTime(syncTime)
                .setPlatform(ChannelEnum.TIKTOK.getCode());
    }

    /**
     * 检查订单是否已同步
     */
    private boolean isOrderSynced(TikTokOrderDetail order, List<OtaOrderSynchronizedDO> syncedOrders) {
        return syncedOrders.stream()
                .anyMatch(synced ->
                    // 匹配订单号
                    order.getOrderId().equals(synced.getOtaOrderNo()) &&
                    (
                        // 状态完全匹配
                        order.getBookStatus().toString().equals(synced.getOtaOrderState()) ||
                        // 或者之前同步失败过
                        "fail".equals(synced.getOtaOrderState())
                    )
                );
    }


    private List<TikTokOrderDetail> fetchOrders(SyncContext context) {
        try {
            // 待入住请求
            String addBody = buildTikTokListUrl(context.orderListApi(), Integer.parseInt(OtaOrderStatusEnum.TIKTOK_2.getCode()));
            String receiptResponse = HttpUtils.post(context.orderListApi().getUrl(), OtaApiUtils.parseHeaders(context.orderListApi().getHeaders()), addBody);
            List<TikTokOrderDetail> receiptTikTokOrderList = TikTokOrderParser.parseOrderList(context.hcode(), receiptResponse);
            // 已取消的请求
            String canceledBody = buildTikTokListUrl(context.orderListApi(), Integer.parseInt(OtaOrderStatusEnum.TIKTOK_4.getCode()));
            String canceledResponse = HttpUtils.post(context.orderListApi().getUrl(), OtaApiUtils.parseHeaders(context.orderListApi().getHeaders()), canceledBody);
            List<TikTokOrderDetail> canceledTikTokOrderList = TikTokOrderParser.parseOrderList(context.hcode(), canceledResponse);

            List<TikTokOrderDetail> allTikTokOrderList = new ArrayList<>();
            allTikTokOrderList.addAll(receiptTikTokOrderList);
            allTikTokOrderList.addAll(canceledTikTokOrderList);

            return allTikTokOrderList;
        } catch (Exception e) {
            log.error("[TikTokSyncStrategy][fetchOrders] 获取抖音订单列表失败", e);
            return Collections.emptyList();
        }
    }

    private String buildTikTokListUrl(OtaApiDO api, Integer status) {
        // 1. 解析原始请求体（保留所有原始属性）
        JSONObject body = JSONUtil.parseObj(api.getBody());

        // 2. 仅修改status_list（确保传入的status转为单元素List）
        body.set("status_list", Collections.singletonList(status));

        // 3. 返回修改后的完整JSON（其他属性不变）
        return body.toString();
    }

    /**
     * 创建抖音预订单
     *
     * @param order   抖音订单信息
     * @param context
     * @return PMS的预订单号
     */
    private String createTikTokBook(TikTokOrderDetail order, SyncContext context) {
        try {
            OtaBookOpenReqDTO req = buildBookReqDTO(order, context);
            CommonResult<OtaBookOpenRespDTO> result = bookingApi.createBook(req);

            if (!result.isSuccess()) {
                log.error("创建抖音订单失败 orderId={}, msg={}", order.getOrderId(), result.getMsg());
                otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                        .setGcode(context.gcode())
                        .setHcode(context.hcode())
                        .setPlatform(ChannelEnum.TIKTOK.getCode())
                        .setSyncTime(context.syncTime())
                        .setOtaOrderNo(order.getOrderId())
                        .setOtaOrderState("fail")
                        .setPmsBookNo("0")
                        .setRemark(result.getMsg()));
                // 没有房
                if (result.getCode() == 1009001027) {
                    OTAOrderFailAlertProducer.OTAOrderFailAlertBO otaOrderFailAlertBO = new OTAOrderFailAlertProducer.OTAOrderFailAlertBO();
                    otaOrderFailAlertBO.setHcode(context.hcode());
                    otaOrderFailAlertBO.setChannelName("抖音");
                    otaOrderFailAlertBO.setOutOrderNo(order.getOrderId());
                    otaOrderFailAlertBO.setOtaRtName(order.getPhysicalRoomName());
                    otaOrderFailAlertBO.setPlanCheckinTime(req.getPlanCheckinTime());
                    otaOrderFailAlertBO.setPlanCheckoutTime(req.getPlanCheckoutTime());
                    otaOrderFailAlertBO.setGuestName(req.getGuestName());
                    otaOrderFailAlertBO.setReason(result.getMsg());
                    otaOrderFailAlertProducer.sendOTAOrderFailAlertMessage(otaOrderFailAlertBO);
                }
                return null;
            }
            return result.getData().getPmsResNo();
        } catch (Exception e) {
            log.error("创建抖音订单异常 orderId={}", order.getOrderId(), e);
            return null;
        }
    }

    /**
     * 修改订单  TODO
     *
     * @param order
     * @param context
     * @return
     */
    private String modifyOrder(TikTokOrderDetail order, SyncContext context) {
        return null;
    }

    /**
     * 取消订单
     */
    private String cancelOrder(TikTokOrderDetail order, SyncContext context) {
        OtaOrderSynchronizedDO syncedOrder = findSyncedOrder(order, context.syncedOrders());
        if (syncedOrder == null) {
            log.warn("未找到已同步订单，无法取消 orderId={}", order.getOrderId());
            return null;
        }
        if (OtaOrderStatusEnum.TIKTOK_4.getCode().equals(syncedOrder.getOtaOrderState())) {
            log.info("订单已取消，无需取消 orderId={}", order.getOrderId());
            return null;
        }
        if (OtaOrderStatusEnum.TIKTOK_2.getCode().equals(syncedOrder.getOtaOrderState())) {
            try {
                OtaBookCancelOpenReqDTO req = new OtaBookCancelOpenReqDTO()
                        .setGcode(context.gcode())
                        .setHcode(context.hcode())
                        .setBookNo(syncedOrder.getPmsBookNo());

                CommonResult<?> result = bookingApi.cancleBook(req);
                if (!result.isSuccess()) {
                    log.error("取消抖音订单失败 orderId={}, msg={}", order.getOrderId(), result.getMsg());
                    otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                            .setGcode(context.gcode())
                            .setHcode(context.hcode())
                            .setPlatform(ChannelEnum.TIKTOK.getCode())
                            .setSyncTime(context.syncTime())
                            .setOtaOrderNo(order.getOrderId())
                            .setOtaOrderState("fail")
                            .setRemark(result.getMsg())
                            .setPmsBookNo(syncedOrder.getPmsBookNo())
                    );
                    return null;
                }
                return syncedOrder.getPmsBookNo();
            } catch (Exception e) {
                log.error("取消抖音订单异常 orderId={}", order.getOrderId(), e);
                return null;
            }
        }
        return null;
    }

    /**
     * 查找已同步订单,包括状态
     * 1. 如果order状态是 待入住 ，已同步订单里没有这个订单号，则返回 null
     * 2. 如果order状态是 已取消，
     * 已同步订单里存在待入住的当前order，且不存在已取消的当前order，则返回这个已同步订单
     * 已同步订单里不存在待入住的当前order，则返回 null
     * 已同步订单里存在已取消的当前order，则返回这个已同步订单
     */
    private OtaOrderSynchronizedDO findSyncedOrder(
            TikTokOrderDetail order,
            List<OtaOrderSynchronizedDO> syncedOrders) {

        // 1. 过滤出当前订单的所有同步记录（按订单号匹配）
        List<OtaOrderSynchronizedDO> matchedOrders = CollectionUtils.filterList(syncedOrders,
                otaOrderSynchronizedDO -> order.getOrderId().equals(otaOrderSynchronizedDO.getOtaOrderNo()));

        // 2. 当前订单存在失败状态
        boolean isCanceled = matchedOrders.stream().anyMatch(synced -> "fail".equals(synced.getOtaOrderState()));

        if (isCanceled) {
            return null;
        }

        // 3. 根据订单状态处理
        if (isPendingCheckIn(order)) {
            // 状态：待入住 → 必须完全匹配
            return matchedOrders.stream()
                    .filter(this::isPendingCheckIn)
                    .findFirst()
                    .orElse(null);
        } else if (isCancelled(order)) {
            // 状态：已取消 → 复杂逻辑处理
            return handleCancelledOrder(matchedOrders, order);
        }

        // 其他状态暂不处理
        return null;
    }

    /**
     * 处理已取消订单的特殊逻辑
     */
    private OtaOrderSynchronizedDO handleCancelledOrder(
            List<OtaOrderSynchronizedDO> matchedOrders,
            TikTokOrderDetail order) {

        // 查找已存在的取消记录
        Optional<OtaOrderSynchronizedDO> cancelledRecord = matchedOrders.stream()
                .filter(this::isCancelled)
                .findFirst();

        if (cancelledRecord.isPresent()) {
            // 情况3：已存在取消记录 → 返回该记录
            return cancelledRecord.get();
        }

        // 查找待入住记录
        Optional<OtaOrderSynchronizedDO> pendingRecord = matchedOrders.stream()
                .filter(this::isPendingCheckIn)
                .findFirst();

        // 情况1：存在待入住记录且无取消记录 → 返回待入住记录 情况2：无任何匹配记录 → 返回null
        return pendingRecord.orElse(null);

    }

    /**
     * 判断订单是否为"待入住"状态
     */
    private boolean isPendingCheckIn(TikTokOrderDetail order) {
        return order.getBookStatus().toString().equals(OtaOrderStatusEnum.TIKTOK_2.getCode());
    }

    /**
     * 判断订单是否为"已取消"状态
     */
    private boolean isCancelled(TikTokOrderDetail order) {
        return order.getBookStatus().toString().equals(OtaOrderStatusEnum.TIKTOK_4.getCode());
    }

    /**
     * 判断同步记录是否为"待入住"状态
     */
    private boolean isPendingCheckIn(OtaOrderSynchronizedDO syncedOrder) {
        return OtaOrderStatusEnum.TIKTOK_2.getCode().equals(syncedOrder.getOtaOrderState()) && ChannelEnum.TIKTOK.getCode().equals(syncedOrder.getPlatform());
    }

    /**
     * 判断同步记录是否为"已取消"状态
     */
    private boolean isCancelled(OtaOrderSynchronizedDO syncedOrder) {
        return OtaOrderStatusEnum.TIKTOK_4.getCode().equals(syncedOrder.getOtaOrderState()) && ChannelEnum.TIKTOK.getCode().equals(syncedOrder.getPlatform());
    }

    private OtaBookOpenReqDTO buildBookReqDTO(TikTokOrderDetail tikTokOrderDetail, SyncContext context) {
        OtaBookOpenReqDTO bookSaveReqVO = new OtaBookOpenReqDTO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setChannelCode(ChannelEnum.TIKTOK.getCode())
                .setOrderSource(OrderSrcEnum.AGENT.getCode())
                .setGuestSrcType(GuestSrcTypeEnum.AGENT.getCode())
                .setBookType(OrderTypeEnum.GENERAL.getCode());
        // 通过bookEndTimeMs 和 bookStartTimeMs 计算是否为钟点房，如果计算的小时数小于12，则为钟点房
        if (isHourRoom(tikTokOrderDetail.getBookEndTimeMs(), tikTokOrderDetail.getBookStartTimeMs())) {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.HOUR_ROOM.getCode());
        } else {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.ALL_DAY.getCode());
        }
        LocalDateTime planCheckInTime = getTikTokCheckInTime(tikTokOrderDetail);
        LocalDateTime planCheckoutTime = getTikTokCheckOutTime(tikTokOrderDetail, context.lastCheckOutTime());
        bookSaveReqVO.setPlanCheckinTime(planCheckInTime)
                .setPlanCheckoutTime(planCheckoutTime);

        // 设置客人信息和联系方式
        setGuestInfo(bookSaveReqVO, tikTokOrderDetail, context);

        String outOrderRemark = String.format("|订单ID[%s]|", tikTokOrderDetail.getOrderId());
        bookSaveReqVO.setOutOrderRemark(outOrderRemark);

        // 处理订单批次信息
        List<OtaBookOpenReqDTO.Batch> batchList = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch batch = new OtaBookOpenReqDTO.Batch();

        String batchNo = generateBatchNo(planCheckInTime, planCheckoutTime);
        long days = ChronoUnit.DAYS.between(planCheckInTime.toLocalDate(), planCheckoutTime.toLocalDate());

        batch.setBatchNo(batchNo)
                .setDays((int) days)
                .setPlanCheckinTime(planCheckInTime)
                .setPlanCheckoutTime(planCheckoutTime);

        // 处理预订房型信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = processBookRoomTypes(tikTokOrderDetail, context, days);
        batch.setBookRoomTypes(bookRoomTypes);
        batchList.add(batch);
        bookSaveReqVO.setBatches(batchList);

        return bookSaveReqVO;
    }

    /**
     * 生成批量号
     *
     * @param tikTokOrderDetail
     * @param context
     * @param days
     * @return
     */
    private List<OtaBookOpenReqDTO.Batch.BookRoomType> processBookRoomTypes(
            TikTokOrderDetail tikTokOrderDetail, SyncContext context, long days) {
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch.BookRoomType bookRoomType = new OtaBookOpenReqDTO.Batch.BookRoomType();

        // 1. 房型匹配
        String physicalRoomName = tikTokOrderDetail.getPhysicalRoomName();
        RoomTypeRefDO roomTypeRefDO = context.roomTypeRefs().stream()
                .filter(ref -> Objects.equals(ref.getOtaRoomTypeName(), physicalRoomName)
                        && ChannelEnum.TIKTOK.getCode().equals(ref.getChannel()))
                .findFirst()
                .orElseThrow(() -> {
                    log.error("TikTok订单房型匹配失败 hotel={} room={} orderId={}", context.hcode(), physicalRoomName, tikTokOrderDetail.getOrderId());
                    String alertMsg = String.format(
                            "**OTA同步异常报警**\n" +
                                    "> 异常类型: `%s`\n" +
                                    "> 异常信息: `%s`\n" +
                                    "> 发生时间: `%s`\n" +
                                    "请及时处理！",
                            "TikTok订单房型匹配失败，酒店代码:" + context.hcode(),
                            "房型: " + physicalRoomName + "订单ID：" + tikTokOrderDetail.getOrderId(),
                            LocalDateTime.now()
                    );
                    WeChatBotNotifier.sendAlert(alertMsg);
                    return exception(OTA_ROOM_TYPE_REF_NOT_FOUND,
                            tikTokOrderDetail.getOrderId(), physicalRoomName);
                });

        bookRoomType.setRtCode(roomTypeRefDO.getRoomTypeCode())
                .setBkNum(0)
                .setRoomNum(tikTokOrderDetail.getOriginRoomCount());

        // 2. 处理每日价格
        List<OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice> dayPrices = new ArrayList<>();
        TikTokOrderDetail.AmountInfo amountInfo = tikTokOrderDetail.getAmountInfo();
        TikTokOrderDetail.BookRoom bookRoom = tikTokOrderDetail.getBookRoomList().get(0);

        // 获取日期范围
        LocalDate startDate = LocalDate.parse(bookRoom.getDateScope().getStartDate());
        LocalDate endDate = LocalDate.parse(bookRoom.getDateScope().getEndDate());

        // 3. 填充每日价格
        List<TikTokOrderDetail.DailyAmount> dailyAmounts = amountInfo.getSaleAmount().getAmountByDay();

        for (int i = 0; i < days; i++) {
            OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice dayPrice =
                    new OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice();

            LocalDate currentDate = startDate.plusDays(i);
            dayPrice.setPriceDate(currentDate)
                    .setWeek(currentDate.getDayOfWeek().getValue());

            // 获取对应日期的价格（根据顺序或日期匹配）
            if (i < dailyAmounts.size()) {
                TikTokOrderDetail.DailyAmount dailyAmount = dailyAmounts.get(i);
                dayPrice.setPrice(dailyAmount.getAmount())        // 售价（分）
                        .setVipPrice(dailyAmount.getAmount());     // VIP价（分）
            } else {
                // 如果价格数据不足，使用最后一天的价格
                TikTokOrderDetail.DailyAmount lastAmount = dailyAmounts.get(dailyAmounts.size() - 1);
                dayPrice.setPrice(lastAmount.getAmount()).setVipPrice(lastAmount.getAmount());
            }

            dayPrices.add(dayPrice);
        }

        bookRoomType.setDayPrices(dayPrices);
        bookRoomTypes.add(bookRoomType);
        return bookRoomTypes;
    }

    // 提取生成批次编号的方法
    private String generateBatchNo(LocalDateTime checkIn, LocalDateTime checkOut) {
        return DateUtil.format(checkIn, "yyyy-MM-dd") + "/" + DateUtil.format(checkOut, "yyyy-MM-dd");
    }

    private void setGuestInfo(OtaBookOpenReqDTO bookSaveReqVO, TikTokOrderDetail tikTokOrderDetail, SyncContext context) {
        bookSaveReqVO.setGuestCode(context.paCode())
                .setGuestName(ProtocolAgentEnum.TIKTOK_PREPAY.getName());

        if (CollUtil.isNotEmpty(tikTokOrderDetail.getUserList())) {
            // 多个人用","号隔开
            String contacts = tikTokOrderDetail.getUserList().stream()
                    .map(TikTokOrderDetail.UserInfo::getName)
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.joining(","));
//            String phone = tikTokOrderDetail.getUserList().stream()
//                    .map(TikTokOrderDetail.UserInfo::getPhone)
//                    .filter(StrUtil::isNotBlank)
//                    .findFirst()
//                    .orElse("");

            bookSaveReqVO.setContact(contacts)
                    .setCheckinPerson(contacts);

        } else {
            bookSaveReqVO.setContact("").setCheckinPerson("");
        }

        bookSaveReqVO.setIsSendSms(BooleanEnum.FALSE.getValue())
                .setOutOrderNo(tikTokOrderDetail.getOrderId());
    }

    private LocalDateTime getTikTokCheckInTime(TikTokOrderDetail tikTokOrderDetail) {
        // 1. 获取订单中的预订开始时间戳（毫秒）
        Long bookStartTimeMs = tikTokOrderDetail.getBookStartTimeMs();
        if (bookStartTimeMs == null) {
            throw new IllegalArgumentException("TikTok订单缺少预订开始时间");
        }

        // 2. 将时间戳转换为LocalDate（只保留日期部分）
        String dateStr = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(bookStartTimeMs),
                ZoneId.systemDefault()
        ).format(DateTimeFormatter.ISO_LOCAL_DATE); // 格式：yyyy-MM-dd

        // 3. 拼接固定时间"12:00"
        String dateTimeStr = dateStr + " 12:00";

        // 4. 解析为LocalDateTime对象
        return LocalDateTime.parse(
                dateTimeStr,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        );
    }

    private LocalDateTime getTikTokCheckOutTime(TikTokOrderDetail tikTokOrderDetail, String lastCheckOutTime) {
        // 1. 获取订单中的预订结束时间戳（毫秒）
        Long bookEndTimeMs = tikTokOrderDetail.getBookEndTimeMs();
        if (bookEndTimeMs == null) {
            throw new IllegalArgumentException("TikTok订单缺少预订结束时间");
        }

        // 2. 将时间戳转换为LocalDate（只保留日期部分）
        String dateStr = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(bookEndTimeMs),
                ZoneId.systemDefault()
        ).format(DateTimeFormatter.ISO_LOCAL_DATE); // 格式：yyyy-MM-dd

        // 3. 拼接固定时间"12:00"
        String dateTimeStr = dateStr + " " + lastCheckOutTime;

        // 4. 解析为LocalDateTime对象
        return LocalDateTime.parse(
                dateTimeStr,
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
        );
    }


    private Boolean isHourRoom(Long bookEndTimeMs, Long bookStartTimeMs) {
        long hours = (bookEndTimeMs - bookStartTimeMs) / (60 * 60 * 1000);
        return hours <= 12;
    }


    /**
     * 定义了一个名为 SyncContext 的私有不可变记录类（record），用于封装与OTA订单同步相关的上下文数据，包括房型编码、API配置、已同步订单列表等信息
     *
     * @param gcode
     * @param hcode
     * @param orderListApi     获取订单列表的api
     * @param roomTypeRefs     房型关联列表
     * @param lastCheckOutTime 最晚退房时间
     * @param syncTime         同步时间
     * @param syncedOrders     已同步订单列表
     * @param paCode            中介编码
     */
    private record SyncContext(
            String gcode,
            String hcode,
            OtaApiDO orderListApi,
            List<RoomTypeRefDO> roomTypeRefs,
            String lastCheckOutTime,
            Long syncTime,
            List<OtaOrderSynchronizedDO> syncedOrders,
            String paCode
    ) {
    }
}
