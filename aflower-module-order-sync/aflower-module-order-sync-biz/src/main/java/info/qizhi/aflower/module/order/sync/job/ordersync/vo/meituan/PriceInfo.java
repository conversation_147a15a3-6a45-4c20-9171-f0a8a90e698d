package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "价格信息")
public class PriceInfo {
    @Schema(description = "佣金")
    private Long commission;

    @Schema(description = "日期(时间戳)")
    private Long date;

    @Schema(description = "日期字符串")
    private String dateString;

    @Schema(description = "底价")
    private Long floorPrice;

    @Schema(description = "支付单元ID")
    private String paymentUnitId;

    @Schema(description = "价格")
    private Long price;

    @Schema(description = "子比例")
    private Long subRatio;

    @Schema(description = "子比例描述")
    private String subRatioDesc;

    @Schema(description = "微信和低补贴")
    private Boolean wxAndLowSub;
}
