package info.qizhi.aflower.module.order.sync.mq.producer;


import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.HotelMessageTypeEnum;
import info.qizhi.aflower.framework.common.enums.QueueConstants;
import info.qizhi.aflower.framework.common.message.HotelMessage;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * OTA订单失败提醒 消息生产者
 */

@Slf4j
@Component
public class OTAOrderFailAlertProducer {

    @Resource
    private RabbitTemplate rabbitTemplate; // 重点：注入 RabbitTemplate 对象


    private void sendOTAOrderFailAlertMessage(String hcode, String msg) {
        HotelMessage message = new HotelMessage().setHcode(hcode)
                .setType(HotelMessageTypeEnum.OTA_ORDER_FAIL.getCode())
                .setMsg(msg);
        rabbitTemplate.convertAndSend(QueueConstants.HOTEL_MESSAGE_QUEUE, message);
    }

    /**
     * OTA订单提醒服务消息
     *
     */
    public void sendOTAOrderFailAlertMessage(OTAOrderFailAlertBO otaOrderFailAlertBO) {
        String reason = otaOrderFailAlertBO.getReason();
        // 截取"冲突预订单号"之前的内容
        if (StrUtil.isNotBlank(reason) && reason.contains("冲突预订单号")) {
            reason = reason.split("冲突预订单号")[0];
        }
        sendOTAOrderFailAlertMessage(otaOrderFailAlertBO.getHcode(), JsonUtils.toJsonPrettyString(otaOrderFailAlertBO)
                .replace(JsonUtils.toJsonPrettyString(otaOrderFailAlertBO.getReason()), JsonUtils.toJsonPrettyString(reason)));
    }


    @Data
    public static class OTAOrderFailAlertBO {
        @Schema(description = "酒店代码")
        private String hcode;
        @Schema(description = "渠道，如：携程，美团")
        private String channelName;
        @Schema(description = "OTA订单号")
        private String outOrderNo;
        @Schema(description = "OTA房型名称")
        private String otaRtName;
        @Schema(description = "入住时间")
        private LocalDateTime planCheckinTime;
        @Schema(description = "离店时间")
        private LocalDateTime planCheckoutTime;
        @Schema(description = "预订人姓名")
        private String guestName;
        @Schema(description = "失败原因")
        private String reason;
    }

} 
