package info.qizhi.aflower.module.order.sync.dal.mysql.ota;

import info.qizhi.aflower.framework.mybatis.core.mapper.BaseMapperX;
import info.qizhi.aflower.framework.mybatis.core.query.LambdaQueryWrapperX;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.HotelRefReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.HotelRefDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * OTA酒店关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HotelRefMapper extends BaseMapperX<HotelRefDO> {

    default List<HotelRefDO> selectList(HotelRefReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HotelRefDO>()
                .eq(HotelRefDO::getGcode, reqVO.getGcode())
                .eq(HotelRefDO::getHcode, reqVO.getHcode())
                .eqIfPresent(HotelRefDO::getChannel, reqVO.getChannel())
                .orderByDesc(HotelRefDO::getId));
    }

}