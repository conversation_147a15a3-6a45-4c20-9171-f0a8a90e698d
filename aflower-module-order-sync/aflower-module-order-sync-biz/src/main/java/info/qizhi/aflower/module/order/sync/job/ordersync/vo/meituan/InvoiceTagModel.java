package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "发票标签模型")
public class InvoiceTagModel {
    @Schema(description = "发票预约")
    private Integer invoiceAppointment;

    @Schema(description = "发票是否可以创建")
    private Boolean invoiceCanCreate;

    @Schema(description = "发票是否已预约")
    private Boolean invoiceIsAppointed;

    @Schema(description = "发票金额")
    private Object invoiceMoney;

    @Schema(description = "发票方")
    private Integer invoiceParty;
}
