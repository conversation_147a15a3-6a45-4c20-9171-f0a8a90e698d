package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "卡片信息")
public class Card {
    @Schema(description = "活动标签")
    private Integer activeTag;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "评论")
    private String comment;

    @Schema(description = "金额")
    private Integer money;

    @Schema(description = "状态")
    private Object status;

    @Schema(description = "标题")
    private String title;
}
