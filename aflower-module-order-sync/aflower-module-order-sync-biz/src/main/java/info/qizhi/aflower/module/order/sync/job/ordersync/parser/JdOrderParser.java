package info.qizhi.aflower.module.order.sync.job.ordersync.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.OtaOrderStatusEnum;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.jd.JdOrder;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
public class JdOrderParser {
    public static List<JdOrder> parseOrderList(String hcode, String json) {
        List<String> orderStatusTypes = List.of(
                OtaOrderStatusEnum.JD_ORDER_STATUS_CONFIRMED.getCode(),
                OtaOrderStatusEnum.JD_ORDER_STATUS_CANCELED.getCode()
        );

        List<JdOrder> jdOrderList = CollUtil.newArrayList();

        if (StrUtil.isNotBlank(json)) {
            JSONObject jsonResponse = JSONUtil.parseObj(json);
            if (jsonResponse.getInt("code", -1) == 200) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null) {
                    JSONArray orderArray = data.getJSONArray("list");
                    if (CollUtil.isNotEmpty(orderArray)) {
                        for (int i = 0; i < orderArray.size(); i++) {
                            JSONObject orderJson = orderArray.getJSONObject(i);
                            JdOrder order = parseJdOrder(orderJson);
                            if (order != null && orderStatusTypes.contains(order.getOrderStatus().toString())) {
                                jdOrderList.add(order);
                            }
                        }
                    }
                    log.info("京东订单列表解析成功，hcode: {}, 订单数：{}", hcode, jdOrderList.size());
                }
            } else {
                log.error("京东订单同步失败，hcode: {}, 响应码：{}，消息：{}, JSON:{}",
                        hcode,
                        jsonResponse.getInt("code"),
                        jsonResponse.getStr("msg"),
                        json);
                String alertMsg = String.format(
                        "**OTA同步异常报警**\n" +
                                "> 异常类型: `%s`\n" +
                                "> 异常信息: `%s`\n" +
                                "> 发生时间: `%s`\n" +
                                "请及时处理！",
                        "京东订单同步失败 hcode:" + hcode,
                        jsonResponse.getStr("msg"),
                        LocalDateTime.now()
                );
                WeChatBotNotifier.sendAlert(alertMsg);
            }
        }
        return jdOrderList;
    }

    private static JdOrder parseJdOrder(JSONObject orderJson) {
        if (orderJson == null) {
            return null;
        }

        JdOrder order = new JdOrder();
        try {
            order.setErpOrderId(orderJson.getStr("erpOrderId"));
            order.setOrderStatus(orderJson.getInt("orderStatus"));
            order.setOrderStatusDesc(orderJson.getStr("orderStatusDesc"));
            order.setPayTime(orderJson.getStr("payTime"));
            order.setBookBeginTime(orderJson.getStr("bookBeginTime"));
            order.setBookEndTime(orderJson.getStr("bookEndTime"));
            order.setCheckInTime(orderJson.getStr("checkInTime"));
            order.setCheckOutTime(orderJson.getStr("checkOutTime"));
            order.setCancelTime(orderJson.getStr("cancelTime"));
            order.setRoomTypeName(orderJson.getStr("roomTypeName"));
            order.setRoomTypeId(orderJson.getLong("roomTypeId"));
            order.setNightNum(orderJson.getInt("nightNum"));
            order.setRoomAmount(orderJson.getInt("roomAmount"));
            order.setJdHotelId(orderJson.getLong("jdHotelId"));
            order.setId(orderJson.getStr("id"));

            // 解析住客信息
            JSONArray guestArray = orderJson.getJSONArray("hotelGuest");
            if (CollUtil.isNotEmpty(guestArray)) {
                order.setHotelGuest(guestArray.toList(String.class));
            }

        } catch (Exception e) {
            log.error("解析京东订单异常，订单JSON: {}", orderJson, e);
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> JSON: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "解析京东订单异常",
                    orderJson,
                    e.getMessage(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            return null;
        }

        return order;
    }
}