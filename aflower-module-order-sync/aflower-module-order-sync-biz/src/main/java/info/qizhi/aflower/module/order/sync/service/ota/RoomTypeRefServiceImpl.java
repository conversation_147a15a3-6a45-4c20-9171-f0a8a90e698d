package info.qizhi.aflower.module.order.sync.service.ota;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.ChannelEnum;
import info.qizhi.aflower.framework.common.enums.OtaMethodTypeEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.dal.mysql.ota.RoomTypeRefMapper;
import info.qizhi.aflower.module.order.sync.framework.utils.HttpUtils;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okhttp3.brotli.BrotliInterceptor;
import org.apache.commons.io.IOUtils;
import org.brotli.dec.BrotliInputStream;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.*;

/**
 * OTA房型关联表;ota房型可以关联多个酒店房型 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class RoomTypeRefServiceImpl implements RoomTypeRefService {

    @Resource
    private RoomTypeRefMapper roomTypeRefMapper;

    @Resource
    private OtaApiService otaApiService;

    @Override
    public List<OtaRoomTypeRespVO> getRemoteOtaRoomTypeList(RoomTypeRefReqVO reqVO) {
        OtaApiDO otaApiDO = otaApiService.getOtaApi(new OtaApiReqVO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setChannel(reqVO.getChannel())
                .setMethod(OtaMethodTypeEnum.ROOM_TYPE.getCode()));
        if (otaApiDO == null) {
            throw exception(OTA_API_NOT_EXISTS);
        }

        ChannelEnum channelEnum = ChannelEnum.getChannelEnumByCode(reqVO.getChannel());
        if (channelEnum == null) {
            throw exception(UNSUPPORTED_CHANNEL);
        }

        switch (channelEnum) {
            case MEITUAN:
                return getRemoteRoomTypeList(otaApiDO, true); // GET
            case CTRIP:
            case TIKTOK:
                return getRemoteRoomTypeList(otaApiDO, false); // POST
            case XIAOHONGSHU:
                throw exception(UNSUPPORTED_CHANNEL, "小红书房型同步暂未支持");
            default:
                throw exception(UNSUPPORTED_CHANNEL);
        }
    }
    private List<OtaRoomTypeRespVO> getRemoteRoomTypeList(OtaApiDO otaApiDO, boolean isGet) {
        try {
            String result;
            if (isGet) {
                // GET请求
                result = HttpUtils.get(
                        otaApiDO.getUrl(),
                        OtaApiUtils.parseHeaders(otaApiDO.getHeaders())
                );
            } else {
                // POST请求
                result = HttpUtils.post(
                        otaApiDO.getUrl(),
                        OtaApiUtils.parseHeaders(otaApiDO.getHeaders()),
                        otaApiDO.getBody()
                );
            }
            return parseRoomTypeJson(result, otaApiDO.getChannel());
        } catch (IOException e) {
            throw new RuntimeException("获取OTA房型数据失败: " + e.getMessage(), e);
        }
    }

    private String decodeResponse(Response response) throws IOException {
        // 1. 获取原始字节（避免 OkHttp 自动解码）
        byte[] rawBytes = response.body().bytes();
        String encoding = response.header("Content-Encoding");

        // 2. 根据压缩类型处理
        InputStream inputStream = new ByteArrayInputStream(rawBytes);
        if ("br".equals(encoding)) {
            try (BrotliInputStream brotliStream = new BrotliInputStream(inputStream)) {
                return IOUtils.toString(brotliStream, StandardCharsets.UTF_8);
            }
        } else if ("gzip".equals(encoding)) {
            try (GZIPInputStream gzipStream = new GZIPInputStream(inputStream)) {
                return IOUtils.toString(gzipStream, StandardCharsets.UTF_8);
            }
        } else {
            return new String(rawBytes, StandardCharsets.UTF_8); // 无压缩
        }
    }

    private List<OtaRoomTypeRespVO> parseRoomTypeJson(String json, String channelCode) {
        ChannelEnum channelEnum = ChannelEnum.getChannelEnumByCode(channelCode);
        if (channelEnum == null) {
            throw exception(UNSUPPORTED_CHANNEL);
        }

        switch (channelEnum) {
            case MEITUAN:
                return parseMeituanRoomTypeJson(json);
            case CTRIP:
                return parseCtripRoomTypeJson(json);
            case TIKTOK:
                return parseTitokRoomTypeJson(json);
            default:
                throw exception(UNSUPPORTED_CHANNEL);
        }
    }

    /**
     * 解析抖音房型数据
     * @param json
     * @return
     */
    private List<OtaRoomTypeRespVO> parseTitokRoomTypeJson(String json) {
        // Parse the JSON string into a JSON object
        JSONObject jsonObject = JSONUtil.parseObj(json);

        if (jsonObject.getInt("status_code") != 0) {
            throw exception(GET_ROOM_TYPE_FAIL, jsonObject.getStr("status_msg"));
        }

        if (!jsonObject.containsKey("rooms")) {
            return Collections.emptyList();
        }

        // Get the rooms array
        JSONArray roomsArray = jsonObject.getJSONArray("rooms");
        if (roomsArray == null || roomsArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeRespVO> roomTypeRefList = new ArrayList<>();

        // Process each room in the array
        for (int i = 0; i < roomsArray.size(); i++) {
            JSONObject roomObject = roomsArray.getJSONObject(i);
            if (roomObject == null) {
                continue;
            }

            OtaRoomTypeRespVO roomTypeRef = new OtaRoomTypeRespVO();

            // Set basic room type information
            roomTypeRef.setOtaRoomTypeCode(roomObject.getStr("physical_room_id"));
            roomTypeRef.setOtaRoomTypeName(roomObject.getStr("physical_room_name"));
            roomTypeRefList.add(roomTypeRef);
        }

        return roomTypeRefList;
    }

    private List<OtaRoomTypeRespVO> parseCtripRoomTypeJson(String json) {
        JSONObject jsonObject = JSONUtil.parseObj(json);
        if (jsonObject.getInt("code") !=200){
            throw exception(GET_ROOM_TYPE_FAIL, jsonObject.getStr("message"));
        }
        if (!jsonObject.containsKey("data")) {
            return Collections.emptyList();
        }

        JSONArray dataArray = jsonObject.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeRespVO> roomTypeRefList = new ArrayList<>();

        // Process each room type in the array
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject roomTypeObject = dataArray.getJSONObject(i);
            if (roomTypeObject == null) {
                continue;
            }

            // Create a base RoomTypeRefDO with basic room type info
            OtaRoomTypeRespVO baseRoomType = new OtaRoomTypeRespVO();
            baseRoomType.setOtaRoomTypeCode(roomTypeObject.getStr("basicRoomTypeID"));
            baseRoomType.setOtaRoomTypeName(HtmlUtil.unescape(roomTypeObject.getStr("roomName")));
            List<RoomTypeRefDO.OtaProduct> otaProducts = new ArrayList<>();
            // Process roomInfos (rate plans)
            JSONArray roomInfos = roomTypeObject.getJSONArray("roomInfos");
            if (roomInfos != null && !roomInfos.isEmpty()) {
                for (int j = 0; j < roomInfos.size(); j++) {
                    JSONObject roomInfo = roomInfos.getJSONObject(j);
                    if (roomInfo == null) {
                        continue;
                    }

                    // Create product info for this rate plan
                    RoomTypeRefDO.OtaProduct product = new RoomTypeRefDO.OtaProduct();
                    product.setProductCode(roomInfo.getStr("roomTypeID"));
                    product.setProductName(roomInfo.getStr("roomNameDesc"));

                    otaProducts.add(product);
                }
            }
            baseRoomType.setOtaProducts(otaProducts);

            roomTypeRefList.add(baseRoomType);
        }

        return roomTypeRefList;
    }

    /**
     * 解析美团房型信息
     * @param json
     * @return
     */
    private List<OtaRoomTypeRespVO> parseMeituanRoomTypeJson(String json) {
        JSONObject jsonObject = JSONUtil.parseObj(json);
        if (jsonObject.getInt("status") != 0 || !jsonObject.containsKey("data")) {
            return Collections.emptyList();
        }

        JSONArray dataArray = jsonObject.getJSONArray("data");
        if (dataArray == null || dataArray.isEmpty()) {
            return Collections.emptyList();
        }

        List<OtaRoomTypeRespVO> roomTypeRefList = new ArrayList<>();

        // Process each room type in the array
        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject roomTypeObject = dataArray.getJSONObject(i);
            if (roomTypeObject == null) {
                continue; // Skip inactive room types
            }

            OtaRoomTypeRespVO roomTypeRef = new OtaRoomTypeRespVO();
            roomTypeRef.setOtaRoomTypeCode(roomTypeObject.getStr("roomId"));
            roomTypeRef.setOtaRoomTypeName(roomTypeObject.getStr("roomName"));

            roomTypeRefList.add(roomTypeRef);
        }

        return roomTypeRefList;
    }

    @Override
    public Long createRoomTypeRef(RoomTypeRefSaveReqVO createReqVO) {
        // 插入
        RoomTypeRefDO roomTypeRef = BeanUtils.toBean(createReqVO, RoomTypeRefDO.class);
        roomTypeRefMapper.insert(roomTypeRef);
        // 返回
        return roomTypeRef.getId();
    }

    @Override
    public void updateRoomTypeRef(RoomTypeRefSaveReqVO updateReqVO) {
        // 先删除后添加
        List<RoomTypeRefDO> roomTypeRefDOS = roomTypeRefMapper.selectList(new RoomTypeRefReqVO().setGcode(updateReqVO.getGcode())
                .setHcode(updateReqVO.getHcode()).setRoomTypeCode(updateReqVO.getRoomTypeCode())
                .setChannel(updateReqVO.getChannel()));
        if (CollUtil.isNotEmpty(roomTypeRefDOS)) {
            // 删除
            roomTypeRefMapper.deleteBatchIds(roomTypeRefDOS.stream().map(RoomTypeRefDO::getId).collect(Collectors.toList()));
        }
        // 添加
        List<RoomTypeRefDO> roomTypeRefDOs = warpRoomTypeRef(updateReqVO);
        roomTypeRefMapper.insertBatch(roomTypeRefDOs);
    }

    @Override
    public void updateRoomTypeRefList(List<RoomTypeRefDO> roomTypeRefDOList) {
        roomTypeRefMapper.updateBatch(roomTypeRefDOList);
    }

    private List<RoomTypeRefDO> warpRoomTypeRef(RoomTypeRefSaveReqVO updateReqVO) {
        List<RoomTypeRefDO> result = new ArrayList<>();
        updateReqVO.getOtaRoomTypes().forEach(otaRoomType -> {
            List<RoomTypeRefSaveReqVO.OtaRoomType.OtaProduct> otaProducts = otaRoomType.getOtaProducts();
            RoomTypeRefDO roomTypeRefDO = new RoomTypeRefDO().setGcode(updateReqVO.getGcode())
                    .setHcode(updateReqVO.getHcode())
                    .setRoomTypeCode(updateReqVO.getRoomTypeCode())
                    .setChannel(updateReqVO.getChannel())
                    .setOtaRoomTypeCode(otaRoomType.getOtaRoomTypeCode())
                    .setOtaRoomTypeName(otaRoomType.getOtaRoomTypeName());
            if (CollUtil.isNotEmpty(otaProducts)) {
                List<RoomTypeRefDO.OtaProduct> otaProducts1 = new ArrayList<>();
                otaProducts.forEach(otaProduct -> {
                    otaProducts1.add(new RoomTypeRefDO.OtaProduct().setProductCode(otaProduct.getProductCode()).setProductName(otaProduct.getProductName()));
                });
                roomTypeRefDO.setOtaProducts(otaProducts1);
            }
            result.add(roomTypeRefDO);
        });

        return result;
    }

    @Override
    public RoomTypeRefDO getRoomTypeRefByProductCode(RoomTypeRefReq2VO reqVO) {
        List<RoomTypeRefDO> roomTypeRefDOS = roomTypeRefMapper.selectList(new RoomTypeRefReqVO().setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setChannel(reqVO.getChannel()));
        if (CollUtil.isEmpty(roomTypeRefDOS)) {
            return null;
        }
        return roomTypeRefDOS.stream()
                .filter(roomTypeRefDO -> CollUtil.isNotEmpty(roomTypeRefDO.getOtaProducts())) // 过滤空列表
                .filter(roomTypeRefDO -> roomTypeRefDO.getOtaProducts().stream()
                        .anyMatch(otaProduct -> reqVO.getOtaProductCode().equals(otaProduct.getProductCode()))
                )
                .findFirst()
                .orElse(null);
    }

    @Override
    public void deleteRoomTypeRef(RoomTypeRefDeleteReqVO deleteReqVO) {
        List<RoomTypeRefDO> roomTypeRefDOS = roomTypeRefMapper.selectList(new RoomTypeRefReqVO().setGcode(deleteReqVO.getGcode())
                .setHcode(deleteReqVO.getHcode())
                .setRoomTypeCode(deleteReqVO.getRoomTypeCode())
                .setChannel(deleteReqVO.getChannel()));
        if (CollUtil.isEmpty(roomTypeRefDOS)) {
            return;
        }
        List<Long> ids = CollectionUtils.convertList(roomTypeRefDOS, RoomTypeRefDO::getId);
        roomTypeRefMapper.deleteBatchIds(ids);
    }

    @Override
    public void deleteRoomTypeRef(Long id) {
        // 校验存在
        validateRoomTypeRefExists(id);
        // 删除
        roomTypeRefMapper.deleteById(id);
    }

    private void validateRoomTypeRefExists(Long id) {
        if (roomTypeRefMapper.selectById(id) == null) {
            throw exception(ROOM_TYPE_REF_NOT_EXISTS);
        }
    }

    @Override
    public RoomTypeRefDO getRoomTypeRef(Long id) {
        return roomTypeRefMapper.selectById(id);
    }

    @Override
    public List<RoomTypeRefDO> getRoomTypeRefList(RoomTypeRefReqVO reqVO) {
        return roomTypeRefMapper.selectList(reqVO);
    }

}