package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiRespVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaApiSaveReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaRoomTypeSaveReqVO;
import info.qizhi.aflower.module.order.sync.controller.open.vo.AllOtaApiRespVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * OTA_api Service 接口
 *
 * <AUTHOR>
 */
public interface OtaApiService {

    /**
     * 创建OTA_api
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOtaApi(@Valid OtaApiSaveReqVO createReqVO);

    /**
     * 更新OTA_api
     *
     * @param updateReqVO 更新信息
     */
    void updateOtaApi(@Valid OtaApiSaveReqVO updateReqVO);

    /**
     * 删除OTA_api
     *
     * @param id 编号
     */
    void deleteOtaApi(Long id);

    /**
     * 获得OTA_api
     *
     * @param reqVO 请求
     * @return OTA_api
     */
    OtaApiDO getOtaApi(OtaApiReqVO reqVO);

    /**
     * 获得OTA_api列表
     *
     * @param reqVO 查询
     * @return OTA_api列表
     */
    List<OtaApiDO> getOtaApiList(OtaApiReqVO reqVO);

    /**
     * 获得所有酒店 详情和订单列表 API 列表
     * @return
     */
    List<OtaApiDO> getDetailAndOrderListOtaApi();

    List<OtaApiRespVO> getRoomTypeOtaApiList();

    List<OtaApiRespVO> getMtApiList();

    List<AllOtaApiRespVO> getAllApiList();

    Boolean saveOtaRoomType(OtaRoomTypeSaveReqVO reqVO);
}