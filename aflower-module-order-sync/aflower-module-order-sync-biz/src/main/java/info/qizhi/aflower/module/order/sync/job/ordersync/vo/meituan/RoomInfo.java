package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "房间信息")
public class RoomInfo {
    @Schema(description = "业务日期")
    private String bizDay;

    @Schema(description = "剩余数量")
    private Integer leftCount;

    @Schema(description = "退款数量")
    private Integer refundCount;

    @Schema(description = "总数量")
    private Integer totalCount;
}
