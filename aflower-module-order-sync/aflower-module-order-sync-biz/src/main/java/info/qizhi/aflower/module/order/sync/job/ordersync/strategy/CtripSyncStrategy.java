package info.qizhi.aflower.module.order.sync.job.ordersync.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.http.HttpUtils;
import info.qizhi.aflower.framework.common.util.json.JsonUtils;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.*;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaApiDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.RoomTypeRefDO;
import info.qizhi.aflower.module.order.sync.framework.utils.WeChatBotNotifier;
import info.qizhi.aflower.module.order.sync.job.ordersync.parser.CtripOrderParser;
import info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils;
import info.qizhi.aflower.module.order.sync.job.ordersync.vo.ctrip.*;
import info.qizhi.aflower.module.order.sync.mq.producer.OTAOrderFailAlertProducer;
import info.qizhi.aflower.module.order.sync.service.ota.OtaOrderSynchronizedService;
import info.qizhi.aflower.module.order.sync.service.ota.RoomTypeRefService;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookCancelOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.OtaBookOpenRespDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigReq2DTO;
import info.qizhi.aflower.module.pms.api.config.dto.general.GeneralConfigRespDTO;
import info.qizhi.aflower.module.pms.api.protocolagent.ProtocolAgentApi;
import info.qizhi.aflower.module.pms.api.protocolagent.dto.ProtocolAgentSimpleRespDTO;
import info.qizhi.aflower.module.pms.api.serviceintegration.dto.ServiceIntegrationRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.order.sync.framework.utils.OtaApiUtils.buildOrderListRequest;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.OTA_ROOM_TYPE_REF_NOT_FOUND;

@Service
@Slf4j
public class CtripSyncStrategy implements OtaSyncStrategy {
    @Resource
    private RoomTypeRefService roomTypeRefService;
    @Resource
    private BookingApi bookingApi;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private OtaOrderSynchronizedService otaOrderSynchronizedService;
    @Resource
    private ProtocolAgentApi protocolAgentApi;
    @Resource
    private OTAOrderFailAlertProducer otaOrderFailAlertProducer;

    /**
     * 同步携程订单
     *
     * @param service          服务
     * @param otaApis          接口列表
     * @param gcode            集团代码
     * @param hcode            酒店代码
     * @param lastCheckOutTime 最晚退房时间
     */
    @Override
    public void syncOrders(ServiceIntegrationRespDTO service,
                           List<OtaApiDO> otaApis,
                           String gcode,
                           String hcode,
                           String lastCheckOutTime) {
        try {
            log.info("开始同步携程订单,酒店代码: {}", hcode);

            // 1. Prepare sync context
            SyncContext context = prepareSyncContext(gcode, hcode, otaApis, lastCheckOutTime);
            if (!context.isValid()) {
                log.error("Invalid sync context for hotel: {}", hcode);
                return;
            }

            // 2. 抓取订单
            List<CtripOrder> orders = fetchOrders(context.orderListApi());
            // 循环orders输出订单号
            orders.forEach(order -> log.info("携程订单号>>>>>>>>>>: {}", order.getOrderID()));
            // 2.1 从orders中排除已同步的订单
            orders = excludeSyncedOrders(orders, context.syncedOrders());
            if (orders != null) {
                orders.forEach(order -> log.info("待处理的携程订单号------------>: {}", order.getOrderID()));
            } else {
                log.warn("经过过滤后，没有需要处理的携程订单");
            }            // 输出日志
            if (orders != null) {
                log.info("需要同步的携程订单数量: {}", orders.size());
            }
            if (CollUtil.isEmpty(orders)) {
                log.info("没有携程订单需要同步,酒店代码: {}", hcode);
                return;
            }
            // 3. 处理订单
            processOrdersIndependently(orders, context, service);
            log.info("携程订单同步完成,酒店代码: {}", hcode);
        } catch (Exception e) {
            log.error("携程订单同步失败,酒店代码: {}", hcode, e);
            throw e;
        }
    }

    @Override
    public void processUploadedJson(String gcode, String hcode, String acceptedJson, String cancelJson, List<String> jsonList) {
        // acceptedJson,cancelJson 为空无需处理，只需要处理jsonList，jsonList不为空，需要处理
        if (CollUtil.isEmpty(jsonList)) {
            return;
        }

        List<CtripOrderDetail> ctripOrderDetails = new ArrayList<>();
        //jsonList里是携程订单json，循环里面的订单详情json,判断如果code==200 则解析订单详情json
        for (String json : jsonList) {
            try {
                CtripOrderDetail ctripOrderDetail = JsonUtils.parseObject(json, CtripOrderDetail.class);
                // 根据响应码判断是否成功获取订单详情
                if (ctripOrderDetail != null && ctripOrderDetail.getCode() == 200) {
                    ctripOrderDetails.add(ctripOrderDetail);
                } else {
                    // 记录失败日志
                    if (ctripOrderDetail != null) {
                        log.error("携程订单详情同步失败，响应码：{}，消息：{}", ctripOrderDetail.getCode(), ctripOrderDetail.getMessage());
                    } else {
                        log.error("携程订单详情同步失败，响应为空");
                    }
                }
            }
            catch (Exception e) {
                log.error("解析携程订单详情失败", e);
            }
        }

        if (CollUtil.isEmpty(ctripOrderDetails)) {
            log.info("没有有效的携程订单详情需要处理");
            return;
        }
        ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.CTRIP_PREPAY.getName()).getData();
        if (protocolAgentSimpleRespDTO == null) {
            log.error("Missing required Ctrip protocol agent for hotel: {}", hcode);
            return;
        }
        try {
            // 准备同步上下文
            SyncContext context = new SyncContext(
                    gcode,
                    hcode,
                    null,
                    null,
                    roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(gcode).setHcode(hcode).setChannel(ChannelEnum.CTRIP.getCode())),
                    getLastCheckOutTime(gcode),
                    System.currentTimeMillis(),
                    otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                            .setPlatform(ChannelEnum.CTRIP.getCode())
                            .setGcode(gcode)
                            .setHcode(hcode)),
                    protocolAgentSimpleRespDTO.getPaCode()
            );
//            if (!context.isValid()) {
//                log.error("Invalid sync context for hotel: {}", hcode);
//                return;
//            }

            // 排除已同步的订单
            ctripOrderDetails = excludeSyncedOrderDetails(ctripOrderDetails, context.syncedOrders());

            if (CollUtil.isEmpty(ctripOrderDetails)) {
                log.info("过滤后没有需要处理的携程订单");
                return;
            }

            // 处理订单详情
            List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();
            ServiceIntegrationRespDTO service = new ServiceIntegrationRespDTO().setGcode(gcode).setHcode(hcode);
            for (CtripOrderDetail detail : ctripOrderDetails) {
                try {
                    OtaOrderSynchronizedSaveVO record = processOrder(detail, context, service); // service参数设为null
                    if (record != null) {
                        syncRecords.add(record);
                    }
                } catch (Exception e) {
                    log.error("处理携程订单失败，订单ID: {}",
                            detail != null && detail.getData() != null ? detail.getData().getOrderID() : "unknown", e);
                }
            }

            // 批量保存同步记录
            if (CollUtil.isNotEmpty(syncRecords)) {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
                log.info("成功处理并保存{}条携程订单同步记录", syncRecords.size());
            }
        } catch (Exception e) {
            log.error("处理上传的携程订单JSON失败", e);
            throw e;
        }
    }

    /**
     * 排除已同步的订单详情
     * 规则与excludeSyncedOrders类似，但针对的是CtripOrderDetail对象
     */
    private List<CtripOrderDetail> excludeSyncedOrderDetails(List<CtripOrderDetail> orderDetails,
                                                             List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(orderDetails)) {
            return Collections.emptyList();
        }

        return orderDetails.stream()
                .filter(detail -> {
                    if (detail == null || detail.getData() == null) {
                        return false;
                    }

                    String orderId = detail.getData().getOrderID();
                    String orderStatus = detail.getData().getOrderStatusType();

                    if ("已过离店日期".equals(detail.getData().getOrderStatusDisplay())) {
                        return false;
                    }

                    // 排除非 Receipt、Canceled 状态的订单
                    if (!List.of(
                            OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode(),
                            OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode()
                    ).contains(orderStatus)) {
                        return false;
                    }

                    // 检查 syncedOrders 中是否存在相同订单号的记录
                    boolean existsInSynced = syncedOrders.stream()
                            .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId));

                    // 情况1：orderStatus 是 Receipt，只要 syncedOrders 中存在就排除
                    if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode().equals(orderStatus)) {
                        return !existsInSynced;
                    }

                    // 情况2：orderStatus 是 Canceled
                    if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode().equals(orderStatus)) {
                        // 如果 syncedOrders 中不存在该订单号，排除
                        if (!existsInSynced) {
                            return false;
                        }
                        // 如果 syncedOrders 中存在 Canceled 状态的相同订单号，排除
                        boolean shouldExclude = syncedOrders.stream()
                                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId)
                                        && OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode()
                                        .equals(synced.getOtaOrderState()));
                        return !shouldExclude;
                    }

                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取最晚离店时间
     * @param gcode 集团代码
     * @return 最晚离店时间
     */
    private String getLastCheckOutTime(String gcode) {
        // 获取最晚离店时间
        GeneralConfigRespDTO generalConfig = generalConfigApi.getGeneralConfig(new GeneralConfigReq2DTO()
                .setGcode(gcode)
                .setHcode(NumberEnum.ZERO.getNumber())
                .setCode(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())
                .setType(GeneralConfigTypeEnum.LATEST_CHECK_OUT_TIME.getCode())).getData();
        if (generalConfig == null) {
            throw new RuntimeException("[订单同步] 获取最晚离店时间失败");
        }
        String lastCheckOutTime = generalConfig.getValue();
        // 检查是否为HH:mm格式的时间
        if (StrUtil.isBlank(lastCheckOutTime) || !lastCheckOutTime.matches("\\d{2}:\\d{2}")) {
            throw new RuntimeException("[订单同步] 最晚离店时间格式错误,集团代码:" + gcode);
        }
        return lastCheckOutTime;
    }

    /**
     * 如果CtripOrder状态非Receipt、Canceled状态，则排除该订单。
     * 如果 CtripOrder 状态是 Receipt（已接单）：
     * 只要 syncedOrders 中存在相同订单号（无论状态如何），则排除该订单。
     * <p>
     * 如果 CtripOrder 状态是 Canceled（已取消）：
     * 如果 syncedOrders 中存在相同订单号且状态为 Receipt，且存在相同订单号且状态为 Canceled，则排除该订单。
     * 如果 syncedOrders 中存在相同订单号且状态为 Canceled，也排除该订单。
     * 如果 syncedOrders 中不存在该订单号，则排除该订单（即只保留未同步的已取消订单）。
     *
     * @param orders
     * @param syncedOrders
     * @return
     */
    private List<CtripOrder> excludeSyncedOrders(List<CtripOrder> orders, List<OtaOrderSynchronizedDO> syncedOrders) {
        if (CollUtil.isEmpty(orders)) {
            return null;
        }
        if (CollUtil.isEmpty(syncedOrders)) {
            return orders.stream()
                    .filter(order -> OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode().equals(order.getOrderStatusType())
                            || OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode().equals(order.getOrderStatusType()))
                    .collect(Collectors.toList());
        }

        return orders.stream()
                .filter(order -> {
                    String orderId = order.getOrderID();
                    String orderStatus = order.getOrderStatusType();

                    // 排除已过离店日期的订单
                    if ("已过离店日期".equals(order.getOrderStatusDisplay())) {
                        return false;
                    }

                    // 排除非 Receipt、Canceled 状态的订单
                    if (!List.of(OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode(), OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode()).contains(orderStatus)) {
                        return false;
                    }

                    // 检查 syncedOrders 中是否存在相同订单号的记录
                    boolean existsInSynced = syncedOrders.stream()
                            .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId));

                    // 情况1：orderStatus 是 Receipt，只要 syncedOrders 中存在就排除
                    if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode().equals(orderStatus)) {
                        return !existsInSynced;
                    }

                    // 情况2：orderStatus 是 Canceled
                    if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode().equals(orderStatus)) {
                        // 如果 syncedOrders 中不存在该订单号，排除
                        if (!existsInSynced) {
                            return false;
                        }
                        // 如果 syncedOrders 中存在 Receipt 或 Canceled 状态的相同订单号，排除
                        boolean shouldExclude = syncedOrders.stream()
                                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId)
                                        && OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode().equals(synced.getOtaOrderState()));
                        return !shouldExclude;
                    }

                    // 其他情况（理论上不会走到这里）
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取携程订单的计划离店时间
     *
     * @param ctripOrderDetail 携程订单详情
     * @param lastCheckOutTime 默认离店时间（格式：HH:mm，如"14:00"）
     * @return 离店时间（LocalDateTime）
     */
    public static LocalDateTime getCtripPlanCheckOutTime(CtripOrderDetail ctripOrderDetail, String lastCheckOutTime) {
        // 验证参数
        if (ctripOrderDetail == null || ctripOrderDetail.getData() == null || lastCheckOutTime == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        try {
            String planCheckOutTimeStr;

            // 处理钟点房（liveDays == -1）
            if (ctripOrderDetail.getData().getLiveDays() == -1) {
                planCheckOutTimeStr = processHourlyRoom(ctripOrderDetail);
            }
            // 处理非钟点房
            else {
                planCheckOutTimeStr = processRegularRoom(ctripOrderDetail, lastCheckOutTime);
            }

            // 转换为LocalDateTime
            return LocalDateTime.parse(planCheckOutTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));

        } catch (Exception e) {
            throw new RuntimeException("解析携程离店时间失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理钟点房离店时间
     */
    private static String processHourlyRoom(CtripOrderDetail ctripOrderDetail) {
        String[] timeRange = ctripOrderDetail.getData().getArrivalEarlyAndLatestTime().split("-");
        if (timeRange.length != 2) {
            throw new IllegalArgumentException("无效的ArrivalEarlyAndLatestTime格式");
        }

        String endTime = timeRange[1].trim();
        String arrivalDate = ctripOrderDetail.getData().getArrival();

        return arrivalDate + " " + endTime;
    }

    /**
     * 处理普通房离店时间
     */
    private static String processRegularRoom(CtripOrderDetail ctripOrderDetail, String lastCheckOutTime) {
        // 解析到达和离开日期（如："2025/05/05 - 2025/05/06"）
        String[] dateRange = ctripOrderDetail.getData().getArrivalAndDeparture().split(" - ");
        if (dateRange.length != 2) {
            throw new IllegalArgumentException("无效的ArrivalAndDeparture格式");
        }
        String departureDate = dateRange[1].replace("/", "-");
        return departureDate + " " + lastCheckOutTime;
    }


    private SyncContext prepareSyncContext(String gcode, String hcode, List<OtaApiDO> otaApis, String lastCheckOutTime) {
        OtaApiDO orderListApi = OtaApiUtils.findApi(otaApis, OtaMethodTypeEnum.ORDER_LIST, ChannelEnum.CTRIP);
        OtaApiDO orderDetailApi = OtaApiUtils.findApi(otaApis, OtaMethodTypeEnum.ORDER_DETAIL, ChannelEnum.CTRIP);

        if (orderListApi == null || orderDetailApi == null) {
            log.error("Missing required Ctrip APIs for hotel: {}", hcode);
            return SyncContext.invalid();
        }
        ProtocolAgentSimpleRespDTO protocolAgentSimpleRespDTO = protocolAgentApi.getProtocolAgentSimple(hcode, ProtocolAgentEnum.CTRIP_PREPAY.getName()).getData();
        if (protocolAgentSimpleRespDTO == null) {
            log.error("Missing required Ctrip protocol agent for hotel: {}", hcode);
            return SyncContext.invalid();
        }
        return new SyncContext(
                gcode,
                hcode,
                orderListApi,
                orderDetailApi,
                roomTypeRefService.getRoomTypeRefList(new RoomTypeRefReqVO().setGcode(gcode).setHcode(hcode).setChannel(ChannelEnum.CTRIP.getCode())),
                lastCheckOutTime,
                System.currentTimeMillis(),
                otaOrderSynchronizedService.getOtaOrderSynchronizedList(new OtaOrderSynchronizedReqVO()
                        .setPlatform(ChannelEnum.CTRIP.getCode())
                        .setGcode(gcode)
                        .setHcode(hcode)),
                protocolAgentSimpleRespDTO.getPaCode()
        );
    }

    /**
     * 这里需要跑3次
     *
     * @param orderListApi
     * @return
     */
    private List<CtripOrder> fetchOrders(OtaApiDO orderListApi) {
        try {
            // 获取新订状态的订单列表
            String receiptResponse = HttpUtils.post(
                    orderListApi.getUrl(),
                    OtaApiUtils.parseHeaders(orderListApi.getHeaders()),
                    buildOrderListRequest(orderListApi.getBody(), "0", "None", OtaOrderStatusEnum.CTRIP_ORDER_TYPE_ALL.getCode(), "FormDate")
            );
            return CtripOrderParser.parseOrderList(orderListApi.getHcode(), receiptResponse);
        } catch (Exception e) {
            log.error("Failed to fetch ctrip orders", e);
            return Collections.emptyList();
        }
    }

    /**
     * 去重，相同的订单号保留已取消状态的
     *
     * @param receiptOrders  已接待订单（Receipt状态）
     * @param canceledOrders 已取消订单（Canceled状态）
     * @return 去重后的订单列表（相同订单号只保留Canceled状态的记录）
     */
    private List<CtripOrder> removeDuplicationCtripOrder(List<CtripOrder> receiptOrders, List<CtripOrder> canceledOrders) {
        if (CollUtil.isEmpty(receiptOrders) && CollUtil.isEmpty(canceledOrders)) {
            return Collections.emptyList();
        }

        // 1. 将 canceledOrders 转换为 Map，按订单号分组（确保 Canceled 订单优先）
        Map<String, CtripOrder> canceledOrderMap = canceledOrders.stream()
                .collect(Collectors.toMap(
                        CtripOrder::getOrderID,
                        order -> order,
                        (existing, replacement) -> existing // 如果键冲突，保留现有值（理论上不会发生）
                ));

        // 2. 遍历 receiptOrders，仅保留不存在于 canceledOrderMap 的订单
        List<CtripOrder> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(receiptOrders)) {
            receiptOrders.stream()
                    .filter(order -> !canceledOrderMap.containsKey(order.getOrderID()))
                    .forEach(result::add);
        }

        // 3. 添加所有 canceledOrders（已自动去重）
        result.addAll(canceledOrderMap.values());
        log.info("已接待订单数: {}, 已取消订单数: {}", receiptOrders.size(), canceledOrders.size());
        log.info("去重后的订单数: {}", result.size());
        return result;
    }

    /**
     * Process each order independently with its own transaction
     */
    private void processOrdersIndependently(List<CtripOrder> orders, SyncContext context, ServiceIntegrationRespDTO service) {
        List<CtripOrderDetail> orderDetails = fetchCtripOrderDetailsConcurrently(
                context.orderDetailApi(),
                orders
        );
        log.info("携程需要同步的订单详情数:{}", orderDetails.size());

        List<OtaOrderSynchronizedSaveVO> syncRecords = new ArrayList<>();

        for (CtripOrderDetail detail : orderDetails) {
            try {
                OtaOrderSynchronizedSaveVO record = processOrderInTransaction(detail, context, service);
                if (record != null) {
                    syncRecords.add(record);
                }
            } catch (Exception e) {
                log.error("处理携程订单失败，订单ID: {}, 错误: {}",
                        detail != null && detail.getData() != null ? detail.getData().getOrderID() : "unknown",
                        e.getMessage());
            }
        }

        if (CollUtil.isNotEmpty(syncRecords)) {
            try {
                otaOrderSynchronizedService.batchCreateOtaOrderSynchronized(syncRecords);
            } catch (Exception e) {
                log.error("批量创建同步记录失败", e);
                // Optionally retry individual records here
            }
        }
    }

    private OtaOrderSynchronizedSaveVO processOrderInTransaction(CtripOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        return processOrder(detail, context, service);
    }

    /**
     * 处理携程订单详情，根据订单类型进行创建或取消操作
     *
     * @param detail  携程订单详情对象
     * @param context 同步上下文，包含必要的业务数据
     * @return 返回同步记录VO，如果处理失败则返回null
     */
    private OtaOrderSynchronizedSaveVO processOrder(CtripOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        // 参数校验
        if (detail == null || detail.getData() == null || detail.getData().getOrderStatusType() == null) {
            log.warn("无效的携程订单详情");
            return null;
        }

        String orderStatusType = detail.getData().getOrderStatusType();
        String orderId = detail.getData().getOrderID();

        if (StrUtil.isBlank(orderId)) {
            log.warn("订单号为空，无法处理订单");
            return null;
        }

        // 处理不同订单类型
        // 修改订单
//    if (OtaOrderStatusEnum.CTRIP_ORDER_TYPE_M.getCode().equals(orderStatusType)) {
//        log.error("订单修改类型暂未实现，订单号：{}", orderId);
//        // 可选：抛出异常或返回特定状态
//        return null;
//    }
        log.info("处理订单时的订单类型：{}, 订单号：{}", orderStatusType, orderId);
        // 新订和续住
        if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode().equals(orderStatusType)) {
            return handleNewOrContinueOrder(detail, context, service, orderStatusType, orderId);
        }
        // 取消订单
        if (OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_CANCELED.getCode().equals(orderStatusType)) {
            String bookNo = cancelOrder(orderId, context.syncedOrders());
            if (bookNo != null) {
                return buildSyncRecord(context, orderId, bookNo, orderStatusType);
            }
        } else {
            log.warn("未知的订单类型：{}, 订单号：{}", orderStatusType, orderId);
        }

        return null;
    }

    // 处理新订和续住订单的公共逻辑
    private OtaOrderSynchronizedSaveVO handleNewOrContinueOrder(CtripOrderDetail detail,
                                                                SyncContext context,
                                                                ServiceIntegrationRespDTO service,
                                                                String orderType,
                                                                String orderId) {
        String bookNo = createCtripBook(detail, context, service);
        log.info("创建携程订单成功，酒店代码：{} 订单号：{}", service.getHcode(), orderId);
        if (bookNo != null) {
            return buildSyncRecord(context, orderId, bookNo, orderType);
        }
        return null;
    }


    /**
     * 调用 bookingApi 创建携程预订单
     *
     * @param detail  携程订单详情
     * @param context 同步上下文
     * @param service 集成服务响应对象
     * @return 返回PMS预订单号，如果创建失败则返回null
     */
    private String createCtripBook(CtripOrderDetail detail, SyncContext context, ServiceIntegrationRespDTO service) {
        // 检查是否已同步
        List<OtaOrderSynchronizedDO> syncedOrders = context.syncedOrders();
        String orderId = detail.getData().getOrderID();

        if (syncedOrders != null && syncedOrders.stream()
                .anyMatch(synced -> synced.getOtaOrderNo().equals(orderId))) {
            log.info("订单同步过，跳过创建，酒店代码，{} 订单号：{}", service.getHcode(), orderId);
            return null;
        }

        // 构建请求参数并调用创建接口
        OtaBookOpenReqDTO req = buildCtripBookSaveReqVO(detail, service, context, context.lastCheckOutTime());
        CommonResult<OtaBookOpenRespDTO> result = bookingApi.createBook(req);

        if (!CommonResult.isSuccess(result.getCode())) {
            log.error("创建携程预订单失败，酒店代码，{},订单号：{}，错误信息：{}", service.getHcode(), orderId, result.getMsg());
            otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                    .setGcode(context.gcode())
                    .setHcode(context.hcode())
                    .setPlatform(ChannelEnum.CTRIP.getCode())
                    .setSyncTime(context.syncTime())
                    .setOtaOrderNo(orderId)
                    .setOtaOrderState("fail")
                    .setPmsBookNo("0")
                    .setRemark(result.getMsg()));
            // 没有房
            if (result.getCode() == 1009001027) {
                OTAOrderFailAlertProducer.OTAOrderFailAlertBO otaOrderFailAlertBO = new OTAOrderFailAlertProducer.OTAOrderFailAlertBO();
                otaOrderFailAlertBO.setHcode(service.getHcode());
                otaOrderFailAlertBO.setChannelName("携程");
                otaOrderFailAlertBO.setOutOrderNo(orderId);
                otaOrderFailAlertBO.setOtaRtName(detail.getData().getRoomName());
                otaOrderFailAlertBO.setPlanCheckinTime(req.getPlanCheckinTime());
                otaOrderFailAlertBO.setPlanCheckoutTime(req.getPlanCheckoutTime());
                otaOrderFailAlertBO.setGuestName(req.getGuestName());
                otaOrderFailAlertBO.setReason(result.getMsg());
                otaOrderFailAlertProducer.sendOTAOrderFailAlertMessage(otaOrderFailAlertBO);
            }
            return null;
        }

        return result.getData().getPmsResNo();
    }

    /**
     * 封装携程订单数据  TODO 需要区分 去哪儿 携程 同程 订单
     *
     * @param ctripOrderDetail          携程订单信息
     * @param serviceIntegrationRespDTO 服务集成信息
     * @param context
     * @param lastCheckOutTime          最后退房时间
     * @return 封装后的订单保存请求对象
     */
    private OtaBookOpenReqDTO buildCtripBookSaveReqVO(CtripOrderDetail ctripOrderDetail,
                                                      ServiceIntegrationRespDTO serviceIntegrationRespDTO,
                                                      SyncContext context,
                                                      String lastCheckOutTime) {
        // 空值检查
        if (ctripOrderDetail == null || ctripOrderDetail.getData() == null) {
            throw new IllegalArgumentException("携程订单信息不能为空");
        }
        OrderData orderData = ctripOrderDetail.getData();
        // 初始化BookSaveReqVO对象并设置基本信息
        OtaBookOpenReqDTO bookSaveReqVO = new OtaBookOpenReqDTO().setGcode(serviceIntegrationRespDTO.getGcode())
                .setHcode(serviceIntegrationRespDTO.getHcode())
                .setChannelCode(ChannelEnum.CTRIP.getCode())
                .setOrderSource(OrderSrcEnum.AGENT.getCode())
                .setGuestSrcType(GuestSrcTypeEnum.AGENT.getCode())
                .setBookType(OrderTypeEnum.GENERAL.getCode());
        // 根据下单网站修改渠道代码
        ChannelEnum channel = ChannelEnum.getChannelEnumByCode(orderData.getAllinanceName());
        if (channel != null) {
            bookSaveReqVO.setChannelCode(channel.getCode());
        }

        // 获取计划入住和退房时间
        LocalDateTime planCheckInTime = getCtripPlanCheckInTime(ctripOrderDetail);
        LocalDateTime planCheckoutTime = getCtripPlanCheckOutTime(ctripOrderDetail, lastCheckOutTime);

        // 判断是否是钟点房
        if (orderData.getLiveDays() == -1) {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.HOUR_ROOM.getCode());
            // 计算入住时长
            int liveHours = (int) Duration.between(planCheckInTime, planCheckoutTime).toHours();
            bookSaveReqVO.setHourCode(HourRoomEnum.getCodeByValue(liveHours));
        } else {
            bookSaveReqVO.setCheckinType(CheckInTypeEnum.ALL_DAY.getCode());
        }

        bookSaveReqVO.setPlanCheckinTime(planCheckInTime);
        bookSaveReqVO.setPlanCheckoutTime(planCheckoutTime);

        // 设置客人信息和联系方式
        bookSaveReqVO.setGuestCode(context.paCode())
                .setGuestName(ProtocolAgentEnum.CTRIP_PREPAY.getName());
        bookSaveReqVO.setContact(orderData.getClientName())
                .setCheckinPerson(orderData.getClientName())
                .setIsSendSms(BooleanEnum.FALSE.getValue())
                .setOutOrderNo(orderData.getOrderID());

        // 处理订单备注信息
        // 比如：06-13开票金额140结算价123.2 不含餐；06-14开票金额140结算价123.2不含餐
        StringBuilder outOrderRemark = new StringBuilder(String.format("|订单ID[%s]|", orderData.getOrderID()));
        Invoice invoice = orderData.getInvoice();
        if (invoice != null) {
            outOrderRemark.append(invoice.getInfo());
        }
        List<OrderRoomPrice> orderRoomPrices = orderData.getOrderRoomPrices();
        if (orderRoomPrices != null && !orderRoomPrices.isEmpty()) {
            for (OrderRoomPrice orderRoomPrice : orderRoomPrices) {
                outOrderRemark.append("|").append(orderRoomPrice.getLivingDate()).append("房价").append(orderRoomPrice.getSellPrice())
                        .append("结算价").append(orderRoomPrice.getCostPrice()).append("/")
                        .append(orderRoomPrice.getMealInfo());
            }
        }
        bookSaveReqVO.setOutOrderRemark(outOrderRemark.toString());

        // 处理预订批次信息
        List<OtaBookOpenReqDTO.Batch> batchList = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch batch = new OtaBookOpenReqDTO.Batch();
        String batchNo = DateUtil.format(planCheckInTime, "yyyy-MM-dd") + "/" + DateUtil.format(planCheckoutTime, "yyyy-MM-dd");
        batch.setBatchNo(batchNo)
                .setDays(orderData.getLiveDays() == -1 ? 0 : orderData.getLiveDays())
                .setPlanCheckinTime(planCheckInTime)
                .setPlanCheckoutTime(planCheckoutTime);

        // 处理预订房型信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType> bookRoomTypes = CollUtil.newArrayList();
        OtaBookOpenReqDTO.Batch.BookRoomType bookRoomType = new OtaBookOpenReqDTO.Batch.BookRoomType();
        Long roomID = orderData.getRoomID();
        RoomTypeRefDO roomTypeRefDO = context.roomTypeRefs().stream()
                .filter(roomTypeRef ->
                        roomTypeRef.getOtaProducts() != null &&
                                roomTypeRef.getOtaProducts().stream()
                                        .anyMatch(product ->
                                                product.getProductCode() != null &&
                                                        product.getProductCode().equals(roomID.toString())
                                        ) &&
                                ChannelEnum.CTRIP.getCode().equals(roomTypeRef.getChannel())
                )
                .findFirst()
                .orElse(null);
        if (roomTypeRefDO != null) {
            bookRoomType.setRtCode(roomTypeRefDO.getRoomTypeCode());
        } else {
            // TODO 是不是要将这个错误记录到订单同步日志表？
            log.error("携程订单房型代码不存在，酒店代码：{} 房型代码：{} 订单ID：{}", serviceIntegrationRespDTO.getHcode(), roomID, orderData.getOrderID());
            String alertMsg = String.format(
                    "**OTA同步异常报警**\n" +
                            "> 异常类型: `%s`\n" +
                            "> 异常信息: `%s`\n" +
                            "> 发生时间: `%s`\n" +
                            "请及时处理！",
                    "携程订单房型代码不存在，酒店代码:" + serviceIntegrationRespDTO.getHcode(),
                    "房型代码: " + roomID + "订单ID：" + orderData.getOrderID(),
                    LocalDateTime.now()
            );
            WeChatBotNotifier.sendAlert(alertMsg);
            throw exception(OTA_ROOM_TYPE_REF_NOT_FOUND);
        }
        bookRoomType.setBkNum(0);
        bookRoomType.setRoomNum(orderData.getQuantity());

        // 处理每日价格信息
        List<OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice> dayPrices = CollUtil.newArrayList();
        orderData.getOrderRoomPrices().forEach(orderRoomPrice -> {
            try {
                OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice dayPrice = new OtaBookOpenReqDTO.Batch.BookRoomType.DayPrice();
                LocalDate priceDate = LocalDate.parse(orderRoomPrice.getOriginLivingDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                dayPrice.setPriceDate(priceDate);
                dayPrice.setRoomBkNum(orderRoomPrice.getBreakfast());
                dayPrice.setWeek(priceDate.getDayOfWeek().getValue());
                // 金额转换（元 → 分）
                dayPrice.setPrice(convertYuanToFen(orderRoomPrice.getSellPrice()));
                dayPrice.setVipPrice(convertYuanToFen(orderRoomPrice.getCostPrice()));
                dayPrices.add(dayPrice);
            } catch (DateTimeParseException e) {
                log.error("日期格式解析错误，订单ID：{}，日期：{}", orderData.getOrderID(), orderRoomPrice.getOriginLivingDate(), e);
                throw new IllegalArgumentException("日期格式错误", e);
            }
        });

        // 组装房型和批次信息
        bookRoomType.setDayPrices(dayPrices);
        bookRoomTypes.add(bookRoomType);
        batch.setBookRoomTypes(bookRoomTypes);
        batchList.add(batch);
        bookSaveReqVO.setBatches(batchList);

        return bookSaveReqVO;
    }

    // 金额转换方法
    private long convertYuanToFen(String yuan) {
        BigDecimal yuanValue = new BigDecimal(yuan);
        return yuanValue.multiply(BigDecimal.valueOf(100)).longValue();
    }


    /**
     * 获取携程的入住时间
     *
     * @param ctripOrderDetail 携程订单信息
     * @return LocalDateTime
     */
    private LocalDateTime getCtripPlanCheckInTime(CtripOrderDetail ctripOrderDetail) {
        String planCheckInTime;
        // 判断是否为钟点房
        if (ctripOrderDetail.getData().getLiveDays() == -1) {
            // 获取并处理钟点房的入住时间
            String[] ctripHour = ctripOrderDetail.getData().getArrivalEarlyAndLatestTime().split("-");
            // 携程钟点房
            planCheckInTime = ctripOrderDetail.getData().getArrival() + " " + ctripHour[0];
        } else {
            // 处理非钟点房的入住时间
            String[][] arrivalEarlyAndLatestTime = Arrays.stream(ctripOrderDetail.getData().getArrivalEarlyAndLatestTime().split(" - "))
                    .map(e -> e.split(" "))
                    .toArray(String[][]::new);
            String arrival = ctripOrderDetail.getData().getArrival();
            // 拼接小时
            String hour = "00:00".equals(arrivalEarlyAndLatestTime[0][1]) ? "12:00" : arrivalEarlyAndLatestTime[0][1];
            planCheckInTime = arrival + " " + hour;
        }
        // 解析并返回 LocalDateTime 对象
        return DateUtil.parseLocalDateTime(planCheckInTime, "yyyy-MM-dd HH:mm");
    }

    /**
     * 构建同步记录VO
     *
     * @param context   同步上下文
     * @param orderId   OTA订单号
     * @param bookNo    PMS预订单号
     * @param orderType 订单类型
     * @return 返回 OtaOrderSynchronizedSaveVO 对象
     */
    private OtaOrderSynchronizedSaveVO buildSyncRecord(SyncContext context, String orderId, String bookNo, String orderType) {
        return new OtaOrderSynchronizedSaveVO()
                .setGcode(context.gcode())
                .setHcode(context.hcode())
                .setPmsBookNo(bookNo)
                .setOtaOrderNo(orderId)
                .setOtaOrderState(orderType)
                .setSyncTime(context.syncTime())
                .setPlatform(ChannelEnum.CTRIP.getCode());
    }



    /**
     * 取消OTA订单
     * @param orderId 要取消的订单号
     * @param syncedOrders 已同步的订单记录列表
     * @return PMS预订单号(bookNo)，如果取消失败或不符合条件则返回null
     */
    private String cancelOrder(String orderId, List<OtaOrderSynchronizedDO> syncedOrders) {
        // 1. 参数校验
        if (CollUtil.isEmpty(syncedOrders)) {
            log.debug("[取消订单] 无可取消订单：无同步记录");
            return null;
        }

        // 2. 查找所有匹配的订单记录（按同步时间倒序）
        List<OtaOrderSynchronizedDO> matchedOrders = syncedOrders.stream()
                .filter(order -> order.getOtaOrderNo().equals(orderId))
                .sorted(Comparator.comparing(OtaOrderSynchronizedDO::getSyncTime).reversed())
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(matchedOrders)) {
            log.debug("[取消订单] 无可取消订单：未找到匹配的订单记录，orderId={}", orderId);
            return null;
        }

        // 3. 获取最新的一条记录（取消操作只针对最新状态）
        OtaOrderSynchronizedDO latestOrder = matchedOrders.getFirst();

        // 4. 检查订单是否可取消（只允许取消RECEIPT状态的订单）
        if (!OtaOrderStatusEnum.CTRIP_ORDER_STATUSS_RECEIPT.getCode().equals(latestOrder.getOtaOrderState())) {
            log.debug("[取消订单] 订单不可取消：当前状态={}, orderId={}", latestOrder.getOtaOrderState(), orderId);
            return null;
        }

        // 5. 执行取消操作
        try {
            OtaBookCancelOpenReqDTO cancelReq = new OtaBookCancelOpenReqDTO()
                    .setGcode(latestOrder.getGcode())
                    .setHcode(latestOrder.getHcode())
                    .setBookNo(latestOrder.getPmsBookNo());

            CommonResult<?> result = bookingApi.cancleBook(cancelReq);

            if (!result.isSuccess()) {
                log.error("[取消订单] 取消失败 orderId={}, msg={}", orderId, result.getMsg());
                // 记录失败状态
                otaOrderSynchronizedService.createOtaOrderSynchronized(new OtaOrderSynchronizedSaveVO()
                        .setGcode(latestOrder.getGcode())
                        .setHcode(latestOrder.getHcode())
                        .setPlatform(ChannelEnum.CTRIP.getCode())
                        .setSyncTime(System.currentTimeMillis())
                        .setOtaOrderNo(orderId)
                        .setOtaOrderState("fail")
                        .setRemark(result.getMsg())
                        .setPmsBookNo(latestOrder.getPmsBookNo()));
                return null;
            }

            log.info("[取消订单] 取消成功 orderId={}, pmsBookNo={}", orderId, latestOrder.getPmsBookNo());
            return latestOrder.getPmsBookNo();
        } catch (Exception e) {
            log.error("[取消订单] 取消异常 orderId={}", orderId, e);
            return null;
        }
    }



    /**
     * 获取携程订单详情 采用多线程的方式
     *
     * @param orderDetailApi         获取订单详情api
     * @param needSyncCtripOrderList 需要同步的订单列表
     * @return 携程订单详情列表
     */
    private List<CtripOrderDetail> fetchCtripOrderDetailsConcurrently(OtaApiDO orderDetailApi, List<CtripOrder> needSyncCtripOrderList) {
        // 创建线程池 - 根据实际情况调整线程数
        ExecutorService executor = Executors.newFixedThreadPool(Math.min(5, needSyncCtripOrderList.size()));
        List<Future<CtripOrderDetail>> futures = new ArrayList<>();
        List<CtripOrderDetail> resultList = Collections.synchronizedList(new ArrayList<>());

        // 提交所有任务到线程池
        for (CtripOrder ctripOrder : needSyncCtripOrderList) {
            futures.add(executor.submit(() -> getCtripOrderDetail(orderDetailApi, ctripOrder)));
        }

        // 收集结果并处理异常
        for (Future<CtripOrderDetail> future : futures) {
            try {
                CtripOrderDetail detail = future.get(10, TimeUnit.SECONDS); // 设置超时时间
                if (detail != null) {
                    resultList.add(detail);
                }
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                log.error("获取携程订单详情异常", e);
            }
        }

        // 关闭线程池
        executor.shutdown();
        try {
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }

        return resultList;
    }

    /**
     * 同步携程订单详情
     *
     * @param otaApiDO   OTA API配置对象，包含请求的URL、方法、渠道等信息
     * @param ctripOrder 携程订单对象，包含订单ID、表单ID、token等信息
     * @return CtripOrderDetail 返回携程订单详情对象，如果请求失败或解析错误则返回null
     */
    private CtripOrderDetail getCtripOrderDetail(OtaApiDO otaApiDO, CtripOrder ctripOrder) {
        // 检查请求方法和渠道是否匹配携程订单详情的要求
        if (!OtaMethodTypeEnum.ORDER_DETAIL.getCode().equals(otaApiDO.getMethod()) || !ChannelEnum.CTRIP.getCode().equals(otaApiDO.getChannel())) {
            return null;
        }
        try {
            // 处理headers，将|||分隔符转换为标准的HTTP头格式
            Map<String, String> headers = OtaApiUtils.parseHeaders(otaApiDO.getHeaders());
            // 动态设置日期参数
            JSONObject requestBody = JSONUtil.parseObj(otaApiDO.getBody());
            requestBody.set("orderID", ctripOrder.getOrderID());
            requestBody.set("formID", ctripOrder.getFormID());
            requestBody.set("token", ctripOrder.getToken());
            // 发送HTTP请求
            String response = HttpUtils.post(otaApiDO.getUrl(), headers, requestBody.toString());
            CtripOrderDetail orderDetail = JsonUtils.parseObject(response, CtripOrderDetail.class);

            // 根据响应码判断是否成功获取订单详情
            if (orderDetail != null && orderDetail.getCode() == 200) {
                return orderDetail;
            } else {
                // 记录失败日志
                if (orderDetail != null) {
                    log.error("携程订单详情同步失败，响应码：{}，消息：{}",
                            orderDetail.getCode(),
                            orderDetail.getMessage());
                } else {
                    log.error("携程订单详情同步失败，响应为空");
                }
            }
        } catch (Exception e) {
            // 记录异常日志
            log.error("携程订单详情同步异常", e);
            return null;
        }
        return null;
    }

    /**
     * 定义了一个不可变的上下文记录类 SyncContext，用于承载同步所需的数据。其中：
     * invalid() 返回一个所有字段为 null 的无效上下文对象。
     * isValid() 判断上下文中两个关键 API 对象是否非空，以确认上下文是否有效。
     *
     * @param gcode
     * @param hcode
     * @param orderListApi     获取订单列表api
     * @param orderDetailApi   获取订单详情api
     * @param roomTypeRefs     获取房型关联数据
     * @param lastCheckOutTime 最晚退房时间
     * @param syncTime         同步时间
     * @param syncedOrders     已同步订单列表
     * @param paCode 中介代码
     */
    private record SyncContext(
            String gcode,
            String hcode,
            OtaApiDO orderListApi,
            OtaApiDO orderDetailApi,
            List<RoomTypeRefDO> roomTypeRefs,
            String lastCheckOutTime,
            Long syncTime,
            List<OtaOrderSynchronizedDO> syncedOrders,
            String paCode
    ) {
        public static SyncContext invalid() {
            return new SyncContext(null, null, null, null, null, null, null, null, null);
        }

        public boolean isValid() {
            return (orderListApi != null && orderDetailApi != null) || CollUtil.isEmpty(roomTypeRefs);
        }
    }
}
