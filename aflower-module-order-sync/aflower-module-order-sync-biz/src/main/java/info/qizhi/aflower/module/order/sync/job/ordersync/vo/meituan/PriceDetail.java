package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "价格详情")
public class PriceDetail {
    @Schema(description = "原始价格")
    private Integer originalPrice;

    @Schema(description = "本地原始价格")
    private Integer originalPriceLocal;

    @Schema(description = "价格")
    private Integer price;

    @Schema(description = "价格描述")
    private String priceDesc;

    @Schema(description = "本地价格")
    private Integer priceLocal;

    @Schema(description = "是否显示原始价格")
    private Boolean showOriginalPrice;
}
