package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "美团订单详情")
public class MeituanOrderDetail {

    @Schema(description = "酒店地址")
    private String address;

    @Schema(description = "是否协议托管")
    private Boolean agreementHosting;

    @Schema(description = "预约状态")
    private Integer appointmentStatus;

    @Schema(description = "预约创建时间(时间戳)")
    private Long aptCreatTime;

    @Schema(description = "预约创建时间字符串")
    private String aptCreatTimeString;

    @Schema(description = "到达时间(时间戳)")
    private Long arriveTime;

    @Schema(description = "到达时间字符串")
    private String arriveTimeStr;

    @Schema(description = "自动接受状态")
    private Integer autoAccept;

    @Schema(description = "自动订单确认")
    private Integer autoOrderConfirm;

    @Schema(description = "业务卡数量")
    private Integer bizCardCounts;

    @Schema(description = "业务促销详情列表")
    private List<BizPromotionDetail> bizPromotionDetail;

    @Schema(description = "是否业务退款")
    private Boolean bizRefund;

    @Schema(description = "是否业务短信联系")
    private Boolean bizSmsContact;

    @Schema(description = "黑名单标志")
    private String blackFlag;

    @Schema(description = "预订成功时间(时间戳)")
    private Long bookSucTime;

    @Schema(description = "预订成功时间字符串")
    private String bookSucTimeString;

    @Schema(description = "预订到取消时间差")
    private Integer booking2CancelDiffTime;

    @Schema(description = "预订时间(时间戳)")
    private Long bookingTime;

    @Schema(description = "预订时间字符串")
    private String bookingTimeString;

    @Schema(description = "取消费用")
    private Integer cancelFee;

    @Schema(description = "是否取消订单")
    private Boolean cancelOrder;

    @Schema(description = "取消规则列表")
    private List<Object> cancelRules;

    @Schema(description = "卡数量")
    private Integer cardCounts;

    @Schema(description = "卡片列表")
    private List<Card> cards;

    @Schema(description = "渠道标签")
    private Boolean channelLabel;

    @Schema(description = "入住日期(时间戳)")
    private Long checkInDate;

    @Schema(description = "入住日期模型列表")
    private List<CheckInDateModel> checkInDateModels;

    @Schema(description = "入住日期字符串")
    private String checkInDateString;

    @Schema(description = "入住状态")
    private Integer checkInStatus;

    @Schema(description = "入住类型")
    private Integer checkInType;

    @Schema(description = "退房日期(时间戳)")
    private Long checkOutDate;

    @Schema(description = "退房日期字符串")
    private String checkOutDateString;

    @Schema(description = "佣金")
    private Integer commission;

    @Schema(description = "佣金描述模型")
    private CommissionDescModel commissionDescModel;

    @Schema(description = "联系人列表")
    private List<Contact> contacts;

    @Schema(description = "入住人姓名，多个用','号隔开")
    private String checkInPersons;

    @Schema(description = "容器状态")
    private Integer containerStatus;

    @Schema(description = "是否续住订单")
    private Boolean continueLiveOrder;

    @Schema(description = "CRS促销码")
    private String crsPromotionCode;

    @Schema(description = "凌晨标志")
    private Integer dawnFlag;

    @Schema(description = "配送订单")
    private Integer deliveryOrder;

    @Schema(description = "离开信息")
    private Object departure;

    @Schema(description = "折扣类型")
    private Integer discountType;

    @Schema(description = "分销商ID")
    private Integer distributorId;

    @Schema(description = "是否EB保存订单")
    private Boolean ebSaveOrder;

    @Schema(description = "企业名称")
    private String enterpriseName;

    @Schema(description = "是否固定房间")
    private Boolean fixRoom;

    @Schema(description = "底价")
    private Integer floorPrice;

    @Schema(description = "免费提示")
    private Integer freeTip;

    @Schema(description = "融合信息")
    private Object fuseInfo;

    @Schema(description = "FX底价发票信息")
    private Object fxFloorInvoiceInfo;

    @Schema(description = "商品ID")
    private Long goodsId;

    @Schema(description = "商品信息列表")
    private List<GoodsInfo> goodsInfos;

    @Schema(description = "商品标签码")
    private Integer goodsTagCode;

    @Schema(description = "商品类型")
    private Integer goodsType;

    @Schema(description = "商品版本")
    private Integer goodsVersion;

    @Schema(description = "绿色通道标记")
    private Integer greenChannelMark;

    @Schema(description = "担保类型")
    private Integer guaranteeType;

    @Schema(description = "客人列表")
    private List<Guest> guests;

    @Schema(description = "是否有踢出价格")
    private Boolean hasKickOutPrice;

    @Schema(description = "是否有促销")
    private Boolean hasPromotion;

    @Schema(description = "钟点房时间")
    private Integer hourRoomTime;

    @Schema(description = "IM标记")
    private Integer imMark;

    @Schema(description = "是否询价")
    private Boolean inquiryPrice;

    @Schema(description = "保险索赔")
    private Integer insuranceClaim;

    @Schema(description = "发票详情模型")
    private Object invDetailModel;

    @Schema(description = "发票标记")
    private Integer invoiceMark;

    @Schema(description = "发票标签模型")
    private InvoiceTagModel invoiceTagModel;

    @Schema(description = "是否广告")
    private Boolean isAdvertise;

    @Schema(description = "是否现金支付变更")
    private Boolean isCashPayChange;

    @Schema(description = "是否占用转成功")
    private Object isOccupiedTransSuc;

    @Schema(description = "关键标签列表")
    private List<Object> keyTags;

    @Schema(description = "是否踢出新政策订单")
    private Boolean kickOutNewPolicyOrder;

    @Schema(description = "踢出价格")
    private Object kickOutPrice;

    @Schema(description = "长接受时间标记")
    private Boolean longAcceptTimeMark;

    @Schema(description = "是否查看敏感")
    private Boolean lookSensitive;

    @Schema(description = "低碳信息")
    private Object lowerCarbon;

    @Schema(description = "会员权益")
    private Integer memberBenefits;

    @Schema(description = "会员权益文字")
    private List<String> memberBenefitsWord;

    @Schema(description = "会员佣金风控标记")
    private Integer memberCommissionRiskControlMark;

    @Schema(description = "会员显示模式")
    private Integer memberDisplayMode;

    @Schema(description = "会员等级")
    private Integer memberLevel;

    @Schema(description = "会员标记")
    private Integer memberMark;

    @Schema(description = "是否修改客人")
    private Boolean modifyGuest;

    @Schema(description = "占用标记")
    private Integer occupiedMark;

    @Schema(description = "是否线下")
    private Integer offLine;

    @Schema(description = "订单基础价格模型")
    private OrderBasePriceModel orderBasePriceModel;

    @Schema(description = "订单联系人")
    private List<Object> orderContacts;

    @Schema(description = "订单显示标签 新订、取消、")
    private String orderDisplayLabel;

    @Schema(description = "订单ID")
    private String orderId;

    @Schema(description = "订单标签")
    private Integer orderLabel;

    @Schema(description = "订单价格模式")
    private Integer orderPricePattern;

    @Schema(description = "订单状态说明")
    private String orderStatusExplain;

    @Schema(description = "原始或改期")
    private Integer originOrRescheduled;

    @Schema(description = "业主电话列表")
    private List<Object> ownerPhones;

    @Schema(description = "套餐基础信息列表")
    private List<Object> packageBaseInfos;

    @Schema(description = "套餐标记")
    private Integer packageMark;

    @Schema(description = "套餐房间")
    private Integer packageRoom;

    @Schema(description = "部分退款信息")
    private PartRefundInfo partRefundInfo;

    @Schema(description = "合作伙伴ID")
    private Integer partnerId;

    @Schema(description = "合作伙伴名称")
    private String partnerName;

    @Schema(description = "支付时间(时间戳)")
    private Long payTime;

    @Schema(description = "支付时间字符串")
    private String payTimeString;

    @Schema(description = "支付类型")
    private Integer paymentType;

    @Schema(description = "POI ID")
    private Long poiId;

    @Schema(description = "POI ID字符串")
    private String poiIdStr;

    @Schema(description = "POI名称")
    private String poiName;

    @Schema(description = "是否伪装")
    private Boolean pretender;

    @Schema(description = "价格")
    private Integer price;

    @Schema(description = "价格信息列表")
    private List<PriceInfo> priceInfo;

    @Schema(description = "价格信息构成")
    private List<PriceInfo> priceInfoConstitute;

    @Schema(description = "价格模式")
    private Integer priceMode;

    @Schema(description = "实际佣金")
    private Integer realCommission;

    @Schema(description = "实际底价")
    private Integer realFloorPrice;

    @Schema(description = "减少佣金标记")
    private Integer reduceCommisionMark;

    @Schema(description = "退款承担方")
    private Object refundBearer;

    @Schema(description = "退款订单模型")
    private Object refundOrderModel;

    @Schema(description = "退款记录")
    private List<Object> refundRecords;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "转售确认状态")
    private Object resaleConfirmStatus;

    @Schema(description = "转售标记")
    private Integer resaleMark;

    @Schema(description = "转售新订单类型")
    private Integer resaleNewOrderType;

    @Schema(description = "改期订单ID")
    private String rescheduledOrderId;

    @Schema(description = "反向价格TMC订单标签")
    private String reversePriceTmcOrderTag;

    @Schema(description = "权益名称列表")
    private List<Object> rightsNames;

    @Schema(description = "权益需要确认")
    private Integer rightsNeedConfirm;

    @Schema(description = "权益状态")
    private Integer rightsStatus;

    @Schema(description = "风险订单信息")
    private RiskOrderInfo riskOrderInfo;

    @Schema(description = "房间数量")
    private Integer roomCount;

    @Schema(description = "房间ID")
    private Long roomId;

    @Schema(description = "房间名称")
    private String roomName;

    @Schema(description = "房间夜价格模型列表")
    private List<RoomNightPriceModel> roomNightPriceModels;

    @Schema(description = "房间号")
    private String roomNo;

    @Schema(description = "RP自定义名称")
    private String rpCustomName;

    @Schema(description = "RP信息")
    private String rpInfo;

    @Schema(description = "RP服务信息列表")
    private List<RpServiceInfo> rpServiceInfoes;

    @Schema(description = "二次确认状态")
    private Integer secondConfirmStatus;

    @Schema(description = "是否显示佣金")
    private Boolean showCommision;

    @Schema(description = "是否显示底价")
    private Boolean showFloor;

    @Schema(description = "是否显示红包")
    private Integer showRedPackage;

    @Schema(description = "是否显示拒绝按钮")
    private Boolean showRefuseButton;

    @Schema(description = "是否显示VIP标签")
    private Integer showVIPTag;

    @Schema(description = "是否签署入境TMC协议")
    private Boolean signInboundTmcAgreement;

    @Schema(description = "来源订单ID")
    private String sourceOrderId;

    @Schema(description = "状态 ACCEPTED:已接单, CONSUMED:已入住, CANCELED:已取消, ABORT:已退款, RESCHEDULED:已改签")
    private String status;

    @Schema(description = "是否超级团")
    private Boolean superGroup;

    @Schema(description = "标签列表")
    private List<Object> tags;

    @Schema(description = "任务数量")
    private Object taskCount;

    @Schema(description = "任务创建时间")
    private Object taskCreateTime;

    @Schema(description = "任务创建时间字符串")
    private String taskCreateTimeString;

    @Schema(description = "任务类型")
    private Object taskType;

    @Schema(description = "第三方来源")
    private Integer thirdFrom;

    @Schema(description = "是否第三方合作伙伴")
    private Boolean thirdPartner;

    @Schema(description = "是否及时确认")
    private Boolean timelyConfirm;

    @Schema(description = "超时退款发送消息按钮")
    private Boolean timeoutRefundSendMsgBtn;

    @Schema(description = "TMC发票标签")
    private Boolean tmcInvoiceLabel;

    @Schema(description = "是否今日入住")
    private Boolean todayCheckIn;

    @Schema(description = "总费用")
    private Integer totalFee;

    @Schema(description = "总原始底价")
    private Integer totalOriginFloorPrice;

    @Schema(description = "游客标记")
    private Integer touristMark;

    @Schema(description = "追踪房间")
    private Integer traceRoom;

    @Schema(description = "转换订单标记")
    private Integer transformOrderMark;

    @Schema(description = "类型")
    private Integer type;

    @Schema(description = "紧急程度")
    private Integer urgent;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "商品信息")
    private VeilGoodsInfo veilGoodsInfo;

    @Schema(description = "验证状态")
    private Integer verifyStatus;

    @Schema(description = "访客是否过期")
    private Boolean visitorExpired;

    @Schema(description = "访客列表")
    private List<Object> visitors;
}

