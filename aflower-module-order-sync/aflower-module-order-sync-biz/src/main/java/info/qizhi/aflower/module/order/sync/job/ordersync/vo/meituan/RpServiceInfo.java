package info.qizhi.aflower.module.order.sync.job.ordersync.vo.meituan;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "RP服务信息")
public class RpServiceInfo {
    @Schema(description = "早餐日期(时间戳)")
    private Long breakfastDate;

    @Schema(description = "早餐日期字符串")
    private String breakfastDateStr;

    @Schema(description = "支付单元ID")
    private Long paymentUnitId;

    @Schema(description = "服务项目数量")
    private Integer serviceItemNum;

    @Schema(description = "服务项目类型")
    private Integer serviceItemType;

    @Schema(description = "服务名称")
    private String serviceName;

    @Schema(description = "服务开关")
    private Integer serviceSwitch;

    @Schema(description = "服务类型")
    private Integer serviceType;

    @Schema(description = "特殊早餐")
    private Integer specBreak;
}
