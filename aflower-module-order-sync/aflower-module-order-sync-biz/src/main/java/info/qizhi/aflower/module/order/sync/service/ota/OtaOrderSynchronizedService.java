package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaOrderSynchronizedReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.OtaOrderSynchronizedSaveVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.OtaOrderSynchronizedDO;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.List;

/**
 * OtaMonitorHotel Service 接口
 *
 * <AUTHOR>
 */
public interface OtaOrderSynchronizedService {

    /**
     * 批量创建 OTA已同步订单
     * @param createReqVO
     */
    void batchCreateOtaOrderSynchronized(List<OtaOrderSynchronizedSaveVO> createReqVO);

    Long createOtaOrderSynchronized(@Valid OtaOrderSynchronizedSaveVO createReqVO);

    void updateOtaOrderSynchronized(@Valid OtaOrderSynchronizedSaveVO updateReqVO);

    List<OtaOrderSynchronizedDO> getOtaOrderSynchronizedList(OtaOrderSynchronizedReqVO reqVO);

    /**
     * 删除门店下所有同步订单记录
     * @param gcode
     * @param hcode
     */
    void deleteOtaOrderSynchronized(@NotBlank String gcode, @NotBlank String hcode);

}