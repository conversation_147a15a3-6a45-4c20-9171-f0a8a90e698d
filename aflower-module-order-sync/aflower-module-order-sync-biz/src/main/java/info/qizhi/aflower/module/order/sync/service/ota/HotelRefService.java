package info.qizhi.aflower.module.order.sync.service.ota;

import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.HotelRefReqVO;
import info.qizhi.aflower.module.order.sync.controller.admin.ota.vo.HotelRefSaveReqVO;
import info.qizhi.aflower.module.order.sync.dal.dataobject.ota.HotelRefDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * OTA酒店关联 Service 接口
 *
 * <AUTHOR>
 */
public interface HotelRefService {

    /**
     * 创建OTA酒店关联
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createHotelRef(@Valid HotelRefSaveReqVO createReqVO);

    /**
     * 更新OTA酒店关联
     *
     * @param updateReqVO 更新信息
     */
    void updateHotelRef(@Valid HotelRefSaveReqVO updateReqVO);

    /**
     * 删除OTA酒店关联
     *
     * @param id 编号
     */
    void deleteHotelRef(Long id);

    /**
     * 获得OTA酒店关联
     *
     * @param id 编号
     * @return OTA酒店关联
     */
    HotelRefDO getHotelRef(Long id);

    /**
     * 获得OTA酒店关联
     *
     * @param reqVO 查询
     * @return OTA酒店关联
     */
    List<HotelRefDO> getHotelRefs(HotelRefReqVO reqVO);

}