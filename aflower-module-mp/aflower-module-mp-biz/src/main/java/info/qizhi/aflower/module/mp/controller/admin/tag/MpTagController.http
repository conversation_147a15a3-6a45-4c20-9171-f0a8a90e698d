### 请求 /mp/tag/create 接口 => 成功
POST {{baseUrl}}/mp/tag/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

{
  "accountId": "1",
  "name": "测试"
}

### 请求 /mp/tag/update 接口 => 成功
PUT {{baseUrl}}/mp/tag/update
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

{
  "id": "3",
  "name": "测试标签啦"
}

### 请求 /mp/tag/delete 接口 => 成功
DELETE {{baseUrl}}/mp/tag/delete?id=3
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

### 请求 /mp/tag/page 接口 => 成功
GET {{baseUrl}}/mp/tag/page?accountId=1&pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

### 请求 /mp/tag/sync 接口 => 成功
POST {{baseUrl}}/mp/tag/sync?accountId=1
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
