package info.qizhi.aflower.module.sse.api;

import info.qizhi.aflower.module.sse.enums.ApiConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Author: TY
 * @CreateTime: 2024-07-04
 * @Description: sseApi
 * @Version: 1.0
 */
@FeignClient(name = ApiConstants.NAME)
@Tag(name = "RPC 服务 - SSE")
public interface SseApi {
    String PREFIX = ApiConstants.PREFIX;

    /**
     * 给一个酒店群发消息
     *
     * @param hcode 酒店代码
     * @param msg   发送的消息json
     */
    @PostMapping(PREFIX + "/broadcast")
    @Operation(summary = "单独给一个酒店群发消息")
    @Parameters({
            @Parameter(name = "hcode", required = true, description = "要发到的酒店代码"),
            @Parameter(name = "msg", required = true, description = "消息内容json"),
            @Parameter(name = "messageType", required = true, description = "消息类型 info.qizhi.aflower.framework.common.message.HotelMessage")
    })
    void sendMessageToAllClient(@RequestParam("hcode") String hcode, @RequestParam("messageType") String messageType, @RequestParam("msg") String msg);
}
