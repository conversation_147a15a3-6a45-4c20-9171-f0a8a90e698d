package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignPageVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignReqVO;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 下午8:14
*/
public interface SmsSignClineService {

    CommonResult<PageResultSms<SmsSignPageVo>> enabledSign(SmsSignReqVO enabledReqVO);

}
