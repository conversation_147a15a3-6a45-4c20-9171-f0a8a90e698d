package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.enums.SmsSceneEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.record.SmsRecordServiceImpl;
import info.qizhi.aflower.module.marketing.api.sms.send.dto.SendRecordPageReq;
import info.qizhi.aflower.module.marketing.api.sms.send.dto.SendRecordTotalReq;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordPageVo;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordStatisticsVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.record.SmsRecordPageReqVO;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 上午11:23
*/
@Service
public class SmsRecordClientServiceImpl  implements  SmsRecordClientService{
    @Resource
    private SmsRecordServiceImpl smsRecordServiceImpl;

    @Resource
    private MerchantApi merchantApi;


    @Override
    public CommonResult<PageResultSms<RecordPageVo>> pageList(SmsRecordPageReqVO sendRecordPageReqVO) {
        SendRecordPageReq sendRecordBusinessPageReq = BeanUtil.toBean(sendRecordPageReqVO, SendRecordPageReq.class);
        CommonResult<PageResultSms<RecordPageVo>> result =  smsRecordServiceImpl.pageList(sendRecordBusinessPageReq);
        if(ObjectUtil.isEmpty(result.getData().getList())){
            return result;
        }
        CommonResult<MerchantRespDTO> merchant = merchantApi.getMerchant(result.getData().getList().getFirst().getHcode());
        //集团总数，不需要返回酒店名称
        if(ObjectUtil.isEmpty(merchant.getData())){
            return result;
        }
        result.getData().getList().forEach(recordPageVo -> {
            // 只处理 sendScene 为 "验证码" 的情况
            if (SmsSceneEnum.CODE.getName().equals(recordPageVo.getSendScene()) && recordPageVo.getMsgContent() != null) {
                String msgContent = recordPageVo.getMsgContent();
                // 使用正则表达式提取验证码
                String regex = "(\\d{4})"; // 匹配四位数字验证码
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(msgContent);

                if (matcher.find()) {
                    String code = matcher.group(0); // 获取验证码
                    // 将验证码的最后两位替换为 '**'
                    String maskedCode = code.charAt(0) + "**" + code.charAt(3);
                    // 替换 msgContent 中的验证码部分
                    msgContent = msgContent.replace(code, maskedCode);
                    // 设置脱敏后的 msgContent
                    recordPageVo.setMsgContent(msgContent);
                }
            }
            recordPageVo.setHname(merchant.getData().getHname());
        });
        return result;
    }

    @Override
    public CommonResult<List<RecordStatisticsVo>> statistics(SendRecordTotalReq sendRecordTotalReq) {
        SendRecordTotalReq recordStatisticsVo = BeanUtil.toBean(sendRecordTotalReq, SendRecordTotalReq.class);
        CommonResult<List<RecordStatisticsVo>> result = smsRecordServiceImpl.statistics(recordStatisticsVo);
        if(ObjectUtil.isEmpty(result.getData())){
            return result;
        }
        //集团总数，不需要返回酒店名称
        if(ObjectUtil.isEmpty(result.getData().getFirst().getHcode())){
            return result;
        }
        CommonResult<MerchantRespDTO> merchant = merchantApi.getMerchant(result.getData().getFirst().getHcode());
        result.getData().forEach(recordPageVo -> recordPageVo.setHname(merchant.getData().getHname()));
        return result;
    }

}
