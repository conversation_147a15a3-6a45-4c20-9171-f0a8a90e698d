package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 短信充值活动 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsActivityRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20506")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "活动代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动代码")
    private String activityCode;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("活动名称")
    private String activityName;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "短信条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("短信条数")
    private Integer smsNum;

    @Schema(description = "赠送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("赠送条数")
    private Integer giveNum;

    @Schema(description = "活动开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动开始日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate dateStart;

    @Schema(description = "活动结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动结束日期")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate dateEnd;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0：无效 1：有效")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "产品名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产品名称")
    private String productName;

    @Schema(description = "购买内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("购买内容")
    private String purchaseContent;

    @Schema(description = "充值总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("充值总数")
    private Integer totalNum;

}