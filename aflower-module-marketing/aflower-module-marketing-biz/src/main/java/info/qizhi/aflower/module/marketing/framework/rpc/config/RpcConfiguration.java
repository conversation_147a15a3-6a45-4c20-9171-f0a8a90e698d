package info.qizhi.aflower.module.marketing.framework.rpc.config;

import info.qizhi.aflower.module.infra.api.file.FileApi;
import info.qizhi.aflower.module.marketing.api.sms.send.SmsSendClientApi;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.memberType.MemberTypeApi;
import info.qizhi.aflower.module.pay.api.base.PayBaseApi;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.HotelParamConfigApi;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.OrderPriceApi;
import info.qizhi.aflower.module.pms.api.price.PriceStrategyApi;
import info.qizhi.aflower.module.pms.api.pricerule.PriceAllDayRuleApi;
import info.qizhi.aflower.module.system.api.group.GroupInfoApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Configuration;

@Configuration(proxyBeanMethods = false)
@EnableFeignClients(clients = {FileApi.class, AccountApi.class, HotelParamConfigApi.class,
        MemberApi.class, OrderApi.class, BookingApi.class, MerchantApi.class, MemberTypeApi.class,
        GroupInfoApi.class, OrderPriceApi.class, GeneralConfigApi.class, PayBaseApi.class, SmsSendClientApi.class,
        PriceStrategyApi.class, PriceAllDayRuleApi.class })
public class RpcConfiguration {
}
