package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 短信签名 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsSignRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "10202")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果是集团签名，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码;如果是集团签名，该字段为0")
    private String hcode;

    @Schema(description = "签名代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签名代码")
    private String signCode;

    @Schema(description = "签名", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("签名")
    private String sign;

    @Schema(description = "状态;0:待审核 1：有效 2：未通过", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0:待审核 1：有效 2：未通过")
    private String state;

    @Schema(description = "未通过原因", example = "不喜欢")
    @ExcelProperty("未通过原因")
    private String reason;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}