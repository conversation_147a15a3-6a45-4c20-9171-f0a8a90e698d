package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityListReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivitySaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsActivityDO;

import java.util.List;

/**
 * 短信充值活动 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsActivityService {

    /**
     * 创建短信充值活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSmsActivity(@Valid SmsActivitySaveReqVO createReqVO);

    /**
     * 更新短信充值活动
     *
     * @param updateReqVO 更新信息
     */
    void updateSmsActivity(@Valid SmsActivitySaveReqVO updateReqVO);

    /**
     * 删除短信充值活动
     *
     * @param id 编号
     */
    void deleteSmsActivity(Long id);

    /**
     * 获得短信充值活动
     * @return 短信充值活动
     */
    SmsActivityRespVO getSmsActivity(String activeCode);

    /**
     * 获得短信充值活动列表
     *
     * @param pageReqVO 分页查询
     * @return 短信充值活动分页
     */
    List<SmsActivityDO> getSmsActivityList(SmsActivityListReqVO pageReqVO);

}