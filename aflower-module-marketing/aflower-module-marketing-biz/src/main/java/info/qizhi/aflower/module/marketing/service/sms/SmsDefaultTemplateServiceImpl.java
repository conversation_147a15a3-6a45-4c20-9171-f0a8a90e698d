package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.template.SmsDefaultTemplateClient;
import info.qizhi.aflower.module.marketing.api.sms.template.dto.SmsDefaultTemplateSaveReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.template.dto.SmsDefaultTplEnabledReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.template.dto.SmsTplInnerPageDTO;
import info.qizhi.aflower.module.marketing.api.sms.template.vo.TemplateVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplateReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTplEnabledReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsPublicTplResVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsDefaultTemplateDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsTemplateEnableDO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsDefaultTemplateMapper;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsTemplateEnableMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 短信默认模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsDefaultTemplateServiceImpl implements SmsDefaultTemplateService {

    @Resource
    private SmsDefaultTemplateMapper smsDefaultTemplateMapper;
    @Resource
    private SmsDefaultTemplateClient smsDefaultTemplateClient;
    @Resource
    private SmsTemplateEnableMapper smsTemplateEnableMapper;

    @Override
    public Long createSmsDefaultTemplate(SmsDefaultTemplateReqVO createReqVO) {
        // 插入
        SmsDefaultTemplateDO smsDefaultTemplate = BeanUtils.toBean(createReqVO, SmsDefaultTemplateDO.class);
        smsDefaultTemplateMapper.insert(smsDefaultTemplate);
        // 返回
        return smsDefaultTemplate.getId();
    }

    @Override
    @Deprecated
    public CommonResult<Boolean> updateSmsDefaultTemplate(SmsDefaultTemplateReqVO updateReqVO) {
        SmsDefaultTemplateSaveReqDTO updateReqDTO = BeanUtils.toBean(updateReqVO, SmsDefaultTemplateSaveReqDTO.class);
        return smsDefaultTemplateClient.updateSmsTemplate(updateReqDTO);
    }

    @Override
    public CommonResult<Boolean> enableDefaultTemplate(SmsDefaultTplEnabledReqVO enabledReqVO) {
        SmsDefaultTplEnabledReqDTO enableReqDTO = BeanUtils.toBean(enabledReqVO, SmsDefaultTplEnabledReqDTO.class);
        return smsDefaultTemplateClient.enabledTemplate(enableReqDTO);
    }

    @Override
    public SmsDefaultTemplateDO getSmsDefaultTemplate(String bizTypeCode) {
        return smsDefaultTemplateMapper.selectOne(SmsDefaultTemplateDO::getBizTypeCode,bizTypeCode);
    }

    @Override
    public PageResultSms<TemplateVo> getSmsDefaultTemplatePage(SmsDefaultTemplatePageReqVO pageReqVO) {
        SmsTplInnerPageDTO DTO = BeanUtils.toBean(pageReqVO, SmsTplInnerPageDTO.class);
        PageResultSms<TemplateVo> templateVo = smsDefaultTemplateClient.pageList(DTO);
        SmsPublicTplResVO pageResult = BeanUtils.toBean(pageReqVO, SmsPublicTplResVO.class );
        PageResult<SmsTemplateEnableDO> enabledList = smsTemplateEnableMapper.selectPage(pageResult);
        //筛选启用模板，默认为false
        templateVo.getList().forEach(vo ->{
            vo.setEnabled(false);
            enabledList.getList().forEach(enabled ->{
                if(enabled.getTemplateId().equals(vo.getId())) vo.setEnabled(enabled.getEnabled());
            });
        });
        return templateVo;
    }

    public TemplateVo getSmsDefaultTemplateById(SmsDefaultTplEnabledReqVO vo) {
        SmsTemplateEnableDO templateEnableDO =  smsTemplateEnableMapper.selectOne(vo);
        return BeanUtils.toBean(templateEnableDO, TemplateVo.class);
    }

    @Override
    public boolean updateSmsTemplateEnable(SmsDefaultTplEnabledReqVO updateReqVO) {
        SmsTemplateEnableDO oldEnable =  smsTemplateEnableMapper.selectOne(updateReqVO);
        // 模板不存在
        if(ObjectUtil.isEmpty(oldEnable)){
            smsTemplateEnableMapper.insert(SmsTemplateEnableDO.builder()
                    .enabled(updateReqVO.getEnabled())
                    .templateId(updateReqVO.getId())
                    .gcode(updateReqVO.getGcode())
                    .hcode(updateReqVO.getHcode())
                    .build());
            return true;
        }
        return smsTemplateEnableMapper.updateEnableById(updateReqVO);
    }

}