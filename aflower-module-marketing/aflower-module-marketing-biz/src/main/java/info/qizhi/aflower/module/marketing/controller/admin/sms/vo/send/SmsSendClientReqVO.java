package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send;

import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.validation.InStringEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 短信发送 - 发送对象客户群组 Request VO")
@Data
public class SmsSendClientReqVO {

    @Schema(description = "门店代码;", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "集团代码;", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "选择客户; 0:会员级别, 1:客户标签", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{choose.notempty}")
    private String choose;

    @Schema(description = "会员类型(级别)代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mtCode;

    @Schema(description = "性别；0：女，1：男，2：保密", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sex;

    @Schema(description = "客户标签; 0:预抵客人, 1:挂账客人, 2:在住客人, 3:客人在住且生日,", requiredMode = Schema.RequiredMode.REQUIRED)
    private String customerGroup;

    @Schema(description = "是否拉黑;0：否 1：拉黑")
    @InStringEnum(value = BooleanEnum.class,message =  "{isBlack.invalid}")
    private String isBlack;

    @Schema(description = "状态;1:正常  0:停用")
    @InStringEnum(value = BooleanEnum.class,message =  "{state.invalid}")
    private String state;

}
