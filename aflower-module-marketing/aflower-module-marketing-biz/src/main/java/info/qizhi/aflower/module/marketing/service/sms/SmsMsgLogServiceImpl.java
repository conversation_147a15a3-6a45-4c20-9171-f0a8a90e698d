package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log.SmsMsgLogPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log.SmsMsgLogSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsSendStatMapper;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsMsgLogDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsMsgLogMapper;

import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;

/**
 * 短信发送内容日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsMsgLogServiceImpl implements SmsMsgLogService {

    @Resource
    private SmsMsgLogMapper smsMsgLogMapper;
    @Resource
    private SmsSendStatService smsSendStatService;

    @Override
    public Long createSmsMsgLog(SmsMsgLogSaveReqVO createReqVO) {
        // 插入
        SmsMsgLogDO smsMsgLog = BeanUtils.toBean(createReqVO, SmsMsgLogDO.class);
        smsMsgLogMapper.insert(smsMsgLog);
        // 返回
        return smsMsgLog.getId();
    }

    @Override
    public PageResult<SmsMsgLogDO> getSmsMsgLogPage(SmsMsgLogPageReqVO pageReqVO) {
        return smsMsgLogMapper.selectPage(pageReqVO);
    }

    /**
     * 夜审统计当天短信发送量
     */
    @Override
    public void smsSendStat(String gcode, String hcode, LocalDate bizDate) {
        List<SmsMsgLogDO> smsMsgLogDOS = smsMsgLogMapper.selectList(gcode, hcode, bizDate);
        // 判断如果短信发送量不为0，则进行短信发送量统计
        if (smsMsgLogDOS.size() != NumberEnum.ZERO.getNumberInt()) {
            // 当日短信总发送量
            int sum = smsMsgLogDOS.stream().mapToInt(SmsMsgLogDO::getSendNum).sum();
            //  当日短信发送人数量
            int sendPersons = smsMsgLogDOS.stream().mapToInt(SmsMsgLogDO::getSendPersons).sum();
            SmsSendStatSaveReqVO smsSendStatSaveReqVO = new SmsSendStatSaveReqVO();
            smsSendStatSaveReqVO.setSendNum(sum);
            smsSendStatSaveReqVO.setSendPersons(sendPersons);
            smsSendStatSaveReqVO.setSendDate(bizDate);
            smsSendStatSaveReqVO.setGcode(gcode);
            smsSendStatSaveReqVO.setHcode(hcode);
            smsSendStatService.createSmsSendStat(smsSendStatSaveReqVO);
        }
        if (smsMsgLogDOS.size() == NumberEnum.ZERO.getNumberInt()){
            SmsSendStatSaveReqVO smsSendStatSaveReqVO = new SmsSendStatSaveReqVO();
            smsSendStatSaveReqVO.setSendNum(NumberEnum.ZERO.getNumberInt());
            smsSendStatSaveReqVO.setSendPersons(NumberEnum.ZERO.getNumberInt());
            smsSendStatSaveReqVO.setSendDate(bizDate);
            smsSendStatSaveReqVO.setGcode(gcode);
            smsSendStatSaveReqVO.setHcode(hcode);
            smsSendStatService.createSmsSendStat(smsSendStatSaveReqVO);
        }
    }

}