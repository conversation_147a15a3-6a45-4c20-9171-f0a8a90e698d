package info.qizhi.aflower.module.marketing.service.coupon;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifyBreakfastRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifySummaryRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.*;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastOfContinuationReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastVerifyReq;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponDO;
import jakarta.validation.Valid;

import java.time.LocalDate;
import java.util.List;

/**
 * 优惠券 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponService {

    /**
     * 批量创建早餐卷
     *
     * @return 编号
     */
    void createBreakfasts(@Valid BreakFastSaveReqVO createReqVo);

    /**
     * 续住延长未使用的购买的早餐券
     */
    void continueUpdateBuyBreakFast(@Valid BreakFastOfContinuationReqVO reqVO);

    /**
     *  早餐卷核销
     */
    void breakfastVerify(BreakFastVerifyReq req);

    /**
     * 获得该房间需要核销的早餐卷
     */
    VerifyBreakfastRespVO getBreakfast(BreakFastReqVO reqVO);

    /**
     * 获得优惠券
     * @return 优惠券
     */
    CouponRespVO getCoupon(String couponCode,String gcode);

    /**
     * 获得优惠券分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券分页
     */
    PageResult<CouponRespVO> getCouponPage(CouponPageReqVO pageReqVO);

    /**
     * 核销数据总览
     */
    VerifySummaryRespVO verifyDataOverview(LocalDate bizDate,String gcode,String hcode);

    /**
     * 获取该会员的优惠卷
     */
    PageResult<CouponRespVO> getMemberCouponPage(@Valid CouponGetReqVO reqVO);

    /**
     * 获取该会员的优惠卷数
     */
    Long getMemberCouponListCount(String gcode, String belong);

    /**
     * 生成优惠券
     */
    Boolean createCoupon(@Valid CouponGenerateReqVO createReqVO);

    /**
     * 发放优惠券
     */
    Boolean grantCoupon(@Valid CouponGrantReqVO reqVO);

    /**
     * 使用优惠卷
     */
    Boolean useCoupon(CouponUseReqVO reqVO);

    /**
     * 获取该优惠券模板下生成的优惠券数量
     */
    Long getCouponCount(String templateCode,String gcode);

    /**
     * 批量作废优惠券
     */
    void invalidCoupon(List<CouponDO> reqVO);

    /**
     * 获取赠券活动赠送的券
     */
    List<CouponDO> getFreeTicket(String gcode,String orderNo);

    /**
     * 获取该会员的优惠卷列表
     * @param reqVO
     * @return
     */
    List<CouponRespVO> getMemberCouponList(@Valid CouponReqVO reqVO);
    /**
     * 获取优惠卷列表
     * @param reqVO
     * @return
     */
    List<CouponDO> getCouponList(@Valid CouponReqVO reqVO);

}