package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 短信发送内容日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsMsgLogPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果为集团发送，该字段为0")
    private String hcode;

    @Schema(description = "内容或手机号")
    private String keyWords;

    @Schema(description = "发送方式;0:系统业务触发 1：人工群发")
    private String sendMode;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

}