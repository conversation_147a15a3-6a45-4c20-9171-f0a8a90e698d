package info.qizhi.aflower.module.marketing.service.coupon;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.marketing.api.couponconfig.dto.CouponConfigSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.config.CouponConfigSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponConfigDO;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponConfigMapper;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;

/**
 * 优惠券设置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CouponConfigServiceImpl implements CouponConfigService {

    @Resource
    private CouponConfigMapper couponConfigMapper;

    @Override
    public Long createCouponConfig(CouponConfigSaveReqDTO configSaveReqDTO) {
        // 插入
        CouponConfigDO couponConfig = BeanUtils.toBean(configSaveReqDTO, CouponConfigDO.class);
        couponConfig.setIsWxMore(NumberEnum.ZERO.getNumber());
        couponConfig.setIsWalkinMore(NumberEnum.ZERO.getNumber());
        couponConfigMapper.insert(couponConfig);
        // 返回
        return couponConfig.getId();
    }

    @Override
    public void updateCouponConfig(CouponConfigSaveReqVO updateReqVO) {
        // 校验存在
        CouponConfigDO couponConfigDO = couponConfigMapper.selectOne(CouponConfigDO::getGcode, updateReqVO.getGcode(), CouponConfigDO::getHcode, updateReqVO.getHcode());
        if (couponConfigDO == null) {
            throw exception(COUPON_CONFIG_NOT_EXISTS);
        }
        // 更新
        CouponConfigDO updateObj = BeanUtils.toBean(updateReqVO, CouponConfigDO.class);
        updateObj.setId(couponConfigDO.getId());
        couponConfigMapper.updateById(updateObj);
    }

    @Override
    public List<CouponConfigDO> getCouponConfig(String gcode, String hcode) {
        return couponConfigMapper.selectListByHcode(gcode,hcode);
    }


}