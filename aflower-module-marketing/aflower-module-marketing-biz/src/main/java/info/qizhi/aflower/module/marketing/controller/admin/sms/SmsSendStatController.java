package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsSendStatDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsSendStatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信发送统计")
@RestController
@RequestMapping("/marketing/sms-send-stat")
@Validated
@Deprecated
public class SmsSendStatController {

    @Resource
    private SmsSendStatService smsSendStatService;

    @PostMapping("/create")
    @Operation(summary = "创建短信发送统计")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-send-stat:create')")
    public CommonResult<Long> createSmsSendStat(@Valid @RequestBody SmsSendStatSaveReqVO createReqVO) {
        return success(smsSendStatService.createSmsSendStat(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得短信发送统计分页")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-send-stat:query')")
    public CommonResult<PageResult<SmsSendStatRespVO>> getSmsSendStatPage(@Valid SmsSendStatPageReqVO pageReqVO) {
        PageResult<SmsSendStatDO> pageResult = smsSendStatService.getSmsSendStatPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SmsSendStatRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出短信发送统计 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-send-stat:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsSendStatExcel(@Valid SmsSendStatPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SmsSendStatDO> list = smsSendStatService.getSmsSendStatPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "短信发送统计.xls", "数据", SmsSendStatRespVO.class,
                        BeanUtils.toBean(list, SmsSendStatRespVO.class));
    }

}