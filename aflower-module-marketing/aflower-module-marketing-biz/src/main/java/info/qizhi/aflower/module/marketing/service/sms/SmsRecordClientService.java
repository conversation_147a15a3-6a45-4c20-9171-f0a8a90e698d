package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.send.dto.SendRecordTotalReq;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordPageVo;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordStatisticsVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.record.SmsRecordPageReqVO;
import jakarta.validation.Valid;

import java.util.List;

/*
    消息发送客户端
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 上午11:22
*/
public interface SmsRecordClientService {

    /**
     * 发送记录列表
     */
    CommonResult<PageResultSms<RecordPageVo>> pageList(@Valid SmsRecordPageReqVO reqVO);
    /**
     * 发送统计
     */
    CommonResult<List<RecordStatisticsVo>> statistics(@Valid SendRecordTotalReq reqVO);

}
