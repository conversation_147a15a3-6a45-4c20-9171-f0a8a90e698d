package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 短信默认模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsDefaultTemplateRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "201")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "模板代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板代码")
    private String templateCode;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("模板名称")
    private String templateName;

    @Schema(description = "模板内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板内容")
    private String content;

    @Schema(description = "业务类型代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务类型代码")
    private String bizTypeCode;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型")
    private String bizType;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}