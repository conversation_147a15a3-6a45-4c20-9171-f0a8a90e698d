package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.module.marketing.api.sms.balance.dto.SmsBalanceSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance.SmsBalanceSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsBalanceDO;
import jakarta.validation.Valid;

/**
 * 短信余额 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsBalanceService {

    /**
     * 创建短信余额
     *
     * @param smsBalanceSaveReqDTO 创建信息
     * @return 编号
     */
    Long createSmsBalance(@Valid SmsBalanceSaveReqDTO smsBalanceSaveReqDTO);

    /**
     * 更新短信余额
     *
     * @param updateReqVO 更新信息
     */
    void updateSmsBalance(@Valid SmsBalanceSaveReqVO updateReqVO);

    /**
     * 获得短信余额
     *
     * @return 短信余额
     */
    SmsBalanceDO getSmsBalance(String gcode);

//    /**
//     * 短信预警
//     */
//    void smsWarning(String gcode);

    /**
     * TODO 短信充值-确认订单
     */
    String smsRecharge(SmsRechargeReqVO reqVO);

    /**
     * TODO 短信充值-获取支付回调参数
     */
    void smsRechargeCallback(String gcode, String hcode, String orderNo);
}