package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log.SmsMsgLogPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log.SmsMsgLogRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log.SmsMsgLogSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsMsgLogDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsMsgLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.time.LocalDate;
import java.util.List;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信发送内容日志")
@RestController
@RequestMapping("/marketing/sms-msg-log")
@Validated
@Deprecated
public class SmsMsgLogController {

    @Resource
    private SmsMsgLogService smsMsgLogService;

    @PostMapping("/create")
    @Operation(summary = "创建短信发送内容日志")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-msg-log:create')")
    public CommonResult<Long> createSmsMsgLog(@Valid @RequestBody SmsMsgLogSaveReqVO createReqVO) {
        return success(smsMsgLogService.createSmsMsgLog(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得短信发送内容日志分页")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-msg-log:query')")
    public CommonResult<PageResult<SmsMsgLogRespVO>> getSmsMsgLogPage(@Valid SmsMsgLogPageReqVO pageReqVO) {
        PageResult<SmsMsgLogDO> pageResult = smsMsgLogService.getSmsMsgLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SmsMsgLogRespVO.class));
    }

    @PostMapping("/yeshen")
    //@Operation(summary = "夜审统计当日短信发送量")
    public CommonResult<Boolean> createSmsMsgLog(@RequestParam("gcode") String gcode, String hcode,@RequestParam("bizDate") LocalDate bizDate) {
        smsMsgLogService.smsSendStat(gcode, hcode, bizDate);
        return success(true);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出短信发送内容日志 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-msg-log:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsMsgLogExcel(@Valid SmsMsgLogPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SmsMsgLogDO> list = smsMsgLogService.getSmsMsgLogPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "短信发送内容日志.xls", "数据", SmsMsgLogRespVO.class,
                        BeanUtils.toBean(list, SmsMsgLogRespVO.class));
    }

}