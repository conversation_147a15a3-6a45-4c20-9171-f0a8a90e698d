package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.template.vo.TemplateVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplateReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTplEnabledReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsDefaultTemplateDO;
import jakarta.validation.Valid;

/**
 * 短信默认模板 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsDefaultTemplateService {

    /**
     * 创建短信默认模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSmsDefaultTemplate(@Valid SmsDefaultTemplateReqVO createReqVO);

    /**
     * 更新短信默认模板
     *
     * @param updateReqVO 更新信息
     */
    CommonResult<Boolean> updateSmsDefaultTemplate(@Valid SmsDefaultTemplateReqVO updateReqVO);

    /*
     * 是否启动/关闭短信默认模板
     */
    CommonResult<Boolean> enableDefaultTemplate(@Valid SmsDefaultTplEnabledReqVO enabledReqVO);
    /**
     * 获得短信默认模板
     *
     * @return 短信默认模板
     */
    SmsDefaultTemplateDO getSmsDefaultTemplate(String bizTypeCode);

    /**
     * 获得短信默认模板分页
     *
     * @param pageReqVO 分页查询
     * @return 短信默认模板分页
     */
    PageResultSms<TemplateVo> getSmsDefaultTemplatePage(SmsDefaultTemplatePageReqVO pageReqVO);

    /**
     * 更改门店模板启用
     * @return
     */
    boolean updateSmsTemplateEnable(SmsDefaultTplEnabledReqVO updateReqVO);

    /**
     * 获取单个模板
     * @param id
     * @return
     */
    TemplateVo getSmsDefaultTemplateById(SmsDefaultTplEnabledReqVO vo);

}