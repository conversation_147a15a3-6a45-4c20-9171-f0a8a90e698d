package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 短信余额 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsBalanceRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19724")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "短信余额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("短信余额")
    private Long balance;

    @Schema(description = "已发送数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已发送数")
    private Long sendNum;

    @Schema(description = "累计发送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("累计发送条数")
    private Long totalSendNum;

    @Schema(description = "预警条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预警条数")
    private Integer warningNum;

    @Schema(description = "预警接收电话;电话，多个用','隔开")
    @ExcelProperty("预警接收电话;电话，多个用','隔开")
    private String phone;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}