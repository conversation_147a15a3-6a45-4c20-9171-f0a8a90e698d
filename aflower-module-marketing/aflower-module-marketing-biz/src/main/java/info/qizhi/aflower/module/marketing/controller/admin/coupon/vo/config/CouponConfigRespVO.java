package info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.config;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 优惠券设置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CouponConfigRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "18421")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码")
    private String hcode;

    @Schema(description = "允许微信预订时多间夜使用多张优惠券;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("允许微信预订时多间夜使用多张优惠券;0:否 1：是")
    private String isWxMore;

    @Schema(description = "允许门店多间夜使用多张券;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("允许门店多间夜使用多张券;0:否 1：是")
    private String isWalkinMore;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}