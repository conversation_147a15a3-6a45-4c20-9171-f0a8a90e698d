package info.qizhi.aflower.module.marketing.controller.admin.sms.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - 短信参数 Request VO")
@Data
public class SmsParamRespVO {

    @Schema(description = "参数名", requiredMode = Schema.RequiredMode.REQUIRED, example = "code")
    private String paramName;

    @Schema(description = "参数值", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456")
    private String paramValue;

}
