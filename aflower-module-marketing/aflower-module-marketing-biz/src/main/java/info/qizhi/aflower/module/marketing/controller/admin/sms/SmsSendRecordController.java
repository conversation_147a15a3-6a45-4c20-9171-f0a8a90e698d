package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.send.dto.SendRecordTotalReq;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordPageVo;
import info.qizhi.aflower.module.marketing.api.sms.send.vo.RecordStatisticsVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.record.SmsRecordPageReqVO;
import info.qizhi.aflower.module.marketing.service.sms.SmsRecordClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 上午10:59
*/
@Tag(name = "管理后台 - 短信发送记录")
@RestController
@RequestMapping("/marketing/sms-send-record")
@Validated
public class SmsSendRecordController {
    @Resource
    private SmsRecordClientService smsRecordClientService;

    @PostMapping("/pageList")
    @Operation(summary ="短信发送记录")
    @PreAuthorize("@ss.hasPermission('marketing:sms-send-record:query')")
    public CommonResult<PageResultSms<RecordPageVo>> pageList(@RequestBody SmsRecordPageReqVO sendRecordPageReqVO) {
        return smsRecordClientService.pageList(sendRecordPageReqVO);
    }
    @PostMapping("/statistics")
    @Operation(summary ="发送统计")
    @PreAuthorize("@ss.hasPermission('marketing:sms-send-record:query')")
    public CommonResult<List<RecordStatisticsVo>> statistics(@RequestBody SendRecordTotalReq sendRecordPageReqVO) {
        return smsRecordClientService.statistics(sendRecordPageReqVO);
    }
}
