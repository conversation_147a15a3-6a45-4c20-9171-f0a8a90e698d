package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 短信充值订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsRechargeOrderPageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "充值门店代码;如果为0，表示是集团充值")
    private String hcode;

    @Schema(description = "支付流水号")
    private String paySerialNo;

    @Schema(description = "状态;0:未支付 1:已支付 2:已过期")
    private String state;

    @Schema(description = "开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime endTime;

}