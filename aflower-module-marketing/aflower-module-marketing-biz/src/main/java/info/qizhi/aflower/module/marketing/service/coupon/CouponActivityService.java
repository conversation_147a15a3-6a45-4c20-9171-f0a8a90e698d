package info.qizhi.aflower.module.marketing.service.coupon;

import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.activity.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityDetailDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityMerchantDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 赠券活动 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponActivityService {

    /**
     * 创建赠券活动
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCouponActivity(@Valid CouponActivitySaveReqVO createReqVO);

    /**
     * 更新赠券活动
     *
     * @param updateReqVO 更新信息
     */
    void updateCouponActivity(@Valid CouponActivitySaveReqVO updateReqVO);

    /**
     * 修改赠券活动状态
     */
    void updateCouponActivityState(@Valid RechargeActivityStatusReqVO reqVO);

    /**
     * 获得赠券活动
     *
     * @return 赠券活动
     */
    CouponActivityRespVO getCouponActivity(String activityCode, String gcode);

    /**
     * 获得赠券活动列表
     */
    List<CouponActivityDO> getCouponActivityList(CouponActivityListReqVO pageReqVO);

    /**
     * 赠券
     */
    Boolean freeTicket(CouponActivityReqVO reqVO);

    // ==================== 子表（赠券活动明细） ====================

    /**
     * 获得赠券活动明细列表
     *
     * @param activityCode 活动代码
     * @return 赠券活动明细列表
     */
    List<CouponActivityDetailDO> getCouponActivityDetailListByActivityCode(String activityCode);

    // ==================== 子表（赠券活动关联门店） ====================

    /**
     * 获得赠券活动关联门店列表
     *
     * @param activityCode 活动代码
     * @return 赠券活动关联门店列表
     */
    List<CouponActivityMerchantDO> getActivityMerchantListByActivityCode(String activityCode);


}