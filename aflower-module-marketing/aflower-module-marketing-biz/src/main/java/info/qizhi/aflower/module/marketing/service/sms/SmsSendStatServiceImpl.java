package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsSendStatDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsSendStatMapper;

/**
 * 短信发送统计 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsSendStatServiceImpl implements SmsSendStatService {

    @Resource
    private SmsSendStatMapper smsSendStatMapper;

    @Override
    public Long createSmsSendStat(SmsSendStatSaveReqVO createReqVO) {
        // 插入
        SmsSendStatDO smsSendStat = BeanUtils.toBean(createReqVO, SmsSendStatDO.class);
        smsSendStatMapper.insert(smsSendStat);
        // 返回
        return smsSendStat.getId();
    }

    @Override
    public PageResult<SmsSendStatDO> getSmsSendStatPage(SmsSendStatPageReqVO pageReqVO) {
        return smsSendStatMapper.selectPage(pageReqVO);
    }

}