package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsAuditResultVo;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignDetailVo;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignPageVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignAddReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignUpdateReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsSignDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsSignService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信签名")
@RestController
@RequestMapping("/marketing/sms-sign")
@Validated
public class SmsSignController {

    @Resource
    private SmsSignService smsSignService;


    @PostMapping("/create")
    @Operation(summary = "创建短信签名")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:create')")
    public CommonResult<Boolean> createSmsSign(@Valid @RequestBody SmsSignAddReqVO createReqVO) {
        return smsSignService.createSmsSign(createReqVO);
    }

    @PutMapping("/update")
    @Operation(summary = "更新短信签名")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:update')")
    public CommonResult<Boolean> updateSmsSign(@Valid @RequestBody SmsSignUpdateReqVO updateReqVO) {
        return smsSignService.updateSmsSign(updateReqVO);
    }

    @GetMapping("/audit/result")
    @Operation(summary = "查询短信签名审核结果")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:query')")
    public CommonResult<SmsAuditResultVo> queryAuditResult(@RequestParam("id") String id) {
        return smsSignService.queryAuditResult(id);
    }

    @GetMapping("/detail")
    @Operation(summary = "获得短信签名详情")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:query')")
    public CommonResult<SmsSignDetailVo> detail(@RequestParam("id") String id){
        return smsSignService.detail(id);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除短信签名")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('marketing:sms-sign:delete')")
    public CommonResult<Boolean> deleteSmsSign(@RequestParam("id") Long id) {
        smsSignService.deleteSmsSign(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得短信签名")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-sign:query')")
    public CommonResult<SmsSignRespVO> getSmsSign(@RequestParam("id") Long id) {
        SmsSignDO smsSign = smsSignService.getSmsSign(id);
        return success(BeanUtils.toBean(smsSign, SmsSignRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得短信签名分页")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:query')")
    public CommonResult<PageResultSms<SmsSignPageVo>> getSmsSignPage(@Valid @RequestBody SmsSignPageReqVO pageReqVO) {
        return smsSignService.getSmsSignPage(pageReqVO);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出短信签名 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-sign:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsSignExcel(@Valid SmsSignPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        /*List<SmsSignDO> list = smsSignService.getSmsSignPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "短信签名.xls", "数据", SmsSignRespVO.class,
                        BeanUtils.toBean(list, SmsSignRespVO.class));*/
    }

}