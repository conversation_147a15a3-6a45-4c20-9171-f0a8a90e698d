package info.qizhi.aflower.module.marketing.service.coupon;

import cn.hutool.core.collection.CollUtil;
import info.qizhi.aflower.framework.common.enums.CouponEnum;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifyPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifyRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifySaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.VerifyDO;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.VerifyMapper;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;


/**
 * 券核销 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class VerifyServiceImpl implements VerifyService {

    @Resource
    private VerifyMapper verifyMapper;
    @Resource
    private GeneralConfigApi generalConfigApi;

    @Override
    public Boolean createVerify(List<VerifySaveReqVO> createReqVO) {
        // 插入
        List<VerifyDO> verify = BeanUtils.toBean(createReqVO, VerifyDO.class);
        return verifyMapper.insertBatch(verify);
    }


    @Override
    public PageResult<VerifyRespVO> getVerifyPage(VerifyPageReqVO pageReqVO) {
        LocalDate bizDate = generalConfigApi.getBizDay(pageReqVO.getHcode()).getData();
        pageReqVO.setBizDate(bizDate);
        PageResult<VerifyDO> verifyPageResult = verifyMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(verifyPageResult.getList())) {
            return new PageResult<>(CollUtil.newArrayList(), 0L);
        }
        PageResult<VerifyRespVO> result = BeanUtils.toBean(verifyPageResult, VerifyRespVO.class);
        result.getList().forEach(item -> {
            item.setTemplateName(CouponEnum.BREAKFAST.getName());
        });
        return result;
    }


}