package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 短信发送内容日志 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsMsgLogRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20532")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果为集团发送，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码;如果为集团发送，该字段为0")
    private String hcode;

    @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批次号")
    private String batchNo;

    @Schema(description = "模板代码")
    @ExcelProperty("模板代码")
    private String templateCode;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "发送人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送人数")
    private Integer sendPersons;

    @Schema(description = "发送短信数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送短信数")
    private Integer sendNum;

    @Schema(description = "调用者", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("调用者")
    private String invoker;

    @Schema(description = "接收手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接收手机号")
    private String phones;

    @Schema(description = "发送方式;0:系统业务触发 1：人工群发", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送方式;0:系统业务触发 1：人工群发")
    private String sendMode;

    @Schema(description = "发送时间")
    @ExcelProperty("发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}