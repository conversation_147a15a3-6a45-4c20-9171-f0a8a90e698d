package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send;

import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookTodayRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderHisRespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 短信发送 - 发送对象客户群组 Response VO")
@Data
public class SmsSendClientRespVO {

    @Schema(description = "会员信息", required = true, example = "1")
    private List<MemberAndStoreCardRespDTO> memberByMtCode;

    @Schema(description = "今日预抵", required = true, example = "1")
    private List<BookTodayRespDTO> getTodayBookList;

    @Schema(description = "挂s账客人", required = true, example = "1")
    private List<OrderHisRespDTO> getOrderHistory;

    @Schema(description = "在住客人", required = true, example = "1")
    private List<OrderRespDTO> getCurrentOrders;

    @Schema(description = "客人在住且生日", required = true, example = "1")
    private List<OrderRespDTO> getCurrentOrdersWithBirthday;
}
