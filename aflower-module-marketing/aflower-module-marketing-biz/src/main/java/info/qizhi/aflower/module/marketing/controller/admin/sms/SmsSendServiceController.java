package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendClientReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendClientRespVO;
import info.qizhi.aflower.module.marketing.service.sms.SmsSendClientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

/**
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 短信发送对象")
@RestController
@RequestMapping("/marketing/sms-send-contact")
@Validated
@Deprecated
public class SmsSendServiceController {

    @Resource
    private SmsSendClientService smsSendClientService;

    @GetMapping("/phones")
    @Operation(summary = "获得短信发送对象-客户群组")
    @PreAuthorize("@ss.hasPermission('marketing:sms-send-stat:query')")
    public CommonResult<SmsSendClientRespVO> sendMember(@Valid SmsSendClientReqVO reqVO) {
        return success(smsSendClientService.sendMember(reqVO));
    }
}
