package info.qizhi.aflower.module.marketing.service.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import info.qizhi.aflower.framework.common.enums.*;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.date.DateUtils;
import info.qizhi.aflower.framework.common.util.number.MoneyUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.security.core.util.SecurityFrameworkUtils;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifyBreakfastRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifySaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.VerifySummaryRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.*;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.bo.CouponGenerateBO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.bo.GteantUserInfoBO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastOfContinuationReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.breakfast.BreakFastVerifyReq;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.template.CouponTemplateRespVO;
import info.qizhi.aflower.module.marketing.convert.coupon.BreakfastVerifyConvert;
import info.qizhi.aflower.module.marketing.convert.coupon.CouponCouponTemplateConvert;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponConfigDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateMerchantDO;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponMapper;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.pms.api.account.AccountApi;
import info.qizhi.aflower.module.pms.api.account.dto.AccountListReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.AccountRespDTO;
import info.qizhi.aflower.module.pms.api.account.dto.AccountSaveReqDTO;
import info.qizhi.aflower.module.pms.api.account.dto.ConsumeAccountSaveReqDTO;
import info.qizhi.aflower.module.pms.api.config.GeneralConfigApi;
import info.qizhi.aflower.module.pms.api.config.HotelParamConfigApi;
import info.qizhi.aflower.module.pms.api.config.dto.hotel.HotelParamConfigBreakfastTicketRespDTO;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.OrderPriceApi;
import info.qizhi.aflower.module.pms.api.order.dto.BreakFastReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderBreakfastReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderPriceRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderRespDTO;
import info.qizhi.aflower.module.pms.api.pricerule.PriceAllDayRuleApi;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;
import static info.qizhi.aflower.module.member.enums.ErrorCodeConstants.MEMBER_NOT_EXISTS;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.*;

/**
 * 优惠券 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CouponServiceImpl implements CouponService {

    public static final long BREAKFAST_TIMEOUT_MILLIS = 30 * DateUtils.SECOND_MILLIS;
    public static final long COUPON_TIMEOUT_MILLIS = 30 * DateUtils.SECOND_MILLIS;

    private static final BigDecimal HUNDRED = new BigDecimal(100);

    @Resource
    private CouponMapper couponMapper;
    @Resource
    private AccountApi accountApi;
    @Resource
    private VerifyService verifyService;
    @Resource
    private HotelParamConfigApi hotelParamConfigApi;
    @Resource
    private CouponTemplateService couponTemplateService;
    @Resource
    private CouponConfigService couponConfigService;
    @Resource
    private MerchantApi merchantApi;
    @Resource
    private OrderPriceApi orderPriceApi;
    @Resource
    private OrderApi orderApi;
    @Resource
    private PriceAllDayRuleApi priceAllDayRuleApi;
    @Resource
    private GeneralConfigApi generalConfigApi;
    @Resource
    private MemberApi memberApi;


    @Override
    public void createBreakfasts(BreakFastSaveReqVO createReqVO) {
        // 校验是否开启了早餐服务
        validateBreakFast(createReqVO);
        List<CouponDO> breakFastList = buildBreakFastList(createReqVO);
        // 批量插入
        couponMapper.insertBatch(breakFastList);
    }

    /**
     * 验证创建早餐券业务
     *
     * @param createReqVO 入参
     */
    private void validateBreakFast(BreakFastSaveReqVO createReqVO) {
        // 获取早餐券设置
        HotelParamConfigBreakfastTicketRespDTO breakfastTicket = hotelParamConfigApi.getHotelParamConfigBreakfastTicket(createReqVO.getGcode(), createReqVO.getHcode()).getData();
        if (breakfastTicket == null) {
            throw exception(HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_EXISTS);
        }
        if (NumberEnum.ZERO.getNumber().equals(breakfastTicket.getValue().getOpen())) {
            throw exception(HOTEL_PARAM_CONFIG_BREAKFAST_TICKET_NOT_OPEN);
        }
    }

    /**
     * 封装早餐券
     *
     * @param createReqVO 入参
     * @return breakfastList
     */
    private List<CouponDO> buildBreakFastList(BreakFastSaveReqVO createReqVO) {
        List<CouponDO> breakfastList = CollUtil.newArrayList();
        createReqVO.getBkList().forEach(bk -> {
            for (int i = 0; i < bk.getCount(); i++) {
                CouponDO couponDO = CouponDO.builder()
                        .gcode(createReqVO.getGcode())
                        .hcode(createReqVO.getHcode())
                        .rCode(createReqVO.getRCode())
                        .rNo(createReqVO.getRNo())
                        .couponCode(IdUtil.getSnowflakeNextIdStr())
                        .orderNo(createReqVO.getOrderNo())
                        .orderType(createReqVO.getOrderType())
                        .expDate(bk.getExpDate())
                        .effDate(bk.getEffDate())
                        .state(NumberEnum.ONE.getNumber())
                        .gainMode(createReqVO.getGainMode())
                        .money(bk.getMoney())
                        .channelCode(createReqVO.getChannelCode())
                        .couponType(CouponEnum.BREAKFAST.getCode())
                        .templateCode(CouponEnum.BREAKFAST.getCode())
                        .templateName(CouponEnum.BREAKFAST.getName())
                        .grantTime(LocalDateTime.now())
                        .effDate(bk.getEffDate())
                        .expDate(bk.getExpDate())
                        .build();
                breakfastList.add(couponDO);
            }
        });
        return breakfastList;
    }


    /**
     * 续住延长未使用的购买的早餐券
     *
     * @param reqVO
     */
    @Override
    public void continueUpdateBuyBreakFast(BreakFastOfContinuationReqVO reqVO) {
        List<CouponDO> list = couponMapper.selectListByOrderNo(reqVO.getOrderNo(), reqVO.getGcode(), CouponEnum.BREAKFAST.getCode(), TicketGainModeEnum.BUY.getCode());
        if (CollUtil.isNotEmpty(list)) {
            for (CouponDO couponDO : list) {
                couponDO.setExpDate(LocalDate.from(reqVO.getLeaveTime()));
            }
            couponMapper.updateBatch(list);
        }
    }

    /**
     * 早餐卷核销
     */
    @Override
    @GlobalTransactional
    @Lock4j(keys = {"#reqVO.gcode", "#reqVO.hcode", "#reqVO.orderNo"}, expire = BREAKFAST_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public void breakfastVerify(BreakFastVerifyReq reqVO) {
        // 默认内置模板生成优惠券核销逻辑
        verifyBreakfast(reqVO);
        // 早餐券模板生成的早餐券核销逻辑
        List<CouponDO> breakfast = reqVO.getBreakfast();
        if (CollUtil.isNotEmpty(breakfast)) {
            // 执行早餐券核验逻辑
            verify(breakfast, reqVO.getHcode());
            // 创建验证记录
            createVerify(reqVO, breakfast);
        }
    }

    private void verifyBreakfast(BreakFastVerifyReq reqVO) {
        // 获取订单
        OrderRespDTO order = orderApi.getOrderByOrderNo(reqVO.getOrderNo()).getData();
        // 通过订单号 以及 系统时间前一天 确定每日价格表记录 获取赠送早餐数 房包早餐数  通过订单号获取购买早餐数
        // 获取最早入住时间
        //PriceAllDayRuleRespDTO priceAllDayRule = priceAllDayRuleApi.getPriceAllDayRule(reqVO.getGcode(), reqVO.getHcode()).getData();
        // 是否显示当天
       /* boolean flag = false;
        if(order.getCheckinTime().toLocalTime().isBefore(priceAllDayRule.getEarliestCheckinTime())){
         flag = true;
        }*/
        BreakFastReqDTO breakFastReqDTO = new BreakFastReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setOrderNo(reqVO.getOrderNo())
                .setPriceDate(LocalDate.now().minusDays(1));
        OrderPriceRespDTO orderPrice = orderPriceApi.getBreakfastCoupon(breakFastReqDTO).getData();
        if (orderPrice == null) {
            throw exception(ORDER_PRICE_NOT_EXISTS);
        }

        if (order == null) {
            throw exception(ORDER_NOT_EXISTS);
        }
        // 房包早餐数
        Integer roomBkNum = orderPrice.getRoomBkNum();
        // 赠送早餐数
        Integer bkNum = orderPrice.getBkNum();
        // 购买早餐数
        Integer buyBkNum = order.getBuyBkNum();
        // 购早餐券已使用数
        Integer bkUsedNum = order.getBkUsedNum();
        // 购早剩余券数
        Integer buyBkRemain = buyBkNum - bkUsedNum;
        // 可使用早餐券总数
        Integer totalNum = roomBkNum + bkNum + buyBkRemain;
        // 判断核销早餐数是否合法
        if (reqVO.getNum() > totalNum) {
            throw exception(VERIFY_BREAKFAST_NUM_ERROR);
        }

        // 核销早餐
        // 剩余核销数
        int remainNum = reqVO.getNum();
        if (!(NumberEnum.ZERO.getNumberInt().equals(roomBkNum) && NumberEnum.ZERO.getNumberInt().equals(bkNum))) {
            // 使用 房包早餐券
            if (roomBkNum > NumberEnum.ZERO.getNumberInt() && remainNum > NumberEnum.ZERO.getNumberInt()) {
                // 实际核销数
                int veriftyNum = Math.min(roomBkNum, remainNum);
                roomBkNum = roomBkNum - veriftyNum;
                remainNum = remainNum - veriftyNum;
            }
            // 使用 赠送早餐券
            if (bkNum > NumberEnum.ZERO.getNumberInt() && remainNum > NumberEnum.ZERO.getNumberInt()) {
                // 实际核销数
                int veriftyNum = Math.min(bkNum, remainNum);
                bkNum = bkNum - veriftyNum;
                remainNum = remainNum - veriftyNum;
            }
            orderPriceApi.updateBreakfastCoupon(breakFastReqDTO.setRoomBkNum(roomBkNum).setBkNum(bkNum));
        }
        // 使用 购买早餐券
        if (buyBkRemain > NumberEnum.ZERO.getNumberInt() && remainNum > NumberEnum.ZERO.getNumberInt()) {
            // 实际核销数
            int veriftyNum = Math.min(buyBkRemain, remainNum);
            bkUsedNum = bkUsedNum + veriftyNum;
            orderApi.updateOrderUseBreakfast(BeanUtils.toBean(order, OrderBreakfastReqDTO.class).setBkUsedNum(bkUsedNum));
        }

        // 生成已使用的早餐券记录
        List<CouponDO> breakfastList = CollUtil.newArrayList();
        for (int i = 0; i < reqVO.getNum(); i++) {
            CouponDO couponDO = CouponDO.builder()
                    .gcode(reqVO.getGcode())
                    .hcode(reqVO.getHcode())
                    .rCode(order.getRCode())
                    .rNo(order.getRNo())
                    .couponCode(IdUtil.getSnowflakeNextIdStr())
                    .belong(NumberEnum.ZERO.getNumber())
                    .orderNo(reqVO.getOrderNo())
                    .orderType(order.getOrderType())
                    .state(NumberEnum.TWO.getNumber())
                    .money(Long.valueOf(NumberEnum.ZERO.getNumberInt()))
                    .channelCode(order.getChannelCode())
                    .couponType(CouponEnum.BREAKFAST.getCode())
                    .templateCode(CouponEnum.BREAKFAST.getCode())
                    .templateName(CouponEnum.BREAKFAST.getName())
                    .build();
            breakfastList.add(couponDO);
        }
        couponMapper.insertBatch(breakfastList);
        // 生成核销记录
        createVerify(reqVO, breakfastList);
    }

    private void verify(List<CouponDO> breakfast, String hcode) {
        breakfast.forEach(couponDO -> {
            couponDO.setState(NumberEnum.TWO.getNumber());
            couponDO.setUseTime(LocalDateTime.now());
            couponDO.setHcode(hcode);
        });
        // 批量更新早餐券状态
        couponMapper.updateBatch(breakfast);
    }

    private void createVerify(BreakFastVerifyReq reqVo, List<CouponDO> breakfast) {
        String batchNo = IdUtil.getSnowflakeNextIdStr();
        AtomicBoolean isFirst = new AtomicBoolean(true);
        List<VerifySaveReqVO> collect = breakfast.stream().map(couponDO -> {
            VerifySaveReqVO verify = BreakfastVerifyConvert.INSTANCE.breakfastVerifyConvert(couponDO);
            verify.setVerifyMode(reqVo.getVerifyMode());
            verify.setHcode(reqVo.getHcode());
            verify.setCreator(reqVo.getCreator());
            verify.setBatchNo(batchNo);

            // 只有第一个元素设置 num
            if (isFirst.get()) {
                verify.setNum(breakfast.size());  // 只给第一个记录赋值 num
                isFirst.set(false);  // 标记后面的记录不是第一个
            }

            return verify;
        }).collect(Collectors.toList());
        //创建核销记录
        verifyService.createVerify(collect);
    }

    /**
     * 获得该房间需要核销的早餐卷
     */
    @Override
    public VerifyBreakfastRespVO getBreakfast(BreakFastReqVO reqVO) {
        BreakFastReqDTO breakFastReqDTO = new BreakFastReqDTO()
                .setGcode(reqVO.getGcode())
                .setHcode(reqVO.getHcode())
                .setOrderNo(reqVO.getOrderNo())
                .setPriceDate(LocalDate.now().minusDays(1));
        OrderPriceRespDTO orderPrice = orderPriceApi.getBreakfastCoupon(breakFastReqDTO).getData();
        OrderRespDTO order = orderApi.getOrderByOrderNo(reqVO.getOrderNo()).getData();
        // 房包早餐数
        Integer roomBkNum = orderPrice.getRoomBkNum();
        // 赠送早餐数
        Integer bkNum = orderPrice.getBkNum();
        // 购买早餐数
        Integer buyBkNum = order.getBuyBkNum();
        // 购早餐券已使用数
        Integer bkUsedNum = order.getBkUsedNum();
        // 购早剩余券数
        Integer buyBkRemain = buyBkNum - bkUsedNum;
        List<CouponDO> couponDOS = couponMapper.selectBreakfastList(reqVO);
        // 可使用早餐券总数
        Integer totalNum = roomBkNum + bkNum + buyBkRemain + couponDOS.size();
        VerifyBreakfastRespVO verifyBreakfastRespVO = new VerifyBreakfastRespVO();
        verifyBreakfastRespVO.setCoupons(BeanUtils.toBean(couponDOS, CouponRespVO.class));
        verifyBreakfastRespVO.setNum(totalNum);
        return verifyBreakfastRespVO;
    }


    @Override
    public CouponRespVO getCoupon(String couponCode, String gcode) {
        CouponDO couponDO = couponMapper.selectOne(CouponDO::getCouponCode, couponCode, CouponDO::getGcode, gcode);
        List<MerchantRespDTO> merchants = merchantApi.getMerchantList(gcode).getData();
        Map<String, String> collect = merchants.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
        CouponRespVO bean = BeanUtils.toBean(couponDO, CouponRespVO.class);
        bean.setHname(collect.get(couponDO.getHcode()));
        return bean;

    }

    @Override
    public PageResult<CouponRespVO> getCouponPage(CouponPageReqVO pageReqVO) {
        PageResult<CouponDO> couponDOPageResult = couponMapper.selectPage(pageReqVO);
        if (CollUtil.isEmpty(couponDOPageResult.getList())) {
            return new PageResult<>(CollUtil.newArrayList(), 0L);
        }
        List<MerchantRespDTO> merchants = merchantApi.getMerchantList(pageReqVO.getGcode()).getData();
        Map<String, String> collect = merchants.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
        PageResult<CouponRespVO> bean = BeanUtils.toBean(couponDOPageResult, CouponRespVO.class);
        bean.getList().forEach(couponRespVO -> {
            couponRespVO.setHname(collect.get(couponRespVO.getHcode()));
            couponRespVO.setCouponTypeName(CouponEnum.getByCode(couponRespVO.getCouponType()));
            couponRespVO.setActivityName(CouponActivityTypeEnum.getNameByCode(couponRespVO.getActivityCode()));
            if (couponRespVO.getActivityCode() != null && couponRespVO.getActivityName() == null) {
                couponRespVO.setActivityName(CouponActivityTypeEnum.RECHARGE.getName());
            }
        });
        return bean;
    }

    /**
     * 获取该会员的优惠卷
     */
    @Override
    public PageResult<CouponRespVO> getMemberCouponPage(CouponGetReqVO reqVO) {
        if (NumberEnum.ZERO.getNumber().equals(reqVO.getGetMode())) {
            PageResult<CouponDO> couponDO = couponMapper.selectPageByBelong(reqVO);
            PageResult<CouponRespVO> bean = BeanUtils.toBean(couponDO, CouponRespVO.class);
            List<CouponRespVO> couponRespVO = CollectionUtils.filterList(bean.getList(), coupon -> coupon.getCouponType().equals(CouponEnum.BREAKFAST.getCode()) && coupon.getGainMode().equals(TicketGainModeEnum.BUY.getCode()));
            return new PageResult<>(couponRespVO, bean.getTotal());
        }
        PageResult<CouponDO> couponDO = couponMapper.selectPageByBelong(reqVO);
        PageResult<CouponRespVO> bean = BeanUtils.toBean(couponDO, CouponRespVO.class);
        bean.getList().forEach(coupon -> {
            String couponType = coupon.getCouponType();
            coupon.setCouponTypeName(CouponEnum.getByCode(couponType));
            coupon.setActivityName(CouponActivityTypeEnum.getNameByCode(coupon.getActivityCode()));
            if (coupon.getActivityCode() != null && coupon.getActivityName() == null) {
                coupon.setActivityName(CouponActivityTypeEnum.RECHARGE.getName());
            }
        });
        return bean;
    }

    /**
     * 获取该会员的可用优惠卷数
     *
     * @param gcode
     * @param belong
     */
    @Override
    public Long getMemberCouponListCount(String gcode, String belong) {
        return couponMapper.selectByBelongCount(gcode, belong);
    }


    /**
     * 核销数据总览
     */
    @Override
    public VerifySummaryRespVO verifyDataOverview(LocalDate bizDate, String gcode, String hcode) {
        // 获取当日开始和结束时间
        LocalDateTime startOfDay = bizDate.atStartOfDay();
        LocalDateTime endOfDay = bizDate.atTime(LocalTime.MAX);
        // 获取核销数据总览
        return getVerifySummaryRespVO(gcode, startOfDay, endOfDay, hcode);
    }

    private VerifySummaryRespVO getVerifySummaryRespVO(String gcode, LocalDateTime startOfDay, LocalDateTime endOfDay, String hcode) {
        // 今日总数 按营业日统计当日发放的早餐券总数。（房包早+赠送+购买）
        Long todayTotal = couponMapper.selectAll(startOfDay, endOfDay, gcode);
        // 今日已核销
        Long useOrCancellation = couponMapper.selectUse(startOfDay, endOfDay, gcode, hcode);
        // 今日作废
        Long cancellation = couponMapper.selectCancellation(startOfDay, endOfDay, gcode);
        // 今日待核销  未核销的早餐券数。
        Long unwritten = couponMapper.selectVerifyList(gcode);
        // 封装返回
        VerifySummaryRespVO verifySummaryRespVO = new VerifySummaryRespVO();
        verifySummaryRespVO.setTotal(todayTotal);
        verifySummaryRespVO.setWritten(useOrCancellation);
        verifySummaryRespVO.setUnWritten(unwritten);
        verifySummaryRespVO.setCancellation(cancellation);
        return verifySummaryRespVO;
    }

    /**
     * 生成优惠券
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#createReqVO.hcode"}, expire = COUPON_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public Boolean createCoupon(CouponGenerateReqVO createReqVO) {
        // 获取全部优惠卷模板
        List<CouponTemplateDO> couponTemplateList = couponTemplateService.getCouponTemplateList(createReqVO.getGcode(), createReqVO.getHcode(), NumberEnum.ONE.getNumber());
        Map<String, CouponTemplateDO> templateCache = CollectionUtils.convertMap(couponTemplateList, CouponTemplateDO::getTemplateCode);
        // 获取使用优惠卷对应的模板列表
        List<CouponTemplateDO> templates = new ArrayList<>();
        List<CouponGenerateBO> couponGenerates = getCouponGenerateBOS(createReqVO, templateCache, templates);
        // 需要生成的优惠券列表
        List<CouponDO> coupon = new ArrayList<>();
        return create(templates, couponGenerates, coupon, createReqVO);
    }

    private Boolean create(List<CouponTemplateDO> templates, List<CouponGenerateBO> couponGenerates, List<CouponDO> coupon, CouponGenerateReqVO reqVO) {
        templates.forEach(couponTemplateDO -> {
            // 构建优惠卷信息
            CouponSaveReqVO couponSaveReqVO = CouponCouponTemplateConvert.INSTANCE.couponCouponTemplateConvert(couponTemplateDO);
            CouponGenerateBO couponGenerateBo = getCouponGenerateBo(couponGenerates, couponTemplateDO, couponSaveReqVO, reqVO);
            // 生成指定数量的优惠卷
            List<CouponDO> coupons = getCoupons(couponSaveReqVO, couponGenerateBo);
            coupon.addAll(coupons);
            // 更新优惠卷模板发放数和生成数
            couponTemplateDO.setNum(couponGenerateBo.getNum() + couponTemplateDO.getNum());
            couponTemplateDO.setSendNum(couponGenerateBo.getNum() + couponTemplateDO.getSendNum());
        });
        // 批量插入优惠卷
        Boolean aBoolean = couponMapper.insertBatch(coupon);
        // 更新优惠卷模板
        couponTemplateService.updateCouponTemplateBatch(templates);
        return aBoolean;
    }

    private List<CouponDO> getCoupons(CouponSaveReqVO couponSaveReqVO, CouponGenerateBO couponGenerateBo) {
        List<CouponSaveReqVO> newBreakfastList = IntStream.range(0, couponGenerateBo.getNum())
                .parallel()
                .mapToObj(i -> {
                    CouponSaveReqVO coupon = new CouponSaveReqVO();
                    org.springframework.beans.BeanUtils.copyProperties(couponSaveReqVO, coupon); // 复制属性
                    coupon.setCouponCode(IdUtil.getSnowflakeNextIdStr()); // 设置新的 couponCode
                    return coupon;
                })
                .collect(Collectors.toList());
        // 封装优惠卷
        return BeanUtils.toBean(newBreakfastList, CouponDO.class);
    }

    private CouponGenerateBO getCouponGenerateBo(List<CouponGenerateBO> couponGenerates, CouponTemplateDO couponTemplateDO, CouponSaveReqVO couponSaveReqVO, CouponGenerateReqVO reqVO) {
        Map<String, CouponGenerateBO> CouponGenerateMap = CollectionUtils.convertMap(couponGenerates, CouponGenerateBO::getTemplateCode);
        CouponGenerateBO couponGenerateBO = CouponGenerateMap.get(couponTemplateDO.getTemplateCode());
        couponSaveReqVO.setBelong(couponGenerateBO.getBelong());
        couponSaveReqVO.setName(couponGenerateBO.getName());
        couponSaveReqVO.setChannelCode(ChannelEnum.MIMI_APP.getCode());
        couponSaveReqVO.setOrderNo(reqVO.getOrderNo());
        couponSaveReqVO.setActivityCode(couponGenerateBO.getActivityCode());
        couponSaveReqVO.setOrderType(reqVO.getOrderType());
        if (NumberEnum.TWO.getNumber().equals(couponTemplateDO.getEffeType())) {
            couponSaveReqVO.setEffDate(LocalDate.now().plusDays(couponTemplateDO.getNstart()));
            couponSaveReqVO.setExpDate(LocalDate.now().plusDays(couponTemplateDO.getXday()));
        }
        // 判断生成卷的类型
        //1.voucher 代金券
        if (CouponEnum.VOUCHER.getCode().equals(couponSaveReqVO.getCouponType())) {
            if (couponSaveReqVO.getMoney() == null) {
                throw exception(COUPON_MONEY_NOT_NULL);
            }
            couponSaveReqVO.setRebate(BigDecimal.valueOf(NumberEnum.ONE.getNumberInt()));
        }
        //2.discount 折扣券
        if (CouponEnum.DISCOUNT.getCode().equals(couponSaveReqVO.getCouponType())) {
            if (couponSaveReqVO.getRebate() == null) {
                throw exception(COUPON_REBATE_NOT_NULL);
            }
            couponSaveReqVO.setMoney(Long.valueOf(NumberEnum.ZERO.getNumberInt()));
        }
        //3.free 免房券
        if (CouponEnum.FREE.getCode().equals(couponSaveReqVO.getCouponType())) {
            couponSaveReqVO.setMoney(Long.valueOf(NumberEnum.ZERO.getNumberInt()));
            couponSaveReqVO.setRebate(BigDecimal.valueOf(NumberEnum.ONE.getNumberInt()));
        }
        return couponGenerateBO;
    }

    private List<CouponGenerateBO> getCouponGenerateBOS(CouponGenerateReqVO createReqVO, Map<String, CouponTemplateDO> templateCache, List<CouponTemplateDO> templates) {
        List<CouponGenerateBO> couponGenerates = createReqVO.getCouponGenerate();
        for (CouponGenerateBO generateReqVO : couponGenerates) {
            CouponTemplateDO couponTemplateDO = templateCache.get(generateReqVO.getTemplateCode());
            if (couponTemplateDO == null) {
                throw exception(COUPON_TEMPLATE_NOT_APPLICABLE);
            }
            templates.add(couponTemplateDO);
        }
        return couponGenerates;
    }


    /**
     * 发放优惠券
     */
    @Override
    public Boolean grantCoupon(CouponGrantReqVO reqVO) {
        // 获取优惠卷模板
        CouponTemplateRespVO couponTemplate = couponTemplateService.getCouponTemplate(reqVO.getTemplateCode(), reqVO.getGcode(), reqVO.getHcode());
        if (couponTemplate == null) {
            throw exception(COUPON_TEMPLATE_NOT_EXISTS);
        }
        // 判断模板是否有效
        if (!couponTemplate.getState().equals(NumberEnum.ONE.getNumber())) {
            throw exception(COUPON_TEMPLATE_INVALID);
        }
        // 封装优惠券生成
        CouponGenerateReqVO couponGenerateReqVO = new CouponGenerateReqVO();
        couponGenerateReqVO.setGcode(reqVO.getGcode());
        couponGenerateReqVO.setHcode(reqVO.getHcode());
        // 获取优惠券生成信息
        List<CouponGenerateBO> couponGenerate = new ArrayList<>();
        // 发放优惠券
        List<GteantUserInfoBO> gteantUserInfo = reqVO.getGteantUserInfo();
        for (GteantUserInfoBO userInfo : gteantUserInfo) {
            CouponGenerateBO couponGenerateBO = new CouponGenerateBO();
            couponGenerateBO.setBelong(userInfo.getBelong());
            couponGenerateBO.setName(userInfo.getName());
            couponGenerateBO.setGcode(reqVO.getGcode());
            couponGenerateBO.setHcode(reqVO.getHcode());
            couponGenerateBO.setNum(reqVO.getNum());
            couponGenerateBO.setTemplateCode(reqVO.getTemplateCode());
            couponGenerate.add(couponGenerateBO);
        }
        couponGenerateReqVO.setCouponGenerate(couponGenerate);
        return createCoupon(couponGenerateReqVO);
    }

    /**
     * 使用优惠卷
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.hcode", "#reqVO.orderNo"}, expire = COUPON_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public Boolean useCoupon(CouponUseReqVO reqVO) {
        // 使用的优惠卷
        //List<CouponUseSaveReqVO> coupons = reqVO.getCouponCode();
        List<String> couponCodes = CollectionUtils.convertList(reqVO.getCouponCode(), CouponUseSaveReqVO::getCouponCode);
        Map<String, String> accNoMap = CollectionUtils.convertMap(reqVO.getCouponCode(), CouponUseSaveReqVO::getCouponCode, CouponUseSaveReqVO::getAccNo);
        List<CouponDO> coupons = getNoUseCouponList(new CouponReqVO().setGcode(reqVO.getGcode()).setCouponCodes(couponCodes));
        // 判断多间夜是否可以使用多张券
        isMore(reqVO, coupons);
        // 获取优惠卷模板
        Map<String, CouponTemplateDO> templateMap = getCouponTemplateMap(reqVO);
        // 获取订单已使用的所有优惠卷
        List<CouponDO> couponUse = getCouponDOList(reqVO);
        // 获取该订单的房间账务
        List<AccountRespDTO> accountList = getAccountRespDTO(reqVO);
        Map<String, AccountRespDTO> accountMap = CollectionUtils.convertMap(accountList, AccountRespDTO::getAccNo);
        // 当前日期
        LocalDate now = LocalDate.now();
        // 优惠券账务集合
        List<AccountSaveReqDTO> accounts = new ArrayList<>();
        List<ConsumeAccountSaveReqDTO> consumeAccounts = new ArrayList<>();
        // 优惠卷使用逻辑处理
        buildCouponUse(reqVO, coupons, templateMap, couponUse, accountMap, now, accounts, consumeAccounts, accNoMap);
        // 批量修改优惠卷状态
        couponMapper.updateBatch(BeanUtils.toBean(coupons, CouponDO.class));
        // 批量插入账务
        if (CollUtil.isNotEmpty(accounts)) {
            accountApi.createAccounts(accounts);
        }
        return true;
    }

    private List<CouponDO> getNoUseCouponList(CouponReqVO reqVO) {
        return couponMapper.selectNoUseList(reqVO);
    }

    /**
     * 获取该优惠券模板下生成的优惠券数量
     *
     * @param templateCode
     */
    @Override
    public Long getCouponCount(String templateCode, String gcode) {
        return couponMapper.selectCountByTemplateCode(templateCode, gcode);
    }

    /**
     * 批量作废优惠券
     *
     * @param reqVO
     */
    @Override
    public void invalidCoupon(List<CouponDO> reqVO) {
        for (CouponDO couponDO : reqVO) {
            couponDO.setState(NumberEnum.THREE.getNumber());
        }
        couponMapper.updateBatch(reqVO);
    }

    /**
     * 获取赠券活动赠送的券
     *
     * @param orderNo
     * @param gcode
     */
    @Override
    public List<CouponDO> getFreeTicket(String gcode, String orderNo) {
        return couponMapper.selectNoUseList(gcode, orderNo);
    }

    @Override
    public List<CouponRespVO> getMemberCouponList(CouponReqVO reqVO) {
        reqVO.setState(NumberEnum.ONE.getNumber());
        // 获取会员信息
        MemberAndStoreCardRespDTO member = memberApi.getMemberAndStoreCardByMcode(reqVO.getGcode(), reqVO.getMCode(), reqVO.getHcode()).getData();
        if (member == null) {
            throw exception(MEMBER_NOT_EXISTS);
        }
        List<CouponDO> couponDO = couponMapper.selectList(reqVO.setBelong(member.getPhone()));
        if (CollUtil.isEmpty(couponDO)) {
            return new ArrayList<>();
        }
        // 获得优惠券模版
        List<CouponTemplateDO> couponTemplateList = couponTemplateService.getCouponTemplateList(reqVO.getGcode(), reqVO.getHcode(), NumberEnum.ONE.getNumber());
        Map<String, CouponTemplateDO> couponTemplateDOMap = CollectionUtils.convertMap(couponTemplateList, CouponTemplateDO::getTemplateCode);
        // 获得优惠券关联门店
        List<CouponTemplateMerchantDO> couponTemplateMerchantListByGcode = couponTemplateService.getCouponTemplateMerchantListByGcode(reqVO.getGcode());
        Map<String, List<CouponTemplateMerchantDO>> couponTemplateMerchantMap = CollectionUtils.convertMultiMap(couponTemplateMerchantListByGcode, CouponTemplateMerchantDO::getTemplateCode);
        List<CouponRespVO> list = BeanUtils.toBean(couponDO, CouponRespVO.class);
        // 处理每个优惠券并标记是否匹配当前hcode
        list.forEach(coupon -> {
            String couponType = coupon.getCouponType();
            CouponTemplateDO couponTemplateDO = couponTemplateDOMap.getOrDefault(coupon.getTemplateCode(), new CouponTemplateDO());
            coupon.setCouponTypeName(CouponEnum.getByCode(couponType));
            coupon.setConsume(couponTemplateDO.getConsume());
            coupon.setRtCodes(couponTemplateDO.getRtCodes());

            List<String> hcodes = new ArrayList<>();
            couponTemplateMerchantMap.getOrDefault(coupon.getTemplateCode(), new ArrayList<>())
                    .forEach(couponTemplateMerchantDO -> {
                        hcodes.add(couponTemplateMerchantDO.getHcode());
                    });
            coupon.setHcodes(hcodes);

            // 添加一个标记字段表示是否匹配当前hcode
            coupon.setMatchCurrentHcode(hcodes.contains(reqVO.getHcode()));
        });

        // 排序：匹配的在前，不匹配的在后
        list.sort((c1, c2) -> {
            if (c1.isMatchCurrentHcode() && !c2.isMatchCurrentHcode()) {
                return -1;
            } else if (!c1.isMatchCurrentHcode() && c2.isMatchCurrentHcode()) {
                return 1;
            }
            return 0;
        });

        return list;
    }

    @Override
    public List<CouponDO> getCouponList(CouponReqVO reqVO) {
        return couponMapper.selectList(reqVO);
    }

    private void buildCouponUse(CouponUseReqVO reqVO, List<CouponDO> coupons, Map<String, CouponTemplateDO> templateMap,
                                List<CouponDO> couponUse, Map<String, AccountRespDTO> accountMap,
                                LocalDate now, List<AccountSaveReqDTO> accounts, List<ConsumeAccountSaveReqDTO> consumeAccounts,
                                Map<String, String> accNoMap) {
        coupons.forEach(coupon -> {
            // 优惠卷有效期校验
            if (now.isBefore(coupon.getEffDate()) || now.isAfter(coupon.getExpDate())) {
                throw exception(COUPON_EXPIRED);
            }
            String accNo = accNoMap.getOrDefault(coupon.getCouponCode(), "");
            AccountRespDTO account = accountMap.get(accNo);
            if (account == null) {
                return;
            }
            // 获取优惠卷模板
            CouponTemplateDO couponTemplate = templateMap.get(coupon.getTemplateCode());
            // 判断是否可用
            isAvailable(reqVO, coupons, couponUse, coupon, couponTemplate);
            // 优惠卷使用修改相关状态属性
            buildCouponinfo(reqVO, coupon, accNo);

            // 构建账务信息
            AccountSaveReqDTO accountSaveReqDTO = getAccountReqDTO(reqVO, account, coupon, couponTemplate.getAccMode());
            accounts.add(accountSaveReqDTO);

        });
    }

    private void isAvailable(CouponUseReqVO reqVO, List<CouponDO> coupons, List<CouponDO> couponUse, CouponDO coupon, CouponTemplateDO couponTemplate) {
        // 适用门店
        List<CouponTemplateMerchantDO> couponTemplateMerchant = couponTemplateService.getCouponTemplateMerchantListByTemplateCode(coupon.getTemplateCode());
        List<CouponTemplateMerchantDO> couponTemplateMerchantDOS = CollectionUtils.filterList(couponTemplateMerchant, merchant -> merchant.getHcode().equals(reqVO.getHcode()));
        if (couponTemplateMerchantDOS.size() == NumberEnum.ZERO.getNumberInt()) {
            throw exception(HCODE_COUPON_NOT_AVAILABLE);
        }
        /*// 消费门槛
        consume(reqVO, couponTemplate);*/
        // 判断当前订单是否超过该模板的卷数
        checkTicketNum(coupons, couponUse, coupon, couponTemplate);
        // 判断该房型是否可用该优惠券
        List<String> rtCodes = couponTemplate.getRtCodes();
        if (!rtCodes.get(0).equals(NumberEnum.ZERO.getNumber())) {
            List<String> strings = CollectionUtils.filterList(rtCodes, rtCode -> rtCode.equals(reqVO.getRtCode()));
            if (strings.size() == NumberEnum.ZERO.getNumberInt()) {
                throw exception(RTCODE_COUPON_NOT_AVAILABLE);
            }
        }
        // 判断该入住类型是否可用该优惠券
        if (!couponTemplate.getCheckinType().equals(reqVO.getCheckinType())) {
            throw exception(CHECKINTYPE_COUPON_NOT_AVAILABLE);
        }
        // 判断该客源类型是否可用该优惠券
//        List<String> guestSrc = couponTemplate.getGuestSrc();
//        if (guestSrc != null) {
//            List<String> strings = CollectionUtils.filterList(guestSrc, o -> o.equals(reqVO.getGuestSrc()));
//            if (strings.size() == NumberEnum.ZERO.getNumberInt()) {
//                throw exception(GUESTSRC_COUPON_NOT_AVAILABLE);
//            }
//        }
        // 校验参数
        /*if (CouponEnum.FREE.getCode().equals(coupon.getCouponType()) || CouponEnum.DISCOUNT.getCode().equals(coupon.getCouponType())) {
            if (coupon.getRFee() == null) {
                throw exception(RFEE_NOT_NULL);
            }
        }*/
    }

    private static AccountSaveReqDTO getAccountReqDTO(CouponUseReqVO reqVO, AccountRespDTO account, CouponDO coupon, String accMode) {
        AccountSaveReqDTO accountSaveReqDTO = BeanUtils.toBean(account, AccountSaveReqDTO.class);

        accountSaveReqDTO.setNightNum(BigDecimal.ZERO);
        accountSaveReqDTO.setAfterTaxFee(Long.valueOf(NumberEnum.ZERO.getNumberInt()));
        accountSaveReqDTO.setShiftNo(reqVO.getShiftNo());
        accountSaveReqDTO.setBizDate(reqVO.getBizDate());
        accountSaveReqDTO.setState(AccountStatusEnum.CLOSED.getCode());
        accountSaveReqDTO.setHandleShiftNo(reqVO.getShiftNo());
        accountSaveReqDTO.setAccNo(IdUtil.getSnowflakeNextIdStr());
        accountSaveReqDTO.setIsTurnOutIn(NumberEnum.ZERO.getNumber());
        accountSaveReqDTO.setIsSplit(NumberEnum.ZERO.getNumber());
        accountSaveReqDTO.setIsRev(NumberEnum.ZERO.getNumber());
        accountSaveReqDTO.setIsCanRev(NumberEnum.ONE.getNumber());
        accountSaveReqDTO.setRecorder(SecurityFrameworkUtils.getLoginUserName());

        if (StrUtil.isNotEmpty(reqVO.getState())) {
            accountSaveReqDTO.setState(reqVO.getState());
        }

        //BigDecimal roomFee = new BigDecimal(account.getFee()).divide(HUNDRED, 2, RoundingMode.UP);
        // 判断优惠卷类型 生成对应类型账务
        if (CouponEnum.VOUCHER.getCode().equals(coupon.getCouponType())) {
            accountSaveReqDTO.setFee(coupon.getMoney());
            accountSaveReqDTO.setAccDetail(
                    BusinessDetailsEnum.COUPONCODE.getName() + ":" + coupon.getCouponCode() + "," +
                            BusinessDetailsEnum.COUPONTYPE.getName() + ":" + CouponEnum.getByCode(coupon.getCouponType()) + "," +
                            BusinessDetailsEnum.ZKZ.getName() + ":" + MoneyUtils.fenToYuan(Math.toIntExact(coupon.getMoney())));
        }
        if (CouponEnum.DISCOUNT.getCode().equals(coupon.getCouponType())) {
            BigDecimal discount = (new BigDecimal(1).subtract(coupon.getRebate())).multiply(BigDecimal.valueOf(account.getFee()));
            accountSaveReqDTO.setFee(discount.longValue());
            accountSaveReqDTO.setAccDetail(
                    BusinessDetailsEnum.COUPONCODE.getName() + ":" + coupon.getCouponCode() + "," +
                            BusinessDetailsEnum.COUPONTYPE.getName() + ":" + CouponEnum.getByCode(coupon.getCouponType()) + "," +
                            BusinessDetailsEnum.ZKZ.getName() + ":" + MoneyUtils.fenToYuan(Math.toIntExact(discount.longValue())));
        }
        if (CouponEnum.FREE.getCode().equals(coupon.getCouponType())) {
            accountSaveReqDTO.setFee(account.getFee());
            accountSaveReqDTO.setAccDetail(
                    BusinessDetailsEnum.COUPONCODE.getName() + ":" + coupon.getCouponCode() + "," +
                            BusinessDetailsEnum.COUPONTYPE.getName() + ":" + CouponEnum.getByCode(coupon.getCouponType()) + "," +
                            BusinessDetailsEnum.ZKZ.getName() + ":" + MoneyUtils.fenToYuan(Math.toIntExact(account.getFee())));
        }

        if(NumberEnum.ONE.getNumber().equals(accMode)){
            accountSaveReqDTO.setSubType(DictTypeEnum.DICT_TYPE_PAY_ACCOUNT.getCode());
            accountSaveReqDTO.setSubCode(PayAccountEnum.COUPON.getCode());
        }else {
            if(ConsumeAccountEnum.COUPON_DEDUCTION.getCode().equals(accMode)){
                accountSaveReqDTO.setSubType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode());
                accountSaveReqDTO.setSubCode(ConsumeAccountEnum.COUPON_DEDUCTION.getCode());
            }else {
                accountSaveReqDTO.setSubType(DictTypeEnum.DICT_TYPE_CONSUME_ACCOUNT.getCode());
                accountSaveReqDTO.setSubCode(ConsumeAccountEnum.ADJUST_ROOM_FEE.getCode());
            }
            accountSaveReqDTO.setFee(-accountSaveReqDTO.getFee());
        }
        return accountSaveReqDTO;
    }

    private static void buildCouponinfo(CouponUseReqVO reqVO, CouponDO coupon, String accNo) {
        coupon.setState(NumberEnum.TWO.getNumber());
        coupon.setUseTime(LocalDateTime.now());
        coupon.setHcode(reqVO.getHcode());
        coupon.setOrderNo(reqVO.getOrderNo());
        coupon.setAccNo(accNo);
        coupon.setRNo(reqVO.getRNo());
    }

    private static void checkTicketNum(List<CouponDO> coupons, List<CouponDO> couponUse, CouponDO coupon, CouponTemplateDO couponTemplate) {
        // 一个订单可使用卷数
        Integer numOrder = couponTemplate.getNumOrder();
        // 当前订单已使用该模板的卷数
        int numOrderUsed = CollectionUtils.filterList(couponUse, couponDO -> couponDO.getTemplateCode().equals(coupon.getTemplateCode())).size();
        // 当前订单要使用该模板的卷数
        int numOrderUse = CollectionUtils.filterList(coupons, template -> template.getTemplateCode().equals(coupon.getTemplateCode())).size();
        // 当前订单该模板的总使用卷数
        int num = numOrderUsed + numOrderUse;
        // 判断当前订单是否超过该模板的卷数
        if (numOrder > NumberEnum.ZERO.getNumberInt() && num > numOrder) {
            throw exception(COUPON_NUM_NOT_ENOUGH);
        }
    }

    private static void consume(CouponUseReqVO reqVO, CouponTemplateDO couponTemplate) {
        int consume = Math.toIntExact(couponTemplate.getConsume());
        // 判断消费金额是否满足消费门槛
        if (!NumberEnum.ZERO.getNumberInt().equals(consume) && consume > reqVO.getConsumeOrder()) {
            throw exception(COUPON_CONSUME_NOT_ENOUGH);
        }
    }

    private List<CouponDO> getCouponDOList(CouponUseReqVO reqVO) {
        return couponMapper.selectByOrder(reqVO.getOrderNo(), reqVO.getHcode(), reqVO.getGcode());
    }

    private List<AccountRespDTO> getAccountRespDTO(CouponUseReqVO reqVO) {
        //AccountRespDTO account = accountApi.getAccount(new AccountReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setAccNo(reqVO.getAccNo())).getData();
        List<String> accNos = CollectionUtils.convertList(reqVO.getCouponCode(), CouponUseSaveReqVO::getAccNo);
        List<AccountRespDTO> accountList = accountApi.getAccountList(new AccountListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode()).setAccNos(accNos)).getData();
        if (CollUtil.isEmpty(accountList)) {
            throw exception(ACCOUNT_NOT_EXISTS);
        }
        return accountList;
    }

    private Map<String, CouponTemplateDO> getCouponTemplateMap(CouponUseReqVO reqVO) {
        return couponTemplateService.getCouponTemplateList(
                        reqVO.getGcode(),
                        reqVO.getHcode(),
                        NumberEnum.ONE.getNumber())
                .stream()
                .collect(Collectors.toMap(CouponTemplateDO::getTemplateCode, Function.identity()));
    }

    private void isMore(CouponUseReqVO reqVO, List<CouponDO> coupons) {
        List<CouponConfigDO> couponConfig = couponConfigService.getCouponConfig(reqVO.getGcode(), reqVO.getHcode());
        if (NumberEnum.ZERO.getNumber().equals(couponConfig.get(0).getIsWalkinMore()) && coupons.size() > NumberEnum.ONE.getNumberInt()) {
            throw exception(COUPON_MULTIPLE_NOT_ALLOWED);
        }
    }

}