package info.qizhi.aflower.module.marketing.service.coupon;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import jakarta.validation.*;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.VerifyDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;

/**
 * 券核销 Service 接口
 *
 * <AUTHOR>
 */
public interface VerifyService {

    /**
     * 批量创建券核销
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Boolean createVerify(List<VerifySaveReqVO> createReqVO);


    /**
     * 获得券核销分页
     *
     * @param pageReqVO 分页查询
     * @return 券核销分页
     */
    PageResult<VerifyRespVO> getVerifyPage(VerifyPageReqVO pageReqVO);



}