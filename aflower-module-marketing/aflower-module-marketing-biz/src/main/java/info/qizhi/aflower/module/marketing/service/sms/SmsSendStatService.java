package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendStatSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsSendStatDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;

/**
 * 短信发送统计 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsSendStatService {

    /**
     * 创建短信发送统计
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSmsSendStat(@Valid SmsSendStatSaveReqVO createReqVO);

    /**
     * 获得短信发送统计分页
     *
     * @param pageReqVO 分页查询
     * @return 短信发送统计分页
     */
    PageResult<SmsSendStatDO> getSmsSendStatPage(SmsSendStatPageReqVO pageReqVO);

}