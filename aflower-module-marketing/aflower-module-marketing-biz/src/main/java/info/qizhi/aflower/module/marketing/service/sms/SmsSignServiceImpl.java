package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.util.ObjectUtil;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.sign.SmsSignClient;
import info.qizhi.aflower.module.marketing.api.sms.sign.dto.SignAddReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.sign.dto.SignUpdateReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.sign.dto.SmsSignReqDTO;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsAuditResultVo;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignDetailVo;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignPageVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignAddReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignUpdateReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsSignDO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsSignMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.SMS_SIGN_NOT_EXISTS;

/**
 * 短信签名 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsSignServiceImpl implements SmsSignService {

    @Resource
    private SmsSignMapper smsSignMapper;
    @Resource
    private SmsSignClient smsSignClient;
    @Resource
    private MerchantApi merchantApi;

    @Override
    public CommonResult<Boolean> createSmsSign(SmsSignAddReqVO createReqVO) {
        return smsSignClient.addSign(BeanUtils.toBean(createReqVO, SignAddReqDTO.class));
    }

    @Override
    public CommonResult<Boolean> updateSmsSign(SmsSignUpdateReqVO updateReqVO) {
        return smsSignClient.updateSign(BeanUtils.toBean(updateReqVO, SignUpdateReqDTO.class));
    }

    @Override
    public CommonResult<SmsAuditResultVo> queryAuditResult(String id) {
        return smsSignClient.queryAuditResult(id);
    }

    @Override
    public CommonResult<SmsSignDetailVo> detail(String id) {
        return smsSignClient.detail(id);
    }

    @Override
    public void deleteSmsSign(Long id) {
        // 校验存在
        validateSmsSignExists(id);
        // 删除
        smsSignMapper.deleteById(id);
    }

    private void validateSmsSignExists(Long id) {
        if (smsSignMapper.selectById(id) == null) {
            throw exception(SMS_SIGN_NOT_EXISTS);
        }
    }

    @Override
    public SmsSignDO getSmsSign(Long id) {
        return smsSignMapper.selectById(id);
    }

    @Override
    public CommonResult<PageResultSms<SmsSignPageVo>> getSmsSignPage(SmsSignPageReqVO pageReqVO) {
        SmsSignReqDTO page = BeanUtils.toBean(pageReqVO, SmsSignReqDTO.class);
        CommonResult<PageResultSms<SmsSignPageVo>> result =smsSignClient.pageList(page);
        // 注入酒店名称
        if(ObjectUtil.isEmpty(result.getData()) || ObjectUtil.isEmpty(result.getData().getList()) ) return result;
        CommonResult<MerchantRespDTO> Merchant = merchantApi.getMerchant(result.getData().getList().getFirst().getHcode());
        result.getData().getList().forEach(recordPageVo -> recordPageVo.setHname(Merchant.getData().getHname()));
        if(result.getCode() == 0){
            return result.setMsg("");
        }
        return result;
    }

}