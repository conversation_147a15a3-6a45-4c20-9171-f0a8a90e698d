package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsRechargeOrderDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;

/**
 * 短信充值订单 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsRechargeOrderService {

    /**
     * 创建短信充值订单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    SmsRechargeOrderRespVO createSmsRechargeOrder(@Valid SmsRechargeOrderSaveReqVO createReqVO);

    /**
     * 更新短信充值订单
     *
     * @param updateReqVO 更新信息
     */
    void updateSmsRechargeOrder(@Valid SmsRechargeOrderSaveReqVO updateReqVO);

    /**
     * 获得短信充值订单
     *
     */
    SmsRechargeOrderDO getSmsRechargeOrder(String gcode, String hcode, String orderNo);

    /**
     * 获得短信充值订单分页
     *
     * @param pageReqVO 分页查询
     * @return 短信充值订单分页
     */
    PageResult<SmsRechargeOrderDO> getSmsRechargeOrderPage(SmsRechargeOrderPageReqVO pageReqVO);

    /**
     * 累计充值短信数量
     */
    Integer addSmsRechargeOrder(String gcode, String hcode);

}