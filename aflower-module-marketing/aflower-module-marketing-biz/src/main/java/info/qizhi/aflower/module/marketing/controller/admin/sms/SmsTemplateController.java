package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.SmsParamRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplateRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplateSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsTemplateDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信模板")
@RestController
@RequestMapping("/marketing/sms-template")
@Validated
@Deprecated
public class SmsTemplateController {

    @Resource
    private SmsTemplateService smsTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建短信模板")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:create')")
    public CommonResult<Long> createSmsTemplate(@Valid @RequestBody SmsTemplateSaveReqVO createReqVO) {
        return success(smsTemplateService.createSmsTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新短信模板")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:update')")
    public CommonResult<Boolean> updateSmsTemplate(@Valid @RequestBody SmsTemplateSaveReqVO updateReqVO) {
        return smsTemplateService.updateSmsTemplate(updateReqVO);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除短信模板")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:delete')")
    public CommonResult<Boolean> deleteSmsTemplate(@RequestParam("id") Long id) {
        smsTemplateService.deleteSmsTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得短信模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:query')")
    public CommonResult<SmsTemplateRespVO> getSmsTemplate(@RequestParam("id") Long id) {
        SmsTemplateDO smsTemplate = smsTemplateService.getSmsTemplate(id);
        return success(BeanUtils.toBean(smsTemplate, SmsTemplateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得短信模板分页")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:query')")
    public CommonResult<PageResult<SmsTemplateRespVO>> getSmsTemplatePage(@Valid SmsTemplatePageReqVO pageReqVO) {
        PageResult<SmsTemplateDO> pageResult = smsTemplateService.getSmsTemplatePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SmsTemplateRespVO.class));
    }

    @GetMapping("/sms-param")
    @Operation(summary = "获得短信参数")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-template:query')")
    public CommonResult<List<SmsParamRespVO>> getSmsParam() {
        List<SmsParamRespVO> pageResult = smsTemplateService.getSmsParam();
        return success(pageResult);
    }



}