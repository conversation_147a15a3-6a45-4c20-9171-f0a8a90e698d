package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 短信模板 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsTemplateRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22392")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果是集团模板，当前字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码;如果是集团模板，当前字段为0")
    private String hcode;

    @Schema(description = "模板代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("模板代码")
    private String templateCode;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("模板名称")
    private String templateName;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "是否系统模板;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否系统模板;0:否 1：是")
    private String isSys;

    @Schema(description = "模板类型;0:普通模板 1:营销模板", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("模板类型;0:普通模板 1:营销模板")
    private String templateType;

    @Schema(description = "是否集团短信模板;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否集团短信模板;0:否 1：是")
    private String isG;

    @Schema(description = "状态;0:待审核 1:启用 2：停用 3:未通过", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0:待审核 1:启用 2：停用 3:未通过")
    private String state;

    @Schema(description = "未通过理由", example = "不喜欢")
    @ExcelProperty("未通过理由")
    private String reason;

    @Schema(description = "业务代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("业务代码")
    private String bizCode;

    @Schema(description = "业务类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("业务类型名称")
    private String bizName;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}