package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 短信默认模板新增/修改 Request VO")
@Data
public class SmsDefaultTemplateReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "201")
    private Long id;

    @ApiModelProperty(name = "isGroup", notes = "是否为集团模版,默认为否")
    private Boolean isGroup;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{templateName.notempty}")
    private String tplName;

    @NotEmpty(message = "模版内容不能为空！")
    @ApiModelProperty(name = "tplContent", notes = "模本内容")
    private String tplContent;

    @ApiModelProperty(name = "sendScene", notes = "发送场景")
    private String sendScene;

    @ApiModelProperty(name = "enabled", notes = "启用,停用模板,默认为true", example = "true")
    private Boolean enabled = true;

}