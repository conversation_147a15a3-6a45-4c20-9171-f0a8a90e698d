package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.template.vo.TemplateVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplateReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTemplateRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsDefaultTplEnabledReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsDefaultTemplateDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsDefaultTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 公共业务短信默认模板")
@RestController
@RequestMapping("/marketing/sms-default-template")
@Validated
public class SmsDefaultTemplateController {

    @Resource
    private SmsDefaultTemplateService smsDefaultTemplateService;


    @PostMapping("/create")
    @Operation(summary = "创建短信默认模板")
    // TODO 创建门店默认模板，公共模板
    // @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:create')")
    public CommonResult<Long> createSmsDefaultTemplate(@Valid @RequestBody SmsDefaultTemplateReqVO createReqVO) {
        return success(smsDefaultTemplateService.createSmsDefaultTemplate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新短信默认模板")
    // @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:update')")
    public CommonResult<Boolean> updateSmsDefaultTemplate(@Valid @RequestBody SmsDefaultTemplateReqVO updateReqVO) {
        return smsDefaultTemplateService.updateSmsDefaultTemplate(updateReqVO);
    }

    @PutMapping("/update-enabled")
    @Operation(summary = "更改公共门店模板启用")
    @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:update')")
    public CommonResult<Boolean> updateSMsTplEnable(@Valid @RequestBody SmsDefaultTplEnabledReqVO updateReqVo) {
        return success(smsDefaultTemplateService.updateSmsTemplateEnable(updateReqVo));
    }

    @PutMapping("/enabled")
    @Operation(summary = "启用/停用短信默认模板")
    // @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:update')")
    public CommonResult<Boolean> updateSmsDefaultTemplateEnabled(@Valid @RequestBody SmsDefaultTplEnabledReqVO enabledReqVO) {
        return smsDefaultTemplateService.enableDefaultTemplate(enabledReqVO);
    }

    @GetMapping("/get")
    @Operation(summary = "获得短信默认模板")
    @Parameter(name = "bizTypeCode", description = "业务类型代码", required = true)
    // @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:query')")
    // TODO 未使用接口
    public CommonResult<SmsDefaultTemplateRespVO> getSmsDefaultTemplate(@RequestParam("bizTypeCode") String bizTypeCode) {
        SmsDefaultTemplateDO smsDefaultTemplate = smsDefaultTemplateService.getSmsDefaultTemplate(bizTypeCode);
        return success(BeanUtils.toBean(smsDefaultTemplate, SmsDefaultTemplateRespVO.class));
    }

    @PostMapping("/page")
    @Operation(summary = "获得短信默认模板分页")
    @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:query')")
    public CommonResult<PageResultSms<TemplateVo>> getSmsDefaultTemplatePage(@Valid @RequestBody SmsDefaultTemplatePageReqVO pageReqVO) {
        return success(smsDefaultTemplateService.getSmsDefaultTemplatePage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出短信默认模板 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-default-template:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsDefaultTemplateExcel(@Valid SmsDefaultTemplatePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        //List<SmsDefaultTemplateDO> list = smsDefaultTemplateService.getSmsDefaultTemplatePage(pageReqVO).getList();
        // 导出 Excel
        //ExcelUtils.write(response, "短信默认模板.xls", "数据", SmsDefaultTemplateRespVO.class,
         //               BeanUtils.toBean(list, SmsDefaultTemplateRespVO.class));
    }

}