package info.qizhi.aflower.module.marketing.service.coupon;

import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.template.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateMerchantDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 优惠券模板 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponTemplateService {

    /**
     * 创建优惠券模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCouponTemplate(@Valid CouponTemplateSaveReqVO createReqVO);

    /**
     * 更新优惠券模板
     *
     * @param updateReqVO 更新信息
     */
    void updateCouponTemplate(@Valid CouponTemplateSaveReqVO updateReqVO);

    /**
     * 批量更新优惠券模板
     */
    void updateCouponTemplateBatch(@Valid List<CouponTemplateDO> updateReqVO);

    /**
     * 获得优惠券模板
     */
    CouponTemplateRespVO getCouponTemplate(String templateCode, String gcode, String hcode);

    /**
     * 获得优惠券模板分页
     *
     * @param pageReqVO 分页查询
     * @return 优惠券模板分页
     */
    PageResult<CouponTemplateRespVO> getCouponTemplatePage(CouponTemplatePageReqVO pageReqVO);
    /**
     * 获得优惠券模板列表
     *
     */
    List<CouponTemplateDO> getCouponTemplateList(String gcode, String hcode,String state);

    /**
     * 优惠卷模板失效
     */
    void loseEfficacy(@Valid CouponTemplateUpdateStatusReqVO reqVO);

    /**
     * 充值活动获取优惠券模板列表
     */
    List<CouponTemplateOfRechargeActivityRespVO> getCouponTemplateListByRecharge(String gcode, String hcode);

    // ==================== 子表（优惠券模板关联门店） ====================

    /**
     * 获得优惠券模板关联门店列表
     *
     * @param templateCode 模板代码
     * @return 优惠券模板关联门店列表
     */
    List<CouponTemplateMerchantDO> getCouponTemplateMerchantListByTemplateCode(String templateCode);

    /**
     * 获得集团下的优惠券模板关联门店列表
     *
     */

    List<CouponTemplateMerchantDO> getCouponTemplateMerchantListByGcode(String gcode);

    /**
     *
     * @param pageReqVO
     * @return
     */
    PageResult<CouponTemplateRespVO> getRoomCouponTemplatePage(@Valid RoomCouponTemplatePageReqVO pageReqVO);
}