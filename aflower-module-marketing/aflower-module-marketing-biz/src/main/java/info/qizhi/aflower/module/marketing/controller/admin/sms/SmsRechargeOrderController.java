package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsRechargeOrderDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsRechargeOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;
@Tag(name = "管理后台 - 短信充值订单")
@RestController
@RequestMapping("/marketing/sms-recharge-order")
@Validated
@Deprecated
public class SmsRechargeOrderController {

    @Resource
    private SmsRechargeOrderService smsRechargeOrderService;

    @PostMapping("/create")
    @Operation(summary = "创建短信充值订单")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:create')")
    public CommonResult<SmsRechargeOrderRespVO> createSmsRechargeOrder(@Valid @RequestBody SmsRechargeOrderSaveReqVO createReqVO) {
        return success(smsRechargeOrderService.createSmsRechargeOrder(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新短信充值订单")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:update')")
    public CommonResult<Boolean> updateSmsRechargeOrder(@Valid @RequestBody SmsRechargeOrderSaveReqVO updateReqVO) {
        smsRechargeOrderService.updateSmsRechargeOrder(updateReqVO);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得短信充值订单")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:query')")
    public CommonResult<SmsRechargeOrderRespVO> getSmsRechargeOrder(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode,@RequestParam("orderNo") String orderNo) {
        SmsRechargeOrderDO smsRechargeOrder = smsRechargeOrderService.getSmsRechargeOrder(gcode,hcode,orderNo);
        return success(BeanUtils.toBean(smsRechargeOrder, SmsRechargeOrderRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得短信充值订单分页")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:query')")
    public CommonResult<PageResult<SmsRechargeOrderRespVO>> getSmsRechargeOrderPage(@Valid SmsRechargeOrderPageReqVO pageReqVO) {
        PageResult<SmsRechargeOrderDO> pageResult = smsRechargeOrderService.getSmsRechargeOrderPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SmsRechargeOrderRespVO.class));
    }

    @GetMapping("/get-num")
    @Operation(summary = "累计充值短信数量")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:query')")
    public CommonResult<Integer> addSmsRechargeOrder(@RequestParam("gcode") String gcode,@RequestParam("hcode") String hcode) {
        return success(smsRechargeOrderService.addSmsRechargeOrder(gcode,hcode));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出短信充值订单 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-recharge-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsRechargeOrderExcel(@Valid SmsRechargeOrderPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SmsRechargeOrderDO> list = smsRechargeOrderService.getSmsRechargeOrderPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "短信充值订单.xls", "数据", SmsRechargeOrderRespVO.class,
                        BeanUtils.toBean(list, SmsRechargeOrderRespVO.class));
    }

}