package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.record;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 上午11:02
*/
@Schema(description = "管理后台 - 短信发送记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsRecordPageReqVO extends PageParam {



    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;如果是集团发送条数，该字段为0")
    private String hcode;

    @Schema(description = "发送类型，1：自动短信记录（验证码、通知）2：群发记录（营销类）")
    private Integer sendType;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "发送开始时间,格式yyyy-MM-dd")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "发送结束时间,格式yyyy-MM-dd")
    private Date endTime;

    @Schema(description = "发送内容/手机号")
    private String keyword;
}
