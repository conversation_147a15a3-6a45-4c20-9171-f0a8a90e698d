package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity;

import lombok.*;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 短信充值活动列表 Request VO")
@Data
public class SmsActivityListReqVO {

    @Schema(description = "活动代码")
    private String activityCode;

    @Schema(description = "活动名称", example = "王五")
    private String activityName;

    @Schema(description = "活动开始日期")
    private LocalDate dateStart;

    @Schema(description = "活动结束日期")
    private LocalDate dateEnd;

    @Schema(description = "状态;0：无效 1：有效")
    private String state;

}