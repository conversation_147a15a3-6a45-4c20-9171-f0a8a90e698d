package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import info.qizhi.aflower.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 短信余额分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsBalancePageReqVO extends PageParam {

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "短信余额")
    private Long balance;

    @Schema(description = "已发送数")
    private Long sendNum;

    @Schema(description = "累计发送条数")
    private Long totalSendNum;

    @Schema(description = "预警条数")
    private Integer warningNum;

    @Schema(description = "预警接收电话;电话，多个用‘,’隔开")
    private String phone;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}