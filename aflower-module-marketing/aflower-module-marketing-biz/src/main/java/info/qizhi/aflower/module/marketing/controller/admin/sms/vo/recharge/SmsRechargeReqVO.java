package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 短信充值 Request VO")
@Data
public class SmsRechargeReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1969")
    private Long id;

    @Schema(description = "集团代码")
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "充值门店代码;如果为0，表示是集团充值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "充值门店代码;如果为0，表示是集团充值不能为空")
    private String hcode;

    @Schema(description = "活动代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{activityCode.notempty}")
    private String activeCode;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "{activityName.notempty}")
    private String activeName;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{feeAmount.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "短信条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{smsNum.notnull}")
    private Integer smsNum;

    @Schema(description = "赠送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{giveNum.notnull}")
    private Integer giveNum;

    @Schema(description = "状态;0:未支付 1:已支付 2:已过期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{state.notempty}")
    private String state;

}
