package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/26 下午4:50
*/
@Data
@ApiModel("添加签名")
public class SmsSignUpdateReqVO {
    @NotNull(message = "{id.notnull}")
    @ApiModelProperty(name = "id", notes = "签名id")
    private Integer id;

    @NotEmpty(message = "{sign.notempty}！")
    @ApiModelProperty(name = "sign", notes = "签名内容")
    private String sign;

    @NotEmpty(message = "{website.notempty}")
    @ApiModelProperty(name = "website", notes = "签名对应的业务网址")
    private String website;

    @NotNull(message = "{website.notempty}")
    @ApiModelProperty(name = "proverType", notes = "根据签名内容需要提供的证明文件类型，其中：\n" +
            "1：公司营业执照(签名是公司名称)；\n" +
            "2：应用商店APP管理后台全屏截图(签名是APP名称)；\n" +
            "3：ICP备案截图(签名是网站名称)\n" +
            "4：微信公众平台管理界面全屏截图(签名是公众号/小程序名称)\n" +
            "6：商标注册证书/商标软著权证明(签名是商标名称)")
    @Range(min = 1, max = 6, message = "证明文件类型值错误！")
    private Integer proverType;

    @NotEmpty(message = "{licenseUrl.notempty}")
    @ApiModelProperty(name = "licenseUrl", notes = "签名对应的营业执照或其他企业资质的图片文件 URL")
    private String licenseUrl;
}
