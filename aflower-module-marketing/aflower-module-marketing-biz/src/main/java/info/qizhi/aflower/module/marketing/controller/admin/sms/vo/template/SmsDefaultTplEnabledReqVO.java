package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import info.qizhi.aflower.module.marketing.api.sms.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 下午6:25
*/
@Data
@ApiModel("启用停用模板")
public class SmsDefaultTplEnabledReqVO extends BaseEntity {

    @NotNull(message = "{id.notnull}")
    @ApiModelProperty(name = "id", notes = "模版id")
    private Integer id;

    @ApiModelProperty(name = "enabled", notes = "启用,停用模板,默认为true", example = "true")
    private Boolean enabled = true;
}
