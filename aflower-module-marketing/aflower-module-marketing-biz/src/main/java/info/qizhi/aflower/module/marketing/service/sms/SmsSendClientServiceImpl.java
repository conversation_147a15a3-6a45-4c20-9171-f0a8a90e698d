package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import info.qizhi.aflower.framework.common.enums.IdTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendClientReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send.SmsSendClientRespVO;
import info.qizhi.aflower.module.marketing.convert.sms.SmsSendMemberConvert;
import info.qizhi.aflower.module.member.api.member.MemberApi;
import info.qizhi.aflower.module.member.api.member.dto.MemberAndStoreCardRespDTO;
import info.qizhi.aflower.module.pms.api.booking.BookingApi;
import info.qizhi.aflower.module.pms.api.booking.dto.BookListReqDTO;
import info.qizhi.aflower.module.pms.api.booking.dto.BookTodayRespDTO;
import info.qizhi.aflower.module.pms.api.order.OrderApi;
import info.qizhi.aflower.module.pms.api.order.dto.OrderReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderRespDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderHisListReqDTO;
import info.qizhi.aflower.module.pms.api.order.dto.OrderHisRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.CUSTOMER_LABEL_NOT_NULL;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.MEMBER_LEVEL_NOT_NULL;


/**
 * 短信发送-发送对象 Service 实现类
 */
@Service
@Validated
public class SmsSendClientServiceImpl implements SmsSendClientService {

    @Resource
    private MemberApi memberApi;

    @Resource
    private OrderApi orderApi;

    @Resource
    private BookingApi bookingApi;

    /**
     * 发送对象
     *
     */
    @Override
    public SmsSendClientRespVO sendMember(SmsSendClientReqVO reqVO) {
        SmsSendClientRespVO smsSendClientRespVO = new SmsSendClientRespVO();
        // 判断选择对象
        if (NumberEnum.ZERO.getNumber().equals(reqVO.getChoose())){
            // 判断会员级别
            if (StrUtil.isEmpty(reqVO.getMtCode())){
                throw exception(MEMBER_LEVEL_NOT_NULL);
            }
            // 处理会员级别条件
            return smsSendClientRespVO.setMemberByMtCode(member(reqVO));
        }else if (NumberEnum.ONE.getNumber().equals(reqVO.getChoose())){
            // 判断客户标签
            if (StrUtil.isBlank(reqVO.getCustomerGroup())){
                throw exception(CUSTOMER_LABEL_NOT_NULL);
            }
            // 处理客户标签条件
            return customerLabel(reqVO);
        }
        return new SmsSendClientRespVO();
    }

    private SmsSendClientRespVO customerLabel(SmsSendClientReqVO reqVO) {
        SmsSendClientRespVO smsSendClientRespVO = new SmsSendClientRespVO();
        // 当前日期
        LocalDate now = LocalDate.now();
        String customerGroup = reqVO.getCustomerGroup();
        return switch (NumberEnum.valueOf(customerGroup)) {
            case ZERO -> smsSendClientRespVO.setGetTodayBookList(getBookings(reqVO));
            case ONE -> smsSendClientRespVO.setGetOrderHistory(getOrderHistory(reqVO));
            case TWO -> smsSendClientRespVO.setGetCurrentOrders(getCurrentOrders(reqVO));
            case THREE -> smsSendClientRespVO.setGetCurrentOrdersWithBirthday(getCurrentOrdersWithBirthday(reqVO, now));
            default -> null;
        };
    }

    private List<BookTodayRespDTO> getBookings(SmsSendClientReqVO reqVO) {
        List<BookTodayRespDTO> bookList = bookingApi.getTodayBookList(
                new BookListReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
        ).getData();
        return CollUtil.isEmpty(bookList) ? CollUtil.newArrayList() : bookList;
    }

    private List<OrderHisRespDTO> getOrderHistory(SmsSendClientReqVO reqVO) {
        List<OrderHisRespDTO> orderHisList = orderApi.getOrderHisByCheckoutState(
                new OrderHisListReqDTO().setSex(reqVO.getSex()).setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
        ).getData();
        return CollUtil.isEmpty(orderHisList) ? CollUtil.newArrayList() : orderHisList;
    }

    private List<OrderRespDTO> getCurrentOrders(SmsSendClientReqVO reqVO) {
        List<OrderRespDTO> orderList = orderApi.getOrderListForSms(
                new OrderReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())
        ).getData();
        return CollUtil.isEmpty(orderList) ? CollUtil.newArrayList() : orderList;
    }

    private List<OrderRespDTO> getCurrentOrdersWithBirthday(SmsSendClientReqVO reqVO, LocalDate now) {
        List<OrderRespDTO> orderList = orderApi.getOrderListForSms(new OrderReqDTO().setGcode(reqVO.getGcode()).setHcode(reqVO.getHcode())).getData();
        if (CollUtil.isEmpty(orderList)){
            return CollUtil.newArrayList();
        }
        return orderList.stream()
                .filter(order -> isBirthdayMatch(order, now))
                .collect(Collectors.toList());
    }

    // 判断是否是生日
    private boolean isBirthdayMatch(OrderRespDTO order, LocalDate now) {
        if (IdTypeEnum.IDCERT.getCode().equals(order.getIdType()) ||
                IdTypeEnum.DRIVINGLICENCE.getCode().equals(order.getIdType()) ||
                IdTypeEnum.HOUSEHOLDREGISTER.getCode().equals(order.getIdType())) {
            String idCard = order.getIdNo();
            String birthday = null;
            if (idCard.length() == 18) {
                String year = idCard.substring(6).substring(0, 4);// 得到年份
                String month = idCard.substring(10).substring(0, 2);// 得到月份
                String day = idCard.substring(12).substring(0, 2);// 得到日
                birthday = year + "-" + month + "-" + day;
            } else if (idCard.length() == 15) {
                String year = "19" + idCard.substring(6, 8);// 年份
                String month = idCard.substring(8, 10);// 月份
                String day = idCard.substring(10, 12);// 得到日
                birthday = year + "-" + month + "-" + day;
            }
            return birthday != null && now.getMonthValue() == LocalDate.parse(birthday).getMonthValue() && now.getDayOfMonth() == LocalDate.parse(birthday).getDayOfMonth();
        }
        return false;
    }

    private  List<MemberAndStoreCardRespDTO> member(SmsSendClientReqVO reqVO) {
        return memberApi.getMemberList(SmsSendMemberConvert.INSTANCE.smsSendMemberConvert(reqVO)).getData();
    }
}
