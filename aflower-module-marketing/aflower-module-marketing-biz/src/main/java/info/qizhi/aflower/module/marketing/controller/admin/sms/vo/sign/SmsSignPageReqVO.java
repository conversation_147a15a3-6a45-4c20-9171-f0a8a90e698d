package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign;

import info.qizhi.aflower.module.marketing.api.sms.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Schema(description = "管理后台 - 短信签名分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsSignPageReqVO extends BaseEntity {

    @ApiModelProperty(name = "keyword", notes = "id/签名内容")
    private String keyword;

    @ApiModelProperty(name = "auditState", notes = "签名状态:10待审核，20审核成功，30被拒绝")
    private Integer auditState;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "startTime", notes = "申请开始时间,格式yyyy-MM-dd")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(name = "endTime", notes = "申请结束时间,格式yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(name = "pageNumber", notes = "页码，默认=1")
    private Integer pageNum = 1;

    @ApiModelProperty(name = "pageSize", notes = "页大小，默认=10")
    private Integer pageSize = 10;

}