package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import info.qizhi.aflower.framework.common.pojo.PageParam;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/*
    <AUTHOR>
    @description 
    @create 2024 11 2024/11/26 上午11:39
*/
@Schema(description = "管理后台 - 短信默认模板分页 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsPublicTplResVO extends PageParam {

    @NotNull(message = "{id.notnull}")
    @ApiModelProperty(name = "id", notes = "模版id")
    private Integer id;

    @ApiModelProperty(name = "enabled", notes = "启用,停用模板,默认为false", example = "true")
    private Boolean enabled = false;

    @Schema(description = "集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果是集团发送条数，该字段为0")
    private String hcode;
}
