package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.util.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

import static info.qizhi.aflower.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

@Schema(description = "管理后台 - 短信发送统计 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsSendStatRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6176")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "门店代码;如果是集团发送条数，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("门店代码;如果是集团发送条数，该字段为0")
    private String hcode;

    @Schema(description = "发送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送条数")
    private Integer sendNum;

    @Schema(description = "发送人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送人数")
    private Integer sendPersons;

    @Schema(description = "发送日期;统计每天的发送情况，然后保存", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("发送日期;统计每天的发送情况，然后保存")
    @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate sendDate;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}