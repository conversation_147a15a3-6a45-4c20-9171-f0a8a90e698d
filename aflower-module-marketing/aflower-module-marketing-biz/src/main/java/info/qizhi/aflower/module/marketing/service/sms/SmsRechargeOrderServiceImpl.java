package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderPageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderSaveReqVO;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsRechargeOrderDO;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;

import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsRechargeOrderMapper;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;

/**
 * 短信充值订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsRechargeOrderServiceImpl implements SmsRechargeOrderService {

    @Resource
    private SmsRechargeOrderMapper smsRechargeOrderMapper;

    @Override
    public SmsRechargeOrderRespVO createSmsRechargeOrder(SmsRechargeOrderSaveReqVO createReqVO) {
        // 插入
        SmsRechargeOrderDO smsRechargeOrder = BeanUtils.toBean(createReqVO, SmsRechargeOrderDO.class);
        smsRechargeOrder.setOrderNo(IdUtil.getSnowflakeNextIdStr());
        smsRechargeOrderMapper.insert(smsRechargeOrder);

        // 返回
        return BeanUtils.toBean(smsRechargeOrder, SmsRechargeOrderRespVO.class);
    }

    @Override
    public void updateSmsRechargeOrder(SmsRechargeOrderSaveReqVO updateReqVO) {
        // 校验存在
        SmsRechargeOrderDO smsRechargeOrderDO = smsRechargeOrderMapper.selectOne(SmsRechargeOrderDO::getGcode,updateReqVO.getGcode(),SmsRechargeOrderDO::getHcode,updateReqVO.getHcode(),SmsRechargeOrderDO::getOrderNo, updateReqVO.getOrderNo());
        if (smsRechargeOrderDO == null) {
            throw exception(SMS_RECHARGE_ORDER_NOT_EXISTS);
        }
        // 更新
        SmsRechargeOrderDO updateObj = BeanUtils.toBean(updateReqVO, SmsRechargeOrderDO.class);
        updateObj.setId(smsRechargeOrderDO.getId());
        smsRechargeOrderMapper.updateById(updateObj);
    }

    @Override
    public SmsRechargeOrderDO getSmsRechargeOrder(String gcode, String hcode, String orderNo) {
        return smsRechargeOrderMapper.selectOne(SmsRechargeOrderDO::getGcode,gcode,SmsRechargeOrderDO::getHcode,hcode,SmsRechargeOrderDO::getOrderNo,orderNo);
    }

    @Override
    public PageResult<SmsRechargeOrderDO> getSmsRechargeOrderPage(SmsRechargeOrderPageReqVO pageReqVO) {
        return smsRechargeOrderMapper.selectPage(pageReqVO);
    }

    /**
     * 累计充值短信数量
     *
     * @param gcode
     * @param hcode
     */
    @Override
    public Integer addSmsRechargeOrder(String gcode, String hcode) {
        List<SmsRechargeOrderDO> smsRechargeOrderDOS = smsRechargeOrderMapper.selectList(SmsRechargeOrderDO::getGcode, gcode, SmsRechargeOrderDO::getHcode, hcode);
        int smsNum = smsRechargeOrderDOS.stream().mapToInt(SmsRechargeOrderDO::getSmsNum).sum();
        int giveNum = smsRechargeOrderDOS.stream().mapToInt(SmsRechargeOrderDO::getGiveNum).sum();
        int sum = smsNum + giveNum;
        return sum;
    }

}