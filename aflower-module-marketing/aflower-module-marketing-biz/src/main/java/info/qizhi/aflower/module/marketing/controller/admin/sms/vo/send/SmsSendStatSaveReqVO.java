package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.send;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 短信发送统计新增/修改 Request VO")
@Data
public class SmsSendStatSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6176")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;如果是集团发送条数，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "发送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendNum.notnull}")
    private Integer sendNum;

    @Schema(description = "发送人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendPersons.notnull}")
    private Integer sendPersons;

    @Schema(description = "发送日期;统计每天的发送情况，然后保存", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendDate.notnull}")
    private LocalDate sendDate;

}