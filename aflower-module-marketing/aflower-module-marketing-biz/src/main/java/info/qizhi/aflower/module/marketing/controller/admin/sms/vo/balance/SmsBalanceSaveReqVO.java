package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 短信余额新增/修改 Request VO")
@Data
@Builder
public class SmsBalanceSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19724")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "已发送数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendNum.notnull}")
    private Long sendNum;

    @Schema(description = "累计发送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{totalSendNum.notnull}")
    private Long totalSendNum;

    @Schema(description = "预警条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    private Integer warningNum;

    @Schema(description = "预警接收电话;电话，多个用‘,'隔开")
    private String phone;

}