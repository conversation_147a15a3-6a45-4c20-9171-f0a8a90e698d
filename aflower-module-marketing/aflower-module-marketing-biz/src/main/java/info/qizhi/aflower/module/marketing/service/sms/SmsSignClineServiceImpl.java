package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.module.marketing.api.sms.PageResultSms;
import info.qizhi.aflower.module.marketing.api.sms.sign.SmsSignClient;
import info.qizhi.aflower.module.marketing.api.sms.sign.vo.SmsSignPageVo;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.sign.SmsSignReqVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

/*
    <AUTHOR>
    @description 
    @create 2024 10 2024/10/24 下午8:15
*/
@Service
public class SmsSignClineServiceImpl implements SmsSignClineService{
    @Resource
    private SmsSignClient smsSignClient;
    @Override
    public CommonResult<PageResultSms<SmsSignPageVo>> enabledSign(SmsSignReqVO enabledReqVO) {
        return null;
    }
}
