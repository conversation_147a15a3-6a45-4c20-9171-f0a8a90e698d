package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.log;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 短信发送内容日志新增/修改 Request VO")
@Data
public class SmsMsgLogSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20532")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;如果为集团发送，该字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{hcode.notempty}")
    private String hcode;

    @Schema(description = "批次号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{batchNo.notempty}")
    private String batchNo;

    @Schema(description = "模板代码")
    private String templateCode;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{content.notempty}")
    private String content;

    @Schema(description = "发送人数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendPersons.notnull}")
    private Integer sendPersons;

    @Schema(description = "发送短信数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{sendNum.notnull}")
    private Integer sendNum;

    @Schema(description = "调用者", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{invoker.notempty}")
    private String invoker;

    @Schema(description = "接收手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{mobile.notempty}")
    private String phones;

    @Schema(description = "发送方式;0:系统业务触发 1：人工群发", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{sendMode.notempty}")
    private String sendMode;

    @Schema(description = "发送时间")
    private LocalDateTime sendTime;

}