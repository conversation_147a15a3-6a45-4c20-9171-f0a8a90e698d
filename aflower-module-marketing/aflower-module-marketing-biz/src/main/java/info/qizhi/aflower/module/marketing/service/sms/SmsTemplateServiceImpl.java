package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.enums.SmsConfigParamEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.template.SmsDefaultTemplateClient;
import info.qizhi.aflower.module.marketing.api.sms.template.dto.SmsDefaultTemplateSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.SmsParamRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplateSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsTemplateDO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsTemplateMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.SMS_TEMPLATE_NOT_EXISTS;

/**
 * 短信模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsTemplateServiceImpl implements SmsTemplateService {

    @Resource
    private SmsTemplateMapper smsTemplateMapper;
    @Resource
    private SmsDefaultTemplateClient smsDefaultTemplateClient;

    @Override
    public Long createSmsTemplate(SmsTemplateSaveReqVO createReqVO) {
        // 插入
        SmsTemplateDO smsTemplate = BeanUtils.toBean(createReqVO, SmsTemplateDO.class);
        smsTemplateMapper.insert(smsTemplate);
        // 返回
        return smsTemplate.getId();
    }

    @Override
    public Boolean createSmsTemplates(List<SmsDefaultTemplateSaveReqDTO> smsDefaultTemplateSaveReqDTOS) {
        List<SmsTemplateDO> smsTemplateDOS = BeanUtils.toBean(smsDefaultTemplateSaveReqDTOS, SmsTemplateDO.class);
        return smsTemplateMapper.insertBatch(smsTemplateDOS);
    }

    @Override
    public CommonResult<Boolean> updateSmsTemplate(SmsTemplateSaveReqVO updateReqVO) {
        SmsDefaultTemplateSaveReqDTO updateReqDTO = BeanUtils.toBean(updateReqVO, SmsDefaultTemplateSaveReqDTO.class);
        return smsDefaultTemplateClient.updateSmsTemplate(updateReqDTO);
    }

    @Override
    public void deleteSmsTemplate(Long id) {
        // 校验存在
        validateSmsTemplateExists(id);
        // 删除
        smsTemplateMapper.deleteById(id);
    }

    private void validateSmsTemplateExists(Long id) {
        if (smsTemplateMapper.selectById(id) == null) {
            throw exception(SMS_TEMPLATE_NOT_EXISTS);
        }
    }

    @Override
    public SmsTemplateDO getSmsTemplate(Long id) {
        return smsTemplateMapper.selectById(id);
    }

    @Override
    public PageResult<SmsTemplateDO> getSmsTemplatePage(SmsTemplatePageReqVO pageReqVO) {
        return smsTemplateMapper.selectPage(pageReqVO);
    }

    /**
     * 获得短信参数列表
     */
    @Override
    public List<SmsParamRespVO> getSmsParam() {
        List<SmsParamRespVO> list = Arrays.stream(SmsConfigParamEnum.values())
                .map(enumValue -> {
                    SmsParamRespVO vo = new SmsParamRespVO();
                    vo.setParamName(enumValue.getCode());
                    vo.setParamValue(enumValue.getName());
                    return vo;
                })
                .collect(Collectors.toList());
        return list;
    }

}