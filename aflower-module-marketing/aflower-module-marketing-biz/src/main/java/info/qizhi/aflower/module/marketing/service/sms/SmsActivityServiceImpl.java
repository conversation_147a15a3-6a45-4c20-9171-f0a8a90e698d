package info.qizhi.aflower.module.marketing.service.sms;

import cn.hutool.core.util.IdUtil;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityListReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivitySaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsActivityDO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsActivityMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.SMS_ACTIVITY_NOT_EXISTS;

/**
 * 短信充值活动 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsActivityServiceImpl implements SmsActivityService {

    @Resource
    private SmsActivityMapper smsActivityMapper;

    @Override
    public Long createSmsActivity(SmsActivitySaveReqVO createReqVO) {
        // 插入
        SmsActivityDO smsActivity = BeanUtils.toBean(createReqVO, SmsActivityDO.class);
        smsActivity.setActivityCode(IdUtil.getSnowflakeNextIdStr());
        smsActivity.setState(NumberEnum.ONE.getNumber());
        smsActivityMapper.insert(smsActivity);
        // 返回
        return smsActivity.getId();
    }

    @Override
    public void updateSmsActivity(SmsActivitySaveReqVO updateReqVO) {
        // 校验存在
        validateSmsActivityExists(updateReqVO.getId());
        // 更新
        SmsActivityDO updateObj = BeanUtils.toBean(updateReqVO, SmsActivityDO.class);
        smsActivityMapper.updateById(updateObj);
    }

    @Override
    public void deleteSmsActivity(Long id) {
        // 校验存在
        validateSmsActivityExists(id);
        // 删除
        smsActivityMapper.deleteById(id);
    }

    private void validateSmsActivityExists(Long id) {
        if (smsActivityMapper.selectById(id) == null) {
            throw exception(SMS_ACTIVITY_NOT_EXISTS);
        }
    }

    @Override
    public SmsActivityRespVO getSmsActivity(String activeCode) {
        SmsActivityDO smsActivityDO = smsActivityMapper.selectOne(SmsActivityDO::getActivityCode, activeCode);
        SmsActivityRespVO bean = BeanUtils.toBean(smsActivityDO, SmsActivityRespVO.class);
        bean.setProductName("短信充值");
        bean.setPurchaseContent("短信包"+":"+bean.getSmsNum()+"条"+","+"赠送"+bean.getGiveNum()+"条");
        bean.setTotalNum(bean.getSmsNum()+bean.getGiveNum());
        return bean;
    }

    @Override
    public List<SmsActivityDO> getSmsActivityList(SmsActivityListReqVO reqVO) {
        return smsActivityMapper.selectList(reqVO);
    }

}