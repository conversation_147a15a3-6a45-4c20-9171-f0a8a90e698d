package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.enums.PayAccountTypeEnum;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.api.sms.balance.dto.SmsBalanceSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance.SmsBalanceSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeOrderSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsBalanceDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsRechargeOrderDO;
import info.qizhi.aflower.module.marketing.dal.mysql.sms.SmsBalanceMapper;
import info.qizhi.aflower.module.pay.api.base.PayBaseApi;
import info.qizhi.aflower.module.pay.api.base.dto.ActiveScanReqDTO;
import info.qizhi.aflower.module.pay.api.base.dto.ActiveScanRespDTO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;
import static info.qizhi.aflower.module.pms.enums.ErrorCodeConstants.PAY_FAILURE;

/**
 * 短信余额 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SmsBalanceServiceImpl implements SmsBalanceService {

//    public static final String ONLINEPAY = "online_pay";

    @Resource
    private SmsBalanceMapper smsBalanceMapper;
    @Resource
    private SmsDefaultTemplateService smsDefaultTemplateService;
    @Resource
    private SmsActivityService smsActivityService;
    @Resource
    private SmsRechargeOrderService smsRechargeOrderService;
    @Resource
    private PayBaseApi payBaseApi;

    @Override
    public Long createSmsBalance(SmsBalanceSaveReqDTO smsBalanceSaveReqDTO) {
        // 插入
        SmsBalanceDO smsBalance = BeanUtils.toBean(smsBalanceSaveReqDTO, SmsBalanceDO.class);
        smsBalance.setBalance(Long.valueOf(NumberEnum.ZERO.getNumber()));
        smsBalanceMapper.insert(smsBalance);
        // 返回
        return smsBalance.getId();
    }

    @Override
    public void updateSmsBalance(SmsBalanceSaveReqVO updateReqVO) {
        // 校验存在
        SmsBalanceDO smsBalanceDO = smsBalanceMapper.selectOne(SmsBalanceDO::getGcode, updateReqVO.getGcode());
        if (smsBalanceDO == null) {
            throw exception(SMS_BALANCE_NOT_EXISTS);
        }
        // 更新
        SmsBalanceDO updateObj = BeanUtils.toBean(updateReqVO, SmsBalanceDO.class);
        updateObj.setId(smsBalanceDO.getId());
        smsBalanceMapper.updateById(updateObj);
    }

    @Override
    public SmsBalanceDO getSmsBalance(String gcode) {
        return smsBalanceMapper.selectOne(SmsBalanceDO::getGcode, gcode);
    }

//    /**
//     * 短信预警
//     */
/*
    @Override
    public void smsWarning(String gcode) {
        // 判断短信条数是否达到预警值
        SmsBalanceDO smsBalanceDO = smsBalanceMapper.selectOne(SmsBalanceDO::getGcode,gcode);
        if (smsBalanceDO.getBalance() < smsBalanceDO.getWarningNum()){
            // 获取发送手机号，对字符串“，”进行分割
            String[] phone = smsBalanceDO.getPhone().split(",");
            List<String> phones = Arrays.stream(phone).toList();
            // 获取预警模板
            SmsDefaultTemplateDO smsDefaultTemplate = smsDefaultTemplateService.getSmsDefaultTemplate(SmsBizTypeEnum.SMS_WARNING.getCode());
            String content = smsDefaultTemplate.getContent();
            // TODO 发送短信
        }
    }
*/

    /**
     * TODO 短信充值-确认订单
     */
//    @Override
    public String smsRecharge(SmsRechargeReqVO reqVO) {
        // 判断充值活动是否有效
        SmsActivityRespVO smsActivity = smsActivityService.getSmsActivity(reqVO.getActiveCode());
        if (smsActivity == null || NumberEnum.ZERO.getNumber().equals(smsActivity.getState())) {
            throw exception(SMS_ACTIVITY_NOT_EXISTS);
        }
        LocalDate now = LocalDate.now();
        if (now.isBefore(smsActivity.getDateStart()) || now.isAfter(smsActivity.getDateEnd())) {
            throw exception(SMS_ACTIVITY_NOT_EXPIRED);
        }
        // 生成充值订单 ，状态为未支付
        SmsRechargeOrderRespVO smsRechargeOrder = smsRechargeOrderService.createSmsRechargeOrder(BeanUtils.toBean(reqVO, SmsRechargeOrderSaveReqVO.class).setPayMode("ONLINEPAY"));
        // 支付接口（调用）提交订单
        CommonResult<ActiveScanRespDTO> res = payBaseApi.activeScan(new ActiveScanReqDTO()
                .setHcode(reqVO.getHcode()).setAccountType(PayAccountTypeEnum.WECHAT.getCode()).setPlatform("lakala")
                .setNotifyUrl("http://yiduohua-api.natapp1.cc/marketing/sms-balance/callback")
                .setPrice(reqVO.getFee()).setSubject(reqVO.getActiveName()));
        if (res.getCode() == 0) {
            return res.getData().getQrCode();
        } else {
            throw exception(PAY_FAILURE, res.getMsg());
        }
    }

    /**
     * TODO 短信充值-获取支付回调参数
     *
     */
    @Override
    public void smsRechargeCallback(String gcode, String hcode, String orderNo) {
        // 判断充值订单是否存在
        SmsRechargeOrderDO smsRechargeOrder = smsRechargeOrderService.getSmsRechargeOrder(gcode, hcode, orderNo);
        if (smsRechargeOrder == null) {
            throw exception(SMS_RECHARGE_ORDER_NOT_EXISTS);
        }
        // 支付成功回调，更新短信条数，修改订单状态为已支付 修改支付方式
        smsRechargeOrderService.updateSmsRechargeOrder(BeanUtils.toBean(smsRechargeOrder, SmsRechargeOrderSaveReqVO.class).setPayMode("ONLINEPAY").setState(NumberEnum.ONE.getNumber()));
        SmsBalanceDO smsBalanceDO = smsBalanceMapper.selectOne(SmsBalanceDO::getGcode, gcode);
        smsBalanceDO.setBalance(smsBalanceDO.getBalance() + smsRechargeOrder.getSmsNum()+ smsRechargeOrder.getGiveNum());
        smsBalanceMapper.updateById(smsBalanceDO);
        // 支付异步回调，判断支付结果  修改订单状态为已过期或不做修改
        smsRechargeOrderService.updateSmsRechargeOrder(BeanUtils.toBean(smsRechargeOrder, SmsRechargeOrderSaveReqVO.class).setState(NumberEnum.TWO.getNumber()));

    }


}