package info.qizhi.aflower.module.marketing.service.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import info.qizhi.aflower.framework.common.enums.CouponEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.mybatis.core.util.MyBatisUtils;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.template.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponTemplateMerchantDO;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponTemplateMapper;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponTemplateMerchantMapper;
import info.qizhi.aflower.module.system.api.merchant.MerchantApi;
import info.qizhi.aflower.module.system.api.merchant.dto.MerchantRespDTO;
import jakarta.annotation.Resource;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.COUPON_DATE_NOT_NULL;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.COUPON_TEMPLATE_NOT_EXISTS;

/**
 * 优惠券模板 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CouponTemplateServiceImpl implements CouponTemplateService {

    public static final long ZERO = 0L;
    @Resource
    private CouponTemplateMapper couponTemplateMapper;

    @Resource
    private CouponTemplateMerchantMapper couponTemplateMerchantMapper;

    @Resource
    private MerchantApi merchantApi;

    @Resource
    @Lazy
    private CouponService couponService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCouponTemplate(CouponTemplateSaveReqVO createReqVO) {
        // 校验有效日期
        if (NumberEnum.ZERO.getNumber().equals(createReqVO.getEffeType())) {
            if (createReqVO.getEffeBeginDate() == null || createReqVO.getEffeEndDate() == null) {
                throw exception(COUPON_DATE_NOT_NULL);
            }
        }
        if (NumberEnum.TWO.getNumber().equals(createReqVO.getEffeType())) {
            if (createReqVO.getNstart() == null || createReqVO.getXday() == null) {
                throw exception(COUPON_DATE_NOT_NULL);
            }
            createReqVO.setEffeBeginDate(LocalDate.now().plusYears(1000));
            createReqVO.setEffeEndDate(LocalDate.now().plusYears(1000));
        }
        if (NumberEnum.ONE.getNumber().equals(createReqVO.getEffeType())) {
            createReqVO.setEffeBeginDate(LocalDate.now());
            createReqVO.setEffeEndDate(LocalDate.now().plusYears(1000));
        }
        // 插入
        CouponTemplateDO couponTemplate = BeanUtils.toBean(createReqVO, CouponTemplateDO.class);
        couponTemplate.setNum(NumberEnum.ZERO.getNumberInt());
        couponTemplate.setUsedNum(NumberEnum.ZERO.getNumberInt());
        couponTemplate.setSendNum(NumberEnum.ZERO.getNumberInt());
        couponTemplate.setState(NumberEnum.ONE.getNumber());
        couponTemplate.setIsG(NumberEnum.ZERO.getNumber());
        couponTemplate.setTemplateCode(IdUtil.getSnowflakeNextIdStr());
        if (CollUtil.isEmpty(createReqVO.getBizType())) {
            couponTemplate.setBizType(CollUtil.newArrayList("0"));
        }
        couponTemplateMapper.insert(couponTemplate);
        // 插入子表
        if (CollUtil.isNotEmpty(createReqVO.getCouponTemplateMerchants())) {
            List<CouponTemplateMerchantDO> couponTemplateMerchants = CollUtil.newArrayList();
            createReqVO.getCouponTemplateMerchants().forEach(merchantCode -> {
                CouponTemplateMerchantDO arSetMerchant = CouponTemplateMerchantDO.builder().templateCode(couponTemplate.getTemplateCode()).hcode(merchantCode).gcode(createReqVO.getGcode()).build();
                couponTemplateMerchants.add(arSetMerchant);
            });
            createCouponTemplateMerchantList(couponTemplate.getTemplateCode(), couponTemplateMerchants);
        }
        // 返回
        return couponTemplate.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCouponTemplate(CouponTemplateSaveReqVO updateReqVO) {
        // 校验存在
        CouponTemplateDO couponTemplateDO = couponTemplateMapper.selectOne(
                CouponTemplateDO::getGcode, updateReqVO.getGcode(),
                CouponTemplateDO::getTemplateCode, updateReqVO.getTemplateCode(),
                CouponTemplateDO::getHcode, updateReqVO.getHcode()
        );
        if (couponTemplateDO == null) {
            throw exception(COUPON_TEMPLATE_NOT_EXISTS);
        }
        // 更新
        CouponTemplateDO updateObj = BeanUtils.toBean(updateReqVO, CouponTemplateDO.class);
        updateObj.setId(couponTemplateDO.getId());
        couponTemplateMapper.updateById(updateObj);
        // 更新子表
        if (CollUtil.isNotEmpty(updateReqVO.getCouponTemplateMerchants())) {
            List<CouponTemplateMerchantDO> couponTemplateMerchants = CollUtil.newArrayList();
            updateReqVO.getCouponTemplateMerchants().forEach(merchantCode -> {
                CouponTemplateMerchantDO arSetMerchant = CouponTemplateMerchantDO.builder().templateCode(updateReqVO.getTemplateCode()).hcode(merchantCode).gcode(updateReqVO.getGcode()).build();
                couponTemplateMerchants.add(arSetMerchant);
            });
            updateCouponTemplateMerchantList(updateReqVO.getTemplateCode(), couponTemplateMerchants);
        }

    }

    /**
     * 批量更新优惠券模板
     */
    @Override
    public void updateCouponTemplateBatch(List<CouponTemplateDO> updateReqVO) {
        couponTemplateMapper.updateBatch(updateReqVO);
    }

    @Override
    public CouponTemplateRespVO getCouponTemplate(String templateCode, String gcode, String hcode) {
        CouponTemplateDO couponTemplateDO = couponTemplateMapper.selectOne(
                CouponTemplateDO::getGcode, gcode,
                CouponTemplateDO::getHcode, hcode,
                CouponTemplateDO::getTemplateCode, templateCode
        );
        if (couponTemplateDO == null) {
            return new CouponTemplateRespVO();
        }
        CouponTemplateRespVO bean = BeanUtils.toBean(couponTemplateDO, CouponTemplateRespVO.class);
        // 获取活动下所有酒店
        List<CouponTemplateMerchantDO> couponTemplateMerchants = getCouponTemplateMerchantListByTemplateCode(templateCode);
        List<String> hcodes = new ArrayList<>();
        couponTemplateMerchants.forEach(couponTemplateMerchantDO -> {
            hcodes.add(couponTemplateMerchantDO.getHcode());
        });
        bean.setCouponTemplateMerchants(hcodes);
        Long couponCount = couponService.getCouponCount(templateCode, gcode);
        // 判断优惠券是否已发
        if (couponCount.equals(ZERO)) {
            bean.setIsUpdate(NumberEnum.ZERO.getNumber());
        } else {
            bean.setIsUpdate(NumberEnum.ONE.getNumber());
        }
        return bean;
    }

    @Override
    public PageResult<CouponTemplateRespVO> getCouponTemplatePage(CouponTemplatePageReqVO pageReqVO) {
        List<CouponTemplateMerchantDO> couponTemplateMerchantListByGcode = getCouponTemplateMerchantListByGcode(pageReqVO.getGcode());
        // 获取集团下所有酒店
        List<MerchantRespDTO> merchantList = merchantApi.getMerchantList(pageReqVO.getGcode()).getData();
        Map<String, String> collect = merchantList.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
        List<CouponTemplateDO> list = new ArrayList<>();
        Long total = null;
        if (StrUtil.isBlank(pageReqVO.getHcode())) {
            PageResult<CouponTemplateDO> page = couponTemplateMapper.selectPage(pageReqVO);
            list.addAll(page.getList());
            total = page.getTotal();
        } else {
            IPage<CouponTemplateDO> page = couponTemplateMapper.selectCouponTemplatePageByMerchant(MyBatisUtils.buildPage(pageReqVO),
                    pageReqVO.getGcode(),
                    pageReqVO.getHcode(),
                    pageReqVO.getTemplateCode(),
                    pageReqVO.getTemplateName(),
                    pageReqVO.getCouponType(),
                    pageReqVO.getState()
            );
            list.addAll(page.getRecords());
            total = page.getTotal();
        }
        List<CouponTemplateRespVO> bean = getCouponTemplateRespVOS(list, couponTemplateMerchantListByGcode, collect);
        return new PageResult<>(bean, total);
    }

    @NotNull
    private static List<CouponTemplateRespVO> getCouponTemplateRespVOS(List<CouponTemplateDO> list, List<CouponTemplateMerchantDO> couponTemplateMerchantListByGcode, Map<String, String> collect) {
        List<CouponTemplateRespVO> bean = BeanUtils.toBean(list, CouponTemplateRespVO.class);
        bean.forEach(couponTemplateDO -> {
            for (CouponEnum coupon : CouponEnum.values()) {
                if (couponTemplateDO.getCouponType().equals(coupon.getCode())) {
                    couponTemplateDO.setCouponTypeName(coupon.getName());
                }
            }
            List<CouponTemplateMerchantDO> templateMerchantDOList = CollectionUtils.filterList(couponTemplateMerchantListByGcode, couponTemplateMerchantDO -> couponTemplateMerchantDO.getTemplateCode().equals(couponTemplateDO.getTemplateCode()));
            if (CollUtil.isNotEmpty(templateMerchantDOList)) {
                List<CouponTemplateMerchantResp> couponTemplateMerchantResps = templateMerchantDOList.stream().map(couponTemplateMerchantDO -> {
                    CouponTemplateMerchantResp resp = BeanUtils.toBean(couponTemplateMerchantDO, CouponTemplateMerchantResp.class);
                    resp.setHname(collect.get(resp.getHcode()));
                    return resp;
                }).collect(Collectors.toList());
                couponTemplateDO.setHotels(couponTemplateMerchantResps);
            }
        });
        return bean;
    }

    /**
     * 获得优惠券模板列表
     */
    @Override
    public List<CouponTemplateDO> getCouponTemplateList(String gcode, String hcode, String state) {
        return couponTemplateMapper.selectCouponTemplateListByMerchant(gcode, hcode, state);
    }

    /**
     * 优惠卷模板失效
     */
    @Override
    public void loseEfficacy(CouponTemplateUpdateStatusReqVO reqVO) {
        CouponTemplateDO couponTemplateDO = couponTemplateMapper.selectOne(
                CouponTemplateDO::getGcode, reqVO.getGcode(),
                CouponTemplateDO::getTemplateCode, reqVO.getTemplateCode(),
                CouponTemplateDO::getHcode, reqVO.getHcode()
        );
        if (couponTemplateDO == null) {
            throw exception(COUPON_TEMPLATE_NOT_EXISTS);
        }
        couponTemplateMapper.updateById(CouponTemplateDO.builder().id(couponTemplateDO.getId()).state(reqVO.getState()).build());
    }

    /**
     * 充值活动获取优惠券模板列表
     *
     * @param gcode
     * @param hcode
     */
    @Override
    public List<CouponTemplateOfRechargeActivityRespVO> getCouponTemplateListByRecharge(String gcode, String hcode) {
        List<CouponTemplateDO> couponTemplateDOS = couponTemplateMapper.selectCouponTemplateListByMerchant(gcode, hcode, NumberEnum.ONE.getNumber());
        List<CouponTemplateOfRechargeActivityRespVO> couponTemplateOfRechargeActivityRespVOS = CollectionUtils.convertList(couponTemplateDOS, couponTemplateDO -> {
            CouponTemplateOfRechargeActivityRespVO respVO = new CouponTemplateOfRechargeActivityRespVO();
            respVO.setParentCode(couponTemplateDO.getCouponType());
            respVO.setCode(couponTemplateDO.getTemplateCode());
            respVO.setName(couponTemplateDO.getTemplateName());
            return respVO;
        });
        List<CouponEnum> list = Arrays.stream(CouponEnum.values()).toList();
        list.forEach(couponEnum -> {
            couponTemplateOfRechargeActivityRespVOS.add(new CouponTemplateOfRechargeActivityRespVO()
                    .setCode(couponEnum.getCode())
                    .setName(couponEnum.getName())
                    .setParentCode(NumberEnum.ZERO.getNumber()));
        });
        return couponTemplateOfRechargeActivityRespVOS;
    }

    // ==================== 子表（优惠券模板关联门店） ====================

    @Override
    public List<CouponTemplateMerchantDO> getCouponTemplateMerchantListByTemplateCode(String templateCode) {
        return couponTemplateMerchantMapper.selectListByTemplateCode(templateCode);
    }

    /**
     * 获得集团下的优惠券模板关联门店列表
     */
    @Override
    public List<CouponTemplateMerchantDO> getCouponTemplateMerchantListByGcode(String gcode) {
        return couponTemplateMerchantMapper.selectList(CouponTemplateMerchantDO::getGcode, gcode);
    }

    @Override
    public PageResult<CouponTemplateRespVO> getRoomCouponTemplatePage(RoomCouponTemplatePageReqVO pageReqVO) {
        List<CouponTemplateMerchantDO> couponTemplateMerchantListByGcode = getCouponTemplateMerchantListByGcode(pageReqVO.getGcode());
        // 获取集团下所有酒店
        List<MerchantRespDTO> merchantList = merchantApi.getMerchantList(pageReqVO.getGcode()).getData();
        Map<String, String> collect = merchantList.stream().collect(Collectors.toMap(MerchantRespDTO::getHcode, MerchantRespDTO::getHname));
        List<CouponTemplateDO> list = new ArrayList<>();
        Long total = null;
        PageResult<CouponTemplateDO> page = couponTemplateMapper.selectRoomCouponPage(pageReqVO.setState(NumberEnum.ONE.getNumber()));
        list.addAll(page.getList());
        if(StrUtil.isNotEmpty(pageReqVO.getRtCode()) && StrUtil.isNotEmpty(pageReqVO.getChannelCode())){
            list = CollectionUtils.filterList(list, couponTemplateDO -> couponTemplateDO.getRtCodes().contains(pageReqVO.getRtCode())
                    && couponTemplateDO.getChannels().contains(pageReqVO.getChannelCode()));
        }

        total = page.getTotal();

        List<CouponTemplateRespVO> bean = getCouponTemplateRespVOS(list, couponTemplateMerchantListByGcode, collect);
        return new PageResult<>(bean, total);
    }

    private void createCouponTemplateMerchantList(String templateCode, List<CouponTemplateMerchantDO> list) {
        list.forEach(o -> o.setTemplateCode(templateCode));
        couponTemplateMerchantMapper.insertBatch(list);
    }

    private void updateCouponTemplateMerchantList(String templateCode, List<CouponTemplateMerchantDO> list) {
        deleteCouponTemplateMerchantByTemplateCode(templateCode);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createCouponTemplateMerchantList(templateCode, list);
    }

    private void deleteCouponTemplateMerchantByTemplateCode(String templateCode) {
        couponTemplateMerchantMapper.deleteByTemplateCode(templateCode);
    }

}