package info.qizhi.aflower.module.marketing.service.coupon;

import info.qizhi.aflower.module.marketing.api.couponconfig.dto.CouponConfigSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.config.CouponConfigSaveReqVO;
import jakarta.validation.*;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponConfigDO;

import java.util.List;

/**
 * 优惠券设置 Service 接口
 *
 * <AUTHOR>
 */
public interface CouponConfigService {

    /**
     * 创建优惠券设置
     *
     * @param couponConfigSaveReqDTO 创建信息
     * @return 编号
     */
    Long createCouponConfig(@Valid CouponConfigSaveReqDTO couponConfigSaveReqDTO);

    /**
     * 更新优惠券设置
     *
     * @param updateReqVO 更新信息
     */
    void updateCouponConfig(@Valid CouponConfigSaveReqVO updateReqVO);


    /**
     * 获得优惠券设置
     *
     * @param gcode 集团代码
     * @return 优惠券设置
     */
    List<CouponConfigDO> getCouponConfig(String gcode, String hcode);


}