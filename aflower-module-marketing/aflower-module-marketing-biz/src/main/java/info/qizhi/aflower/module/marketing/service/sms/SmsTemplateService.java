package info.qizhi.aflower.module.marketing.service.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.pojo.PageResult;
import info.qizhi.aflower.module.marketing.api.sms.template.dto.SmsDefaultTemplateSaveReqDTO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.SmsParamRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplatePageReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template.SmsTemplateSaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsTemplateDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 短信模板 Service 接口
 *
 * <AUTHOR>
 */
public interface SmsTemplateService {

    /**
     * 创建短信模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSmsTemplate(@Valid SmsTemplateSaveReqVO createReqVO);

    /**
     * 批量创建短信模板
     *
     * @param smsDefaultTemplateSaveReqDTOS 创建信息
     */
    Boolean createSmsTemplates(List<SmsDefaultTemplateSaveReqDTO> smsDefaultTemplateSaveReqDTOS);

    /**
     * 更新短信模板
     *
     * @param updateReqVO 更新信息
     */
    CommonResult<Boolean> updateSmsTemplate(@Valid SmsTemplateSaveReqVO updateReqVO);

    /**
     * 删除短信模板
     *
     * @param id 编号
     */
    void deleteSmsTemplate(Long id);

    /**
     * 获得短信模板
     *
     * @param id 编号
     * @return 短信模板
     */
    SmsTemplateDO getSmsTemplate(Long id);

    /**
     * 获得短信模板分页
     *
     * @param pageReqVO 分页查询
     * @return 短信模板分页
     */
    PageResult<SmsTemplateDO> getSmsTemplatePage(SmsTemplatePageReqVO pageReqVO);

    /**
     * 获得短信参数列表
     */
    List<SmsParamRespVO> getSmsParam();

}