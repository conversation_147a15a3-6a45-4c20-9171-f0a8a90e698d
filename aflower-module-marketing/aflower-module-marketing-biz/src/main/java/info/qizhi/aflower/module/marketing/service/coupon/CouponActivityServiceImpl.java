package info.qizhi.aflower.module.marketing.service.coupon;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import info.qizhi.aflower.framework.common.enums.BooleanEnum;
import info.qizhi.aflower.framework.common.enums.CouponActivityTypeEnum;
import info.qizhi.aflower.framework.common.enums.NumberEnum;
import info.qizhi.aflower.framework.common.util.collection.CollectionUtils;
import info.qizhi.aflower.framework.common.util.date.DateUtils;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.activity.*;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.CouponGenerateReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.coupon.vo.coupon.bo.CouponGenerateBO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityDetailDO;
import info.qizhi.aflower.module.marketing.dal.dataobject.coupon.CouponActivityMerchantDO;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponActivityDetailMapper;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponActivityMapper;
import info.qizhi.aflower.module.marketing.dal.mysql.coupon.CouponActivityMerchantMapper;
import info.qizhi.aflower.module.marketing.dal.redis.activity.CouponActivityRedisDAO;
import info.qizhi.aflower.module.member.api.memberType.MemberTypeApi;
import info.qizhi.aflower.module.member.api.memberType.dto.MemberTypeReqDTO;
import info.qizhi.aflower.module.member.api.memberType.dto.MemberTypeSimpleRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static info.qizhi.aflower.framework.common.exception.util.ServiceExceptionUtil.exception;
import static info.qizhi.aflower.module.marketing.enums.ErrorCodeConstants.*;

/**
 * 赠券活动 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class CouponActivityServiceImpl implements CouponActivityService {

    public static final long COUPON_ACTIVITY_TIMEOUT_MILLIS = 60 * DateUtils.SECOND_MILLIS;
    @Resource
    private CouponActivityRedisDAO couponActivityRedisDAO;
    @Resource
    private CouponActivityMapper couponActivityMapper;
    @Resource
    private CouponActivityDetailMapper couponActivityDetailMapper;
    @Resource
    private CouponActivityMerchantMapper couponActivityMerchantMapper;
    @Resource
    private CouponService couponService;
    @Resource
    private MemberTypeApi memberTypeApi;

    // TODO 初始化
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createCouponActivity(CouponActivitySaveReqVO createReqVO) {
        validateActivity(createReqVO.getCouponActivityDetails(), createReqVO.getGcode(), createReqVO.getActivityCode());
        CouponActivityDO couponActivityDO = couponActivityMapper.selectOne(CouponActivityDO::getActivityCode, createReqVO.getActivityCode());
        if (couponActivityDO != null) {
            throw exception(COUPON_ACTIVITY_EXISTS);
        }
        if (createReqVO.getActivityCode().equals(CouponActivityTypeEnum.REGISTER.getCode()) || createReqVO.getActivityCode().equals(CouponActivityTypeEnum.CHECKOUT.getCode())) {
            List<CouponActivitySaveReqVO.CouponActivityDetail> couponActivityDetails = createReqVO.getCouponActivityDetails();
            for (CouponActivitySaveReqVO.CouponActivityDetail couponActivityDetail : couponActivityDetails) {
                couponActivityDetail.setTargetMtCode(couponActivityDetail.getSourceMtCode());
            }
        }
        // 插入
        CouponActivityDO couponActivity = BeanUtils.toBean(createReqVO, CouponActivityDO.class);
      /*  couponActivity.setIsWx(NumberEnum.ONE.getNumber());
        couponActivity.setIsSms(NumberEnum.ZERO.getNumber());*/
        couponActivityMapper.insert(couponActivity);
        // 插入子表
        List<CouponActivityDetailDO> couponActivityDetailDOS = BeanUtils.toBean(createReqVO.getCouponActivityDetails(), CouponActivityDetailDO.class);
        createCouponActivityDetailList(couponActivity.getActivityCode(), couponActivityDetailDOS, couponActivity.getActivityType(), createReqVO.getGcode());
        if (CollUtil.isNotEmpty(createReqVO.getActivityMerchants())) {
            List<CouponActivityMerchantDO> activityMerchants = CollUtil.newArrayList();
            createReqVO.getActivityMerchants().forEach(merchantCode -> {
                CouponActivityMerchantDO arSetMerchant = CouponActivityMerchantDO.builder().activityCode(couponActivity.getActivityCode()).hcode(merchantCode).gcode(createReqVO.getGcode()).build();
                activityMerchants.add(arSetMerchant);
            });
            createActivityMerchantList(couponActivity.getActivityCode(), activityMerchants);
        }
        // 返回
        return couponActivity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCouponActivity(CouponActivitySaveReqVO updateReqVO) {
        validateActivity(updateReqVO.getCouponActivityDetails(), updateReqVO.getGcode(), updateReqVO.getActivityCode());
        // 校验存在
        CouponActivityDO couponActivityDO = couponActivityMapper.selectOne(CouponActivityDO::getActivityCode, updateReqVO.getActivityCode(), CouponActivityDO::getGcode, updateReqVO.getGcode());
        if (couponActivityDO == null) {
            throw exception(COUPON_ACTIVITY_NOT_EXISTS);
        }
        // 更新
        CouponActivityDO updateObj = BeanUtils.toBean(updateReqVO, CouponActivityDO.class);
        updateObj.setId(couponActivityDO.getId());
        couponActivityMapper.updateById(updateObj);
        // 判断是否是注册和结算，如果是，则将源会员级别和目标会员级别都设置为相同值
        if (updateReqVO.getActivityCode().equals(CouponActivityTypeEnum.REGISTER.getCode()) || updateReqVO.getActivityCode().equals(CouponActivityTypeEnum.CHECKOUT.getCode())) {
            List<CouponActivitySaveReqVO.CouponActivityDetail> couponActivityDetails = updateReqVO.getCouponActivityDetails();
            for (CouponActivitySaveReqVO.CouponActivityDetail couponActivityDetailDO : couponActivityDetails) {
                couponActivityDetailDO.setTargetMtCode(couponActivityDetailDO.getSourceMtCode());
            }
        }
        // 更新子表
        List<CouponActivityDetailDO> couponActivityDetailDOS = BeanUtils.toBean(updateReqVO.getCouponActivityDetails(), CouponActivityDetailDO.class);
        couponActivityDetailDOS.forEach(couponActivityDetailDO -> couponActivityDetailDO.setGcode(updateReqVO.getGcode()));
        updateCouponActivityDetailList(updateReqVO.getActivityCode(), couponActivityDetailDOS, updateReqVO.getActivityType(), updateReqVO.getGcode());
        if (CollUtil.isNotEmpty(updateReqVO.getActivityMerchants())) {
            List<CouponActivityMerchantDO> activityMerchants = CollUtil.newArrayList();
            updateReqVO.getActivityMerchants().forEach(merchantCode -> {
                CouponActivityMerchantDO arSetMerchant = CouponActivityMerchantDO.builder().activityCode(updateReqVO.getActivityCode()).hcode(merchantCode).gcode(updateReqVO.getGcode()).build();
                activityMerchants.add(arSetMerchant);
            });
            updateActivityMerchantList(updateReqVO.getActivityCode(), activityMerchants);
        }
    }

    private void validateActivity(List<CouponActivitySaveReqVO.CouponActivityDetail> couponActivityDetails, String gcode, String activityCode) {
        if (CollUtil.isEmpty(couponActivityDetails)) {
            throw exception(COUPON_ACTIVITY_DETAIL_EMPTY);
        }
        if(CouponActivityTypeEnum.UPGRADE.getCode().equals(activityCode)){
            List<MemberTypeSimpleRespDTO> data = memberTypeApi.getTypeSimpleList(new MemberTypeReqDTO().
                    setGcode(gcode).setIsEnable(NumberEnum.ONE.getNumber())).getData();
            Map<String, String> levelMap = CollectionUtils.convertMap(data, MemberTypeSimpleRespDTO::getMtCode, MemberTypeSimpleRespDTO::getLevel);
            couponActivityDetails.forEach(couponActivityDetail -> {
                if (StrUtil.isNotBlank(couponActivityDetail.getSourceMtCode()) && StrUtil.isNotBlank(couponActivityDetail.getTargetMtCode())) {
              /*  if (couponActivityDetail.getTargetMtLevel() < couponActivityDetail.getSourceMtLevel()) {
                    throw exception(COUPON_TEMPLATE_NOT_EXISTS2);
                }*/
                    try {
                        int targetMtLevel=Integer.parseInt(levelMap.get(couponActivityDetail.getTargetMtCode()));
                        int sourceMtLevel=Integer.parseInt(levelMap.get(couponActivityDetail.getSourceMtCode()));
                        if(targetMtLevel < sourceMtLevel){
                            throw exception(COUPON_TEMPLATE_NOT_EXISTS2);
                        }
                    } catch (NumberFormatException e) {
                        log.info("数字格式错误");
                        // 处理数字格式错误
                         throw exception(COUPON_ACTIVITY_UPGRADE_TYPE_ERROR); // 自定义异常或错误码
                    }
                }
            });
        }

    }

    /**
     * 修改赠券活动状态
     */
    @Override
    public void updateCouponActivityState(RechargeActivityStatusReqVO reqVO) {
        CouponActivityDO couponActivityDO = couponActivityMapper.selectOne(CouponActivityDO::getActivityCode, reqVO.getActivityCode(), CouponActivityDO::getGcode, reqVO.getGcode());
        if (couponActivityDO == null) {
            throw exception(COUPON_ACTIVITY_NOT_EXISTS);
        }
        couponActivityMapper.updateById(CouponActivityDO.builder().id(couponActivityDO.getId()).state(reqVO.getState()).build());
    }

    @Override
    public CouponActivityRespVO getCouponActivity(String activityCode, String gcode) {
        CouponActivityDO couponActivityDO = couponActivityMapper.selectOne(CouponActivityDO::getActivityCode, activityCode, CouponActivityDO::getGcode, gcode);
        if (couponActivityDO == null) {
            throw exception(COUPON_ACTIVITY_NOT_EXISTS);
        }
        CouponActivityRespVO couponActivityRespVO = BeanUtils.toBean(couponActivityDO, CouponActivityRespVO.class);
        // 获取活动下所有酒店
        List<CouponActivityMerchantDO> activityMerchantListByActivityCode = getActivityMerchantListByActivityCode(activityCode);
        List<String> hcodes = new ArrayList<>();
        activityMerchantListByActivityCode.forEach(couponActivityMerchantDO -> {
            hcodes.add(couponActivityMerchantDO.getHcode());
        });
        // 获取活动下明细
        List<CouponActivityDetailDO> activityDetailListByActivityCode = getCouponActivityDetailListByActivityCode(activityCode);
        couponActivityRespVO.setCouponActivityDetails(activityDetailListByActivityCode);
        couponActivityRespVO.setActivityMerchants(hcodes);
        return couponActivityRespVO;
    }

    @Override
    public List<CouponActivityDO> getCouponActivityList(CouponActivityListReqVO reqVO) {
        return couponActivityMapper.selectList(reqVO);
    }

    /**
     * 赠券
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @Lock4j(keys = {"#reqVO.gcode", "#reqVO.hcode", "#reqVO.activityType"}, expire = COUPON_ACTIVITY_TIMEOUT_MILLIS, acquireTimeout = 5000)
    public Boolean freeTicket(CouponActivityReqVO reqVO) {
        // 根据活动代码和全局代码查询优惠活动信息
        CouponActivityDO couponActivityDO = couponActivityMapper.selectOne(CouponActivityDO::getActivityCode, reqVO.getActivityCode(), CouponActivityDO::getGcode, reqVO.getGcode());
        if (couponActivityDO == null) {
            return false;
        }
        // 如果优惠活动状态为无效，则直接返回false
        if (Objects.equals(BooleanEnum.FALSE.getValue(), couponActivityDO.getState())) {
            return false;
        }
        // 检查当前日期是否在优惠活动的有效期内
        if (!LocalDateTimeUtil.isIn(LocalDateTime.now(), couponActivityDO.getDateStart().atStartOfDay(), couponActivityDO.getDateEnd().atTime(LocalTime.MAX), true, true)) {
            return false;
        }
        // 检查门店是否在优惠活动门店范围内
        List<CouponActivityMerchantDO> couponActivityMerchantDOS = getActivityMerchantListByActivityCode(couponActivityDO.getActivityCode());
        List<CouponActivityMerchantDO> couponActivityMerchantDO = CollectionUtils.filterList(couponActivityMerchantDOS, o -> o.getHcode().equals(reqVO.getHcode()));
        if (CollUtil.isEmpty(couponActivityMerchantDO)) {
            return false;
        }
        // 检查会员级别是否在优惠活动会员范围内
        List<CouponActivityDetailDO> couponActivityDetailDOS = getCouponActivityDetailListByActivityCode(couponActivityDO.getActivityCode());
        List<CouponActivityDetailDO> couponActivityDetailDO = CollectionUtils.filterList(couponActivityDetailDOS,
                o -> o.getSourceMtCode().equals(reqVO.getSourceMtCode()) && o.getTargetMtCode().equals(reqVO.getTargetMtCode()));
        if (CollUtil.isEmpty(couponActivityDetailDO)) {
            return false;
        }
        // 生成优惠券
        return createCoupon(reqVO, couponActivityDetailDO);
    }

    private Boolean createCoupon(CouponActivityReqVO reqVO, List<CouponActivityDetailDO> couponActivityDetailDO) {
        List<CouponGenerateBO> couponGenerates = new ArrayList<>();
        for (CouponActivityDetailDO o : couponActivityDetailDO) {
            CouponGenerateBO couponGenerateBO = new CouponGenerateBO();
            couponGenerateBO.setHcode(reqVO.getHcode());
            couponGenerateBO.setTemplateCode(o.getTemplateCode());
            couponGenerateBO.setActivityCode(o.getActivityCode());
            couponGenerateBO.setGcode(reqVO.getGcode());
            couponGenerateBO.setNum(o.getNum());
            couponGenerateBO.setBelong(reqVO.getPhone());
            couponGenerateBO.setName(reqVO.getName());
            couponGenerates.add(couponGenerateBO);
        }
        return couponService.createCoupon(new CouponGenerateReqVO().setCouponGenerate(couponGenerates)
                                                                   .setGcode(reqVO.getGcode())
                                                                   .setHcode(reqVO.getHcode())
                                                                   .setOrderNo(reqVO.getOrderNo())
                                                                   .setOrderType(reqVO.getOrderType()));
    }

    // ==================== 子表（赠券活动明细） ====================

    @Override
    public List<CouponActivityDetailDO> getCouponActivityDetailListByActivityCode(String activityCode) {
        return couponActivityDetailMapper.selectListByActivityCode(activityCode);
    }

    private void createCouponActivityDetailList(String activityCode, List<CouponActivityDetailDO> list, String activityType, String gcode) {
        list.forEach(o -> o.setActivityCode(activityCode).setActivityType(activityType).setGcode(gcode));
        couponActivityDetailMapper.insertBatch(list);
    }

    private void updateCouponActivityDetailList(String activityCode, List<CouponActivityDetailDO> list, String activityType, String gcode) {
        deleteCouponActivityDetailByActivityCode(activityCode);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createCouponActivityDetailList(activityCode, list, activityType, gcode);
    }

    private void deleteCouponActivityDetailByActivityCode(String activityCode) {
        couponActivityDetailMapper.deleteByActivityCode(activityCode);
    }

    // ==================== 子表（赠券活动关联门店） ====================

    @Override
    public List<CouponActivityMerchantDO> getActivityMerchantListByActivityCode(String activityCode) {
        return couponActivityMerchantMapper.selectListByActivityCode(activityCode);
    }

    private void createActivityMerchantList(String activityCode, List<CouponActivityMerchantDO> list) {
        list.forEach(o -> o.setActivityCode(activityCode));
        couponActivityMerchantMapper.insertBatch(list);
    }

    private void updateActivityMerchantList(String activityCode, List<CouponActivityMerchantDO> list) {
        deleteActivityMerchantByActivityCode(activityCode);
        list.forEach(o -> o.setId(null).setUpdater(null).setUpdateTime(null)); // 解决更新情况下：1）id 冲突；2）updateTime 不更新
        createActivityMerchantList(activityCode, list);
    }

    private void deleteActivityMerchantByActivityCode(String activityCode) {
        couponActivityMerchantMapper.deleteByActivityCode(activityCode);
    }

}