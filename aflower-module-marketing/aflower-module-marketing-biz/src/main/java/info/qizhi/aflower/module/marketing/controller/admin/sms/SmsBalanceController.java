package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance.SmsBalanceRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.balance.SmsBalanceSaveReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge.SmsRechargeReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsBalanceDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsBalanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信余额")
@RestController
@RequestMapping("/marketing/sms-balance")
@Validated
public class SmsBalanceController {

    @Resource
    private SmsBalanceService smsBalanceService;

    @PutMapping("/update")
    @Operation(summary = "更新短信余额")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-balance:update')")
    public CommonResult<Boolean> updateSmsBalance(@Valid @RequestBody SmsBalanceSaveReqVO updateReqVO) {
        smsBalanceService.updateSmsBalance(updateReqVO);
        return success(true);
    }


    @GetMapping("/get")
    @Operation(summary = "获得短信余额")
    @Parameter(name = "gcode", description = "集团代码", required = true)
    //@PreAuthorize("@ss.hasPermission('marketing:sms-balance:query')")
    public CommonResult<SmsBalanceRespVO> getSmsBalance(@RequestParam("gcode") String gcode) {
        SmsBalanceDO smsBalance = smsBalanceService.getSmsBalance(gcode);
        return success(BeanUtils.toBean(smsBalance, SmsBalanceRespVO.class));
    }

//    @PutMapping("/submit")
//    @Operation(summary = "短信充值-确认订单")
//    public CommonResult<SmsRechargeOrderRespVO> updateSmsBalance(@Valid @RequestBody SmsRechargeReqVO reqVO) {
//        return success(smsBalanceService.smsRecharge(reqVO));
//    }

    @PutMapping("/submit")
    @Operation(summary = "短信充值-确认订单")
    public CommonResult<String> updateSmsBalance(@Valid @RequestBody SmsRechargeReqVO reqVO) {
        return success(smsBalanceService.smsRecharge(reqVO));
    }

    @PostMapping("/callback")
    @Operation(summary = "处理支付回调")
    public CommonResult<Boolean> handlePaymentCallback(@RequestParam String gcode,
                                                       @RequestParam String hcode,
                                                       @RequestParam String orderNo) {
        smsBalanceService.smsRechargeCallback(gcode, hcode, orderNo);
        return success(true);
    }

//    @GetMapping("/forewarning")
//    @Operation(summary = "预警")
//    @Parameter(name = "gcode", description = "集团代码", required = true)
//    @PreAuthorize("@ss.hasPermission('marketing:sms-balance:query')")
//    public CommonResult<Boolean> smsWarning(@RequestParam("gcode") String gcode) {
//        smsBalanceService.smsWarning(gcode);
//        return success(true);
//    }

}