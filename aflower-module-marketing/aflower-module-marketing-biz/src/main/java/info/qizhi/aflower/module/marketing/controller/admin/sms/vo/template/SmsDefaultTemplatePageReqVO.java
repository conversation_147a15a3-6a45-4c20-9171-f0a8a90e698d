package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import info.qizhi.aflower.module.marketing.api.sms.entity.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 短信默认模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SmsDefaultTemplatePageReqVO extends BaseEntity {

    @ApiModelProperty(name = "sendScene", value = "发送场景")
    private String sendScene;

    @ApiModelProperty(name = "pageNumber", value = "页码，默认=1")
    private Integer pageNum = 1;

    @ApiModelProperty(name = "pageSize", value = "页大小，默认=10")
    private Integer pageSize = 10;

}