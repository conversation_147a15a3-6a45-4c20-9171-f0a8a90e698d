package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.recharge;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import info.qizhi.aflower.framework.common.core.annotation.FenToYuanSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 短信充值订单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SmsRechargeOrderRespVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1969")
    @ExcelProperty("id")
    private Long id;

    @Schema(description = "集团代码")
    @ExcelProperty("集团代码")
    private String gcode;

    @Schema(description = "充值门店代码;如果为0，表示是集团充值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值门店代码;如果为0，表示是集团充值")
    private String hcode;

    @Schema(description = "活动代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("活动代码")
    private String activeCode;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("活动名称")
    private String activeName;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("订单号")
    private String orderNo;

    @Schema(description = "充值金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("充值金额")
    @JsonSerialize(using = FenToYuanSerializer.class)
    private Long fee;

    @Schema(description = "短信条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("短信条数")
    private Integer smsNum;

    @Schema(description = "赠送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("赠送条数")
    private Integer giveNum;

    @Schema(description = "支付方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付方式")
    private String payMode;

    @Schema(description = "支付流水号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("支付流水号")
    private String paySerialNo;

    @Schema(description = "支付时间")
    @ExcelProperty("支付时间")
    private LocalDateTime payTime;

    @Schema(description = "状态;0:未支付 1:已支付 2:已过期", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("状态;0:未支付 1:已支付 2:已过期")
    private String state;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}