package info.qizhi.aflower.module.marketing.controller.admin.sms;

import info.qizhi.aflower.framework.apilog.core.annotation.ApiAccessLog;
import info.qizhi.aflower.framework.common.pojo.CommonResult;
import info.qizhi.aflower.framework.common.util.object.BeanUtils;
import info.qizhi.aflower.framework.excel.core.util.ExcelUtils;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityListReqVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivityRespVO;
import info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity.SmsActivitySaveReqVO;
import info.qizhi.aflower.module.marketing.dal.dataobject.sms.SmsActivityDO;
import info.qizhi.aflower.module.marketing.service.sms.SmsActivityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static info.qizhi.aflower.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static info.qizhi.aflower.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 短信充值活动")
@RestController
@RequestMapping("/marketing/sms-activity")
@Validated
//@Deprecated
public class SmsActivityController {

    @Resource
    private SmsActivityService smsActivityService;

    @PostMapping("/create")
    @Operation(summary = "创建短信充值活动")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-activity:create')")
    public CommonResult<Long> createSmsActivity(@Valid @RequestBody SmsActivitySaveReqVO createReqVO) {
        return success(smsActivityService.createSmsActivity(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新短信充值活动")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-activity:update')")
    public CommonResult<Boolean> updateSmsActivity(@Valid @RequestBody SmsActivitySaveReqVO updateReqVO) {
        smsActivityService.updateSmsActivity(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除短信充值活动")
    @Parameter(name = "id", description = "编号", required = true)
    //@PreAuthorize("@ss.hasPermission('marketing:sms-activity:delete')")
    public CommonResult<Boolean> deleteSmsActivity(@RequestParam("id") Long id) {
        smsActivityService.deleteSmsActivity(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得短信充值活动")
    @Parameter(name = "activeCode", description = "活动代码", required = true)
    //@PreAuthorize("@ss.hasPermission('marketing:sms-activity:query')")
    public CommonResult<SmsActivityRespVO> getSmsActivity(@RequestParam("activeCode")String activeCode) {
        SmsActivityRespVO smsActivity = smsActivityService.getSmsActivity(activeCode);
        return success(smsActivity);
    }

    @GetMapping("/list")
    @Operation(summary = "获得短信充值活动列表")
    //@PreAuthorize("@ss.hasPermission('marketing:sms-activity:query')")
    public CommonResult<List<SmsActivityRespVO>> getSmsActivityPage(@Valid SmsActivityListReqVO reqVO) {
        List<SmsActivityDO> pageResult = smsActivityService.getSmsActivityList(reqVO);
        return success(BeanUtils.toBean(pageResult, SmsActivityRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出短信充值活动 Excel")
    @PreAuthorize("@ss.hasPermission('marketing:sms-activity:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSmsActivityExcel(@Valid SmsActivityListReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        List<SmsActivityDO> list = smsActivityService.getSmsActivityList(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "短信充值活动.xls", "数据", SmsActivityRespVO.class,
                        BeanUtils.toBean(list, SmsActivityRespVO.class));
    }

}