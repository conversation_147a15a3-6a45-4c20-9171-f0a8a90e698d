package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.activity;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import info.qizhi.aflower.framework.common.core.annotation.YuanToFenDeserializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDate;

@Schema(description = "管理后台 - 短信充值活动新增/修改 Request VO")
@Data
public class SmsActivitySaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20506")
    private Long id;

    @Schema(description = "活动代码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String activityCode;

    @Schema(description = "活动名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "{activityName.notempty}")
    private String activityName;

    @Schema(description = "金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{feeAmount.notnull}")
    @JsonDeserialize(using = YuanToFenDeserializer.class)
    private Long fee;

    @Schema(description = "短信条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{smsNum.notnull}")
    private Integer smsNum;

    @Schema(description = "赠送条数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{giveNum.notnull}")
    private Integer giveNum;

    @Schema(description = "活动开始日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{dateStart.notnull}")
    private LocalDate dateStart;

    @Schema(description = "活动结束日期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "{dateEnd.notnull}")
    private LocalDate dateEnd;

    @Schema(description = "状态;0：无效 1：有效", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{state.notempty}")
    private String state;

}