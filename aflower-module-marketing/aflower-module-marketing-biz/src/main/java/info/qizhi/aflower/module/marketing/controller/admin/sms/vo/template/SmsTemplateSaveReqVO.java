package info.qizhi.aflower.module.marketing.controller.admin.sms.vo.template;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

@Schema(description = "管理后台 - 短信模板新增/修改 Request VO")
@Data
public class SmsTemplateSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22392")
    private Long id;

    @Schema(description = "集团代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{gcode.notempty}")
    private String gcode;

    @Schema(description = "门店代码;如果是集团模板，当前字段为0", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "门店代码;如果是集团模板，当前字段为0不能为空")
    private String hcode;

    @Schema(description = "模板代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{templateCode.notempty}")
    private String templateCode;

    @Schema(description = "模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotEmpty(message = "{templateName.notempty}")
    private String templateName;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{content.notempty}")
    private String content;

    @Schema(description = "是否系统模板;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isSys.notempty}")
    private String isSys;

    @Schema(description = "模板类型;0:普通模板 1:营销模板", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "{templateType.notempty}")
    private String templateType;

    @Schema(description = "是否集团短信模板;0:否 1：是", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{isG.notempty}")
    private String isG;

    @Schema(description = "状态;0:待审核 1:启用 2：停用 3:未通过", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{templateState.notempty}")
    private String state;

    @Schema(description = "未通过理由", example = "不喜欢")
    private String reason;

    @Schema(description = "业务代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "{bizCode.notempty}")
    private String bizCode;

    @Schema(description = "业务类型名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "{bizName.notempty}")
    private String bizName;

}